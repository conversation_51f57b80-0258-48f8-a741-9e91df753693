class ContainerStartException(RuntimeError):
    """Exception raised when container fails to start."""
    ...

class ContainerConnectException(RuntimeError):
    """Exception raised when connection to container fails."""
    ...

class ContainerIsNotRunning(RuntimeError):
    """Exception raised when operation requires running container."""
    ...

class NoSuchPortExposed(RuntimeError):
    """Exception raised when requested port is not exposed."""
    ...
