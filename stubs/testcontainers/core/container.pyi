from os import Path<PERSON><PERSON>
from socket import socket
from typing import Any, Self
from collections.abc import Sequence

from docker.models.containers import Container

logger: Any

class DockerContainer:
    def __init__(
        self,
        image: str,
        docker_client_kw: dict[str, Any] | None = None,
        **kwargs: Any
    ) -> None: ...

    def with_env(self, key: str, value: str) -> Self: ...
    def with_env_file(self, env_file: str | PathLike[str]) -> Self: ...
    def with_bind_ports(self, container: int, host: int | None = None) -> Self: ...
    def with_exposed_ports(self, *ports: int) -> Self: ...
    def with_network(self, network: Any) -> Self: ...
    def with_network_aliases(self, *aliases: str) -> Self: ...
    def with_kwargs(self, **kwargs: Any) -> Self: ...
    def maybe_emulate_amd64(self) -> Self: ...
    def start(self) -> Self: ...
    def stop(self, force: bool = True, delete_volume: bool = True) -> None: ...
    def __enter__(self) -> Self: ...
    def __exit__(
        self,
        exc_type: type[BaseException] | None,
        exc_val: BaseException | None,
        exc_tb: Any
    ) -> None: ...
    def get_container_host_ip(self) -> str: ...
    def get_exposed_port(self, port: int) -> int: ...
    def with_command(self, command: str | Sequence[str]) -> Self: ...
    def with_name(self, name: str) -> Self: ...
    def with_volume_mapping(
        self,
        host: str,
        container: str,
        mode: str = "rw"
    ) -> Self: ...
    def get_wrapped_container(self) -> Container: ...
    def get_docker_client(self) -> Any: ...
    def get_logs(self) -> tuple[bytes, bytes]: ...
    def exec(self, command: str | Sequence[str]) -> tuple[int, bytes]: ...


class Reaper:
    _instance: Reaper | None
    _container: DockerContainer | None
    _socket: socket | None

    @classmethod
    def get_instance(cls) -> Reaper: ...

    @classmethod
    def delete_instance(cls) -> None: ...
