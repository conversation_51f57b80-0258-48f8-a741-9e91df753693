from os import PathLike
from typing import Any, Self

class DockerImage:
    def __init__(
        self,
        path: str | PathLike[str],
        tag: str,
        dockerfile: str = "Dockerfile",
        **kwargs: Any,
    ) -> None: ...

    def __enter__(self) -> Self: ...
    def __exit__(self, exc_type: Any, exc_val: Any, exc_tb: Any) -> None: ...
    def __str__(self) -> str: ...
    def build(self) -> Self: ...
    def remove(self) -> None: ...

    @property
    def tag(self) -> str: ...

    @property
    def short_id(self) -> str: ...
