from dataclasses import dataclass
from enum import Enum

class ConnectionMode(Enum):
    bridge_ip: str
    gateway_ip: str
    docker_host: str

MAX_TRIES: int
SLEEP_TIME: int
TIMEOUT: int
RYUK_IMAGE: str
RYUK_PRIVILEGED: bool
RYUK_DISABLED: bool
RYUK_DOCKER_SOCKET: str
RYUK_RECONNECTION_TIMEOUT: str

@dataclass
class TestcontainersConfiguration:
    max_tries: int
    sleep_time: int
    ryuk_image: str
    ryuk_privileged: bool
    ryuk_disabled: bool
    ryuk_docker_socket: str
    ryuk_reconnection_timeout: str
    tc_properties: dict[str, str]
    _docker_auth_config: str | None
    tc_host_override: str | None
    connection_mode_override: ConnectionMode | None

testcontainers_config: TestcontainersConfiguration
