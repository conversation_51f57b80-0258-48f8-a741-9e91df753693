from typing import Any, Callable, TypeVar
from collections.abc import Sequence

logger: Any
TRANSIENT_EXCEPTIONS: tuple[type[Exception], ...]
_NOT_EXITED_STATUSES: Sequence[str]

F = TypeVar("F", bound=Callable[..., Any])

def wait_container_is_ready(
    *transient_exceptions: type[Exception]
) -> Callable[[F], F]: ...

def wait_for(condition: Callable[..., bool]) -> bool: ...

def wait_for_logs(
    container: Any,  # DockerContainer
    predicate: Callable[[str], bool] | str,
    timeout: float = 120,
    interval: float = 1,
    predicate_streams_and: bool = True,
    raise_on_exit: bool = True,
) -> float: ...
