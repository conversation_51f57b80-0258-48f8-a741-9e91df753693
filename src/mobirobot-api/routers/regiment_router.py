# ruff: noqa: D103
from typing import Annotated

from fastapi import APIRouter, Body, HTTPException, status

from mobirobot.regiments.regiment import Regiment
from src..dependencies import RegimentServiceDep

router = APIRouter()


@router.get("/regiments")
async def regiments(
    regiment_service: RegimentServiceDep,
) -> list[Regiment]:
    return regiment_service.get_regiment_list()


@router.get("/regiments/{regiment_id}")
async def get_regiment(
    regiment_id: int,
    regiment_service: RegimentServiceDep,
) -> Regiment:
    if regiment_id not in regiment_service.regiments:
        raise HTTPException(status_code=404)

    return regiment_service.regiments[regiment_id]


@router.post("/regiments/{regiment_id}")
async def update_regiment(
    regiment_id: int,
    regiment: Regiment,
    regiment_service: RegimentServiceDep,
) -> Regiment:
    if regiment_id not in regiment_service.regiments:
        raise HTTPException(status_code=404)

    regiment_service.update_regiment(regiment_id, regiment)
    return regiment_service.regiments[regiment_id]


@router.post("/regiments", status_code=status.HTTP_201_CREATED)
async def create_regiment(
    name: Annotated[str, Body(embed=True)],
    regiment_service: RegimentServiceDep,
) -> Regiment:
    return regiment_service.create_regiment(name)
