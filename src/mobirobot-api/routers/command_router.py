from dataclasses import asdict
from typing import Annotated

import sentry_sdk
from fastapi import (
    APIRouter,
    Body,
    Depends,
    HTTPException,
    Response,
    status,
)
from pydantic import BaseModel

from mobirobot.common.structured_logging import LoggerType, get_logger
from mobirobot.execution import ExerciseSessionManager
from src..dependencies import (
    ExerciseData,
    RegimentServiceDep,
    get_exercise_session_manager,
)

logger: LoggerType = get_logger(__name__)


# Define Response Models
class StatusResponse(BaseModel):
    """Standard response model for status messages."""

    message: str


class MessageResponse(BaseModel):
    """Standard response model for confirmation messages."""

    message: str


router = APIRouter(tags=["General Commands"])


@router.post("/stop")
async def stop_control(
    session_manager: Annotated[
        ExerciseSessionManager, Depends(get_exercise_session_manager)
    ],
) -> Response:
    """Stop the current activity.

    Args:
        session_manager: The exercise session manager.

    Returns:
        The response.
    """
    await session_manager.stop_all_activities()
    # Stop always succeeds logically, even if nothing was running
    return Response(status_code=status.HTTP_202_ACCEPTED)


@router.post("/pause")
async def pause_control(
    session_manager: Annotated[
        ExerciseSessionManager, Depends(get_exercise_session_manager)
    ],
) -> Response:
    """Pause the current activity.

    Args:
        session_manager: The exercise session manager.

    Returns:
        The response.
    """
    session_manager.pause()
    return Response(status_code=status.HTTP_202_ACCEPTED)


@router.post("/resume")
async def resume_control(
    session_manager: Annotated[
        ExerciseSessionManager, Depends(get_exercise_session_manager)
    ],
) -> Response:
    """Resume the current activity.

    Args:
        session_manager: The exercise session manager.

    Returns:
        The response.
    """
    session_manager.resume()
    return Response(status_code=status.HTTP_202_ACCEPTED)


@router.post("/volume")
async def volume_control(
    volume: Annotated[float, Body(embed=True)],
    session_manager: Annotated[
        ExerciseSessionManager, Depends(get_exercise_session_manager)
    ],
) -> Response:
    """Set the volume.

    Args:
        volume: The volume level (0.0 to 1.0).
        session_manager: The exercise session manager.

    Returns:
        The response.
    """
    logger.info("Received request for volume control", volume=volume)
    await session_manager.set_volume(volume)
    # Note: The monitoring service will automatically pick up the volume change
    # in its next polling cycle, so no manual update is needed
    return Response(status_code=status.HTTP_200_OK)


@router.post("/stiffness")
async def stiffness_control(
    # Explicitly expect stiffness in the body
    stiffness: Annotated[float, Body(embed=True)],
    session_manager: Annotated[
        ExerciseSessionManager, Depends(get_exercise_session_manager)
    ],
) -> StatusResponse:
    """Set the stiffness.

    Args:
        stiffness: The stiffness level (0.0 to 1.0).
        session_manager: The exercise session manager.

    Returns:
        The response with a status message.
    """
    response_message = await session_manager.set_stiffness(stiffness)
    # Return the Pydantic model instance
    return StatusResponse(message=response_message)


@router.post("/followme")
async def follow_me(
    session_manager: Annotated[
        ExerciseSessionManager, Depends(get_exercise_session_manager)
    ],
) -> StatusResponse:
    """Toggle Follow Me behavior.

    Args:
        session_manager: The exercise session manager.

    Returns:
        The response with a status message.
    """
    response_message = await session_manager.toggle_follow_me()
    # Return the Pydantic model instance
    return StatusResponse(message=response_message)


@router.post("/start")
async def start_control(
    regiment_id: Annotated[int, Body(embed=True)],
    regiment_service: RegimentServiceDep,
    session_manager: Annotated[
        ExerciseSessionManager, Depends(get_exercise_session_manager)
    ],
) -> Response:
    """Start the specified regiment.

    Args:
        regiment_id: The ID of the regiment to start.
        regiment_service: The regiment service.
        session_manager: The exercise session manager.

    Returns:
        The response.

    Raises:
        HTTPException: If the regiment cannot be started.
    """
    logger.info("Received request to start regiment", regiment_id=regiment_id)
    try:
        # Get regiment from service
        try:
            regiment = regiment_service.regiments[regiment_id]
        except KeyError:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Regiment {regiment_id} not found",
            ) from None

        # Start regiment execution
        await session_manager.execute_regiment(regiment)

        try:
            sentry_sdk.set_context("regiment", asdict(regiment))
        except (KeyError, TypeError) as e:
            logger.warning(
                "Failed to set Sentry context for regiment",
                regiment_id=regiment_id,
                error=str(e),
            )

        return Response(status_code=status.HTTP_201_CREATED)

    except Exception as e:
        logger.exception("Unexpected error starting regiment", regiment_id=regiment_id)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred.",
        ) from e


@router.post("/user_code")
async def user_code(user_code: Annotated[str, Body(embed=True)]) -> MessageResponse:
    """Receive user code (purpose to be defined).

    Args:
        user_code: The user code received.

    Returns:
        A confirmation message.
    """
    logger.info("Received user code", user_code=user_code)
    # Return the Pydantic model instance
    return MessageResponse(message="Code received")


@router.post("/singleexercise")
async def singleexercise(
    data: ExerciseData,
    session_manager: Annotated[
        ExerciseSessionManager, Depends(get_exercise_session_manager)
    ],
) -> Response:
    """Start a single exercise.

    Args:
        data: The exercise data (name, duration, station).
        session_manager: The exercise session manager.

    Returns:
        The response.
    """
    logger.info(
        "Received request for single exercise",
        exercise_name=data.exercise_name,
        exercise_duration=data.exercise_duration,
    )

    await session_manager.execute_single_exercise(data.exercise_name)

    return Response(status_code=status.HTTP_200_OK)
