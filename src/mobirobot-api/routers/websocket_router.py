import asyncio
from contextlib import suppress
from typing import TYPE_CHECKING, Annotated

from fastapi import (
    APIRouter,
    Depends,
    WebSocket,
    WebSocketDisconnect,
    status,
)

from mobirobot.common.structured_logging import LoggerType, get_logger
from mobirobot.execution import ExerciseSessionManager
from mobirobot.monitoring import MonitoringService
from src..dependencies import (
    get_exercise_session_manager_ws,
    get_status_monitor_service_ws,
)
from src..web_types import AppStatusModel

if TYPE_CHECKING:
    from mobirobot.monitoring.models import CombinedStatus
    from src..web_types import RunningState

logger: LoggerType = get_logger(__name__)

router = APIRouter(tags=["Websockets"])


def _convert_to_app_status_model(
    combined_status: "CombinedStatus", running_state: "RunningState"
) -> "AppStatusModel":
    """Convert CombinedStatus to AppStatusModel for backward compatibility.

    Args:
        combined_status: The combined status from the monitoring service.
        running_state: The current execution running state.

    Returns:
        AppStatusModel compatible with the frontend.
    """
    # Extract NAO status or use defaults
    nao_status = combined_status.nao
    if nao_status:
        battery = nao_status.battery
        volume = nao_status.volume
        hot_joints = nao_status.hot_joints
        buttons = nao_status.buttons
    else:
        battery = 0.0
        volume = 0.0
        hot_joints = []
        buttons = {}

    return AppStatusModel(
        battery=battery,
        volume=volume,
        hot_joints=hot_joints,
        running=running_state,
        buttons=buttons,
    )


@router.websocket("/state/ws")
async def websocket_endpoint(
    websocket: WebSocket,
    status_monitor_service: Annotated[
        MonitoringService, Depends(get_status_monitor_service_ws)
    ],
    session_manager: Annotated[
        ExerciseSessionManager, Depends(get_exercise_session_manager_ws)
    ],
) -> None:
    """Websocket endpoint for streaming application state."""
    await websocket.accept()
    try:
        while True:
            # Get combined status from monitoring service
            combined_status = status_monitor_service.get_combined_status()

            # Convert to AppStatusModel for backward compatibility
            app_status_model = _convert_to_app_status_model(
                combined_status, session_manager.running_state
            )

            await websocket.send_json(app_status_model.model_dump(mode="json"))
            await asyncio.sleep(1)
    except WebSocketDisconnect:
        logger.debug("State Websocket disconnected")
    except asyncio.CancelledError:
        logger.debug("State Websocket cancelled during shutdown")
    finally:
        if websocket.client_state != websocket.client_state.DISCONNECTED:
            with suppress(RuntimeError):
                await websocket.close(code=status.WS_1000_NORMAL_CLOSURE)
        logger.debug("State websocket closed")
