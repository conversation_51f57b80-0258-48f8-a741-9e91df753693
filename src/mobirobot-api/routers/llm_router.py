from typing import Annotated

from fastapi import APIRouter, Depends, Response, status

from mobirobot.common.structured_logging import LoggerType, get_logger
from mobirobot.execution.session_manager import ExerciseSessionManager
from src..dependencies import (
    get_exercise_session_manager,
)

logger: LoggerType = get_logger(__name__)

router = APIRouter(prefix="/commands/llm", tags=["LLM Commands"])


@router.post("/start")
async def start_llm(
    session_manager: Annotated[
        ExerciseSessionManager, Depends(get_exercise_session_manager)
    ],
):
    """Starts the LLM interaction service.

    Args:
        session_manager (ExerciseSessionManager): The exercise session manager.
    """
    logger.info("Received request to start LLM")
    session_manager.start_llm_chat()


@router.post("/stop")
async def stop_llm(
    session_manager: Annotated[
        ExerciseSessionManager, Depends(get_exercise_session_manager)
    ],
) -> Response:
    """Stops the LLM interaction service.

    Args:
        session_manager (ExerciseSessionManager): The exercise session manager.

    Returns:
        Response: The response.
    """
    logger.info("Received request to stop LLM")
    # Stop LLM by stopping all activities - could be more granular in the future
    await session_manager.stop_all_activities()
    return Response(status_code=status.HTTP_202_ACCEPTED)
