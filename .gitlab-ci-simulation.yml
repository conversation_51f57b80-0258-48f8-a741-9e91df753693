# Enhanced GitLab CI for NAO Simulation with BuildKit caching and non-privileged support
# Include this in your main .gitlab-ci.yml or use as standalone

variables:
  # Docker configuration for non-privileged mode
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"
  DOCKER_HOST: tcp://docker:2376
  DOCKER_TLS_VERIFY: 1
  DOCKER_CERT_PATH: "$DOCKER_TLS_CERTDIR/client"
  
  # BuildKit configuration
  DOCKER_BUILDKIT: 1
  BUILDKIT_PROGRESS: plain
  
  # NAO Simulation specific variables
  NAO_SIM_TAG: "nao-simulation:${CI_COMMIT_SHORT_SHA}"
  NAO_SIM_REGISTRY: "${CI_REGISTRY_IMAGE}"
  NAO_SIM_PLATFORM: "linux/amd64"
  NAO_SIM_PUSH: "true"
  
  # Cache configuration (automatically set based on registry)
  NAO_SIM_CACHE_FROM: "type=registry,ref=${CI_REGISTRY_IMAGE}/nao-simulation:buildcache,type=registry,ref=${CI_REGISTRY_IMAGE}/nao-simulation:latest"
  NAO_SIM_CACHE_TO: "type=registry,ref=${CI_REGISTRY_IMAGE}/nao-simulation:buildcache,mode=max"

# Use docker:dind in non-privileged mode
.docker_template: &docker_template
  image: python:3.12
  services:
    - name: docker:dind
      # Non-privileged dind configuration
      command: ["--tls=false", "--host=0.0.0.0:2375"]
  variables:
    DOCKER_HOST: tcp://docker:2375
    DOCKER_TLS_CERTDIR: ""
  before_script:
    # Install Docker CLI and uv
    - apt-get update && apt-get install -y docker.io
    - pip install uv
    - uv sync
    # Login to registry
    - echo "$CI_REGISTRY_PASSWORD" | docker login -u "$CI_REGISTRY_USER" --password-stdin "$CI_REGISTRY"
    # Verify Docker connection
    - docker info

stages:
  - build
  - test
  - security
  - deploy

# Build simulation container with BuildKit caching
build_simulation:
  <<: *docker_template
  stage: build
  script:
    # Build with optimized BuildKit caching
    - uv run mobirobot-cli simulation build 
        --tag "${NAO_SIM_TAG}"
        --registry "${NAO_SIM_REGISTRY}"
        --push
        --ci
        --buildkit
        --platform "${NAO_SIM_PLATFORM}"
        --optimize
    
    # Tag as latest for main branch
    - |
      if [ "$CI_COMMIT_REF_NAME" = "$CI_DEFAULT_BRANCH" ]; then
        docker tag "${NAO_SIM_TAG}" "${CI_REGISTRY_IMAGE}/nao-simulation:latest"
        docker push "${CI_REGISTRY_IMAGE}/nao-simulation:latest"
      fi
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - changes:
        - packages/nao-simulation/**/*
        - packages/mobirobot-cli/src/mobirobot_cli/commands/simulation.py
  artifacts:
    reports:
      # Store build metadata
      dotenv: build.env
  cache:
    key: "simulation-build-${CI_COMMIT_REF_SLUG}"
    paths:
      - .uv-cache/

# Test simulation container
test_simulation:
  <<: *docker_template
  stage: test
  needs: ["build_simulation"]
  script:
    # Test the built container
    - uv run mobirobot-cli simulation test 
        --tag "${NAO_SIM_TAG}" 
        --timeout 120
    
    # Run package unit tests
    - cd packages/nao-simulation
    - uv run python -m pytest tests/ -v --junitxml=test-results.xml --cov=nao_simulation --cov-report=xml
  artifacts:
    reports:
      junit: packages/nao-simulation/test-results.xml
      coverage_report:
        coverage_format: cobertura
        path: packages/nao-simulation/coverage.xml
    expire_in: 1 week
  coverage: '/TOTAL.*\s+(\d+%)$/'
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - changes:
        - packages/nao-simulation/**/*

# Security scanning
security_scan:
  <<: *docker_template
  stage: security
  needs: ["build_simulation"]
  script:
    # Pull the image for scanning
    - docker pull "${CI_REGISTRY_IMAGE}/${NAO_SIM_TAG}"
    
    # Install and run Trivy for vulnerability scanning
    - |
      curl -sfL https://raw.githubusercontent.com/aquasecurity/trivy/main/contrib/install.sh | sh -s -- -b /usr/local/bin
      trivy image --format template --template "@/usr/local/share/trivy/templates/gitlab.tpl" -o gl-container-scanning-report.json "${CI_REGISTRY_IMAGE}/${NAO_SIM_TAG}"
  artifacts:
    reports:
      container_scanning: gl-container-scanning-report.json
    expire_in: 1 week
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
  allow_failure: true

# Multi-platform build (optional, for releases)
build_multiplatform:
  <<: *docker_template
  stage: build
  variables:
    NAO_SIM_PLATFORMS: "linux/amd64,linux/arm64"
  script:
    # Build for multiple platforms
    - uv run mobirobot-cli simulation build 
        --tag "nao-simulation:${CI_COMMIT_TAG}"
        --registry "${NAO_SIM_REGISTRY}"
        --platforms "${NAO_SIM_PLATFORMS}"
        --push
        --ci
        --buildkit
        --optimize
  rules:
    - if: $CI_COMMIT_TAG
  when: manual

# Deploy to production
deploy_production:
  <<: *docker_template
  stage: deploy
  needs: ["test_simulation", "security_scan"]
  environment:
    name: production
    url: https://registry.gitlab.com/$CI_PROJECT_PATH/container_registry
  script:
    # Pull tested image
    - docker pull "${CI_REGISTRY_IMAGE}/${NAO_SIM_TAG}"
    
    # Tag as production and push
    - docker tag "${CI_REGISTRY_IMAGE}/${NAO_SIM_TAG}" "${CI_REGISTRY_IMAGE}/nao-simulation:production"
    - docker push "${CI_REGISTRY_IMAGE}/nao-simulation:production"
    
    # Clean up old images (keep last 10)
    - |
      # This would typically be done via GitLab's cleanup policies
      echo "Production deployment completed"
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: manual
  
# Cleanup job for registry
cleanup_registry:
  image: registry.gitlab.com/gitlab-org/gitlab-build-images:gitlab-ci-cleanup
  stage: deploy
  script:
    # Clean up old container images (this requires appropriate permissions)
    - echo "Registry cleanup would run here"
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
  when: manual