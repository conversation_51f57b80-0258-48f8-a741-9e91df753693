"""Integration tests using NAO simulation container.

This demonstrates how to use the new nao-simulation package for testing.
"""

import asyncio
from typing import Final

import pytest
import qi
from hypothesis import HealthCheck, Phase, given, settings
from hypothesis import strategies as st
from nao import (
    ConnectionManager,
    ConnectionState,
    NaoRobot,
    SignalManager,
)
from nao.connection_config import ConnectionConfig
from nao.task_manager import TaskManager
from pydantic import TypeAdapter

from mobirobot.common.structured_logging import LoggerType, get_logger
from mobirobot.exercises.exercise_service import ExerciseService
from mobirobot.models import Station
from mobirobot.regiments.regiment import Regiment

# Import the new simulation package
try:
    from nao_simulation import NaoSimulationContainer
    HAS_SIMULATION = True
except ImportError:
    HAS_SIMULATION = False

logger: LoggerType = get_logger(__name__)

# Skip all tests if simulation package not available
pytestmark = pytest.mark.skipif(
    not HAS_SIMULATION,
    reason="nao-simulation package not available. Install with: uv sync"
)


@st.composite
def regiment_strategy(draw: st.DrawFn, station: Station) -> Regiment:
    """Generate Regiment objects for testing."""
    exercise_repository = ExerciseService()
    possible_moves = exercise_repository.filter_by_station(station)

    moves_count = draw(st.integers(min_value=1, max_value=min(3, len(possible_moves))))
    moves = draw(
        st.lists(
            st.sampled_from(list(possible_moves.keys())),
            min_size=moves_count,
            max_size=moves_count,
            unique=True,
        )
    )

    exercise_duration = draw(st.integers(min_value=15, max_value=30))  # Shorter for tests

    regiment_args = {
        "id": draw(st.integers(min_value=1, max_value=1000)),
        "name": draw(st.text(min_size=3, max_size=20)),
        "moves": moves,
        "station": station,
        "exercise_duration": exercise_duration,
    }

    if station in {Station.HUF, Station.KC}:
        regiment_args.update(
            {
                "iterations": 1,
                "short_break_duration": 0,
                "long_break_duration": 0,
                "cooldown_duration": 0,
                "button_warmup": False,
                "break_type": "BreakShort" if station == Station.KJP else "BreakBreath",
            }
        )
    else:  # KJP
        regiment_args.update(
            {
                "iterations": 1,  # Single iteration for faster tests
                "short_break_duration": draw(st.integers(min_value=5, max_value=10)),
                "long_break_duration": draw(st.integers(min_value=10, max_value=15)),
                "button_warmup": False,
                "break_type": "BreakShort",
            }
        )

    regiment = TypeAdapter(Regiment).validate_python(regiment_args)
    regiment.check_regiment_properties()
    return regiment


@pytest.mark.anyio
@pytest.mark.simulation
async def test_simulation_container_basic() -> None:
    """Test basic simulation container functionality."""
    async with NaoSimulationContainer(timeout=60) as container:
        nao_url = container.get_nao_url()
        assert nao_url.startswith("tcp://localhost:")
        
        # Test basic NAOqi connection
        session = qi.Session()
        session.connect(nao_url)
        
        autonomous_life = session.service("ALAutonomousLife")
        assert autonomous_life is not None
        
        state = autonomous_life.getState()
        assert isinstance(state, str)


@pytest.mark.anyio
@pytest.mark.simulation
@pytest.mark.regiment_simulation
@settings(
    deadline=None,
    suppress_health_check=[HealthCheck.function_scoped_fixture],
    max_examples=1,  # Single example for demonstration
    phases=[Phase.generate, Phase.shrink],
)
@given(regiment=regiment_strategy(station=Station.KJP))
async def test_regiment_simulation_with_container(regiment: Regiment) -> None:
    """Test regiment execution using simulation container."""
    logger.info(
        "[Container Test] Running regiment",
        regiment_name=regiment.name,
    )
    
    async with NaoSimulationContainer(timeout=120) as container:
        nao_url = container.get_nao_url()
        host, port = nao_url.replace("tcp://", "").split(":")
        
        await _run_regiment_on_simulation(
            regiment, 
            host=host, 
            port=int(port),
            timeout=30.0  # Short timeout for tests
        )
    
    logger.info(
        "[Container Test] Completed regiment",
        regiment_name=regiment.name,
    )


async def _run_regiment_on_simulation(
    regiment: Regiment, 
    host: str,
    port: int,
    timeout: float = 30.0
) -> None:
    """Run a regiment against the simulation using new architecture."""
    connection_manager = None
    robot_controller = None
    run_task = None
    
    try:
        # Setup using updated architecture
        connection_config = ConnectionConfig(ip=host, port=port)
        connection_manager = ConnectionManager(config=connection_config)
        signal_manager = SignalManager()
        task_manager = TaskManager()
        
        robot_controller = NaoRobot(
            config=connection_config,
            connection_manager=connection_manager,
            signal_manager=signal_manager,
            task_manager=task_manager,
        )
        
        await robot_controller.start()
        await asyncio.sleep(0.5)
        
        assert connection_manager.state == ConnectionState.CONNECTED, (
            f"Failed to connect to simulation for {regiment.name}"
        )
        
        # Use the new execution architecture
        from mobirobot.execution.services import ExecutionServices
        from mobirobot.execution.session_manager import SessionManager
        
        services = ExecutionServices(
            robot=robot_controller,
            exercise_service=ExerciseService(),
        )
        
        session_manager = SessionManager(services)
        
        # Run regiment with timeout
        run_task = asyncio.create_task(session_manager.run_regiment(regiment))
        await asyncio.wait_for(run_task, timeout=timeout)

    except TimeoutError:
        logger.exception(
            "Regiment execution timed out",
            timeout=timeout,
            regiment_name=regiment.name,
        )
        if run_task:
            run_task.cancel()
            await asyncio.sleep(0.1)
        pytest.fail(f"Regiment execution timed out ({timeout}s): {regiment.name}")
        
    except Exception as e:
        logger.exception(
            "Regiment execution failed",
            regiment_name=regiment.name,
            error=str(e),
        )
        raise
        
    finally:
        # Cleanup
        if robot_controller:
            await robot_controller.stop()


@pytest.mark.anyio 
@pytest.mark.simulation
async def test_multiple_simulations_sequential() -> None:
    """Test that multiple simulation containers can be used sequentially."""
    for i in range(2):
        async with NaoSimulationContainer(timeout=60) as container:
            nao_url = container.get_nao_url()
            
            session = qi.Session()
            session.connect(nao_url)
            
            autonomous_life = session.service("ALAutonomousLife")
            state = autonomous_life.getState()
            
            logger.info(f"Container {i+1} state: {state}")
            assert isinstance(state, str)