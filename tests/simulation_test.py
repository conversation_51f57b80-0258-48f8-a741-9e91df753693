import asyncio
from typing import Final

import pytest
import qi
from hypothesis import HealthCheck, Phase, given, settings
from hypothesis import strategies as st
from nao import (
    ConnectionManager,
    ConnectionState,
    NaoRobot,
    SignalManager,
)
from nao.connection_config import ConnectionConfig
from nao.task_manager import TaskManager
from pydantic import TypeAdapter

from mobirobot.common.structured_logging import LoggerType, get_logger
from mobirobot.exercises.exercise_service import ExerciseService
from mobirobot.models import Station
from mobirobot.regiments.regiment import Regiment

logger: LoggerType = get_logger(__name__)

SIMULATION_HOST: Final = "127.0.0.1"
SIMULATION_PORT: Final = 8432


def is_simulation_available(
    host: str = SIMULATION_HOST, port: int = SIMULATION_PORT
) -> bool | None:
    """Check if the Naoqi simulation is reachable on the specified host/port.

    Args:
        host: The hostname or IP address of the simulation.
        port: The port number of the simulation.

    Returns:
        bool: True if the simulation is reachable, False if not, None if an error occurs.

    """
    try:
        session: qi.Session = qi.Session()
        session.connect(f"tcp://{host}:{port}")
        session.service("ALAutonomousLife")
    except RuntimeError:
        return False
    else:
        return True


pytestmark = pytest.mark.skipif(
    not is_simulation_available(),
    reason=f"Naoqi simulation not available at {SIMULATION_HOST}:{SIMULATION_PORT}",
)


@st.composite
def regiment_strategy(draw: st.DrawFn, station: Station) -> Regiment:
    """Generate Regiment objects with varied moves, durations, stations, iterations, and cooldown configurations.

    Args:
        draw: A hypothesis strategy draw function.
        station: The station for which the Regiment is generated.

    Returns:
        A valid Regiment instance.

    """
    exercise_repository = ExerciseService()
    # Filter exercises based on the provided station
    possible_moves = exercise_repository.filter_by_station(station)

    # Generate a random number of moves (1 to 5, or fewer if not enough available)
    moves_count = draw(st.integers(min_value=1, max_value=min(5, len(possible_moves))))
    # Select a list of unique move names from the possible moves
    moves = draw(
        st.lists(
            st.sampled_from(list(possible_moves.keys())),
            min_size=moves_count,
            max_size=moves_count,
            unique=True,
        )
    )

    # Generate exercise duration (15 to 60 seconds) - kept reasonable for tests
    exercise_duration = draw(st.integers(min_value=15, max_value=60))

    # Base Regiment attributes common to all stations
    regiment_args = {
        "id": draw(st.integers(min_value=1, max_value=1000)),  # Random ID
        "name": draw(st.text(min_size=3, max_size=20)),  # Random name
        "moves": moves,  # Generated moves list
        "station": station,  # Provided station
        "exercise_duration": exercise_duration,  # Generated duration
    }

    # Station-specific configurations
    if station in {Station.HUF, Station.KC}:
        # For HUF and KC stations, use fixed values as per domain rules/checks
        # (Assumes these stations have simpler, non-iterative structures)
        regiment_args.update(
            {
                "iterations": 1,
                "short_break_duration": 0,
                "long_break_duration": 0,
                "cooldown_duration": 0,
                "button_warmup": False,
                # Determine break type based on station (example logic)
                "break_type": "BreakShort" if station == Station.KJP else "BreakBreath",
            }
        )
    else:  # KJP (or other stations needing more complex config)
        # For other stations, generate varied values for iterations and breaks
        regiment_args.update(
            {
                "iterations": draw(
                    st.integers(
                        min_value=1, max_value=2
                    )  # Limit iterations for test speed
                ),
                "short_break_duration": draw(
                    st.integers(min_value=5, max_value=15)  # Shorter breaks for tests
                ),
                "long_break_duration": draw(
                    st.integers(min_value=10, max_value=20)  # Shorter breaks for tests
                ),
                "button_warmup": False,  # Assuming false for tests
                "break_type": "BreakShort",  # Assuming short break type
            }
        )

        # Optionally include cooldown configuration if stretch moves are available
        # Find moves containing "dehnen" (stretch) in their name
        stretch_moves = [name for name in possible_moves if "dehnen" in name.lower()]
        # Decide randomly whether to add a cooldown, only if stretch moves exist
        if draw(st.booleans()) and stretch_moves:
            # Generate number of cooldown moves (1 to 2, or fewer if not enough)
            cooldown_moves_count = draw(
                st.integers(min_value=1, max_value=min(2, len(stretch_moves)))
            )
            # Select unique cooldown moves from the stretch moves
            cooldown_moves = draw(
                st.lists(
                    st.sampled_from(stretch_moves),
                    min_size=cooldown_moves_count,
                    max_size=cooldown_moves_count,
                    unique=True,
                )
            )
            # Add cooldown parameters to the regiment arguments
            regiment_args.update(
                {
                    "cooldown_moves": cooldown_moves,
                    "cooldown_duration": draw(
                        st.integers(
                            min_value=10, max_value=20
                        )  # Shorter cooldown duration
                    ),
                    "cooldown_break_duration": draw(
                        st.integers(
                            min_value=5, max_value=10
                        )  # Shorter cooldown breaks
                    ),
                }
            )

    # Validate the generated dictionary against the Regiment Pydantic model
    # This ensures the generated combination of arguments is valid
    regiment = TypeAdapter(Regiment).validate_python(regiment_args)
    # Perform additional internal consistency checks defined within the Regiment model
    regiment.check_regiment_properties()
    return regiment


# --- Parameterized Short Regiment Simulation Tests for Each Station ---
# These tests run fewer examples and have a shorter timeout, suitable for quicker CI checks.
# They are marked with 'regiment_simulation' for selective running via pytest -m.


@pytest.mark.anyio
@pytest.mark.regiment_simulation  # Custom marker for these specific tests
@settings(
    deadline=None,
    suppress_health_check=[HealthCheck.function_scoped_fixture],
    max_examples=3,  # Fewer examples for speed
    phases=[Phase.generate, Phase.shrink],  # Include shrinking phase
)
@given(regiment=regiment_strategy(station=Station.KJP))
async def test_short_regiment_simulation_kjp(regiment: Regiment) -> None:
    """Run a short, random KJP regiment on the simulation (quick check).

    Uses the shared runner function.

    Args:
        regiment: The generated Regiment instance to test.

    """
    logger.info(
        "[Short Test KJP] Running regiment",
        regiment_name=regiment.name,
    )
    await _run_regiment_on_simulation(regiment, timeout=60.0)  # Use 60s timeout
    logger.info(
        "[Short Test KJP] Completed regiment",
        regiment_name=regiment.name,
    )


@pytest.mark.anyio
@pytest.mark.regiment_simulation
@settings(
    deadline=None,
    suppress_health_check=[HealthCheck.function_scoped_fixture],
    max_examples=3,
    phases=[Phase.generate, Phase.shrink],
)
@given(regiment=regiment_strategy(station=Station.KC))
async def test_short_regiment_simulation_kc(regiment: Regiment) -> None:
    """Run a short, random KC regiment on the simulation (quick check).

    Uses the shared runner function.

    Args:
        regiment: The generated Regiment instance to test.

    """
    logger.info(
        "[Short Test KC] Running regiment",
        regiment_name=regiment.name,
    )
    await _run_regiment_on_simulation(regiment, timeout=60.0)  # Use 60s timeout
    logger.info(
        "[Short Test KC] Completed regiment",
        regiment_name=regiment.name,
    )


@pytest.mark.anyio
@pytest.mark.regiment_simulation
@settings(
    deadline=None,
    suppress_health_check=[HealthCheck.function_scoped_fixture],
    max_examples=3,
    phases=[Phase.generate, Phase.shrink],
)
@given(regiment=regiment_strategy(station=Station.HUF))
async def test_short_regiment_simulation_huf(regiment: Regiment) -> None:
    """Run a short, random HUF regiment on the simulation (quick check).

    Uses the shared runner function.

    Args:
        regiment: The generated Regiment instance to test.

    """
    logger.info(
        "[Short Test HUF] Running regiment",
        regiment_name=regiment.name,
    )
    await _run_regiment_on_simulation(regiment, timeout=60.0)  # Use 60s timeout
    logger.info(
        "[Short Test HUF] Completed regiment",
        regiment_name=regiment.name,
    )


# --- Shared runner function for parameterized regiment tests ---
async def _run_regiment_on_simulation(
    regiment: Regiment, timeout: float = 60.0
) -> None:
    """Encapsulate the setup, run, and teardown logic.

    for running a single regiment against the simulation.

    Args:
        regiment: The Regiment instance to run.
        timeout: The maximum time to wait for the regiment to complete.

    """
    connection_manager = None
    robot_controller = None
    app = None
    run_task = None
    try:
        # Setup
        connection_config = ConnectionConfig(ip=SIMULATION_HOST, port=SIMULATION_PORT)
        connection_manager = ConnectionManager(config=connection_config)
        signal_manager = SignalManager()
        task_manager = TaskManager()
        robot_controller = NaoRobot(
            config=connection_config,
            connection_manager=connection_manager,
            signal_manager=signal_manager,
            task_manager=task_manager,
        )
        await robot_controller.start()
        await asyncio.sleep(0.5)
        assert connection_manager.state == ConnectionState.CONNECTED, (
            f"Failed to connect to simulation for {regiment.name}"
        )
        app = Application(
            robot_controller=robot_controller,
            exercise_service=ExerciseService(),
        )

        run_task = asyncio.create_task(app.run_regiment(regiment))
        await asyncio.wait_for(run_task, timeout=timeout)

    except TimeoutError:
        logger.exception(
            "Short regiment execution timed out",
            timeout=timeout,
            regiment_name=regiment.name,
        )
        if run_task:
            run_task.cancel()
            await asyncio.sleep(0.1)
        pytest.fail(f"Short regiment execution timed out ({timeout}s): {regiment.name}")
    finally:
        # Teardown
        if robot_controller:
            # robot_controller.stop() handles stopping the connection manager
            await robot_controller.stop()
