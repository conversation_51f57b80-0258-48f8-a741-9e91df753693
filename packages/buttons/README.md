# Buttons Package

Bluetooth Low Energy (BLE) button integration service for the MobiRobot system.

## Overview

The `buttons` package provides integration with wireless BLE buttons for controlling NAO robots during therapeutic exercise sessions. It enables therapists and patients to interact with the robot using physical buttons for better accessibility and control.

## Features

- **BLE Button Discovery**: Automatic detection of compatible wireless buttons
- **Button Press Handling**: Real-time button press detection and processing
- **Service Integration**: Seamless integration with MobiRobot execution services
- **Event-Driven Architecture**: Button events trigger robot actions
- **Multi-Button Support**: Handle multiple buttons simultaneously
- **Connection Management**: Automatic reconnection and error handling

## Supported Buttons

- **Aqara Wireless Button**: Primary supported button type
- **Generic BLE Buttons**: Basic BLE button support
- **Custom Button Types**: Extensible for additional button hardware

## Installation

From the project root:

```bash
# Install dependencies (includes BLE support)
uv sync

# Enable button integration
export MOBIROBOT_BUTTONS=1
```

### System Requirements

**Linux (Recommended)**:
```bash
# Install BlueZ for BLE support
sudo apt-get install libbluetooth-dev bluez

# Add user to bluetooth group
sudo usermod -a -G bluetooth $USER
```

**macOS**:
- Bluetooth permissions required
- May need to grant terminal Bluetooth access

**Windows**:
- Windows 10+ with BLE support
- May require additional drivers

## Usage

### Basic Button Service

```python
from buttons.button_service import ButtonService
from buttons.button_types import ButtonType

# Initialize button service
button_service = ButtonService()

# Start scanning for buttons
await button_service.start_scanning()

# Register button event handler
@button_service.on_button_press
async def handle_button_press(button_id: str, button_type: ButtonType):
    print(f"Button {button_id} pressed: {button_type}")
    # Trigger robot action
    await robot.say("Button pressed!")

# Stop service
await button_service.stop()
```

### Integration with Exercise Sessions

```python
from mobirobot.execution.session_manager import SessionManager
from buttons.button_service import ButtonService

# Setup with exercise session
session_manager = SessionManager(services)
button_service = ButtonService()

# Configure button actions
async def button_handler(button_id: str, button_type: ButtonType):
    if button_type == ButtonType.SINGLE_PRESS:
        # Pause/resume exercise
        await session_manager.toggle_pause()
    elif button_type == ButtonType.DOUBLE_PRESS:
        # Emergency stop
        await session_manager.emergency_stop()
    elif button_type == ButtonType.LONG_PRESS:
        # Start LLM chat
        await session_manager.start_llm_chat()

button_service.on_button_press(button_handler)
```

### Button Press Types

```python
from buttons.button_types import ButtonType

# Available button press patterns
ButtonType.SINGLE_PRESS    # Quick single press
ButtonType.DOUBLE_PRESS    # Two quick presses
ButtonType.LONG_PRESS      # Hold for 2+ seconds
ButtonType.TRIPLE_PRESS    # Three quick presses (if supported)
```

## Configuration

### Environment Variables

```bash
# Enable button integration
export MOBIROBOT_BUTTONS=1

# Button scanning timeout (seconds)
export MOBIROBOT_BUTTON_SCAN_TIMEOUT=30

# Auto-reconnect failed buttons
export MOBIROBOT_BUTTON_AUTO_RECONNECT=true

# Button press debounce time (milliseconds)
export MOBIROBOT_BUTTON_DEBOUNCE=200
```

### Button Configuration

```python
from buttons.button import Button
from buttons.button_types import ButtonConfig

# Configure specific button
config = ButtonConfig(
    device_name="Aqara Wireless Button",
    press_debounce_ms=200,
    long_press_threshold_ms=2000,
    auto_reconnect=True
)

button = Button(config)
```

## API Reference

### ButtonService

Main service for managing button connections:

```python
class ButtonService:
    async def start_scanning(self, timeout: float = 10.0) -> None
    async def stop_scanning(self) -> None
    async def connect_button(self, device_id: str) -> bool
    async def disconnect_button(self, device_id: str) -> None
    async def get_connected_buttons(self) -> List[Button]
    def on_button_press(self, handler: ButtonHandler) -> None
    async def stop(self) -> None
```

### Button

Individual button instance:

```python
class Button:
    @property
    def is_connected(self) -> bool
    @property  
    def device_info(self) -> DeviceInfo
    @property
    def battery_level(self) -> Optional[int]
    
    async def connect(self) -> bool
    async def disconnect(self) -> None
    def on_press(self, handler: ButtonHandler) -> None
```

### ButtonPressWaiter

Utility for waiting for specific button presses:

```python
from buttons.button_press_waiter import ButtonPressWaiter

# Wait for any button press
waiter = ButtonPressWaiter()
button_press = await waiter.wait_for_press(timeout=30.0)

# Wait for specific button type
single_press = await waiter.wait_for_single_press(timeout=10.0)
double_press = await waiter.wait_for_double_press(timeout=10.0)
```

## Integration Examples

### Exercise Control

```python
# Setup button-controlled exercise session
async def setup_button_exercise_control():
    button_service = ButtonService()
    session_manager = SessionManager(services)
    
    async def exercise_control(button_id: str, press_type: ButtonType):
        if press_type == ButtonType.SINGLE_PRESS:
            # Next exercise in regiment
            await session_manager.next_exercise()
        elif press_type == ButtonType.DOUBLE_PRESS:
            # Repeat current exercise
            await session_manager.repeat_exercise()
        elif press_type == ButtonType.LONG_PRESS:
            # End session
            await session_manager.stop_session()
    
    button_service.on_button_press(exercise_control)
    await button_service.start_scanning()
```

### Patient Interaction

```python
# Button-triggered patient responses
async def patient_interaction_handler(button_id: str, press_type: ButtonType):
    if press_type == ButtonType.SINGLE_PRESS:
        await robot.say("Great job! Keep going!")
    elif press_type == ButtonType.DOUBLE_PRESS:
        await robot.say("Do you need help with this exercise?")
        await session_manager.start_llm_chat()
```

### Therapist Controls

```python
# Therapist override controls
async def therapist_controls(button_id: str, press_type: ButtonType):
    if press_type == ButtonType.LONG_PRESS:
        # Emergency stop
        await session_manager.emergency_stop()
        await robot.say("Exercise stopped by therapist")
    elif press_type == ButtonType.TRIPLE_PRESS:
        # Switch to manual mode
        await session_manager.switch_to_manual_mode()
```

## Troubleshooting

### Common Issues

**Button Not Discovered**:
```bash
# Check BLE is working
bluetoothctl scan on

# Check permissions
groups $USER  # Should include 'bluetooth'

# Restart Bluetooth service
sudo systemctl restart bluetooth
```

**Connection Drops**:
```python
# Enable auto-reconnect
button_service = ButtonService(auto_reconnect=True)

# Check battery level
if button.battery_level and button.battery_level < 20:
    print("Button battery low, replace soon")
```

**Button Press Not Detected**:
```python
# Adjust debounce settings
config = ButtonConfig(press_debounce_ms=100)  # Shorter debounce

# Check button responsiveness
button.on_press(lambda: print("Press detected"))
```

### Debugging

```python
import logging

# Enable button service debugging
logging.getLogger('buttons').setLevel(logging.DEBUG)

# Monitor BLE events
logging.getLogger('bleak').setLevel(logging.DEBUG)
```

## Development

### Adding New Button Types

1. Implement button detection in `button.py`
2. Add button-specific configuration
3. Update `button_types.py` with new patterns
4. Test with hardware

### Testing

```bash
# Unit tests
pytest packages/buttons/tests/

# Integration tests (requires BLE hardware)
pytest packages/buttons/tests/ -m integration

# Mock testing without hardware
pytest packages/buttons/tests/ -m mock
```

### Hardware Testing

```python
# Test button discovery
python -m buttons.test_discovery

# Test button presses
python -m buttons.test_presses
```

## Dependencies

- **BLE**: `bleak` for cross-platform Bluetooth Low Energy support
- **Core**: `mobirobot` package for service integration
- **Async**: `asyncio` for non-blocking button handling

## Security

- Button connections use BLE security features
- Button IDs are validated before action execution
- Rate limiting prevents button spam attacks
- Secure pairing for sensitive operations