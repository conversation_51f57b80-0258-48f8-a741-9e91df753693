# QI Stubs Package

Type stubs for the NAOqi framework to enable type checking and IDE support in MobiRobot development.

## Overview

The `qi-stubs` package provides comprehensive type stubs for the NAOqi SDK, enabling modern Python development with full type checking, IDE autocompletion, and static analysis. This package is essential for type-safe NAO robot programming.

## Features

- **Complete Type Coverage**: Type stubs for all major NAOqi services
- **IDE Integration**: Full autocompletion and error detection in PyCharm, VSCode, etc.
- **Static Analysis**: Compatible with mypy, pyright, and other type checkers
- **Service Protocols**: Type-safe interfaces for NAO robot services
- **Method Signatures**: Accurate parameter and return type definitions
- **Documentation**: Inline documentation for NAOqi methods and parameters

## Included Services

### Core NAOqi Services

- **ALTextToSpeech**: Text-to-speech synthesis
- **ALAnimatedSpeech**: Speech with synchronized gestures
- **ALMotion**: Robot movement and joint control
- **ALRobotPosture**: Predefined poses and postures
- **ALAutonomousLife**: Autonomous behavior management
- **ALMemory**: Robot state and event management
- **ALLeds**: LED control for eyes and body
- **ALAudioPlayer**: Audio file playback
- **ALAudioDevice**: Microphone and speaker management
- **ALBehaviorManager**: Choreographe behavior execution

### Type Infrastructure

- **qi.Session**: Main NAOqi session interface
- **qi.Application**: NAOqi application lifecycle
- **Service Protocols**: Type-safe service interfaces
- **Parameter Types**: Common NAOqi data structures

## Installation

Automatically installed with the project:

```bash
# Install project dependencies
uv sync

# Type stubs are automatically available for type checking
```

## Usage

### Type Checking

```python
# With type stubs, this code gets full type checking
from qi_stubs.services.al_text_to_speech import ALTextToSpeech
from qi_stubs.services.al_motion import ALMotion

async def robot_demo(tts: ALTextToSpeech, motion: ALMotion) -> None:
    # IDE provides autocompletion and type checking
    await tts.say("Hello, I am NAO")  #  Type checked
    await motion.rest()               #  Type checked
    
    # Type errors caught at development time
    await tts.say(123)               #  Type error: expected str
    await motion.invalid_method()    #  Type error: method doesn't exist
```

### IDE Integration

```python
# Full autocompletion in your IDE
robot.tts.  # Shows: say, setLanguage, setParameter, etc.
robot.motion.  # Shows: moveTo, setAngles, setStiffnesses, etc.
robot.leds.  # Shows: setIntensity, fadeRGB, rasta, etc.
```

### Service Protocols

```python
from qi_stubs.services import ALTextToSpeech, ALMotion
from typing import Protocol

class NaoSessionProtocol(Protocol):
    """Type-safe NAOqi session interface"""
    
    def service(self, name: str) -> Any: ...
    def close(self) -> None: ...

# Type-safe service access
async def get_robot_services(session: NaoSessionProtocol) -> tuple[ALTextToSpeech, ALMotion]:
    tts = session.service("ALTextToSpeech")
    motion = session.service("ALMotion") 
    return tts, motion
```

## Type Checking Configuration

### mypy Configuration

Add to `pyproject.toml`:

```toml
[tool.mypy]
python_version = "3.12"
strict = true
packages = ["your_package"]

# Enable qi-stubs
files = ["packages/qi-stubs/src"]

[[tool.mypy.overrides]]
module = "qi.*"
ignore_missing_imports = false
```

### pyright/Pylance

Add to `pyrightconfig.json`:

```json
{
    "include": ["src", "packages/qi-stubs/src"],
    "typeCheckingMode": "strict",
    "pythonVersion": "3.12",
    "stubPath": "packages/qi-stubs/src"
}
```

## Service Reference

### ALTextToSpeech

```python
from qi_stubs.services.al_text_to_speech import ALTextToSpeech

class ALTextToSpeech:
    async def say(self, text: str) -> None: ...
    async def setLanguage(self, language: str) -> None: ...
    async def setParameter(self, parameter: str, value: float) -> None: ...
    async def getLanguage(self) -> str: ...
    async def getAvailableLanguages(self) -> list[str]: ...
    async def getAvailableVoices(self) -> list[str]: ...
```

### ALMotion

```python
from qi_stubs.services.al_motion import ALMotion

class ALMotion:
    async def setStiffnesses(self, names: str | list[str], stiffnesses: float | list[float]) -> None: ...
    async def setAngles(self, names: str | list[str], angles: float | list[float], fractionMaxSpeed: float) -> None: ...
    async def moveTo(self, x: float, y: float, theta: float) -> None: ...
    async def rest(self) -> None: ...
    async def wakeUp(self) -> None: ...
    async def getAngles(self, names: str | list[str], useSensors: bool) -> list[float]: ...
```

### ALAutonomousLife

```python
from qi_stubs.services.al_autonomous_life import ALAutonomousLife

class ALAutonomousLife:
    async def setState(self, state: str) -> None: ...
    async def getState(self) -> str: ...
    async def setAutonomousAbilityEnabled(self, ability: str, enabled: bool) -> None: ...
    async def getAutonomousAbilityEnabled(self, ability: str) -> bool: ...
```

### ALMemory

```python
from qi_stubs.services.al_memory import ALMemory

class ALMemory:
    async def insertData(self, key: str, value: Any) -> None: ...
    async def getData(self, key: str) -> Any: ...
    async def subscribeToEvent(self, event: str, module: str, callback: str) -> str: ...
    async def unsubscribeToEvent(self, event: str, subscriber: str) -> None: ...
```

## Development

### Adding New Service Stubs

1. Create service file in `src/qi_stubs/services/`
2. Define class with method signatures
3. Add proper type annotations
4. Include docstrings for documentation
5. Test with type checker

Example:

```python
# src/qi_stubs/services/al_new_service.py
class ALNewService:
    """New NAOqi service with type annotations"""
    
    async def newMethod(self, param: str) -> bool:
        """
        Description of the method.
        
        Args:
            param: Parameter description
            
        Returns:
            Return value description
        """
        ...
```

### Testing Type Stubs

```bash
# Run type checking
mypy packages/nao/src/

# Check specific files
pyright packages/nao/src/nao/nao_robot.py

# Verify autocompletion works
python -c "
from qi_stubs.services.al_text_to_speech import ALTextToSpeech
help(ALTextToSpeech.say)
"
```

### Generating Stubs

For new NAOqi versions:

```python
# Generate stubs from NAOqi documentation
python scripts/generate_qi_stubs.py

# Manual stub creation for complex services
python scripts/create_service_stub.py ALNewService
```

## Type Coverage

### Current Coverage

-  **ALTextToSpeech**: Complete coverage
-  **ALMotion**: Complete coverage  
-  **ALAutonomousLife**: Complete coverage
-  **ALMemory**: Complete coverage
-  **ALLeds**: Complete coverage
-  **ALAudioPlayer**: Complete coverage
-  **ALRobotPosture**: Complete coverage
-  **ALAnimatedSpeech**: Complete coverage
-  **ALBehaviorManager**: Complete coverage
-  **ALAudioDevice**: Complete coverage

### Missing Services

If you need stubs for additional services:

1. Check NAOqi documentation for service API
2. Create stub file following existing patterns
3. Add comprehensive type annotations
4. Submit PR or create issue

## Best Practices

### Type Annotations

```python
# Always use proper type annotations
async def robot_speech(tts: ALTextToSpeech, message: str) -> None:
    await tts.say(message)

# Use union types for flexible parameters
from typing import Union
async def set_joint_angles(
    motion: ALMotion, 
    names: Union[str, list[str]], 
    angles: Union[float, list[float]]
) -> None:
    await motion.setAngles(names, angles, 0.2)
```

### Protocol Usage

```python
from typing import Protocol

class RobotServices(Protocol):
    tts: ALTextToSpeech
    motion: ALMotion
    leds: ALLeds

async def demo_routine(robot: RobotServices) -> None:
    await robot.tts.say("Starting demo")
    await robot.motion.wakeUp()
    await robot.leds.rasta(2.0)
```

### Error Handling

```python
from typing import Optional

async def safe_robot_operation(tts: ALTextToSpeech) -> Optional[str]:
    try:
        await tts.say("Test message")
        return "Success"
    except Exception as e:
        return None  # Type checker knows this is Optional[str]
```

## Integration with NAO Package

The qi-stubs work seamlessly with the nao package:

```python
from nao.nao_robot import NaoRobot
from qi_stubs.services import ALTextToSpeech

# Type checking works across packages
robot = NaoRobot("192.168.178.110")
await robot.connect()

# robot.tts is properly typed as ALTextToSpeech
await robot.tts.say("Type-safe robot communication")
```

## Troubleshooting

### Type Checker Not Finding Stubs

```bash
# Ensure stubs are in Python path
export PYTHONPATH="${PYTHONPATH}:packages/qi-stubs/src"

# Check mypy configuration
mypy --show-traceback your_file.py
```

### Missing Method Signatures

```python
# Temporary workaround for missing methods
from typing import Any

class ALServiceExtension:
    def missing_method(self, *args: Any, **kwargs: Any) -> Any: ...
```

### IDE Configuration

**VSCode**: Install Python and Pylance extensions

**PyCharm**: Enable type checking in settings

**Vim/Neovim**: Configure with pyright or mypy LSP

## Contributing

To improve type coverage:

1. Identify missing or incorrect type annotations
2. Check NAOqi documentation for accurate signatures  
3. Add comprehensive docstrings
4. Test with multiple type checkers
5. Ensure backward compatibility

## Dependencies

- **Python 3.12+**: For advanced type annotation features
- **typing_extensions**: For backward-compatible type features (if needed)
- **No runtime dependencies**: Pure type stub package