# NAO Simulation Package

This package provides NAO robot simulation container management and testcontainers integration for the MobiRobot system.

## Features

- **Docker Container Management**: Automated NAO simulation container lifecycle
- **Testcontainers Integration**: Python testcontainers wrapper for integration tests
- **CLI Tools**: Command-line interface for container operations
- **Multi-arch Support**: Optimized containers for different platforms
- **GitLab CI Integration**: Registry integration for CI/CD pipelines

## Quick Start

### Building the simulation container

```bash
# Build the container
uv run nao-simulation build

# Build with custom tag
uv run nao-simulation build --tag my-nao-sim:latest

# Build for CI with registry push
uv run nao-simulation build --push --registry registry.gitlab.com/your-project
```

### Using in tests

```python
from nao_simulation import NaoSimulationContainer

async def test_with_simulation():
    async with NaoSimulationContainer() as container:
        # Container is automatically started and configured
        nao_url = container.get_nao_url()
        # Use nao_url to connect to simulation
```

### CLI Usage

```bash
# Start simulation container
uv run nao-simulation start

# Stop simulation container  
uv run nao-simulation stop

# Build container with optimizations
uv run nao-simulation build --optimize

# Push to registry
uv run nao-simulation push --registry registry.gitlab.com/your-project
```

## Architecture

The package provides:

1. **Container Builder**: Optimized Dockerfile with multi-stage builds
2. **Testcontainers Wrapper**: Python integration for automated testing
3. **CLI Interface**: Easy container management from command line
4. **Registry Integration**: GitLab CI/CD pipeline support

## Container Optimizations

- Multi-stage builds to reduce image size
- Distroless base images where possible
- Cached layers for faster rebuilds
- Automatic cleanup of temporary files
- ARM64 and AMD64 support