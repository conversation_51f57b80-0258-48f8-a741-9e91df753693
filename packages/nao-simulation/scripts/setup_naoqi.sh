#!/bin/bash
set -euo pipefail

# Configuration
readonly LOG_FILE="/tmp/naoqi_setup.log"
readonly NAOQI_BIN_PATH="/opt/naoqi/choregraphe-2.8.8-ubuntu2204/bin/naoqi-bin"
readonly MANIFEST_FILE="/home/<USER>/.local/share/PackageManager/apps/core/manifest.xml"
readonly ROBOT_MODEL_FILE="/opt/naoqi/choregraphe-2.8.8-ubuntu2204/etc/naoqi/ALRobotModel.xml"
readonly TARGET_LOG_LINE="packagemanager: getPackages is deprecated please use packages instead."
readonly OLD_URL="tcp://0.0.0.0:9559"
readonly NEW_URL="tcp://0.0.0.0:8432"
readonly OLD_ROBOT_MODEL="JULIETTEY20B2C.xml"
readonly NAO_ROBOT_DESCRIPTION="NAOH25V60.xml"
readonly WAIT_INTERVAL=5
readonly MAX_WAIT_TIME=120  # Reduced from 300s for faster builds

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $*" >&2
}

cleanup() {
    local exit_code=$?
    if [[ -n "${NAOQI_PID:-}" ]] && kill -0 "$NAOQI_PID" 2>/dev/null; then
        log "Cleaning up NAOqi process (PID: $NAOQI_PID)"
        kill "$NAOQI_PID" || true
        sleep 2
    fi
    
    # Clean up temporary files to reduce image size
    rm -f "$LOG_FILE"
    rm -rf /home/<USER>/.local/share/aldebaran/breakpad/* 2>/dev/null || true
    rm -rf /home/<USER>/.local/share/PackageManager/logs/* 2>/dev/null || true
    
    exit $exit_code
}

trap cleanup EXIT

wait_for_naoqi_ready() {
    log "Starting NAOqi for initial setup..."
    "$NAOQI_BIN_PATH" &> "$LOG_FILE" &
    readonly NAOQI_PID=$!
    log "NAOqi started with PID: $NAOQI_PID"
    
    local start_time
    start_time=$(date +%s)
    
    while true; do
        local current_time elapsed_time
        current_time=$(date +%s)
        elapsed_time=$((current_time - start_time))
        
        if [[ $elapsed_time -ge $MAX_WAIT_TIME ]]; then
            log "ERROR: Timeout waiting for NAOqi setup after ${MAX_WAIT_TIME}s"
            return 1
        fi
        
        if [[ -r "$LOG_FILE" ]] && grep -qF "$TARGET_LOG_LINE" "$LOG_FILE"; then
            log "NAOqi ready after ${elapsed_time}s"
            break
        fi
        
        if ! kill -0 "$NAOQI_PID" 2>/dev/null; then
            log "ERROR: NAOqi process stopped unexpectedly"
            [[ -r "$LOG_FILE" ]] && tail -n 10 "$LOG_FILE" >&2
            return 1
        fi
        
        sleep "$WAIT_INTERVAL"
    done
}

modify_manifest() {
    log "Modifying manifest file: $MANIFEST_FILE"
    
    if [[ ! -f "$MANIFEST_FILE" ]]; then
        log "ERROR: Manifest file not found: $MANIFEST_FILE"
        return 1
    fi
    
    if sed -i "s#${OLD_URL}#${NEW_URL}#g" "$MANIFEST_FILE"; then
        log "Manifest file successfully modified"
    else
        log "ERROR: Failed to modify manifest file"
        return 1
    fi
}

modify_robot_model() {
    log "Modifying robot model file: $ROBOT_MODEL_FILE"
    
    if [[ ! -f "$ROBOT_MODEL_FILE" ]]; then
        log "ERROR: Robot model file not found: $ROBOT_MODEL_FILE"
        return 1
    fi
    
    if sed -i "s#${OLD_ROBOT_MODEL}#${NAO_ROBOT_DESCRIPTION}#g" "$ROBOT_MODEL_FILE"; then
        log "Robot model file successfully modified"
    else
        log "ERROR: Failed to modify robot model file"
        return 1
    fi
}

main() {
    log "Starting NAOqi setup process"
    
    # Wait for NAOqi to be ready
    wait_for_naoqi_ready
    
    # Modify configuration files
    modify_manifest
    modify_robot_model
    
    log "NAOqi setup completed successfully"
}

main "$@"