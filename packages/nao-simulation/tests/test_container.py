"""Tests for NAO simulation container."""

import pytest
import qi
from nao_simulation import NaoSimulationContainer


@pytest.mark.anyio
async def test_container_lifecycle() -> None:
    """Test container start/stop lifecycle."""
    container = NaoSimulationContainer(
        image="nao-simulation:latest",
        timeout=60,  # Shorter timeout for tests
    )

    # Container should not be running initially
    assert not container.is_running

    # Start container
    await container.start()
    assert container.is_running

    # Should have NAO URL and port
    nao_url = container.get_nao_url()
    assert nao_url.startswith("tcp://localhost:")

    host_port = container.get_host_port()
    assert isinstance(host_port, int)
    assert 1024 <= host_port <= 65535

    # Stop container
    await container.stop()
    assert not container.is_running


@pytest.mark.anyio
async def test_container_context_manager() -> None:
    """Test container using async context manager."""
    async with NaoSimulationContainer(timeout=60) as container:
        assert container.is_running

        nao_url = container.get_nao_url()
        assert nao_url.startswith("tcp://localhost:")

    # Container should be stopped after context exit
    assert not container.is_running


@pytest.mark.anyio
async def test_container_error_when_not_started() -> None:  # noqa: RUF029
    """Test that accessing URL/port fails when container not started."""
    container = NaoSimulationContainer()

    with pytest.raises(RuntimeError, match="Container not started"):
        container.get_nao_url()

    with pytest.raises(RuntimeError, match="Container not started"):
        container.get_host_port()


@pytest.mark.anyio
async def test_double_start_error() -> None:
    """Test that starting container twice raises error."""
    container = NaoSimulationContainer(timeout=60)

    await container.start()

    try:
        with pytest.raises(RuntimeError, match="Container already started"):
            await container.start()
    finally:
        await container.stop()


@pytest.mark.anyio
async def test_naoqi_connection() -> None:
    """Test basic NAOqi connection to simulation."""
    pytest.importorskip("qi", reason="qi module not available")

    async with NaoSimulationContainer(timeout=60) as container:
        nao_url = container.get_nao_url()

        # Test basic connection
        session = qi.Session()
        session.connect(nao_url)

        # Test accessing a service
        autonomous_life = session.service("ALAutonomousLife")
        assert autonomous_life is not None

        # Test getting state (should work even if disabled)
        state = autonomous_life.getState()
        assert isinstance(state, str)
