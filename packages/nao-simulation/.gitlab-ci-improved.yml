# Improved GitLab CI configuration using mobirobot-cli
variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"
  SIMULATION_TAG: "nao-simulation:${CI_COMMIT_SHORT_SHA}"

services:
  - docker:dind

stages:
  - build
  - test
  - deploy

# Build simulation container using CLI
build_simulation:
  stage: build
  image: python:3.12
  services:
    - docker:dind
  variables:
    DOCKER_HOST: tcp://docker:2376
    DOCKER_TLS_CERTDIR: "/certs"
    DOCKER_TLS_VERIFY: 1
    DOCKER_CERT_PATH: "$DOCKER_TLS_CERTDIR/client"
  before_script:
    - apt-get update && apt-get install -y docker.io
    - pip install uv
    - uv sync
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
    # Build with commit SHA tag
    - uv run mobirobot-cli simulation build 
        --tag "${SIMULATION_TAG}"
        --optimize
        --platform linux/amd64
    
    # Tag and push to registry
    - docker tag "${SIMULATION_TAG}" "${CI_REGISTRY_IMAGE}/${SIMULATION_TAG}"
    - docker tag "${SIMULATION_TAG}" "${CI_REGISTRY_IMAGE}/nao-simulation:latest"
    - docker push "${CI_REGISTRY_IMAGE}/${SIMULATION_TAG}"
    - docker push "${CI_REGISTRY_IMAGE}/nao-simulation:latest"
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - changes:
        - packages/nao-simulation/**/*

# Test simulation container using CLI
test_simulation:
  stage: test
  image: python:3.12
  services:
    - docker:dind
  variables:
    DOCKER_HOST: tcp://docker:2376
    DOCKER_TLS_CERTDIR: "/certs"
    DOCKER_TLS_VERIFY: 1
    DOCKER_CERT_PATH: "$DOCKER_TLS_CERTDIR/client"
  before_script:
    - apt-get update && apt-get install -y docker.io
    - pip install uv
    - uv sync
  script:
    # Test the built container
    - uv run mobirobot-cli simulation test --tag "${SIMULATION_TAG}" --timeout 120
    
    # Run package tests
    - cd packages/nao-simulation
    - uv run python -m pytest tests/ -v
  dependencies:
    - build_simulation
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - changes:
        - packages/nao-simulation/**/*

# Deploy to production (main branch only)
deploy_simulation:
  stage: deploy
  image: python:3.12
  services:
    - docker:dind
  before_script:
    - apt-get update && apt-get install -y docker.io
    - pip install uv
    - uv sync
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
    # Pull the tested image
    - docker pull "${CI_REGISTRY_IMAGE}/${SIMULATION_TAG}"
    
    # Tag as production and push
    - docker tag "${CI_REGISTRY_IMAGE}/${SIMULATION_TAG}" "${CI_REGISTRY_IMAGE}/nao-simulation:production"
    - docker push "${CI_REGISTRY_IMAGE}/nao-simulation:production"
  dependencies:
    - test_simulation
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
  environment:
    name: production