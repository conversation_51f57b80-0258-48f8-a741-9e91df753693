[project]
name = "nao-simulation"
version = "0.1.0"
description = "NAO robot simulation container and testcontainers integration"
authors = [
    {name = "MobiRobot Team", email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "testcontainers>=4.7.0",
    "docker>=7.0.0",
    "pydantic>=2.0.0",
    "typer>=0.12.0",
    "rich>=13.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=8.0.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.uv.sources]
qi-stubs = { workspace = true }

