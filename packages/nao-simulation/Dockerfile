# Multi-stage build for optimized NAO simulation container
ARG UBUNTU_VERSION=22.04
ARG UV_VERSION=latest

# ===================================
# Stage 1: Download and extract NAOqi
# ===================================
FROM ubuntu:${UBUNTU_VERSION} as downloader
RUN apt-get update && apt-get install -y --no-install-recommends \
    wget \
    tar \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

WORKDIR /tmp
RUN wget --no-check-certificate \
    https://community-static.aldebaran.com/resources/2.8.8/choregraphe-2.8.8-ubuntu2204-standalone.tar.gz \
    && tar -xzf choregraphe-2.8.8-ubuntu2204-standalone.tar.gz \
    && rm choregraphe-2.8.8-ubuntu2204-standalone.tar.gz

# ===================================
# Stage 2: Runtime image
# ===================================
FROM ubuntu:${UBUNTU_VERSION} as runtime

# Install only runtime dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    libglib2.0-0 \
    sqlite3 \
    libflac8 \
    libvorbisenc2 \
    libdbus-1-3 \
    libgl1-mesa-glx \
    portaudio19-dev \
    netcat-openbsd \
    curl \
    sudo \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Copy NAOqi from downloader stage
COPY --from=downloader /tmp/choregraphe-2.8.8-ubuntu2204 /opt/naoqi/choregraphe-2.8.8-ubuntu2204

# Create non-root user (required for NAOqi simulation)
RUN useradd -m -s /bin/bash nao && \
    usermod -aG sudo nao && \
    echo "nao ALL=(ALL) NOPASSWD: ALL" >> /etc/sudoers

# Install uv for potential Python operations
COPY --from=ghcr.io/astral-sh/uv:${UV_VERSION} /uv /uvx /bin/

USER nao
WORKDIR /opt/naoqi/choregraphe-2.8.8-ubuntu2204/bin

# Copy setup script
COPY --chown=nao:nao scripts/setup_naoqi.sh /home/<USER>/setup_naoqi.sh
RUN chmod +x /home/<USER>/setup_naoqi.sh

# Run setup script during build to pre-configure NAOqi
RUN /home/<USER>/setup_naoqi.sh

# Health check to ensure NAOqi is responding
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8432 || exit 1

# Expose NAOqi port
EXPOSE 8432

# Use exec form for proper signal handling
CMD ["./naoqi-bin", "--qi-listen-url", "127.0.0.1,0.0.0.0"]