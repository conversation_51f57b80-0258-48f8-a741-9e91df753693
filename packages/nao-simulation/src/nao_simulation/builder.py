"""Container builder for NAO simulation."""

import os
import subprocess
from pathlib import Path

from pydantic import BaseModel
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn

console = Console()


class BuildConfig(BaseModel):
    """Configuration for container build."""

    tag: str = "nao-simulation:latest"
    platform: str | None = None
    no_cache: bool = False
    push: bool = False
    registry: str | None = None
    optimize: bool = True
    # BuildKit caching options
    cache_from: list[str] | None = None
    cache_to: str | None = None
    use_buildkit: bool = True
    # Multi-arch options
    platforms: list[str] | None = None
    # CI-specific options
    ci_mode: bool = False
    builder_name: str = "nao-simulation-builder"


class ContainerBuilder:
    """Builder for NAO simulation container."""

    def __init__(self, dockerfile_path: Path | None = None) -> None:
        """Initialize builder.

        Args:
            dockerfile_path: Path to Dockerfile (defaults to package directory)
        """
        if dockerfile_path is None:
            # Default to package directory
            package_dir = Path(__file__).parent.parent.parent
            dockerfile_path = package_dir / "Dockerfile"

        self.dockerfile_path: Path | None = dockerfile_path
        self.build_context: Path = dockerfile_path.parent

    def build(self, config: BuildConfig) -> None:
        """Build the container image.

        Args:
            config: Build configuration

        Raises:
            subprocess.CalledProcessError: If the build fails
        """
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            task = progress.add_task("Building NAO simulation container...", total=None)

            try:
                if config.use_buildkit:
                    progress.update(task, description="Setting up BuildKit builder...")
                    self._setup_buildkit(config)

                progress.update(task, description="Building container image...")
                self._build_image(config)
                progress.update(task, description="✅ Container built successfully")

                if config.push:
                    progress.update(task, description="Pushing to registry...")
                    ContainerBuilder._push_image(config)
                    progress.update(
                        task, description="✅ Container pushed successfully"
                    )

            except subprocess.CalledProcessError as e:
                progress.update(task, description="❌ Build failed")
                console.print(f"[red]Build failed: {e}[/red]")
                raise

    def _setup_buildkit(self, config: BuildConfig) -> None:
        """Set up BuildKit builder for enhanced caching and multi-platform support."""
        try:
            # Check if builder already exists
            result = subprocess.run(
                ["docker", "buildx", "inspect", config.builder_name],
                capture_output=True,
                text=True,
                check=False,
            )

            if result.returncode != 0:
                # Create new builder
                create_cmd = [
                    "docker",
                    "buildx",
                    "create",
                    "--name",
                    config.builder_name,
                    "--driver",
                    "docker-container",
                    "--use",
                ]

                if not config.ci_mode:
                    # Use docker-container driver for better caching locally
                    create_cmd.extend(["--bootstrap"])

                subprocess.run(create_cmd, check=True, capture_output=True)
            else:
                # Use existing builder
                subprocess.run(
                    ["docker", "buildx", "use", config.builder_name],
                    check=True,
                    capture_output=True,
                )

        except subprocess.CalledProcessError as e:
            console.print(f"[yellow]Warning: BuildKit setup failed: {e}[/yellow]")
            raise

    def _build_image(self, config: BuildConfig) -> None:
        """Build Docker image with BuildKit support."""
        if config.use_buildkit:
            cmd = ["docker", "buildx", "build"]
        else:
            cmd = ["docker", "build"]

        # Add BuildKit-specific options
        if config.use_buildkit:
            # Enable BuildKit features
            cmd.extend(["--builder", config.builder_name])

            # Add cache configuration
            if config.cache_from:
                for cache_source in config.cache_from:
                    cmd.extend(["--cache-from", cache_source])

            if config.cache_to:
                cmd.extend(["--cache-to", config.cache_to])

            # Multi-platform support
            if config.platforms:
                platforms_str = ",".join(config.platforms)
                cmd.extend(["--platform", platforms_str])
            elif config.platform:
                cmd.extend(["--platform", config.platform])

            # Enable load for single platform builds (required for local use)
            if (
                (not config.platforms or len(config.platforms or []) == 1)
                and not config.push
            ):
                cmd.append("--load")

        # Add build arguments
        if config.optimize:
            cmd.extend(["--build-arg", "BUILDKIT_INLINE_CACHE=1"])

        # Add no-cache flag
        if config.no_cache:
            cmd.append("--no-cache")

        # Add tags
        cmd.extend(["-t", config.tag])

        # Add registry tag if pushing
        if config.push and config.registry:
            registry_tag = f"{config.registry}/{config.tag}"
            cmd.extend(["-t", registry_tag])

        # Add push flag for BuildKit
        if config.use_buildkit and config.push:
            cmd.append("--push")

        # Add Dockerfile and context
        cmd.extend(["-f", str(self.dockerfile_path), str(self.build_context)])

        console.print(f"[dim]Running: {' '.join(cmd)}[/dim]")
        subprocess.run(cmd, check=True, capture_output=False)

    @staticmethod
    def _push_image(config: BuildConfig) -> None:
        """Push image to registry (only if not already pushed by BuildKit)."""
        if config.use_buildkit:
            # BuildKit already pushed with --push flag
            return

        if not config.registry:
            msg = "Registry must be specified for push"
            raise ValueError(msg)

        registry_tag = f"{config.registry}/{config.tag}"
        cmd = ["docker", "push", registry_tag]

        console.print(f"[dim]Running: {' '.join(cmd)}[/dim]")
        subprocess.run(cmd, check=True, capture_output=False)

    @staticmethod
    def get_cache_config_for_ci(registry: str, tag: str) -> tuple[list[str], str]:
        """Get optimized cache configuration for CI environments.

        Args:
            registry: Registry URL (e.g., registry.gitlab.com/project)
            tag: Base tag name

        Returns:
            Tuple of (cache_from list, cache_to string)
        """
        cache_from = [
            f"type=registry,ref={registry}/{tag}:buildcache",
            f"type=registry,ref={registry}/{tag}:latest",
        ]
        cache_to = f"type=registry,ref={registry}/{tag}:buildcache,mode=max"

        return cache_from, cache_to

    @staticmethod
    def get_env_config() -> BuildConfig:
        """Create BuildConfig from environment variables.

        Useful for CI environments where config comes from env vars.

        Returns:
            BuildConfig: Build configuration
        """
        cache_from = []
        if cache_from_env := os.getenv("NAO_SIM_CACHE_FROM"):
            cache_from = cache_from_env.split(",")

        platforms = []
        if platforms_env := os.getenv("NAO_SIM_PLATFORMS"):
            platforms = platforms_env.split(",")

        return BuildConfig(
            tag=os.getenv("NAO_SIM_TAG", "nao-simulation:latest"),
            registry=os.getenv("NAO_SIM_REGISTRY"),
            platform=os.getenv("NAO_SIM_PLATFORM"),
            platforms=platforms or None,
            cache_from=cache_from or None,
            cache_to=os.getenv("NAO_SIM_CACHE_TO"),
            ci_mode=os.getenv("CI", "false").lower() == "true",
            push=os.getenv("NAO_SIM_PUSH", "false").lower() == "true",
            no_cache=os.getenv("NAO_SIM_NO_CACHE", "false").lower() == "true",
        )

    @staticmethod
    def cleanup() -> None:
        """Clean up build artifacts."""
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            task = progress.add_task("Cleaning up...", total=None)

            try:
                # Remove dangling images
                subprocess.run(
                    ["docker", "image", "prune", "-f"],
                    check=True,
                    capture_output=True,
                )
                progress.update(task, description="✅ Cleanup completed")

            except subprocess.CalledProcessError:
                progress.update(task, description="⚠️ Cleanup skipped")

    @staticmethod
    def get_image_size(tag: str) -> str:
        """Get the size of a built image.

        Args:
            tag: Image tag

        Returns:
            Human-readable size string
        """
        try:
            result = subprocess.run(
                ["docker", "images", "--format", "{{.Size}}", tag],
                capture_output=True,
                text=True,
                check=True,
            )
            return result.stdout.strip()
        except subprocess.CalledProcessError:
            return "Unknown"
