"""Pytest fixtures for NAO simulation testing."""

from collections.abc import AsyncGenerator

import pytest

from .container import NaoSimulationContainer


@pytest.fixture()
async def nao_simulation() -> AsyncGenerator[NaoSimulationContainer, None]:
    """Provide a NAO simulation container for testing.

    This fixture automatically starts and stops the simulation container.
    Use this in tests that need a NAOqi simulation environment.

    Yields:
        NaoSimulationContainer: The NAO simulation container

    Example:
        @pytest.mark.anyio
        async def test_with_simulation(nao_simulation):
            nao_url = nao_simulation.get_nao_url()
            # Use nao_url to connect to simulation
    """
    container = NaoSimulationContainer(
        image="nao-simulation:latest",
        timeout=120,  # Allow more time for CI environments
    )

    await container.start()

    try:
        yield container
    finally:
        await container.stop()


@pytest.fixture()
async def nao_simulation_url(nao_simulation: NaoSimulationContainer) -> str:  # noqa: RUF029
    """Provide just the NAO URL for simplified testing.

    Args:
        nao_simulation: The NAO simulation container

    Returns:
        str: The NAO URL

    Example:
        @pytest.mark.anyio
        async def test_nao_connection(nao_simulation_url):
            import qi
            session = qi.Session()
            session.connect(nao_simulation_url)
    """
    return nao_simulation.get_nao_url()
