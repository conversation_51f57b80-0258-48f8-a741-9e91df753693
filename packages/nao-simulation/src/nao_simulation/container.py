"""Testcontainers integration for NAO simulation."""

import asyncio
import socket
from types import TracebackType
from typing import Self

from testcontainers.core.container import DockerContainer
from testcontainers.core.waiting_utils import wait_for_logs


class NaoSimulationContainer:
    """Testcontainers wrapper for NAO simulation."""

    def __init__(
        self,
        image: str = "nao-simulation:latest",
        port: int = 8432,
        timeout: int = 120,
    ) -> None:
        """Initialize NAO simulation container.

        Args:
            image: Docker image name for NAO simulation
            port: Port to expose for NAOqi service
            timeout: Timeout in seconds for container startup
        """
        self.image: str = image
        self.port: int = port
        self.timeout: int = timeout
        self._container: DockerContainer | None = None
        self._exposed_port: int | None = None

    async def __aenter__(self) -> Self:
        """Start the container asynchronously."""
        await self.start()
        return self

    async def __aexit__(
        self,
        exc_type: type[BaseException] | None,
        exc_val: BaseException | None,
        exc_tb: TracebackType | None,
    ) -> None:
        """Stop the container asynchronously."""
        await self.stop()

    async def start(self) -> None:
        """Start the NAO simulation container."""
        if self._container is not None:
            msg = "Container already started"
            raise RuntimeError(msg)

        # Find available port
        self._exposed_port = self._find_free_port()

        self._container = (
            DockerContainer(self.image)
            .with_exposed_ports(self.port)
            .with_bind_ports(self._exposed_port, self.port)
            .with_env("QI_LOG_LEVEL", "warning")  # Reduce log noise
        )

        # Start container in thread pool to avoid blocking
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, self._start_container)

    def _start_container(self) -> None:
        """Start container synchronously (for thread pool)."""
        if self._container is None:
            msg = "Container not initialized"
            raise RuntimeError(msg)

        self._container.start()

        # Wait for NAOqi to be ready
        wait_for_logs(
            self._container,
            predicate="packagemanager: getPackages is deprecated",
            timeout=self.timeout,
        )

    async def stop(self) -> None:
        """Stop the NAO simulation container."""
        if self._container is None:
            return

        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, self._container.stop)
        self._container = None
        self._exposed_port = None

    def get_nao_url(self) -> str:
        """Get the NAOqi connection URL.

        Returns:
            NAOqi connection URL (e.g., "tcp://localhost:12345")

        Raises:
            RuntimeError: If container is not started
        """
        if self._exposed_port is None:
            msg = "Container not started"
            raise RuntimeError(msg)

        return f"tcp://localhost:{self._exposed_port}"

    def get_host_port(self) -> int:
        """Get the exposed host port.

        Returns:
            Host port number

        Raises:
            RuntimeError: If container is not started
        """
        if self._exposed_port is None:
            msg = "Container not started"
            raise RuntimeError(msg)

        return self._exposed_port

    @staticmethod
    def _find_free_port() -> int:
        """Find a free port on the host."""
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(("", 0))
            s.listen(1)
            return s.getsockname()[1]  # pyright: ignore[reportAny]

    @property
    def is_running(self) -> bool:
        """Check if container is running."""
        return self._container is not None


class NaoSimulationFixture:
    """Pytest fixture helper for NAO simulation."""

    def __init__(self, image: str = "nao-simulation:latest") -> None:
        """Initialize fixture.

        Args:
            image: Docker image name for NAO simulation
        """
        self.image: str = image
        self._container: NaoSimulationContainer | None = None

    async def start(self) -> NaoSimulationContainer:
        """Start simulation container for test."""
        if self._container is not None:
            msg = "Container already started"
            raise RuntimeError(msg)

        self._container = NaoSimulationContainer(image=self.image)
        await self._container.start()
        return self._container

    async def stop(self) -> None:
        """Stop simulation container."""
        if self._container is not None:
            await self._container.stop()
            self._container = None
