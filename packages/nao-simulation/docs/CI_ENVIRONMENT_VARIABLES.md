# NAO Simulation CI Environment Variables

This document describes the environment variables used for configuring NAO simulation builds in CI/CD environments.

## Required Variables (GitLab CI)

These are automatically provided by GitLab CI:

```bash
# GitLab Registry Authentication
CI_REGISTRY_USER              # GitLab registry username
CI_REGISTRY_PASSWORD          # GitLab registry password
CI_REGISTRY                   # GitLab registry URL
CI_REGISTRY_IMAGE            # Full image path for this project

# GitLab Project Information
CI_PROJECT_PATH              # Project path (group/project)
CI_COMMIT_SHORT_SHA          # Short commit SHA for tagging
CI_COMMIT_REF_NAME           # Branch or tag name
CI_DEFAULT_BRANCH            # Default branch name (usually 'main')
CI                           # Set to "true" in CI environments
```

## NAO Simulation Configuration Variables

### Basic Configuration

```bash
# Container tagging
NAO_SIM_TAG="nao-simulation:latest"                    # Container tag
NAO_SIM_REGISTRY="registry.gitlab.com/project"         # Registry URL

# Build behavior
NAO_SIM_PUSH="true"                                    # Push to registry
NAO_SIM_NO_CACHE="false"                              # Disable build cache
NAO_SIM_PLATFORM="linux/amd64"                        # Target platform
NAO_SIM_PLATFORMS="linux/amd64,linux/arm64"           # Multi-platform builds
```

### BuildKit Caching (Advanced)

```bash
# Cache sources (comma-separated)
NAO_SIM_CACHE_FROM="type=registry,ref=registry.gitlab.com/project/nao-simulation:buildcache,type=registry,ref=registry.gitlab.com/project/nao-simulation:latest"

# Cache destination
NAO_SIM_CACHE_TO="type=registry,ref=registry.gitlab.com/project/nao-simulation:buildcache,mode=max"
```

### Docker Configuration

```bash
# Docker daemon configuration
DOCKER_DRIVER="overlay2"                               # Docker storage driver
DOCKER_TLS_CERTDIR="/certs"                           # TLS certificate directory
DOCKER_HOST="tcp://docker:2376"                       # Docker daemon host
DOCKER_TLS_VERIFY="1"                                 # Enable TLS verification
DOCKER_CERT_PATH="/certs/client"                      # Client certificate path

# BuildKit configuration
DOCKER_BUILDKIT="1"                                   # Enable BuildKit
BUILDKIT_PROGRESS="plain"                             # BuildKit progress output
```

## Example GitLab CI Variables Configuration

### Project-Level Variables (GitLab CI/CD Settings)

Set these in your GitLab project under Settings > CI/CD > Variables:

```bash
# Optional: Override default registry
NAO_SIM_REGISTRY = "your-custom-registry.com/project"

# Optional: Default platform for builds
NAO_SIM_PLATFORM = "linux/amd64"

# Optional: Enable multi-platform builds
NAO_SIM_PLATFORMS = "linux/amd64,linux/arm64"
```

### Environment-Specific Variables

#### Development Environment
```bash
NAO_SIM_TAG="nao-simulation:dev-${CI_COMMIT_SHORT_SHA}"
NAO_SIM_PUSH="false"                                  # Don't push dev builds
```

#### Production Environment  
```bash
NAO_SIM_TAG="nao-simulation:${CI_COMMIT_TAG}"
NAO_SIM_PUSH="true"
NAO_SIM_REGISTRY="${CI_REGISTRY_IMAGE}"
```

## Usage Examples

### CLI with Environment Variables

```bash
# Load all configuration from environment
uv run mobirobot-cli simulation build --from-env

# Override specific settings
NAO_SIM_TAG="custom-tag:latest" uv run mobirobot-cli simulation build --from-env
```

### GitLab CI Job Configuration

```yaml
build_simulation:
  variables:
    NAO_SIM_TAG: "nao-simulation:${CI_COMMIT_SHORT_SHA}"
    NAO_SIM_REGISTRY: "${CI_REGISTRY_IMAGE}"
    NAO_SIM_PLATFORM: "linux/amd64"
    NAO_SIM_PUSH: "true"
  script:
    - uv run mobirobot-cli simulation build --from-env --ci
```

## Cache Configuration Best Practices

### Local Development
- No cache configuration needed (automatic local BuildKit cache)
- Use `--buildkit` for faster rebuilds

### CI/CD Environment
- Use registry-based caching for maximum efficiency
- Set cache retention policies to manage storage costs
- Use `mode=max` for comprehensive caching

### Multi-Platform Builds
- Use separate cache entries per platform
- Consider build time vs. cache storage trade-offs

## Security Considerations

### Sensitive Variables
- Mark registry credentials as "Protected" and "Masked"
- Use project-level variables for sensitive configuration
- Avoid logging cache URLs that might contain tokens

### Non-Privileged Docker
- Use `docker:dind` without privileged mode
- Configure TLS properly for secure daemon communication
- Validate Docker access before building

## Troubleshooting

### Common Issues

```bash
# BuildKit not available
export DOCKER_BUILDKIT=1

# Cache access issues
# Ensure registry permissions are correct
docker login $CI_REGISTRY

# Platform issues
# Specify exact platform for consistency
export NAO_SIM_PLATFORM="linux/amd64"
```

### Debug Commands

```bash
# Test environment configuration
uv run python -c "from nao_simulation import ContainerBuilder; print(ContainerBuilder.get_env_config())"

# Verify Docker BuildKit
docker buildx ls

# Check cache access
docker buildx build --help | grep cache
```