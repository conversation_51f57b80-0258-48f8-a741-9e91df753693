# Optimized GitLab CI configuration for NAO simulation container
# Combines BuildKit optimization with observability and CLI integration

variables:
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"
  SIMULATION_TAG: "nao-simulation:${CI_COMMIT_SHORT_SHA}"
  SIMULATION_LATEST_TAG: "${CI_REGISTRY_IMAGE}/nao-simulation:latest"
  SIMULATION_CACHE_TAG: "${CI_REGISTRY_IMAGE}/nao-simulation:buildcache"
  
  # NAO simulation environment variables
  NAO_SIM_TAG: "${SIMULATION_TAG}"
  NAO_SIM_REGISTRY: "${CI_REGISTRY_IMAGE}"
  NAO_SIM_CACHE_FROM: "type=registry,ref=${SIMULATION_CACHE_TAG},type=registry,ref=${SIMULATION_LATEST_TAG}"
  NAO_SIM_CACHE_TO: "type=registry,ref=${SIMULATION_CACHE_TAG},mode=max"
  NAO_SIM_PLATFORM: "linux/amd64"
  NAO_SIM_MEASURE_METRICS: "true"
  NAO_SIM_OUTPUT_METRICS: "true"

services:
  - docker:dind

stages:
  - build
  - test  
  - deploy

# Build simulation container with BuildKit optimization
build_simulation:
  stage: build
  image: python:3.12
  services:
    - docker:dind
  variables:
    DOCKER_HOST: tcp://docker:2376
    DOCKER_TLS_CERTDIR: "/certs"
    DOCKER_TLS_VERIFY: 1
    DOCKER_CERT_PATH: "$DOCKER_TLS_CERTDIR/client"
  before_script:
    # Install Docker and dependencies
    - apt-get update && apt-get install -y docker.io
    - pip install uv
    - uv sync
    # Login to registry for caching
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
    # Build using the refactored builder with metrics
    - cd packages/nao-simulation
    - |
      uv run python -c "
      import sys
      sys.path.insert(0, 'src')
      from nao_simulation.builder import build_container, get_env_config, BuildConfig
      
      # Get config from environment
      config = get_env_config()
      config.push = True
      config.ci_mode = True
      config.use_buildkit = True
      
      # Build with metrics
      metrics = build_container(config)
      
      # Export metrics for GitLab
      print(f'BUILD_TIME_SECONDS={metrics.build_time_seconds}')
      if metrics.image_size_mb:
          print(f'IMAGE_SIZE_MB={metrics.image_size_mb}')
      "
    
    # Tag for registry
    - docker tag "${SIMULATION_TAG}" "${SIMULATION_LATEST_TAG}"
    - docker tag "${SIMULATION_TAG}" "${CI_REGISTRY_IMAGE}/${SIMULATION_TAG}"
    
    # Push all tags
    - docker push "${CI_REGISTRY_IMAGE}/${SIMULATION_TAG}"
    - docker push "${SIMULATION_LATEST_TAG}"
  artifacts:
    reports:
      metrics: 
        - packages/nao-simulation/build_metrics.txt
    expire_in: 1 week
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - changes:
        - packages/nao-simulation/**/*

# Test simulation container startup and functionality
test_simulation:
  stage: test
  image: python:3.12
  services:
    - docker:dind
  variables:
    DOCKER_HOST: tcp://docker:2376
    DOCKER_TLS_CERTDIR: "/certs"
    DOCKER_TLS_VERIFY: 1
    DOCKER_CERT_PATH: "$DOCKER_TLS_CERTDIR/client"
  before_script:
    - apt-get update && apt-get install -y docker.io
    - pip install uv
    - cd packages/nao-simulation
    - uv sync --dev
  script:
    # Pull the built image
    - docker pull "${CI_REGISTRY_IMAGE}/${SIMULATION_TAG}"
    - docker tag "${CI_REGISTRY_IMAGE}/${SIMULATION_TAG}" "${SIMULATION_TAG}"
    
    # Test container startup time
    - |
      uv run python -c "
      import sys
      sys.path.insert(0, 'src')
      from nao_simulation.builder import measure_startup_time
      
      startup_time = measure_startup_time('${SIMULATION_TAG}', timeout=120)
      if startup_time:
          print(f'Container started successfully in {startup_time:.2f}s')
          print(f'STARTUP_TIME_SECONDS={startup_time}')
      else:
          print('Failed to measure startup time')
          exit(1)
      "
    
    # Run package tests
    - uv run python -m pytest tests/ -v --tb=short
    
    # Test CLI integration
    - uv run mobirobot-cli simulation test --tag "${SIMULATION_TAG}" --timeout 120
  dependencies:
    - build_simulation
  artifacts:
    reports:
      junit: packages/nao-simulation/test-results.xml
    when: always
    expire_in: 1 week
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    - changes:
        - packages/nao-simulation/**/*

# Deploy to production registry (main branch only)
deploy_simulation:
  stage: deploy
  image: docker:latest
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
    # Pull the tested image
    - docker pull "${CI_REGISTRY_IMAGE}/${SIMULATION_TAG}"
    
    # Tag as production release with timestamp
    - export PRODUCTION_TAG="${CI_REGISTRY_IMAGE}/nao-simulation:production-$(date +%Y%m%d-%H%M%S)"
    - docker tag "${CI_REGISTRY_IMAGE}/${SIMULATION_TAG}" "${PRODUCTION_TAG}"
    - docker tag "${CI_REGISTRY_IMAGE}/${SIMULATION_TAG}" "${CI_REGISTRY_IMAGE}/nao-simulation:production"
    
    # Push production tags
    - docker push "${PRODUCTION_TAG}"
    - docker push "${CI_REGISTRY_IMAGE}/nao-simulation:production"
    
    # Output deployment info
    - echo "Deployed ${PRODUCTION_TAG}"
    - echo "PRODUCTION_IMAGE=${PRODUCTION_TAG}" >> deploy.env
  dependencies:
    - test_simulation
  artifacts:
    reports:
      dotenv: deploy.env
    expire_in: 1 week
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
  environment:
    name: production
    url: "${CI_REGISTRY_IMAGE}/nao-simulation:production"

# Cleanup old images (scheduled job)
cleanup:
  stage: deploy
  image: docker:latest
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
    # Clean up old cache images (keep last 5)
    - |
      docker run --rm -v /var/run/docker.sock:/var/run/docker.sock \
        registry.gitlab.com/gitlab-org/docker-images/docker-registry-gc:latest \
        /bin/registry-garbage-collect --dry-run=false /etc/docker/registry/config.yml
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
  when: manual
  allow_failure: true