# NAO Robot Package

Low-level NAO robot communication and control package for the MobiRobot system.

## Overview

The `nao` package provides a comprehensive, type-safe interface for communicating with NAO humanoid robots. It handles connection management, service orchestration, and provides async wrappers around the NAOqi framework for modern Python development.

## Features

- **Type-Safe NAOqi Interface**: Full type hints and protocols for NAOqi services
- **Async Communication**: Non-blocking robot operations with proper async/await support
- **Connection Management**: Automatic reconnection with exponential backoff
- **Service Wrappers**: High-level interfaces for all NAO robot services
- **Task Management**: Proper async task lifecycle and cleanup
- **Error Handling**: Comprehensive error recovery and logging
- **Signal Management**: Graceful shutdown and resource cleanup

## Supported NAO Services

### Core Services
- **Motion**: Joint control, posture, and movement
- **Text-to-Speech (TTS)**: Voice synthesis and speech output
- **Animated Speech**: Synchronized speech with gestures
- **Autonomous Life**: Robot behavior and awareness management
- **Memory**: Robot state and data storage
- **LEDs**: Eye and body LED control
- **Audio Player**: Sound file playback

### Utility Services
- **Behavior Manager**: Choreographe behavior execution
- **Robot Posture**: Predefined poses and postures
- **Audio Device**: Microphone and speaker management

## Installation

From the project root:

```bash
# Install dependencies
uv sync

# The nao package is automatically available
python -c "import nao; print('Package installed successfully')"
```

### NAOqi Requirements

**For Real Robot**:
- NAOqi SDK installed and configured
- Robot IP address accessible on network
- Robot must be powered on and connected

**For Simulation**:
```bash
# Run NAOqi simulation
uv run poe simulation
```

## Basic Usage

### Robot Connection

```python
from nao.nao_robot import NaoRobot

# Connect to real robot
robot = NaoRobot("***************")
await robot.connect()

# Check connection
if robot.is_connected:
    print("Connected to NAO robot")

# Graceful disconnect
await robot.disconnect()
```

### Text-to-Speech

```python
# Basic speech
await robot.tts.say("Hello, I am your NAO robot assistant")

# Set language
await robot.tts.set_language("German")
await robot.tts.say("Hallo, ich bin dein NAO Roboter")

# Control speech parameters
await robot.tts.set_parameter("speed", 80)
await robot.tts.set_parameter("pitch", 1.2)
```

### Motion Control

```python
# Set stiffness for movement
await robot.motion.set_stiffnesses("Body", 1.0)

# Go to predefined posture
await robot.posture.go_to_posture("Stand", 1.0)

# Move joints
await robot.motion.set_angles(
    names=["HeadYaw", "HeadPitch"],
    angles=[0.5, -0.3],
    fraction_max_speed=0.2
)

# Walk forward
await robot.motion.move_to(x=0.5, y=0.0, theta=0.0)
```

### Autonomous Life Management

```python
# Disable autonomous behaviors during exercise
await robot.autonomous_life.set_state("disabled")

# Configure specific abilities
configs = {
    "AutonomousBlinking": True,
    "BackgroundMovement": True,
    "BasicAwareness": False,
    "ListeningMovement": False,
    "SpeakingMovement": False,
}

for ability, enabled in configs.items():
    await robot.autonomous_life.set_autonomous_ability_enabled(ability, enabled)
```

### LED Control

```python
# Set eye colors
await robot.leds.set_intensity("FaceLeds", 1.0)
await robot.leds.fade_rgb("FaceLedsTopBlue", 0.0, 0.0, 1.0, 1.0)

# Blink patterns
await robot.leds.rasta(2.0)  # Rainbow effect
await robot.leds.random_eyes(2.0)  # Random eye colors
```

### Audio Playback

```python
# Play sound file
await robot.audio_player.play_file("/usr/share/naoqi/wav/start_sound.wav")

# Set volume
await robot.audio_player.set_master_volume(60)

# Check if playing
is_playing = await robot.audio_player.is_sound_playing()
```

## Advanced Usage

### Connection Management

```python
from nao.connection_manager import ConnectionManager

# Custom connection configuration
config = ConnectionConfig(
    ip="***************",
    port=9559,
    timeout=10.0,
    max_retries=3,
    retry_delay=2.0
)

manager = ConnectionManager(config)
session = await manager.connect()

# Use session directly
tts = session.service("ALTextToSpeech")
await tts.say("Direct service access")
```

### Task Management

```python
from nao.task_manager import TaskManager

# Create task manager for robot
task_manager = TaskManager(robot)

# Start background blinking
await task_manager.start_task(
    "blinking",
    robot.autonomous_life.blink_eyes,
    interval=3.0
)

# Stop specific task
await task_manager.stop_task("blinking")

# Stop all tasks
await task_manager.stop_all()
```

### Error Handling

```python
from nao.exceptions import NaoConnectionError, NaoServiceError

try:
    await robot.connect()
    await robot.tts.say("Testing speech")
except NaoConnectionError as e:
    print(f"Failed to connect: {e}")
except NaoServiceError as e:
    print(f"Service error: {e}")
except Exception as e:
    print(f"Unexpected error: {e}")
```

### Signal Handling

```python
from nao.signal_manager import SignalManager

# Setup graceful shutdown
signal_manager = SignalManager()

@signal_manager.on_shutdown
async def cleanup():
    print("Shutting down robot connection...")
    await robot.disconnect()
    
# Start signal handling
signal_manager.setup()
```

## Service Reference

### Motion Service (`robot.motion`)

```python
# Joint control
await robot.motion.set_angles(names, angles, speed)
await robot.motion.set_stiffnesses(names, stiffnesses)

# Walking
await robot.motion.move_to(x, y, theta)
await robot.motion.walk_to(x, y, theta)

# Postures
await robot.motion.rest()
await robot.motion.wake_up()
```

### TTS Service (`robot.tts`)

```python
# Speech synthesis
await robot.tts.say(text)
await robot.tts.say_to_file(text, filename)

# Language and voice
await robot.tts.set_language(language)
await robot.tts.set_voice(voice)

# Parameters
await robot.tts.set_parameter(param, value)
```

### Autonomous Life (`robot.autonomous_life`)

```python
# State management
await robot.autonomous_life.set_state(state)
await robot.autonomous_life.get_state()

# Ability control
await robot.autonomous_life.set_autonomous_ability_enabled(ability, enabled)
```

### Memory Service (`robot.memory`)

```python
# Data storage
await robot.memory.insert_data(key, value)
value = await robot.memory.get_data(key)

# Event handling
await robot.memory.subscribe_to_event(event, callback)
await robot.memory.unsubscribe_to_event(subscriber_id)
```

## Configuration

### Environment Variables

```bash
# Default robot IP
export ROBOT_IP=***************

# Connection timeout
export NAO_CONNECTION_TIMEOUT=10

# Enable connection retries
export NAO_ENABLE_RETRIES=true

# Log level for NAO communication
export NAO_LOG_LEVEL=INFO
```

### Connection Config

```python
from nao.connection_config import ConnectionConfig

config = ConnectionConfig(
    ip="***************",
    port=9559,
    timeout=10.0,
    max_retries=3,
    retry_delay=2.0,
    enable_heartbeat=True
)
```

## Testing

### Unit Tests

```bash
# Run unit tests
pytest packages/nao/tests/

# Test with mocked robot
pytest packages/nao/tests/ -m unit
```

### Integration Tests

```bash
# Test with real robot (requires NAO)
pytest packages/nao/tests/ -m integration

# Test with simulation
uv run poe simulation
pytest packages/nao/tests/ -m simulation
```

### Manual Testing

```python
# Test basic functionality
python -c "
import asyncio
from nao.nao_robot import NaoRobot

async def test():
    robot = NaoRobot('***************')
    await robot.connect()
    await robot.tts.say('Test successful')
    await robot.disconnect()

asyncio.run(test())
"
```

## Debugging

### Connection Issues

```python
# Enable debug logging
import logging
logging.getLogger('nao').setLevel(logging.DEBUG)

# Test connection manually
from nao.connection_manager import ConnectionManager
manager = ConnectionManager()
session = await manager.connect()
print(f"Connected: {session is not None}")
```

### Service Errors

```python
# Check service availability
services = await robot.memory.get_data("ALMemory/InstalledModules")
print("Available services:", services)

# Test individual service
try:
    await robot.tts.say("Test")
    print("TTS working")
except Exception as e:
    print(f"TTS error: {e}")
```

## Performance

### Connection Optimization

- Connection pooling for multiple operations
- Service caching to reduce lookup overhead
- Heartbeat mechanism for connection health
- Automatic reconnection on network issues

### Memory Management

- Proper cleanup of NAOqi resources
- Task cancellation and cleanup
- Memory leak prevention
- Resource monitoring

## Security

- No default credentials stored in code
- Secure connection protocols when available
- Input validation for robot commands
- Rate limiting for safety-critical operations

## Dependencies

- **Core**: Python 3.12+ with async/await support
- **NAOqi**: NAOqi SDK for robot communication (external)
- **Networking**: For robot communication
- **Logging**: Structured logging with correlation IDs
- **Type Safety**: Full type hints and runtime checking

## Migration Guide

### From Legacy NAOqi Code

```python
# Old synchronous code
import naoqi
tts = naoqi.ALProxy("ALTextToSpeech", "***************", 9559)
tts.say("Hello")

# New async code
from nao.nao_robot import NaoRobot
robot = NaoRobot("***************")
await robot.connect()
await robot.tts.say("Hello")
```

### Key Differences

- All operations are async and must be awaited
- Automatic connection management and retries
- Type-safe interfaces with proper error handling
- Resource cleanup and task management
- Structured logging and monitoring integration