from __future__ import annotations

import json
from pathlib import Path
from typing import TYPE_CHECKING, Literal, Protocol, TypedDict, TypeVar, cast

import numpy as np
from faster_whisper import WhisperModel
from openai import AsyncOpenAI
from speech_recognition import AudioData

from mobirobot.common.structured_logging import LoggerType, get_logger

if TYPE_CHECKING:
    from collections.abc import Awaitable, Callable, Generator

    from openai.types.chat import (
        ChatCompletion,
        ChatCompletionAssistantMessageParam,
        ChatCompletionMessageParam,
        ChatCompletionSystemMessageParam,
        ChatCompletionUserMessageParam,
    )

    from .nao_audio import NAOAudioSource

logger: LoggerType = get_logger(__name__)

T = TypeVar("T")


class NAOTextToSpeech(Protocol):
    """Protocol defining the text-to-speech interface for NAO."""

    async def say(self, text: str) -> None:
        """Make the robot say the given text."""
        ...


MessageRole = Literal["system", "user", "assistant"]


class Message(TypedDict):
    """Represents a single message in a chat conversation."""

    role: MessageRole
    content: str


def get_audio_data(
    data: AudioData | Generator[AudioData, None, None],
) -> AudioData | None:
    """Extracts a single AudioData object from input.

    Returns:
        The extracted AudioData object, or None if the generator is exhausted.
    """
    if isinstance(data, AudioData):
        return data
    try:
        return next(data)
    except StopIteration:
        return None


class NaoChat:
    """Manages the chat interaction loop for NAO, including listening and responding."""

    def __init__(
        self, audio_source: NAOAudioSource, say: Callable[[str], Awaitable[None]]
    ) -> None:
        """Initializes NaoChat with audio source and speech synthesis."""
        self.chat_service: ChatService = ChatService()
        self.session: ChatSession = self.chat_service.init_session("basic_intro")
        logger.info("NaoChat initialized")
        self.audio_source: NAOAudioSource = audio_source
        self._say: Callable[[str], Awaitable[None]] = say
        self.model: WhisperModel = WhisperModel("medium", device="cuda")

    async def listen_and_respond(self) -> None:
        """Listens for user speech, transcribes it, generates a response, and speaks it."""
        async for audio_data_np in self.audio_source.stream():
            # audio_data_np is already the NumPy array from the stream
            # The stream should only yield arrays, so check size directly.
            if audio_data_np.size == 0:
                logger.debug("Received empty audio data from stream.")
                continue

            logger.info(
                "Received audio data chunk",
                shape=audio_data_np.shape,
                dtype=audio_data_np.dtype,
            )
            try:
                # Data should already be np.int16 from NAOAudioSource buffer
                # Normalize the int16 data to float32
                if audio_data_np.dtype != np.int16:
                    logger.warning(
                        "Unexpected audio data type",
                        dtype=audio_data_np.dtype,
                    )
                    # Attempt conversion or handle error
                    try:
                        audio_data_np = audio_data_np.astype(np.int16)
                    except ValueError:
                        logger.exception(
                            "Could not convert audio data to int16",
                            dtype=audio_data_np.dtype,
                        )
                        continue

                data_float32 = audio_data_np.astype(np.float32) / 32768.0

                # Transcribe the processed audio data
                segments, _ = self.model.transcribe(
                    data_float32, language="de", vad_filter=True
                )
                segments = list(segments)
                logger.info("Transcription complete.")

                if not segments:
                    logger.info("No speech segments detected after transcription.")
                    continue

                segment_text = segments[0].text.strip()
                if not segment_text:
                    logger.info("Detected segment text is empty.")
                    continue

                logger.info("Transcribed Text", text=segment_text)
                response = await self.session.generate_response(segment_text)
                content = response.choices[0].message.content

                if content is None:
                    logger.error("LLM response content is None.")
                    continue

                logger.info("User", text=segment_text)
                logger.info("System", text=content)
                await self._say(content)

            except Exception:
                logger.exception("Error processing audio data or generating response")
                continue  # Continue to next audio chunk


class ChatSession:
    """Manages a single chat session, maintaining history and interacting with the LLM."""

    def __init__(
        self,
        system_prompt: str,
        api_wrapper: AsyncOpenAI,
        model_name: str,
        use_history: bool = True,
    ):
        """Initializes a chat session with a system prompt and API details."""
        self.system_prompt: str = system_prompt
        self.chat_history: list[ChatCompletionMessageParam] = []
        sys_msg: ChatCompletionSystemMessageParam = {
            "role": "system",
            "content": system_prompt,
        }
        self.chat_history.append(sys_msg)
        self.api_wrapper: AsyncOpenAI = api_wrapper
        self.model_name: str = model_name
        self.use_history: bool = use_history
        logger.info(
            "ChatSession created with system prompt", system_prompt=system_prompt
        )

    async def generate_response(self, user_message: str) -> ChatCompletion:
        """Generates a response from the LLM based on the user message and history.

        Returns:
            The ChatCompletion object containing the LLM's response.
        """
        user_msg: ChatCompletionUserMessageParam = {
            "role": "user",
            "content": user_message,
        }
        # Decide whether to include full history or just system prompt + user message
        messages_to_send: list[ChatCompletionMessageParam]
        if self.use_history:
            messages_to_send = [*self.chat_history, user_msg]
        else:
            # Find the system prompt
            system_message = next(
                (msg for msg in self.chat_history if msg["role"] == "system"), None
            )
            if system_message:
                messages_to_send = [system_message, user_msg]
            else:
                # Fallback if system message not found (should not happen with current init)
                messages_to_send = [user_msg]
                logger.warning(
                    "System prompt not found in history for no-history response."
                )

        response = await self.api_wrapper.chat.completions.create(
            model=self.model_name,
            messages=messages_to_send,
        )

        # Append user message and assistant response to history *only* if using history
        if self.use_history:
            self.chat_history.append(user_msg)
            content = response.choices[0].message.content
            if content:
                assistant_msg: ChatCompletionAssistantMessageParam = {
                    "role": "assistant",
                    "content": content,
                }
                self.chat_history.append(assistant_msg)
            else:
                logger.warning(
                    "Assistant message content was None, not adding to history."
                )

        # Log differently based on history usage
        logger.info("User", text=user_message)
        logger.info("History Sent", use_history=self.use_history)
        logger.info("Response", text=response.choices[0].message.content)
        return response


class ChatService:
    """Service managing chat sessions and loading system prompts."""

    def __init__(
        self,
        model_name: str = "phi3",
        prompt_file: str = "system_prompts.json",
    ):
        """Initializes the ChatService with model details and prompts."""
        self.model_name: str = model_name
        self.api_wrapper: AsyncOpenAI = AsyncOpenAI(
            api_key="ollama", base_url="http://localhost:11434/v1"
        )
        self.system_prompts: dict[str, str] = self.load_system_prompts(prompt_file)
        self.sessions: list[ChatSession] = []
        logger.info(
            "ChatService initialized",
            model_name=model_name,
            total_system_prompts=len(self.system_prompts),
        )

    def load_system_prompts(self, prompt_file: str) -> dict[str, str]:
        """Loads system prompts from a JSON file.

        Returns:
            A dictionary mapping prompt tags to prompt strings.
        """
        file_path = Path(__file__).parent / "data" / prompt_file
        with file_path.open(encoding="utf-8") as f:
            # Explicitly type the return of json.load
            # Use cast to satisfy the type checker about json.load returning Any
            return cast("dict[str, str]", json.load(f))

    def init_session(self, system_prompt_tag: str) -> ChatSession:
        """Initializes and returns a new chat session with the specified system prompt.

        Returns:
            The newly created ChatSession instance.
        """
        system_prompt = self.system_prompts[system_prompt_tag]
        session = ChatSession(system_prompt, self.api_wrapper, self.model_name)
        self.sessions.append(session)
        return session
