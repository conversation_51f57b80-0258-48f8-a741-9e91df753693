"""NAO audio source implementation.

This module handles audio input from the NAO robot's microphones through the ALAudioDevice
module. It supports both interleaved and deinterleaved audio at NAO's supported sample rates
and channel configurations.
"""

import asyncio
from asyncio import Task
from collections.abc import AsyncIterator
from dataclasses import dataclass, field
from typing import Literal, Protocol, runtime_checkable

import numpy as np
import numpy.typing as npt
from nao import NaoRobot
from nao.services.audio_device import AudioDeviceService

from mobirobot.common.structured_logging import LoggerType, get_logger

# Import Task only for type hinting

logger: LoggerType = get_logger(__name__)


# Define Protocols for NAOqi proxies to satisfy type checking without Any
# Removed AudioDeviceProtocol as we now use the typed AudioDeviceService


@runtime_checkable
class MemoryProtocol(Protocol):
    """Protocol defining methods expected from the NAOqi ALMemory proxy (if any used here)."""

    # Add methods used from ALMemory here if needed


class NAOAudioError(Exception):
    """Base exception for NAO audio related errors."""


class AudioFormatError(NAOAudioError):
    """Raised when audio format is invalid or unsupported."""


class AudioSubscriptionError(NAOAudioError):
    """Raised when subscription to audio device fails."""


class AudioProcessingError(NAOAudioError):
    """Raised when processing audio data fails."""


@dataclass(frozen=True)
class AudioFormat:
    """Audio format specification for NAO audio device.

    Args:
        sample_rate: Sample rate in Hz (16000 or 48000)
        channels: Number of channels (1, 3, or 4)
        deinterleaved: Whether audio is deinterleaved
    """

    sample_rate: Literal[16000, 48000]
    channels: Literal[1, 3, 4]
    deinterleaved: bool = False

    def __post_init__(self) -> None:
        """Validate audio format against NAO's supported configurations.

        Raises:
            AudioFormatError: If the format is unsupported.
        """
        valid = (
            (self.channels in {3, 4} and self.sample_rate == 48000)  # noqa: PLR2004
            or (self.channels == 1 and self.sample_rate == 16000)  # noqa: PLR2004
        )
        if not valid:
            msg = (
                f"Unsupported format: {self.sample_rate}Hz, {self.channels} channels. "
                "NAO supports: 48000Hz with 3-4 channels or 16000Hz with 1 channel."
            )
            raise AudioFormatError(msg)


class AudioBuffer:
    """Efficient circular buffer for audio data with numpy backend."""

    def __init__(self, audio_format: AudioFormat, duration_ms: float = 170.0) -> None:
        """Initialize the audio buffer.

        Args:
            audio_format: The audio format specification.
            duration_ms: The duration of the buffer in milliseconds.

        Raises:
            ValueError: If duration_ms results in a non-positive buffer size.
        """
        self.format: AudioFormat = audio_format
        samples = int(audio_format.sample_rate * (duration_ms / 1000))
        shape = (
            (samples, audio_format.channels)
            if audio_format.deinterleaved
            else (samples,)
        )
        self._buffer: npt.NDArray[np.int16] = np.zeros(shape, dtype=np.int16)
        self._index: int = 0
        self._is_full: bool = False
        if samples <= 0:
            msg = "Buffer size must be positive"
            raise ValueError(msg)

    def write(self, data: npt.NDArray[np.int16]) -> None:
        """Write data to buffer, handling wrap-around."""
        data_len = len(data)
        buffer_len = len(self._buffer)

        if data_len > buffer_len:
            data = data[-buffer_len:]
            data_len = buffer_len

        space_left = buffer_len - self._index
        if data_len <= space_left:
            self._buffer[self._index : self._index + data_len] = data
            self._index = (self._index + data_len) % buffer_len
        else:
            self._buffer[self._index :] = data[:space_left]
            self._buffer[: data_len - space_left] = data[space_left:]
            self._index = data_len - space_left

        if not self._is_full and self._index >= buffer_len:
            self._is_full = True

    def read(self) -> npt.NDArray[np.int16]:
        """Read entire buffer in correct temporal order.

        Returns:
            npt.NDArray[np.int16]: A copy of the buffer data in temporal order.
        """
        if not self._is_full:
            return self._buffer[: self._index].copy()

        return np.concatenate(
            [self._buffer[self._index :], self._buffer[: self._index]]
        )


class NAOAudioCallback(Protocol):
    """Protocol for NAO audio processing callback."""

    def process_remote(
        self,
        nb_channels: int,
        nb_samples_per_channel: int,
        timestamp: float,
        input_buffer: bytes,
    ) -> None:
        """Process audio buffer received from NAO."""
        ...


@dataclass
class NAOAudioSource:
    """Audio source implementation for NAO robot."""

    robot_controller: NaoRobot
    format: AudioFormat
    module_name: str = "AudioSource"

    _buffer: AudioBuffer = field(init=False)
    _queue: asyncio.Queue[npt.NDArray[np.int16]] = field(
        default_factory=asyncio.Queue, init=False
    )
    _running: bool = field(default=False, init=False)
    _energy_enabled: bool = field(default=False, init=False)
    _tasks: set[Task[None]] = field(default_factory=set, init=False)  # Use Task[None]

    def __post_init__(self) -> None:
        """Initialize audio buffer."""
        self._buffer = AudioBuffer(self.format)

    @property
    def audio_device(self) -> AudioDeviceService:
        """Get the audio device service from the robot controller."""
        # Assuming robot_controller has an 'audio_device' attribute of type AudioDeviceService
        return self.robot_controller.audio_device

    async def _subscribe_to_events(self) -> None:
        """Subscribe to relevant NAO events.

        Raises:
            AudioSubscriptionError: If subscription fails.
        """
        try:
            # Use the AudioDeviceService methods directly
            await self.audio_device.set_client_preferences(
                self.module_name,
                self.format.sample_rate,
                self.format.channels,
                int(self.format.deinterleaved),
            )
            await self.audio_device.subscribe(self.module_name)
            logger.info(
                "Set client preferences for audio",
                sample_rate=self.format.sample_rate,
                channels=self.format.channels,
                deinterleaved="deinterleaved"
                if self.format.deinterleaved
                else "interleaved",
            )
        except AttributeError as e:
            msg = f"Robot controller or audio service missing required method/attribute: {e}"
            raise AudioSubscriptionError(msg) from e
        except Exception as e:
            msg = f"Failed to set audio client preferences: {e}"
            raise AudioSubscriptionError(msg) from e

    async def _unsubscribe_from_events(self) -> None:
        """Unsubscribe from NAO events."""
        try:
            await self.audio_device.unsubscribe(self.module_name)
            logger.info("Audio client unsubscribed/cleaned up (if applicable).")
        except AttributeError:
            logger.warning(
                "Audio device service missing unsubscribe method. Cannot unsubscribe."
            )
        except Exception:
            logger.exception("Error unsubscribing/cleaning up audio client")

    async def _clear_queue(self) -> None:
        """Clear the audio queue."""
        while not self._queue.empty():
            try:
                self._queue.get_nowait()
            except asyncio.QueueEmpty:
                break

    def _process_incoming_buffer(
        self, input_buffer: bytes, nb_channels: int
    ) -> npt.NDArray[np.int16] | None:
        """Convert raw bytes buffer to numpy array and reshape if necessary.

        Args:
            input_buffer: Raw audio data bytes received from NAO.
            nb_channels: Number of channels in the audio data.

        Returns:
            Processed audio data as a numpy array (int16), or None if processing fails
            (e.g., due to incomplete frame or reshape error). The array shape will be
            (num_samples, num_channels) if deinterleaved and channels > 1, otherwise
            it will be a 1D array (num_samples * num_channels).
        """
        try:
            audio_data: npt.NDArray[np.int16] = np.frombuffer(
                input_buffer, dtype=np.int16
            )

            # Reshape if deinterleaved and more than one channel
            if self.format.deinterleaved and nb_channels > 1:
                if len(audio_data) % nb_channels != 0:
                    logger.warning(
                        "Received incomplete audio frame",
                        size=len(audio_data),
                        channels=nb_channels,
                    )
                    return None

                # Attempt to reshape. Assuming data is interleaved samples across channels:
                # [C1_S1, C2_S1, ..., CN_S1, C1_S2, C2_S2, ...] -> shape (num_samples, num_channels)
                try:
                    audio_data = audio_data.reshape(-1, nb_channels)
                except ValueError:
                    logger.exception(
                        "Reshape error",
                        data_length=len(audio_data),
                        channels=nb_channels,
                    )
                    return None
        except (ValueError, BufferError):
            logger.exception("Error converting buffer to numpy array")
            return None
        else:
            return audio_data

    def _enqueue_buffer_data(self, audio_data: npt.NDArray[np.int16]) -> None:
        """Write processed audio data to the internal buffer and enqueue it for streaming.

        Creates an asynchronous task to put the latest buffer content onto the queue,
        preventing the callback from blocking.

        Args:
            audio_data: The processed audio data numpy array to write and enqueue.
        """
        self._buffer.write(audio_data)
        # Create task to put data in queue without blocking callback
        buffer_content = self._buffer.read()
        task = asyncio.create_task(self._queue.put(buffer_content))
        self._tasks.add(task)
        task.add_done_callback(self._tasks.discard)

    def process_remote(
        self,
        nb_channels: int,
        _nb_samples_per_channel: int,  # Often not reliable, calculate if needed
        _timestamp: float,  # Not currently used
        input_buffer: bytes,
    ) -> None:
        """Process incoming audio data from NAO's audio system callback.

        This method is called directly by the NAOqi framework when new audio data
        is available. It converts the raw buffer, handles potential format issues
        (like reshaping deinterleaved data), writes it to an internal buffer,
        and enqueues the buffer's content for asynchronous consumption via the stream() method.

        Args:
            nb_channels: The number of audio channels detected in the input buffer.
            _nb_samples_per_channel: The number of samples per channel (Note: often unreliable).
            _timestamp: The timestamp associated with the audio buffer (Note: not currently used).
            input_buffer: The raw audio data as bytes.
        """
        if not self._running:
            # Don't process if the source isn't actively streaming
            return

        try:
            processed_data = self._process_incoming_buffer(input_buffer, nb_channels)
            if processed_data is not None:
                self._enqueue_buffer_data(processed_data)
        except Exception:
            # Catch any unexpected errors during processing or enqueueing
            logger.exception(
                "Unexpected error in audio processing callback (process_remote)",
            )

    async def enable_energy_computation(self) -> None:
        """Enable microphone energy level computation via the audio device service."""
        if not self._energy_enabled:
            try:
                # Use the AudioDeviceService method
                await self.audio_device.enable_energy_computation()
                self._energy_enabled = True
                logger.info("Enabled energy computation.")
            except AttributeError:
                logger.warning(
                    "Audio device service missing enable_energy_computation method."
                )
            except Exception:
                logger.exception("Failed to enable energy computation")

    async def disable_energy_computation(self) -> None:
        """Disable microphone energy level computation via the audio device service."""
        if self._energy_enabled:
            try:
                # Use the AudioDeviceService method
                await self.audio_device.disable_energy_computation()
                self._energy_enabled = False
                logger.info("Disabled energy computation.")
            except AttributeError:
                logger.warning(
                    "Audio device service missing disable_energy_computation method."
                )
            except Exception:
                logger.exception("Failed to disable energy computation")

    async def get_energy_levels(self) -> dict[str, float]:
        """Get current energy levels for all microphones.

        Returns:
            dict[str, float]: Dictionary mapping microphone name to energy level.

        Raises:
            AudioProcessingError: If energy computation is not enabled or fetching fails.
        """
        if not self._energy_enabled:
            msg = "Energy computation not enabled"
            raise AudioProcessingError(msg)

        energy_levels: dict[str, float] = {}
        method_map = {
            "front": self.audio_device.get_front_mic_energy,
            "rear": self.audio_device.get_rear_mic_energy,  # Assuming this exists
            "left": self.audio_device.get_left_mic_energy,  # Assuming this exists
            "right": self.audio_device.get_right_mic_energy,  # Assuming this exists
        }

        for mic, method in method_map.items():
            try:
                # Call the async service method directly
                energy: float = await method()
                energy_levels[mic] = energy
            except AttributeError:
                logger.warning("Audio device service missing method for mic", mic=mic)
                energy_levels[mic] = 0.0
            except Exception:
                logger.exception("Failed to get energy level for mic", mic=mic)
                energy_levels[mic] = 0.0

        return energy_levels

    async def stream(self) -> AsyncIterator[npt.NDArray[np.int16]]:
        """Stream audio data asynchronously.

        Yields:
            npt.NDArray[np.int16]: Chunks of audio data.

        Raises:
            AudioProcessingError: If streaming is already active or subscription fails.
        """
        if self._running:
            msg = "Audio streaming already active"
            raise AudioProcessingError(msg)

        await self._subscribe_to_events()
        self._running = True

        try:
            while self._running:
                try:
                    yield await self._queue.get()
                except asyncio.CancelledError:
                    break
                except Exception:
                    logger.exception("Error in audio stream")
        finally:
            self._running = False
            await self._unsubscribe_from_events()
            await self._clear_queue()

    async def stop(self) -> None:
        """Stop audio streaming."""
        self._running = False
