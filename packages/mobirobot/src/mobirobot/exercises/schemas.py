"""TOML configuration schemas for exercises.

These schemas are used for loading exercise definitions from TOML files.
They differ from the runtime models in that movements are specified as
file paths (strings) rather than Movement objects, allowing for dynamic
loading at runtime.

The TOML structure is organized into logical sections:
- [demo]: Contains explanation and optional demo movement
- [exercise]: Contains core exercise configuration
- [audio]: Optional audio settings
- [model_processing]: Optional model processing settings
- [metadata]: Optional exercise metadata
"""

from pathlib import Path
from typing import Annotated, ClassVar, Literal

from nao.nao_types import RobotPosture
from pydantic import BaseModel, ConfigDict, Field, field_validator, model_validator

from mobirobot.common.config import settings
from mobirobot.models.common import Intensity, Turntype
from mobirobot.models.station import Station


def _validate_secure_path(
    path_str: str,
    allowed_extensions: set[str] | None = None,
    base_path: Path | None = None,
    field_name: str = "path",
) -> str:
    """Validate that a path is secure and within allowed boundaries.

    Args:
        path_str: The path string to validate
        allowed_extensions: Set of allowed file extensions (e.g., {".py", ".pkl"})
        base_path: Optional base path to resolve relative paths against
        field_name: Name of the field being validated (for error messages)

    Returns:
        The validated path string

    Raises:
        ValueError: If the path is invalid, contains path traversal, or is outside allowed boundaries
    """
    if not path_str or not path_str.strip():
        msg = f"Path specified in '{field_name}' field cannot be empty"
        raise ValueError(msg)

    path_str = path_str.strip()

    # Check for path traversal attempts
    if ".." in path_str:
        msg = f"Path specified in '{field_name}' field contains path traversal (..) which is not allowed: {path_str}"
        raise ValueError(msg)

    # Ensure path is relative (no absolute paths)
    if Path(path_str).is_absolute():
        msg = f"Path specified in '{field_name}' field must be relative, not absolute: {path_str}"
        raise ValueError(msg)

    # Validate file extension if specified
    if allowed_extensions:
        path_obj = Path(path_str)
        if path_obj.suffix not in allowed_extensions:
            extensions_str = ", ".join(sorted(allowed_extensions))
            msg = f"Path specified in '{field_name}' field must have one of these extensions: {extensions_str}. Got: {path_str}"
            raise ValueError(msg)

    # If base_path is provided, resolve and check existence
    if base_path is not None:
        try:
            full_path = (base_path / path_str).resolve()

            # Ensure the resolved path is still within the base directory
            if not str(full_path).startswith(str(base_path.resolve())):
                msg = f"Path specified in '{field_name}' field resolves outside the allowed base directory: {path_str}"
                raise ValueError(msg)

            # Check if file exists
            if not full_path.exists():
                msg = (
                    f"Path specified in '{field_name}' field does not exist: {path_str}"
                )
                raise ValueError(msg)

        except (OSError, RuntimeError) as e:
            msg = f"Error resolving path specified in '{field_name}' field: {path_str} - {e}"
            raise ValueError(msg) from e

    return path_str


class TomlDemoSection(BaseModel):
    """Demo section for TOML configuration."""

    explanation: Annotated[
        str,
        Field(
            description=(
                "Detailed explanation of how to perform the exercise, "
                "including speech markers and pauses for robot narration"
            )
        ),
    ]
    demo: Annotated[
        str | None,
        Field(
            default=None,
            description="Optional path to demo movement Python file that demonstrates the exercise",
        ),
    ] = None
    model_config: ClassVar[ConfigDict] = ConfigDict(frozen=True)

    @field_validator("demo")
    @classmethod
    def validate_demo_path(cls, v: str | None) -> str | None:
        """Validate that demo path has .py extension if provided and exists."""
        if v is not None and v:
            return _validate_secure_path(
                v,
                allowed_extensions={".py"},
                base_path=settings.movements_dir,
                field_name="demo",
            )
        return v or None


class TomlExerciseSection(BaseModel):
    """Core exercise configuration section."""

    name: Annotated[
        str, Field(description="Display name of the exercise as shown to users")
    ]
    init_pose: Annotated[
        RobotPosture,
        Field(description="Initial robot posture before starting the exercise"),
    ]
    station_tags: Annotated[
        list[Station],
        Field(
            description="List of therapy stations where this exercise can be performed"
        ),
    ]
    turntype: Annotated[
        Turntype,
        Field(
            default=Turntype.NONE,
            description="Type of turn the robot should perform during the exercise",
        ),
    ] = Turntype.NONE
    model_config: ClassVar[ConfigDict] = ConfigDict(frozen=True)


class TomlStandardExerciseSection(TomlExerciseSection):
    """Standard exercise section with movements."""

    movements: Annotated[list[str], Field(description="Paths to movement Python files")]

    @field_validator("movements")
    @classmethod
    def validate_movement_paths(cls, v: list[str]) -> list[str]:
        """Validate that movement paths have .py extension and exist."""
        for path_str in v:
            _validate_secure_path(
                path_str,
                allowed_extensions={".py"},
                base_path=settings.movements_dir,
                field_name="movements",
            )
        return v


class TomlAlternatingExerciseSection(TomlExerciseSection):
    """Alternating exercise section with left/right movements."""

    left_movements: Annotated[
        list[str], Field(description="Paths to left movement Python files")
    ]
    right_movements: Annotated[
        list[str], Field(description="Paths to right movement Python files")
    ]
    alternating_explanation: Annotated[
        str,
        Field(
            description="Explanation text used when switching between left and right sides"
        ),
    ]

    @field_validator("left_movements")
    @classmethod
    def validate_left_movement_paths(cls, v: list[str]) -> list[str]:
        """Validate that left movement paths have .py extension and exist."""
        for path_str in v:
            _validate_secure_path(
                path_str,
                allowed_extensions={".py"},
                base_path=settings.movements_dir,
                field_name="left_movements",
            )
        return v

    @field_validator("right_movements")
    @classmethod
    def validate_right_movement_paths(cls, v: list[str]) -> list[str]:
        """Validate that right movement paths have .py extension and exist."""
        for path_str in v:
            _validate_secure_path(
                path_str,
                allowed_extensions={".py"},
                base_path=settings.movements_dir,
                field_name="right_movements",
            )
        return v


class TomlVariationExerciseSection(TomlExerciseSection):
    """Variation exercise section with variations."""

    variations: Annotated[
        list[list[str]],
        Field(description="Lists of movement file paths for each variation"),
    ]
    variation_explanations: Annotated[
        list[str], Field(description="Explanation texts for each variation")
    ]

    @field_validator("variations")
    @classmethod
    def validate_movement_paths(cls, v: list[list[str]]) -> list[list[str]]:
        """Validate that movement paths have .py extension and exist."""
        for i, variation in enumerate(v):
            for path_str in variation:
                _validate_secure_path(
                    path_str,
                    allowed_extensions={".py"},
                    base_path=settings.movements_dir,
                    field_name=f"variations (variation {i + 1})",
                )
        return v

    @model_validator(mode="after")
    def check_variations_explanations_match(self) -> "TomlVariationExerciseSection":
        """Validate that the number of variations matches the number of explanations."""
        if len(self.variations) != len(self.variation_explanations) + 1:
            msg = (
                f"Number of variations ({len(self.variations)}) must match "
                f"number of variation explanations ({len(self.variation_explanations)}) + 1"
            )
            raise ValueError(msg)
        return self


class TomlAudioSettings(BaseModel):
    """Audio settings for TOML configuration."""

    sounds: Annotated[
        list[str],
        Field(description="List of audio file paths to play during the exercise"),
    ]
    model_config: ClassVar[ConfigDict] = ConfigDict(frozen=True)


class TomlModelProcessingSettings(BaseModel):
    """Model processing settings for TOML configuration."""

    model_files: Annotated[
        list[str],
        Field(
            alias="model",
            description="List of machine learning model files for movement classification",
        ),
    ]
    classifications: Annotated[
        list[str],
        Field(description="List of movement classifications that the model can detect"),
    ]
    hold: Annotated[
        bool,
        Field(
            default=False,
            description="Whether to hold/pause execution while processing model predictions",
        ),
    ] = False
    model_config: ClassVar[ConfigDict] = ConfigDict(populate_by_name=True, frozen=True)

    @field_validator("model_files")
    @classmethod
    def validate_model_file_paths(cls, v: list[str]) -> list[str]:
        """Validate that model file paths are secure and exist."""
        for path_str in v:
            _validate_secure_path(
                path_str,
                allowed_extensions={".pkl", ".joblib", ".onnx", ".pt", ".pth", ".h5"},
                base_path=settings.models_dir,
                field_name="model_files",
            )
        return v

    @model_validator(mode="after")
    def check_model_files_and_classifications_consistent(
        self,
    ) -> "TomlModelProcessingSettings":
        """Validate that if model_files are provided, classifications are also provided and non-empty."""
        if self.model_files and not self.classifications:
            msg = "If 'model_files' are provided, 'classifications' must also be provided and cannot be empty."
            raise ValueError(msg)
        return self


class TomlExerciseMetadata(BaseModel):
    """Exercise metadata for TOML configuration."""

    intensity: Annotated[
        Intensity,
        Field(
            description="Physical intensity level of the exercise (low, medium, high)"
        ),
    ]
    tags: Annotated[
        list[str],
        Field(description="Categorization tags for filtering and organizing exercises"),
    ]
    model_config: ClassVar[ConfigDict] = ConfigDict(frozen=True)


class TomlStandardExercise(BaseModel):
    """Standard exercise TOML configuration.

    Movements are specified as file paths that will be dynamically loaded.
    """

    exercise_type: Literal["standard"] = "standard"

    # Required sections
    demo: Annotated[
        TomlDemoSection,
        Field(
            description="Demo section containing explanation and optional demo movement"
        ),
    ]
    exercise: Annotated[
        TomlStandardExerciseSection,
        Field(description="Core exercise configuration with movements"),
    ]

    # Optional sections
    audio: Annotated[
        TomlAudioSettings | None,
        Field(default=None, description="Optional audio settings"),
    ] = None
    model_processing: Annotated[
        TomlModelProcessingSettings | None,
        Field(default=None, description="Optional model processing settings"),
    ] = None
    metadata: Annotated[
        TomlExerciseMetadata | None,
        Field(default=None, description="Optional exercise metadata"),
    ] = None

    model_config: ClassVar[ConfigDict] = ConfigDict(populate_by_name=True, frozen=True)


class TomlAlternatingExercise(BaseModel):
    """Alternating exercise TOML configuration."""

    exercise_type: Literal["alternating"] = "alternating"

    # Required sections
    demo: Annotated[
        TomlDemoSection,
        Field(
            description="Demo section containing explanation and optional demo movement"
        ),
    ]
    exercise: Annotated[
        TomlAlternatingExerciseSection,
        Field(description="Core exercise configuration with left/right movements"),
    ]

    # Optional sections
    audio: Annotated[
        TomlAudioSettings | None,
        Field(default=None, description="Optional audio settings"),
    ] = None
    model_processing: Annotated[
        TomlModelProcessingSettings | None,
        Field(default=None, description="Optional model processing settings"),
    ] = None
    metadata: Annotated[
        TomlExerciseMetadata | None,
        Field(default=None, description="Optional exercise metadata"),
    ] = None

    model_config: ClassVar[ConfigDict] = ConfigDict(populate_by_name=True, frozen=True)


class TomlVariationExercise(BaseModel):
    """Variation exercise TOML configuration."""

    exercise_type: Literal["variation"] = "variation"

    # Required sections
    demo: Annotated[
        TomlDemoSection,
        Field(
            description="Demo section containing explanation and optional demo movement"
        ),
    ]
    exercise: Annotated[
        TomlVariationExerciseSection,
        Field(description="Core exercise configuration with variations"),
    ]

    # Optional sections
    audio: Annotated[
        TomlAudioSettings | None,
        Field(default=None, description="Optional audio settings"),
    ] = None
    model_processing: Annotated[
        TomlModelProcessingSettings | None,
        Field(default=None, description="Optional model processing settings"),
    ] = None
    metadata: Annotated[
        TomlExerciseMetadata | None,
        Field(default=None, description="Optional exercise metadata"),
    ] = None

    model_config: ClassVar[ConfigDict] = ConfigDict(populate_by_name=True, frozen=True)


# Union type for any TOML exercise configuration
AnyTomlExercise = TomlStandardExercise | TomlAlternatingExercise | TomlVariationExercise
