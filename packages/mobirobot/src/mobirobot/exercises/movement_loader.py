"""Dynamic movement loading from Python files.

This module provides functionality to dynamically load movement data
from Python files and convert them to Movement dataclass instances.
"""

import importlib.util
from pathlib import Path
from typing import TYPE_CHECKING, Any, cast, get_args

from mobirobot.common import Err, Ok, Result
from mobirobot.common.config import settings
from mobirobot.common.structured_logging import LoggerType, get_logger
from mobirobot.models.movement import ExerciseMovementSequence, Movement
from mobirobot.models.nao_types import Nao<PERSON><PERSON><PERSON><PERSON>

if TYPE_CHECKING:
    from collections.abc import Sequence

logger: LoggerType = get_logger(__name__)

# Constants
MAX_MOVEMENT_FILES = 3


class MovementLoader:
    """Loads movement data from Python files dynamically."""

    base_path: Path

    def __init__(self, base_path: str | Path):
        """Initialize the movement loader.

        Args:
            base_path: Base directory path where movement files are located.

        Raises:
            ValueError: If the base path does not exist.

        """
        self.base_path = Path(base_path)
        if not self.base_path.exists():
            msg = f"Base path does not exist: {self.base_path}"
            raise ValueError(msg)

    def load_movement_from_file(self, file_path: str) -> Result[Movement, str]:
        """Load a movement from a Python file.

        Args:
            file_path: Relative path to the Python file (e.g., "moves/Boxen_Init.py")

        Returns:
            Result containing Movement instance or error message.
        """
        full_path = self.base_path / file_path

        if not full_path.exists():
            logger.warning(
                "Movement file not found",
                file_path=file_path,
                full_path=str(full_path),
            )
            return Err(f"Movement file not found: {full_path}")

        if full_path.suffix != ".py":
            logger.warning(
                "Movement file must be a Python file",
                file_path=file_path,
                full_path=str(full_path),
                suffix=full_path.suffix,
            )
            return Err(f"Movement file must be a Python file: {full_path}")

        try:
            # Load the module dynamically
            spec = importlib.util.spec_from_file_location(
                f"movement_{full_path.stem}", full_path
            )
            if spec is None or spec.loader is None:
                logger.error(
                    "Could not load module spec",
                    file_path=file_path,
                    full_path=str(full_path),
                )
                return Err(f"Could not load module spec from {full_path}")

            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)

            # Check if module has a callable __call__ function
            call_func = getattr(module, "__call__", None)  # noqa: B004
            if not callable(call_func):
                logger.error(
                    "Movement file must have a __call__ function",
                    file_path=file_path,
                    full_path=str(full_path),
                )
                return Err(f"Movement file must have a __call__ function: {full_path}")

            # Execute the function to get movement data
            result = call_func()
            joint_names, times, angles = cast(
                "tuple[list[str], list[list[float]], list[list[Any]]]", result
            )

            # Validate joint names are valid NaoJointName values
            validated_joint_names = self._validate_joint_names(joint_names)
            if isinstance(validated_joint_names, str):
                return Err(
                    f"Invalid joint names in {full_path}: {validated_joint_names}"
                )

            # Create Movement instance with proper type casting
            movement = Movement(
                joint_names=validated_joint_names,
                times=cast("Sequence[Sequence[float]]", times),
                angles=cast("Sequence[Sequence[Any]]", angles),
            )

            return Ok(movement)

        except Exception:
            logger.exception(
                "Error loading movement from file",
                file_path=file_path,
                full_path=str(full_path),
            )
            return Err(f"Error loading movement from {full_path}")

    @staticmethod
    def _validate_joint_names(
        joint_names: list[str],
    ) -> "Sequence[NaoJointName] | str":
        """Validate that joint names are valid NaoJointName values.

        Args:
            joint_names: List of joint name strings to validate.

        Returns:
            Validated sequence of NaoJointName values or error message string.
        """
        valid_joint_names = get_args(NaoJointName)
        invalid_names = [name for name in joint_names if name not in valid_joint_names]

        if invalid_names:
            return f"Invalid joint names: {invalid_names}. Valid names are: {valid_joint_names}"

        # Type cast is safe here since we've validated all names
        return cast("Sequence[NaoJointName]", joint_names)

    def load_exercise_movement_sequence(
        self, movement_files: list[str]
    ) -> Result[ExerciseMovementSequence, str]:
        """Load an ExerciseMovementSequence from movement files.

        Expects 1-3 movement files in the order [init, main, exit].
        - If 1 file: main movement only (init and exit will be None)
        - If 2 files: init and main movements (exit will be None)
        - If 3 files: init, main, and exit movements

        Args:
            movement_files: List of 1-3 movement file paths in [init, main, exit] order.

        Returns:
            Result containing ExerciseMovementSequence or error message.
        """
        if not movement_files:
            return Err("At least one movement file must be provided")

        if len(movement_files) > MAX_MOVEMENT_FILES:
            return Err(
                f"Too many movement files provided: {len(movement_files)}. "
                + f"Maximum is {MAX_MOVEMENT_FILES}."
            )

        # Load all movements
        movements: list[Movement] = []
        for file_path in movement_files:
            result = self.load_movement_from_file(file_path)
            match result:
                case Ok(movement):
                    movements.append(movement)
                case Err(error_msg):
                    return Err(f"Failed to load movement from {file_path}: {error_msg}")

        # Map movements to sequence phases based on count
        match len(movements):
            case 1:
                # Only main movement
                sequence = ExerciseMovementSequence(
                    init_movement=None,
                    main_movement=movements[0],
                    exit_movement=None,
                )
            case 2:
                # Init and main movements
                sequence = ExerciseMovementSequence(
                    init_movement=movements[0],
                    main_movement=movements[1],
                    exit_movement=None,
                )
            case 3:
                # Init, main, and exit movements
                sequence = ExerciseMovementSequence(
                    init_movement=movements[0],
                    main_movement=movements[1],
                    exit_movement=movements[2],
                )
            case _:
                return Err(f"Unexpected number of movements: {len(movements)}")

        return Ok(sequence)

    def load_movement_sequence_variations(
        self, variations: list[list[str]]
    ) -> Result[list[ExerciseMovementSequence], str]:
        """Load movement sequence variations from Python files.

        Args:
            variations: List of lists, each containing file paths for a variation sequence.

        Returns:
            Result containing list of ExerciseMovementSequence variations or error message.
        """
        loaded_variations: list[ExerciseMovementSequence] = []

        for i, variation_paths in enumerate(variations):
            result = self.load_exercise_movement_sequence(variation_paths)
            match result:
                case Ok(sequence):
                    loaded_variations.append(sequence)
                case Err(error_msg):
                    return Err(f"Failed to load variation {i}: {error_msg}")

        return Ok(loaded_variations)


def create_default_movement_loader() -> MovementLoader:
    """Create a movement loader with the default base path.

    Returns:
        MovementLoader instance configured for the default movement directory.
    """
    return MovementLoader(settings.movements_dir)
