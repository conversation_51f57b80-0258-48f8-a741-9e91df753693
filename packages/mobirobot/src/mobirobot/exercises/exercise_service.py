"""Exercise service providing a public API for exercise management.

This service handles loading exercises from TOML files, dynamically loading
movement data, and converting between TOML schemas and runtime models.
"""

from pathlib import Path

from mobirobot.common import Err, Ok, Result
from mobirobot.common.config import settings
from mobirobot.common.structured_logging import LoggerType, get_logger
from mobirobot.models.exercise import (
    AlternatingExercise,
    AnyExercise,
    AudioSettings,
    ExerciseMetadata,
    ModelProcessingSettings,
    StandardExercise,
    VariationExercise,
)
from mobirobot.models.movement import Movement
from mobirobot.models.station import Station

from .movement_loader import MovementLoader, create_default_movement_loader
from .schemas import (
    AnyTomlExercise,
    TomlAlternatingExercise,
    TomlStandardExercise,
    TomlVariationExercise,
)
from .toml_exercise_repository import TomlExerciseRepository

logger: LoggerType = get_logger(__name__)


class ExerciseService:
    """Service for managing exercises with dynamic movement loading."""

    def __init__(
        self,
        exercises_dir: str | Path | None = None,
        movement_loader: MovementLoader | None = None,
    ):
        """Initialize the exercise service.

        Args:
            exercises_dir: Directory containing TOML exercise files.
            movement_loader: Optional movement loader. If None, uses default.
        """
        self.toml_repository: TomlExerciseRepository = TomlExerciseRepository(
            exercises_dir or settings.exercises_dir
        )
        self.movement_loader: MovementLoader = (
            movement_loader or create_default_movement_loader()
        )
        self._exercise_cache: dict[str, AnyExercise] = {}

    def load_exercise(self, exercise_name: str) -> Result[AnyExercise, str]:
        """Load a single exercise by name.

        Args:
            exercise_name: Name of the exercise file (without .toml extension).

        Returns:
            Result containing the loaded exercise or error message.
        """
        # Check cache first
        if exercise_name in self._exercise_cache:
            return Ok(self._exercise_cache[exercise_name])

        # Load TOML configuration
        toml_result = self.toml_repository.load_exercise(exercise_name)
        match toml_result:
            case Err(error_msg):
                return Err(error_msg)
            case Ok(toml_exercise):
                pass

        # Convert to runtime model
        runtime_result = self._convert_toml_to_runtime(toml_exercise)
        match runtime_result:
            case Ok(runtime_exercise):
                # Cache the result
                self._exercise_cache[exercise_name] = runtime_exercise
                return Ok(runtime_exercise)
            case Err(error_msg):
                return Err(error_msg)

    def load_all_exercises(self) -> dict[str, AnyExercise]:
        """Load all exercises from the configured directory.

        Returns:
            Dictionary mapping exercise names to loaded exercise instances.
            Failed exercises are logged and skipped.
        """
        toml_exercises = self.toml_repository.load_all_exercises()
        runtime_exercises: dict[str, AnyExercise] = {}

        for exercise_name, toml_exercise in toml_exercises.items():
            result = self._convert_toml_to_runtime(toml_exercise)
            match result:
                case Ok(runtime_exercise):
                    runtime_exercises[exercise_name] = runtime_exercise
                    self._exercise_cache[exercise_name] = runtime_exercise
                case Err(error_msg):
                    logger.error(
                        "Failed to convert exercise to runtime model",
                        exercise_name=exercise_name,
                        error=error_msg,
                    )

        return runtime_exercises

    def filter_by_station(self, station: Station) -> dict[str, AnyExercise]:
        """Get all exercises available at a specific station.

        Args:
            station: The station to filter by.

        Returns:
            Dictionary of exercises available at the station.
        """
        all_exercises = self.load_all_exercises()
        return {
            name: exercise
            for name, exercise in all_exercises.items()
            if station in exercise.station_tags
        }

    def get_exercise(self, exercise_name: str) -> AnyExercise:
        """Get an exercise by name.

        Args:
            exercise_name: Name of the exercise.

        Returns:
            The exercise.

        Raises:
            ValueError: If the exercise cannot be loaded.
        """
        if exercise_name in self._exercise_cache:
            return self._exercise_cache[exercise_name]

        match self.load_exercise(exercise_name):
            case Ok(exercise):
                return exercise
            case Err(error_msg):
                msg = f"Failed to load exercise: {error_msg}"
                raise ValueError(msg)

    def clear_cache(self) -> None:
        """Clear the exercise cache."""
        self._exercise_cache.clear()

    def _convert_toml_to_runtime(
        self, toml_exercise: AnyTomlExercise
    ) -> Result[AnyExercise, str]:
        """Convert a TOML exercise configuration to a runtime model.

        Args:
            toml_exercise: The TOML exercise configuration.

        Returns:
            Result containing the runtime exercise model or error message.
        """
        try:
            # Convert optional sections
            audio = None
            if toml_exercise.audio:
                audio = AudioSettings(sounds=toml_exercise.audio.sounds)

            model_processing = None
            if toml_exercise.model_processing:
                model_processing = ModelProcessingSettings(
                    model_files=toml_exercise.model_processing.model_files,
                    classifications=toml_exercise.model_processing.classifications,
                    hold=toml_exercise.model_processing.hold,
                )

            metadata = None
            if toml_exercise.metadata:
                metadata = ExerciseMetadata(
                    intensity=toml_exercise.metadata.intensity,
                    tags=toml_exercise.metadata.tags,
                )

            # Load demo movement if specified
            demo = None
            if toml_exercise.demo and toml_exercise.demo.demo:
                demo_result = self.movement_loader.load_movement_from_file(
                    toml_exercise.demo.demo
                )
                match demo_result:
                    case Ok(demo_movement):
                        demo = demo_movement
                    case Err(error_msg):
                        return Err(f"Failed to load demo movement: {error_msg}")

            # Convert based on exercise type using match for proper type narrowing
            match toml_exercise:
                case TomlStandardExercise():
                    result = self._convert_standard_exercise(
                        toml_exercise, audio, model_processing, metadata, demo
                    )
                    return result.map(lambda x: x)  # Convert to AnyExercise
                case TomlAlternatingExercise():
                    result = self._convert_alternating_exercise(
                        toml_exercise, audio, model_processing, metadata, demo
                    )
                    return result.map(lambda x: x)  # Convert to AnyExercise
                case TomlVariationExercise():
                    result = self._convert_variation_exercise(
                        toml_exercise, audio, model_processing, metadata, demo
                    )
                    return result.map(lambda x: x)  # Convert to AnyExercise

        except Exception:
            logger.exception(
                "Error converting TOML exercise to runtime model",
                exercise_type=type(toml_exercise).__name__,
            )
            return Err("Error converting TOML exercise to runtime model")

    def _convert_standard_exercise(
        self,
        toml_exercise: TomlStandardExercise,
        audio: AudioSettings | None,
        model_processing: ModelProcessingSettings | None,
        metadata: ExerciseMetadata | None,
        demo: Movement | None,
    ) -> Result[StandardExercise, str]:
        """Convert a TOML standard exercise to runtime model."""
        # Load movement sequence
        movements_result = self.movement_loader.load_exercise_movement_sequence(
            toml_exercise.exercise.movements
        )
        match movements_result:
            case Ok(movements):
                pass
            case Err(error_msg):
                return Err(f"Failed to load movement sequence: {error_msg}")

        return Ok(
            StandardExercise(
                name=toml_exercise.exercise.name,
                explanation=toml_exercise.demo.explanation,
                init_pose=toml_exercise.exercise.init_pose,
                station_tags=toml_exercise.exercise.station_tags,
                turntype=toml_exercise.exercise.turntype,
                movements=movements,
                demo=demo,
                audio=audio,
                model_processing=model_processing,
                metadata=metadata,
            )
        )

    def _convert_alternating_exercise(
        self,
        toml_exercise: TomlAlternatingExercise,
        audio: AudioSettings | None,
        model_processing: ModelProcessingSettings | None,
        metadata: ExerciseMetadata | None,
        demo: Movement | None,
    ) -> Result[AlternatingExercise, str]:
        """Convert a TOML alternating exercise to runtime model."""
        # Load left movement sequence
        left_result = self.movement_loader.load_exercise_movement_sequence(
            toml_exercise.exercise.left_movements
        )
        match left_result:
            case Ok(left_movements):
                pass
            case Err(error_msg):
                return Err(f"Failed to load left movement sequence: {error_msg}")

        # Load right movement sequence
        right_result = self.movement_loader.load_exercise_movement_sequence(
            toml_exercise.exercise.right_movements
        )
        match right_result:
            case Ok(right_movements):
                pass
            case Err(error_msg):
                return Err(f"Failed to load right movement sequence: {error_msg}")

        return Ok(
            AlternatingExercise(
                name=toml_exercise.exercise.name,
                explanation=toml_exercise.demo.explanation,
                init_pose=toml_exercise.exercise.init_pose,
                station_tags=toml_exercise.exercise.station_tags,
                turntype=toml_exercise.exercise.turntype,
                left_movements=left_movements,
                right_movements=right_movements,
                alternating_explanation=toml_exercise.exercise.alternating_explanation,
                demo=demo,
                audio=audio,
                model_processing=model_processing,
                metadata=metadata,
            )
        )

    def _convert_variation_exercise(
        self,
        toml_exercise: TomlVariationExercise,
        audio: AudioSettings | None,
        model_processing: ModelProcessingSettings | None,
        metadata: ExerciseMetadata | None,
        demo: Movement | None,
    ) -> Result[VariationExercise, str]:
        """Convert a TOML variation exercise to runtime model."""
        # Load variation sequences
        variations_result = self.movement_loader.load_movement_sequence_variations(
            toml_exercise.exercise.variations
        )
        match variations_result:
            case Ok(variations):
                pass
            case Err(error_msg):
                return Err(f"Failed to load variation sequences: {error_msg}")

        return Ok(
            VariationExercise(
                name=toml_exercise.exercise.name,
                explanation=toml_exercise.demo.explanation,
                init_pose=toml_exercise.exercise.init_pose,
                station_tags=toml_exercise.exercise.station_tags,
                turntype=toml_exercise.exercise.turntype,
                variations=variations,
                variation_explanations=toml_exercise.exercise.variation_explanations,
                demo=demo,
                audio=audio,
                model_processing=model_processing,
                metadata=metadata,
            )
        )
