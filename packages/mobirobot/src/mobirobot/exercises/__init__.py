"""Exercise definitions package.

This package contains a service that loads exercises from TOML files with
dynamic movement loading and provides a repository of exercises.
"""

from .exercise_service import ExerciseService
from .movement_loader import MovementLoader, create_default_movement_loader
from .schemas import (
    AnyTomlExercise,
    TomlAlternatingExercise,
    TomlStandardExercise,
    TomlVariationExercise,
)
from .toml_exercise_repository import TomlExerciseRepository

__all__ = [
    "AnyTomlExercise",
    "ExerciseService",
    "MovementLoader",
    "TomlAlternatingExercise",
    "TomlExerciseRepository",
    "TomlStandardExercise",
    "TomlVariationExercise",
    "create_default_movement_loader",
]
