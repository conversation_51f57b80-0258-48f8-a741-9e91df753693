import tomllib
from pathlib import Path

from pydantic import <PERSON><PERSON><PERSON><PERSON><PERSON>, ValidationError

from mobirobot.common import Err, Ok, Result
from mobirobot.common.structured_logging import LoggerType, get_logger

from .schemas import AnyTomlExercise

logger: LoggerType = get_logger(__name__)


class TomlExerciseRepository:
    """Loads exercise definitions from TOML files.

    Each exercise is expected to be in its own .toml file within the specified directory.
    The Pydantic model 'AnyExercise' (a discriminated union) is used to parse
    the files, automatically determining the correct exercise type (Standard,
    Alternating, Variation) based on the 'exercise_type' field in the TOML.
    """

    def __init__(self, exercises_dir: str | Path):
        """Initializes the repository with the directory containing exercise TOML files.

        Args:
            exercises_dir: Path to the directory where exercise .toml files are stored.

        Raises:
            ValueError: If the exercises_dir does not exist or is not a directory.
        """
        self.exercises_dir: Path = Path(exercises_dir)
        if not self.exercises_dir.is_dir():
            msg = f"Exercises directory not found or is not a directory: {self.exercises_dir}"
            raise ValueError(msg)

    def load_exercise(self, exercise_file_name: str) -> Result[AnyTomlExercise, str]:
        """Loads a single exercise from its TOML file.

        The file name should be provided without the .toml extension.

        Args:
            exercise_file_name: The name of the exercise file (e.g., "Squat").

        Returns:
            An Result containing an instance of a specific exercise type
            (StandardExercise, AlternatingExercise, or VariationExercise) if successful,
            or an error message string if the file is not found, cannot be decoded,
            or fails validation.
        """
        exercise_file_path = self.exercises_dir / f"{exercise_file_name}.toml"

        if not exercise_file_path.exists():
            logger.warning(
                "Exercise file not found",
                exercise_file_name=exercise_file_name,
                file_path=str(exercise_file_path),
            )
            return Err(f"Exercise file not found at {exercise_file_path}")

        try:
            with exercise_file_path.open("rb") as f:
                data = tomllib.load(f)

            adapter: TypeAdapter[AnyTomlExercise] = TypeAdapter(AnyTomlExercise)
            exercise_model: AnyTomlExercise = adapter.validate_python(data)
            return Ok(exercise_model)
        except tomllib.TOMLDecodeError:
            logger.exception(
                "Error decoding TOML file",
                exercise_file_name=exercise_file_name,
                file_path=str(exercise_file_path),
            )
            return Err(f"Error decoding TOML file {exercise_file_path}")
        except ValidationError:
            logger.exception(
                "Validation error for exercise",
                exercise_file_name=exercise_file_name,
                file_path=str(exercise_file_path),
            )
            return Err(f"Validation error for exercise in {exercise_file_path}")
        except OSError:  # Catch common file operation errors
            logger.exception(
                "File operation error",
                exercise_file_name=exercise_file_name,
                file_path=str(exercise_file_path),
            )
            return Err(f"File operation error for {exercise_file_path}")

    def load_all_exercises(self) -> dict[str, AnyTomlExercise]:
        """Loads all exercises from .toml files in the configured directory.

        The keys of the returned dictionary will be the exercise names as defined
        within their respective TOML files ('name' field). If an exercise file
        cannot be loaded or parsed, it will be logged as an error and skipped.

        Returns:
            A dictionary mapping exercise names to their loaded exercise model instances.
        """
        loaded_exercises: dict[str, AnyTomlExercise] = {}
        for toml_file_path in self.exercises_dir.glob("*.toml"):
            exercise_file_name = toml_file_path.stem
            result = self.load_exercise(exercise_file_name)

            match result:
                case Ok(exercise):
                    if exercise.exercise.name in loaded_exercises:
                        logger.warning(
                            "Duplicate exercise name found, overwriting previous entry",
                            exercise_name=exercise.exercise.name,
                            current_file=str(toml_file_path),
                            previous_exercise=str(
                                loaded_exercises[exercise.exercise.name]
                            ),
                        )
                    loaded_exercises[exercise.exercise.name] = exercise
                case Err(error_message):
                    logger.error(
                        "Failed to load exercise from file",
                        file_path=str(toml_file_path),
                        error=error_message,
                    )
        return loaded_exercises
