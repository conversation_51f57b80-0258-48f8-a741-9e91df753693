import asyncio
import contextlib
import time
from concurrent.futures import Thread<PERSON>ool<PERSON>xecutor
from dataclasses import dataclass, field

import cv2
import numpy as np

from mobirobot.common import settings
from mobirobot.common.config import StreamSettings
from mobirobot.common.result_types import Err, Ok
from mobirobot.common.structured_logging import LoggerType, get_logger
from mobirobot.webcam.find_webcam import find_camera_by_name

logger: LoggerType = get_logger(__name__)


@dataclass
class WebcamService:
    """Captures video frames from a single source (real webcam or mock) and dispatches them to multiple asynchronous consumers.

    Manages camera initialization, frame reading, and dispatching.
    """

    settings: StreamSettings
    _capture: cv2.VideoCapture | None = None
    _consumers: set[asyncio.Queue[cv2.typing.MatLike]] = field(default_factory=set)
    _capture_task: asyncio.Task[None] | None = None
    _is_running: bool = False
    _lock: asyncio.Lock = field(default_factory=asyncio.Lock)
    _executor: ThreadPoolExecutor = field(
        default_factory=lambda: ThreadPoolExecutor(
            max_workers=1, thread_name_prefix="WebcamServiceCapture"
        )
    )
    _last_frame_time: float = 0.0
    _target_frame_interval: float = field(init=False)

    def __post_init__(self) -> None:
        """Initialize computed fields after dataclass initialization."""
        self._target_frame_interval = (
            1.0 / self.settings.stream_target_fps
            if self.settings.stream_target_fps > 0
            else 0.033  # Default to ~30 FPS if target_fps is 0 or invalid
        )

    @classmethod
    def create(
        cls,
        config: StreamSettings | None = None,
    ) -> "WebcamService":
        """Create a new WebcamService instance.

        Args:
            config: Stream settings to use. If None, uses default settings.

        Returns:
            A new WebcamService instance.
        """
        service_settings = config or settings.stream_settings
        return cls(settings=service_settings)

    async def _initialize_camera_capture(self) -> cv2.VideoCapture | None:
        """Initializes and opens the video capture device.

        This method is blocking and should be run in an executor.

        Returns:
            A VideoCapture object or None if opening fails.
        """
        loop = asyncio.get_running_loop()
        try:
            capture_result = await loop.run_in_executor(
                self._executor, find_camera_by_name, self.settings.camera_name_query
            )
        except RuntimeError:
            logger.exception(
                "Thread pool executor error while finding camera",
                camera_name_query=self.settings.camera_name_query,
            )
            return None

        match capture_result:
            case Ok(capture_device):
                pass  # capture_device is now available with correct type
            case Err(error):
                logger.warning(
                    "No webcam found for search term",
                    camera_name_query=self.settings.camera_name_query,
                    error=error,
                )
                return None

        # Apply camera settings
        try:
            capture_device.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter.fourcc(*"MJPG"))
            capture_device.set(
                cv2.CAP_PROP_FRAME_WIDTH, self.settings.stream_resolution[0]
            )
            capture_device.set(
                cv2.CAP_PROP_FRAME_HEIGHT, self.settings.stream_resolution[1]
            )
            capture_device.set(cv2.CAP_PROP_FPS, self.settings.stream_target_fps)
        except (cv2.error, ValueError) as e:
            logger.warning(
                "Failed to configure camera settings for",
                camera_name_query=self.settings.camera_name_query,
                error=str(e),
            )
            # Continue anyway as camera might still work with default settings

        logger.info(
            "Camera opened successfully and configured",
            camera_name_query=self.settings.camera_name_query,
        )
        return capture_device

    async def start(self) -> bool:
        """Starts the webcam service: initializes the camera and begins the frame capture loop.

        Returns:
            True if started successfully, False otherwise.
        """
        async with self._lock:
            if self._is_running:
                logger.info("WebcamService is already running.")
                return True

            if not self.settings.use_mock_camera:
                self._capture = await self._initialize_camera_capture()
                if not self._capture:
                    logger.error(
                        "WebcamService failed to start: Could not initialize camera."
                    )
                    return False
            else:
                logger.info("WebcamService starting in mock camera mode.")
                self._capture = None  # Explicitly None for mock mode

            self._is_running = True
            self._capture_task = asyncio.create_task(self._capture_loop())
            logger.info("WebcamService started.")
            return True

    async def stop(self) -> None:
        """Stops the webcam service: halts the capture loop and releases resources."""
        async with self._lock:
            if not self._is_running:
                logger.info("WebcamService is already stopped.")
                return
            self._is_running = False  # Signal the loop to stop

        if self._capture_task:
            try:
                self._capture_task.cancel()
                await self._capture_task
            except asyncio.CancelledError:
                logger.info("Capture task was cancelled successfully.")
            self._capture_task = None

        if self._capture:
            try:
                logger.info("Releasing video capture device.")
                await asyncio.get_running_loop().run_in_executor(
                    self._executor, self._capture.release
                )
            except (RuntimeError, OSError):
                logger.exception("Exception releasing video capture device")
            self._capture = None

        async with self._lock:  # Ensure consumers are handled safely
            for q in list(self._consumers):
                while not q.empty():
                    with contextlib.suppress(asyncio.QueueEmpty):
                        q.get_nowait()
            self._consumers.clear()

        if self._executor:
            self._executor.shutdown(wait=True)

        logger.info("WebcamService stopped and resources released.")

    def _generate_mock_frame(self) -> cv2.typing.MatLike:
        """Generates or loads a mock frame.

        This is a CPU-bound operation (image reading/creation).

        Returns:
            A mock frame.
        """
        if (
            self.settings.mock_camera_image_path
            and self.settings.mock_camera_image_path.exists()
            and self.settings.mock_camera_image_path.is_file()
        ):
            try:
                img = cv2.imread(str(self.settings.mock_camera_image_path))
            except (OSError, cv2.error):
                logger.exception(
                    "Error reading mock image from",
                    mock_camera_image_path=self.settings.mock_camera_image_path,
                )
                return np.zeros(
                    (self.settings.output_size[1], self.settings.output_size[0], 3),
                    dtype=np.uint8,
                )

            try:
                return cv2.resize(img, self.settings.output_size)
            except (cv2.error, ValueError):
                logger.exception(
                    "Error resizing mock image from",
                    mock_camera_image_path=self.settings.mock_camera_image_path,
                )

        return np.zeros(
            (self.settings.output_size[1], self.settings.output_size[0], 3),
            dtype=np.uint8,
        )

    async def _read_frame_from_device(self) -> cv2.typing.MatLike | None:
        """Reads a frame from the currently open capture device.

        This is a blocking I/O operation, run in the executor.

        Returns:
            The frame (resized) or None if reading fails.
        """
        if not self._capture or not self._capture.isOpened():
            logger.warning(
                "Attempted to read from uninitialized or closed capture device."
            )
            return None

        loop = asyncio.get_running_loop()
        try:
            ret, frame = await loop.run_in_executor(self._executor, self._capture.read)
        except RuntimeError:
            logger.exception("Thread pool executor error reading frame from device")
            return None

        # cv2.VideoCapture.read() returns (success_flag, frame)
        if not ret:
            logger.warning("Failed to read frame from capture device.")
            return None

        try:
            return cv2.resize(frame, self.settings.output_size)
        except (cv2.error, ValueError):
            logger.exception("Error resizing frame from device")
            return None

    async def _get_next_frame(self) -> cv2.typing.MatLike | None:
        """Acquires the next frame, either from mock or real camera."""
        if self.settings.use_mock_camera:
            # Mock frame generation can be CPU bound (imread, resize)
            return await asyncio.get_running_loop().run_in_executor(
                self._executor, self._generate_mock_frame
            )
        return await self._read_frame_from_device()

    async def _dispatch_frame(self, frame: cv2.typing.MatLike) -> None:
        """Dispatches the given frame to all subscribed consumers."""
        async with self._lock:  # Protect consumer list during iteration
            if not self._consumers:
                return  # No one is listening

            for queue in list(
                self._consumers
            ):  # Iterate over a copy for safe modification
                if queue.full():
                    with contextlib.suppress(asyncio.QueueEmpty):
                        queue.get_nowait()  # Make space
                try:
                    queue.put_nowait(frame.copy())  # Dispatch a copy
                except asyncio.QueueFull:
                    logger.warning(
                        "Consumer queue full despite trying to make space. Frame dropped.",
                        queue_id=id(queue),
                    )
                except Exception:  # Catch other potential errors during put
                    logger.exception(
                        "Error putting frame to consumer queue",
                        queue_id=id(queue),
                    )

    async def _handle_capture_failure(self) -> None:
        """Handles failures in capturing frames from the real camera, attempting to reconnect."""
        logger.error("Capture failure detected. Attempting to reconnect camera.")
        if self._capture:
            try:
                await asyncio.get_running_loop().run_in_executor(
                    self._executor, self._capture.release
                )
            except (RuntimeError, OSError):
                logger.exception("Exception releasing problematic capture device")
            self._capture = None

        await asyncio.sleep(self.settings.camera_retry_delay)

        # Lock is not strictly needed here for self._capture if only _capture_loop modifies it,
        # but it's safer if _initialize_camera_capture is called from elsewhere too.
        # However, start() already handles locking for the initial setup.
        # For reconnection, this method is only called from _capture_loop.
        if self._is_running:  # Only attempt if service is still supposed to be running
            new_capture = await self._initialize_camera_capture()
            if new_capture:
                self._capture = new_capture
                logger.info("Successfully reconnected to camera.")
            else:
                logger.error(
                    "Failed to reconnect camera. Will retry on next capture failure."
                )

    async def _capture_loop(self) -> None:
        """Main loop for capturing and dispatching frames."""
        logger.info("WebcamService capture loop started.")
        while self._is_running:
            loop_start_time = time.monotonic()

            frame = await self._get_next_frame()

            if frame is not None:
                await self._dispatch_frame(frame)
            elif (
                not self.settings.use_mock_camera and self._is_running
            ):  # If real camera failed
                await self._handle_capture_failure()

            # Calculate sleep time to maintain target FPS
            elapsed_time = time.monotonic() - loop_start_time
            sleep_duration = self._target_frame_interval - elapsed_time
            if sleep_duration > 0:
                await asyncio.sleep(sleep_duration)
            elif sleep_duration < 0:
                logger.debug(
                    "Capture loop took longer than target interval",
                    sleep_duration=-sleep_duration,
                )
            # If sleep_duration is 0 or negative, loop immediately (or after a tiny yield)
            # to catch up or if processing is slow. A small sleep can prevent 100% CPU usage
            # if frame acquisition is extremely fast and there are no consumers.
            # However, the current logic will sleep if processing is faster than interval.
            if (
                not self._consumers
            ):  # If no consumers, sleep a bit more to not busy loop too hard
                await asyncio.sleep(0.1)

        logger.info("WebcamService capture loop finished.")

    async def subscribe(
        self, max_queue_size: int = 1
    ) -> asyncio.Queue[cv2.typing.MatLike]:
        """Subscribes a consumer to receive frames.

        Args:
            max_queue_size: Max size of the queue for this consumer.
                            A size of 1 means only the latest frame is kept.

        Returns:
            An asyncio.Queue that will receive new frames.
        """
        queue: asyncio.Queue[cv2.typing.MatLike] = asyncio.Queue(maxsize=max_queue_size)
        async with self._lock:
            self._consumers.add(queue)
            logger.info(
                "New consumer subscribed",
                queue_id=id(queue),
                total_consumers=len(self._consumers),
            )
        return queue

    async def unsubscribe(self, queue: asyncio.Queue[cv2.typing.MatLike]) -> None:
        """Unsubscribes a consumer and clears its queue."""
        async with self._lock:
            if queue in self._consumers:
                self._consumers.remove(queue)
                logger.info(
                    "Consumer unsubscribed",
                    queue_id=id(queue),
                    total_consumers=len(self._consumers),
                )
                # Clear the queue for the departing consumer
                while not queue.empty():
                    with contextlib.suppress(asyncio.QueueEmpty):
                        queue.get_nowait()
            else:
                logger.warning(
                    "Attempted to unsubscribe a non-existent queue",
                    queue_id=id(queue),
                )

    @property
    def is_active(self) -> bool:
        """Returns True if the service is currently running and capturing."""
        return self._is_running

    @property
    def consumer_count(self) -> int:
        """Returns the number of active consumers."""
        return len(self._consumers)
