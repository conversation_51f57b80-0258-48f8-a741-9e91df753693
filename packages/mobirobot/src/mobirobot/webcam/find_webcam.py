from collections.abc import Sequence

import cv2
import pyudev
from pydantic import BaseModel

from mobirobot.common.result_types import <PERSON><PERSON>, Ok, Result
from mobirobot.common.structured_logging import LoggerType, get_logger

logger: LoggerType = get_logger(__name__)


class WebcamDeviceProperties(BaseModel):
    """A webcam device properties."""

    ID_MODEL: str | None = None
    ID_V4L_PRODUCT: str | None = None
    NAME: str | None = None
    device_node: str | None = None


class WebcamDevice(BaseModel):
    """A webcam device."""

    properties: WebcamDeviceProperties


def _device_matches_query(
    device: WebcamDevice, camera_name_query: str
) -> Result[bool, str]:
    """Check if a device matches the camera name query.

    Args:
        device: WebcamDevice object
        camera_name_query: The camera name to search for

    Returns:
        Ok(True) if device matches the query, Err(str) if failed
    """
    try:
        id_model = device.properties.ID_MODEL
        id_v4l_product = device.properties.ID_V4L_PRODUCT
        dev_name = device.properties.NAME
    except (AttributeError, KeyError):
        return Err("Failed to get device properties from pyudev device")

    # Check if query matches any of the device identifiers using functional chaining
    def _check_product(_error: str) -> Result[bool, str]:
        return (
            Ok(camera_name_query in id_v4l_product)
            if id_v4l_product
            else Err("Failed to get device product from pyudev device")
        )

    def _check_name(_error: str) -> Result[bool, str]:
        return (
            Ok(camera_name_query in dev_name)
            if dev_name
            else Err("Failed to get device name from pyudev device")
        )

    # Try model first, then product, then name
    matches_model: Result[bool, str] = (
        Ok(camera_name_query in id_model)
        if id_model
        else Err("Failed to get device model from pyudev device")
    )

    return matches_model.or_else(_check_product).or_else(_check_name)


def _is_video_device(device: WebcamDevice) -> Result[bool, str]:
    """Check if device is a video device.

    Args:
        device: WebcamDevice object

    Returns:
        Ok(True) if device is a video device, Err(str) if failed
    """
    try:
        device_node = device.properties.device_node
        return Ok(device_node is not None and "video" in str(device_node))
    except (AttributeError, TypeError):
        return Err("Failed to get device node path from pyudev device")


def _get_device_node_path(device: WebcamDevice) -> Result[str, str]:
    """Get device node path from device.

    Args:
        device: WebcamDevice object

    Returns:
        Ok(Device node path as string) if successful, Err(str) if failed
    """
    try:
        device_node = device.properties.device_node
        return (
            Ok(str(device_node))
            if device_node is not None
            else Err("Device node path is None")
        )
    except (AttributeError, TypeError):
        return Err("Failed to get device node path from pyudev device")


def _get_device_info(
    device: WebcamDevice,
) -> Result[tuple[str | None, str | None], str]:
    """Get device model and product info for logging.

    Returns:
        Ok(Tuple of (model, product) strings) if successful, Err(str) if failed
    """
    try:
        model = device.properties.ID_MODEL
        product = device.properties.ID_V4L_PRODUCT
        return Ok((model, product))
    except (AttributeError, KeyError, TypeError):
        return Err("Failed to get device model and product info from pyudev device")


def _pyudev_device_to_webcam_device(dev: pyudev.Device) -> WebcamDevice:
    """Convert pyudev.Device to WebcamDevice model.

    Args:
        dev: pyudev.Device object

    Returns:
        WebcamDevice model instance
    """
    properties = WebcamDeviceProperties(
        ID_MODEL=dev.get("ID_MODEL"),  # pyright: ignore[reportUnknownArgumentType, reportUnknownMemberType] # pyudev.Device.get lacks precise return type in stubs
        ID_V4L_PRODUCT=dev.get("ID_V4L_PRODUCT"),  # pyright: ignore[reportUnknownArgumentType, reportUnknownMemberType] # pyudev.Device.get lacks precise return type in stubs
        NAME=dev.get("NAME"),  # pyright: ignore[reportUnknownArgumentType, reportUnknownMemberType] # pyudev.Device.get lacks precise return type in stubs
        device_node=str(dev.device_node) if dev.device_node else None,  # pyright: ignore[reportUnknownArgumentType, reportUnknownMemberType] # pyudev.Device.device_node lacks precise return type in stubs
    )
    return WebcamDevice(properties=properties)


def _enumerate_video_devices() -> Result[list[WebcamDevice], str]:
    """Enumerate all video4linux devices.

    Returns:
        Ok(List of WebcamDevice objects) if successful, Err(str) if failed
    """
    try:
        context = pyudev.Context()
        devices = list(
            context.list_devices(subsystem="video4linux")  # pyright: ignore[reportUnknownMemberType] # pyudev.Context.list_devices lacks precise return type in stubs
        )
    except (OSError, RuntimeError) as e:
        return Err(f"Failed to enumerate video4linux devices: {e}")

    webcam_devices: list[WebcamDevice] = []
    for dev in devices:
        try:
            webcam_device = _pyudev_device_to_webcam_device(dev)
            webcam_devices.append(webcam_device)
        except (AttributeError, KeyError, TypeError, ValueError) as e:
            logger.warning(
                "Error converting device to WebcamDevice", device=dev, error=str(e)
            )
            continue

    return Ok(webcam_devices)


def _filter_matching_devices(
    devices: Sequence[WebcamDevice], camera_name_query: str
) -> Result[list[WebcamDevice], str]:
    """Filter devices that match the camera name query and are video devices.

    Args:
        devices: List of WebcamDevice objects to filter
        camera_name_query: The camera name to search for

    Returns:
        Ok(List of matching WebcamDevice objects) if successful, Err(str) if failed
    """
    viable_devices: list[WebcamDevice] = []

    for device in devices:
        device_matches = _device_matches_query(device, camera_name_query)
        is_video = _is_video_device(device)

        # Use and_then to chain the results functionally without capturing loop variables
        if (
            device_matches.is_ok()
            and is_video.is_ok()
            and device_matches.unwrap()
            and is_video.unwrap()
        ):
            viable_devices.append(device)

    return Ok(viable_devices)


def _try_open_camera_device(device: WebcamDevice) -> Result[cv2.VideoCapture, str]:
    """Try to open a specific camera device.

    Args:
        device: WebcamDevice to try opening

    Returns:
        Ok(VideoCapture) if successful, Err(str) if failed
    """

    def _attempt_open(device_path: str) -> Result[cv2.VideoCapture, str]:
        try:
            cap = cv2.VideoCapture(device_path)
            if cap.isOpened():
                return Ok(cap)
            cap.release()
            return Err(f"Failed to open camera device: {device_path}")
        except (cv2.error, ValueError, OSError) as e:
            return Err(f"cv2.VideoCapture raised an exception for {device_path}: {e}")

    def _log_attempt(device_path: str) -> str:
        device_info_result = _get_device_info(device)
        model, product = device_info_result.unwrap_or((None, None))
        logger.info(
            "Attempting to open camera device",
            device_path=device_path,
            model=model,
            product=product,
        )
        return device_path

    return _get_device_node_path(device).map(_log_attempt).and_then(_attempt_open)


def _find_viable_camera(
    devices: Sequence[WebcamDevice], camera_name_query: str
) -> Result[cv2.VideoCapture, str]:
    """Find and open the first viable camera from a list of devices.

    Args:
        devices: List of WebcamDevice objects to try
        camera_name_query: The camera name query for error messages

    Returns:
        Ok(VideoCapture) if successful, Err(str) if failed
    """
    if not devices:
        return Err(f"No V4L2 devices found matching query: '{camera_name_query}'")

    # Try to open each device until one succeeds
    for device in devices:
        result = _try_open_camera_device(device)
        if result.is_ok():
            device_path = _get_device_node_path(device).unwrap_or("unknown")
            logger.info(
                "Successfully opened camera",
                camera_name_query=camera_name_query,
                device_path=device_path,
            )
            return result

    return Err(
        f"All viable V4L2 devices failed to open for query: '{camera_name_query}'"
    )


def find_camera_by_name(camera_name_query: str) -> Result[cv2.VideoCapture, str]:
    """Find and open a camera by its name using pyudev.

    This is the main public function that should be used by external modules.
    It follows a functional programming approach by chaining operations using
    Result methods.

    Args:
        camera_name_query: The name or part of the name to search for (e.g., "MX_Brio").

    Returns:
        Ok(VideoCapture) if a matching camera is found and opened successfully,
        Err(str) if no matching camera is found or opening fails.
    """
    return (
        _enumerate_video_devices()
        .and_then(lambda devices: _filter_matching_devices(devices, camera_name_query))
        .and_then(
            lambda viable_devices: _find_viable_camera(
                viable_devices, camera_name_query
            )
        )
    )
