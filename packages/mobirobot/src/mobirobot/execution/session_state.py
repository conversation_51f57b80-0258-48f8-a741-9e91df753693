"""Session and robot state management for exercise execution."""

import asyncio
from dataclasses import dataclass, field
from datetime import datetime

from mobirobot.monitoring.models import RunningState
from mobirobot.regiments.regiment import Regiment


@dataclass
class SessionState:
    """Session state container for exercise execution."""

    running_state: RunningState = RunningState.NOT_STARTED
    current_regiment: Regiment | None = None
    current_exercise: str | None = None
    current_behavior_index: int = 0
    is_paused: bool = False
    pause_event: asyncio.Event = field(default_factory=asyncio.Event)
    session_start_time: datetime | None = None

    def __post_init__(self):
        """Initialize pause event to unpaused state."""
        self.pause_event.set()

    def start_session(
        self, regiment: Regiment | None = None, exercise_name: str | None = None
    ) -> None:
        """Start a new session."""
        self.running_state = RunningState.RUNNING
        self.current_regiment = regiment
        self.current_exercise = exercise_name
        self.current_behavior_index = 0
        self.is_paused = False
        self.session_start_time = datetime.now()
        self.pause_event.set()

    def pause_session(self) -> None:
        """Pause the current session."""
        self.is_paused = True
        self.running_state = RunningState.PAUSED
        self.pause_event.clear()

    def resume_session(self) -> None:
        """Resume the paused session."""
        self.is_paused = False
        self.running_state = RunningState.RUNNING
        self.pause_event.set()

    def stop_session(self) -> None:
        """Stop the current session."""
        self.running_state = RunningState.STOPPED
        self.current_regiment = None
        self.current_exercise = None
        self.current_behavior_index = 0
        self.is_paused = False
        self.pause_event.set()

    def complete_session(self) -> None:
        """Mark session as completed."""
        self.running_state = RunningState.COMPLETED
        self.is_paused = False
        self.pause_event.set()


@dataclass
class RobotState:
    """Robot configuration and operational state."""

    stiffness: float = 1.0
    volume: float = 0.5
    follow_me_active: bool = False
    llm_chat_active: bool = False
    last_command_time: datetime | None = None

    def update_stiffness(self, stiffness: float) -> None:
        """Update robot stiffness setting."""
        self.stiffness = stiffness
        self.last_command_time = datetime.now()

    def update_volume(self, volume: float) -> None:
        """Update robot volume setting."""
        self.volume = volume
        self.last_command_time = datetime.now()

    def set_follow_me(self, active: bool) -> None:
        """Set follow-me behavior state."""
        self.follow_me_active = active
        self.last_command_time = datetime.now()

    def set_llm_chat(self, active: bool) -> None:
        """Set LLM chat interaction state."""
        self.llm_chat_active = active
        self.last_command_time = datetime.now()


@dataclass
class ExecutionState:
    """Combined execution state for session management."""

    session: SessionState = field(default_factory=SessionState)
    robot: RobotState = field(default_factory=RobotState)
    error_count: int = 0
    last_error_time: datetime | None = None

    def record_error(self, error: Exception) -> None:
        """Record an execution error."""
        self.error_count += 1
        self.last_error_time = datetime.now()

    def reset_error_count(self) -> None:
        """Reset error tracking."""
        self.error_count = 0
        self.last_error_time = None
