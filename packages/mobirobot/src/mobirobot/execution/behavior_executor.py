"""Behavior execution functions extracted from Orchestrator."""

import asyncio

from mobirobot.common.structured_logging import Lo<PERSON>Type, get_logger
from mobirobot.execution.behaviors.interfaces import Behaviour

from .task_manager import TaskManager

logger: LoggerType = get_logger(__name__)


async def execute_behavior_sequence(
    behaviors: list[Behaviour],
    pause_event: asyncio.Event,
    task_manager: TaskManager,
) -> None:
    """Execute behavior sequence with pause support."""
    if not behaviors:
        logger.warning("No behaviors to execute")
        return

    logger.info("Starting behavior sequence execution", behavior_count=len(behaviors))

    for i, behavior in enumerate(behaviors):
        # Check if shutdown was requested
        if task_manager.is_shutdown:
            logger.info("Shutdown requested, stopping behavior execution")
            break

        logger.info(
            "Executing behavior",
            behavior_index=i,
            behavior_type=type(behavior).__name__,
        )

        await run_single_behavior(behavior, pause_event)

        logger.info(
            "Completed behavior",
            behavior_index=i,
            behavior_type=type(behavior).__name__,
        )

    logger.info("Behavior sequence execution completed")


async def run_single_behavior(behavior: Behaviour, pause_event: asyncio.Event) -> None:
    """Execute a single behavior with pause/resume support."""
    # Wait for pause event to be set (not paused)
    await pause_event.wait()

    # Execute the behavior
    await behavior.run()


async def execute_behaviors_with_monitoring(
    behaviors: list[Behaviour],
    pause_event: asyncio.Event,
    task_manager: TaskManager,
) -> None:
    """Execute behaviors with task management.

    Note: Monitoring should be handled externally through event-driven patterns
    rather than being directly integrated into behavior execution.
    """
    await execute_behavior_sequence(behaviors, pause_event, task_manager)
