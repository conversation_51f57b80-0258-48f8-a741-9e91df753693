"""Main exercise session manager coordinating all execution components."""

from mobirobot.common.structured_logging import LoggerType, get_logger
from mobirobot.execution.behaviors.interfaces import Behaviour
from mobirobot.monitoring.models import RunningState
from mobirobot.regiments.regiment import Regiment

from .behavior_builder import build_regiment_behaviors, build_single_exercise_behaviors
from .behavior_executor import execute_behaviors_with_monitoring
from .robot_control import (
    emergency_stop,
    set_robot_stiffness,
    set_robot_volume,
    start_llm_chat,
    stop_all_activities,
    toggle_follow_me,
)
from .robot_utils import (
    initialize_robot_for_exercise,
    prepare_robot_for_shutdown,
    start_blinking_task,
    stop_blinking_task,
)
from .services import ExecutionServices, setup_robot
from .session_state import ExecutionState

logger: LoggerType = get_logger(__name__)


class ExerciseSessionManager:
    """Main coordinator for exercise execution and robot commands.

    Consolidates functionality from Application, Orchestrator, and ExecutionManager.
    """

    _services: ExecutionServices
    _execution_state: ExecutionState
    _current_behaviors: list[Behaviour]

    def __init__(self, services: ExecutionServices):
        """Initialize session manager with execution services.

        Args:
            services: Container with all required services
        """
        self._services = services
        self._execution_state = ExecutionState()
        self._current_behaviors = []

    async def initialize(self) -> None:
        """Initialize the session manager and set up robot."""
        logger.info("Initializing exercise session manager")

        # Setup robot for exercise operations
        await setup_robot(self._services.robot_controller)

        # Initialize robot for exercise execution
        await initialize_robot_for_exercise(self._services.robot_controller)

        # Start background services
        await self._start_background_services()

        logger.info("Exercise session manager initialization completed")

    async def execute_regiment(self, regiment: Regiment) -> None:
        """Execute a complete exercise regiment.

        Args:
            regiment: The regiment to execute
        """
        try:
            logger.info("Starting regiment execution", regiment_name=regiment.name)

            # Validate regiment
            if not regiment.moves:
                self._raise_empty_regiment_error()

            # Update session state
            self._execution_state.session.start_session(regiment=regiment)

            # Build behaviors for regiment
            self._current_behaviors = build_regiment_behaviors(regiment, self._services)

            # Update global context pause event for all behaviors
            self._update_behavior_pause_events()

            # Execute behaviors
            await execute_behaviors_with_monitoring(
                self._current_behaviors,
                self._execution_state.session.pause_event,
                self._services.task_manager,
            )

            # Mark as completed
            self._execution_state.session.complete_session()

            logger.info("Regiment execution completed", regiment_name=regiment.name)

        except Exception as error:
            logger.exception(
                "Regiment execution failed",
                regiment_name=regiment.name,
                error=str(error),
            )
            self._execution_state.record_error(error)
            self._execution_state.session.stop_session()
            raise

    async def execute_single_exercise(self, exercise_name: str) -> None:
        """Execute a single exercise.

        Args:
            exercise_name: Name of the exercise to execute
        """
        try:
            logger.info(
                "Starting single exercise execution", exercise_name=exercise_name
            )

            # Update session state
            self._execution_state.session.start_session(exercise_name=exercise_name)

            # Build behaviors for single exercise
            self._current_behaviors = build_single_exercise_behaviors(
                exercise_name, self._services
            )

            # Update global context pause event for all behaviors
            self._update_behavior_pause_events()

            # Execute behaviors
            await execute_behaviors_with_monitoring(
                self._current_behaviors,
                self._execution_state.session.pause_event,
                self._services.task_manager,
            )

            # Mark as completed
            self._execution_state.session.complete_session()

            logger.info(
                "Single exercise execution completed", exercise_name=exercise_name
            )

        except Exception as error:
            logger.exception(
                "Single exercise execution failed",
                exercise_name=exercise_name,
                error=str(error),
            )
            self._execution_state.record_error(error)
            self._execution_state.session.stop_session()
            raise

    async def set_stiffness(self, stiffness: float) -> str:
        """Set robot stiffness.

        Args:
            stiffness: Stiffness value between 0.0 and 1.0

        Returns:
            Status message
        """
        return await set_robot_stiffness(
            self._services.robot_controller, self._execution_state.robot, stiffness
        )

    async def set_volume(self, volume: float) -> None:
        """Set robot volume.

        Args:
            volume: Volume value between 0.0 and 1.0
        """
        await set_robot_volume(
            self._services.robot_controller, self._execution_state.robot, volume
        )

    async def toggle_follow_me(self) -> str:
        """Toggle follow-me behavior.

        Returns:
            Status message
        """
        return await toggle_follow_me(
            self._services.robot_controller,
            self._execution_state.robot,
        )

    def start_llm_chat(self) -> None:
        """Start LLM chat interaction."""
        start_llm_chat(
            self._services.robot_controller,
            self._execution_state.robot,
            self._services.task_manager,
        )

    def pause(self) -> None:
        """Pause the current session."""
        logger.info("Pausing session")
        self._execution_state.session.pause_session()

    def resume(self) -> None:
        """Resume the paused session."""
        logger.info("Resuming session")
        self._execution_state.session.resume_session()

    async def emergency_stop(self) -> None:
        """Execute emergency stop."""
        logger.warning("Emergency stop requested")

        # Stop session
        self._execution_state.session.stop_session()

        # Execute emergency stop
        await emergency_stop(
            self._services.robot_controller,
            self._execution_state.robot,
            self._services.task_manager,
        )

    async def stop_all_activities(self, go_limp: bool = False) -> None:
        """Stop all robot activities.

        Args:
            go_limp: Whether to set robot to limp after stopping
        """
        logger.info("Stopping all activities", go_limp=go_limp)

        # Stop session
        self._execution_state.session.stop_session()

        # Stop all robot activities
        await stop_all_activities(
            self._services.robot_controller,
            self._execution_state.robot,
            self._services.task_manager,
            go_limp,
        )

    async def shutdown(self) -> None:
        """Shutdown the session manager gracefully."""
        logger.info("Shutting down session manager")

        # Stop all activities
        await self.stop_all_activities()

        # Stop background services
        await self._stop_background_services()

        # Prepare robot for shutdown
        await prepare_robot_for_shutdown(self._services.robot_controller)

        # Graceful task manager shutdown
        await self._services.task_manager.graceful_shutdown()

        logger.info("Session manager shutdown completed")

    @property
    def running_state(self) -> RunningState:
        """Get current running state."""
        return self._execution_state.session.running_state

    @property
    def is_paused(self) -> bool:
        """Check if session is paused."""
        return self._execution_state.session.is_paused

    @property
    def current_regiment(self) -> Regiment | None:
        """Get current regiment."""
        return self._execution_state.session.current_regiment

    @property
    def current_exercise(self) -> str | None:
        """Get current exercise name."""
        return self._execution_state.session.current_exercise

    # Private methods

    async def _start_background_services(self) -> None:
        """Start background services."""
        # Start blinking task
        start_blinking_task(
            self._services.robot_controller, self._services.task_manager
        )

        # Start monitoring if available
        if self._services.monitoring_service:
            self._services.task_manager.create_background_task(
                self._services.monitoring_service.start(),
                name="monitoring_service",
            )

    async def _stop_background_services(self) -> None:
        """Stop background services."""
        # Stop blinking task
        stop_blinking_task(self._services.task_manager)

        # Stop monitoring
        if self._services.monitoring_service:
            self._services.task_manager.cancel_background_task("monitoring_service")

    def _update_behavior_pause_events(self) -> None:
        """Update pause events in behavior global contexts."""
        for behavior in self._current_behaviors:
            global_context = getattr(behavior, "global_context", None)
            if global_context is not None:
                global_context.pause_event = self._execution_state.session.pause_event

    @staticmethod
    def _raise_empty_regiment_error() -> None:
        """Raise ValueError for empty regiment."""
        msg = "Regiment has no exercises"
        raise ValueError(msg)
