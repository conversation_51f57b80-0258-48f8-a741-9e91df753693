"""Enhanced async task management for exercise execution."""

import asyncio
from collections.abc import Callable, Coroutine
from typing import Any, TypeVar

from mobirobot.common.structured_logging import LoggerType, get_logger

T = TypeVar("T")

logger: LoggerType = get_logger(__name__)


class TaskManager:
    """Async task management with proper cleanup and cancellation."""

    _tasks: set[asyncio.Task[Any]]
    _background_tasks: dict[str, asyncio.Task[Any]]
    _shutdown_event: asyncio.Event

    def __init__(self):
        """Initialize task manager."""
        self._tasks = set()
        self._background_tasks = {}
        self._shutdown_event = asyncio.Event()

    def create_task(
        self,
        coro: Coroutine[None, None, T],
        name: str | None = None,
        callback: Callable[[asyncio.Task[T]], None] | None = None,
    ) -> asyncio.Task[T]:
        """Create a task with automatic cleanup."""
        task = asyncio.create_task(coro, name=name)
        self._tasks.add(task)

        def _cleanup_task(task: asyncio.Task[T]) -> None:
            self._tasks.discard(task)
            if callback:
                callback(task)

        task.add_done_callback(_cleanup_task)
        return task

    def create_background_task(
        self,
        coro: Coroutine[None, None, T],
        name: str,
        callback: Callable[[asyncio.Task[T]], None] | None = None,
    ) -> asyncio.Task[T]:
        """Create a named background task."""
        # Cancel existing background task with same name
        if name in self._background_tasks:
            self.cancel_background_task(name)

        task = self.create_task(coro, name=name, callback=callback)
        self._background_tasks[name] = task

        def _cleanup_background_task(task: asyncio.Task[T]) -> None:
            self._background_tasks.pop(name, None)
            if callback:
                callback(task)

        task.add_done_callback(_cleanup_background_task)
        return task

    def cancel_background_task(self, name: str) -> bool:
        """Cancel a specific background task by name."""
        task = self._background_tasks.get(name)
        if task and not task.done():
            logger.info("Cancelling background task", task_name=name)
            task.cancel()
            return True
        return False

    def cancel_all_tasks(self) -> None:
        """Cancel all managed tasks."""
        logger.info("Cancelling all tasks", task_count=len(self._tasks))

        for task in self._tasks.copy():
            if not task.done():
                task.cancel()

        # Clear background task tracking
        self._background_tasks.clear()

    async def wait_for_completion(self, timeout: float | None = None) -> None:
        """Wait for all tasks to complete."""
        if not self._tasks:
            return

        try:
            await asyncio.wait_for(
                asyncio.gather(*self._tasks, return_exceptions=True), timeout=timeout
            )
        except TimeoutError:
            logger.warning("Task completion timeout", timeout=timeout)
            self.cancel_all_tasks()

    async def graceful_shutdown(self, timeout: float = 10.0) -> None:
        """Gracefully shutdown all tasks."""
        logger.info("Starting graceful shutdown", timeout=timeout)

        # Signal shutdown to any listening tasks
        self._shutdown_event.set()

        # Cancel all tasks
        self.cancel_all_tasks()

        # Wait for completion with timeout
        try:
            await asyncio.wait_for(
                asyncio.gather(*self._tasks, return_exceptions=True), timeout=timeout
            )
            logger.info("Graceful shutdown completed")
        except TimeoutError:
            logger.warning("Graceful shutdown timeout")

    @property
    def is_shutdown(self) -> bool:
        """Check if shutdown has been initiated."""
        return self._shutdown_event.is_set()

    @property
    def active_task_count(self) -> int:
        """Get count of active tasks."""
        return len([t for t in self._tasks if not t.done()])

    @property
    def background_task_names(self) -> list[str]:
        """Get names of active background tasks."""
        return [
            name for name, task in self._background_tasks.items() if not task.done()
        ]
