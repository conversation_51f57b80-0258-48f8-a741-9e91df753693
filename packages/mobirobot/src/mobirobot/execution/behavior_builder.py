"""Behavior building functions extracted from Orchestrator."""

import asyncio

from mobirobot.execution.behaviors.implementations.break_behaviours import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    Break<PERSON>reathBehaviourContext,
    <PERSON>LongBehaviour,
    BreakLongBehaviourContext,
    BreakShortBehaviour,
    BreakShortBehaviourContext,
)
from mobirobot.execution.behaviors.implementations.button_warmup_behaviour import (
    ButtonWarmupBehaviour,
    ButtonWarmupBehaviourContext,
)
from mobirobot.execution.behaviors.implementations.demo_behaviour import (
    DemoBeh<PERSON>our,
    DemoBehaviourContext,
)
from mobirobot.execution.behaviors.implementations.exercise_execution_behaviour import (
    AlternatingExerciseBehaviour,
    AlternatingExerciseBehaviourContext,
    ExerciseBehaviour,
    ExerciseBehaviourContext,
    VariationExerciseBehaviour,
    VariationExerciseBehaviourContext,
)
from mobirobot.execution.behaviors.implementations.intro_behaviour import (
    Intro<PERSON>ehavi<PERSON>,
    IntroBehaviourContext,
)
from mobirobot.execution.behaviors.implementations.outro_behaviour import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>roBehaviourContext,
)
from mobirobot.execution.behaviors.interfaces import <PERSON><PERSON><PERSON>, GlobalBehaviourContext
from mobirobot.models import ExerciseType, Station
from mobirobot.models.exercise import (
    AlternatingExercise,
    AnyExercise,
    VariationExercise,
)
from mobirobot.regiments.regiment import Regiment

from .services import ExecutionServices


def build_regiment_behaviors(
    regiment: Regiment, services: ExecutionServices
) -> list[Behaviour]:
    """Build behavior sequence from regiment."""
    behaviors: list[Behaviour] = []
    global_context = _create_global_context(services)

    # Add button warmup if enabled
    if regiment.button_warmup:
        behaviors.append(_create_button_warmup(global_context, regiment.station))

    # Add introduction
    behaviors.append(
        _create_intro(global_context, regiment, ExerciseType.MOVE, services)
    )

    # Process exercise iterations
    _add_exercise_iterations(behaviors, global_context, regiment, services)

    # Add cooldown exercises
    _add_cooldown_exercises(behaviors, global_context, regiment, services)

    # Add outro
    behaviors.append(_create_outro(global_context, ExerciseType.MOVE, regiment.station))

    return behaviors


def _add_exercise_iterations(
    behaviors: list[Behaviour],
    global_context: GlobalBehaviourContext,
    regiment: Regiment,
    services: ExecutionServices,
) -> None:
    """Add exercise iterations to behavior list."""
    for iteration in range(1, regiment.iterations + 1):
        # Add demos on first iteration
        if iteration == 1:
            _add_demo_behaviors(behaviors, global_context, regiment, services)

        # Add exercises for this iteration
        _add_exercise_behaviors(behaviors, global_context, regiment, services)

        # Add break between iterations (except after last)
        if iteration < regiment.iterations:
            _add_iteration_break(
                behaviors, global_context, regiment, services, iteration
            )


def _add_demo_behaviors(
    behaviors: list[Behaviour],
    global_context: GlobalBehaviourContext,
    regiment: Regiment,
    services: ExecutionServices,
) -> None:
    """Add demo behaviors for all exercises in regiment."""
    for move in regiment.moves or []:
        exercise = services.exercise_service.get_exercise(move)
        if exercise:
            behaviors.append(
                _create_demo_behavior(global_context, exercise, regiment.station)
            )


def _add_exercise_behaviors(
    behaviors: list[Behaviour],
    global_context: GlobalBehaviourContext,
    regiment: Regiment,
    services: ExecutionServices,
) -> None:
    """Add exercise behaviors for all exercises in regiment."""
    for move in regiment.moves or []:
        exercise = services.exercise_service.get_exercise(move)
        if exercise:
            behaviors.append(
                _create_exercise_behavior(global_context, exercise, regiment.station)
            )


def _add_cooldown_exercises(
    behaviors: list[Behaviour],
    global_context: GlobalBehaviourContext,
    regiment: Regiment,
    services: ExecutionServices,
) -> None:
    """Add cooldown exercise behaviors."""
    for cooldown_move in regiment.cooldown_moves or []:
        exercise = services.exercise_service.get_exercise(cooldown_move)
        if exercise:
            behaviors.append(
                _create_exercise_behavior(global_context, exercise, regiment.station)
            )


def _add_iteration_break(
    behaviors: list[Behaviour],
    global_context: GlobalBehaviourContext,
    regiment: Regiment,
    services: ExecutionServices,
    iteration: int,
) -> None:
    """Add break behavior between iterations."""
    # Get next exercise for break context (first exercise of next iteration)
    next_exercise = None
    if regiment.moves:
        next_exercise = services.exercise_service.get_exercise(regiment.moves[0])

    if next_exercise:
        behaviors.append(
            _create_break_behavior(
                global_context,
                "long",
                iteration,
                regiment.long_break_duration,
                regiment.station,
                next_exercise,
            )
        )


def build_single_exercise_behaviors(
    exercise_name: str, services: ExecutionServices
) -> list[Behaviour]:
    """Build behavior sequence for single exercise.

    Args:
        exercise_name: Name of the exercise to build behaviors for
        services: Execution services
        station: Station where the exercise will be performed

    Returns:
        List of behaviors for the single exercise
    """
    behaviors: list[Behaviour] = []

    # Create global context
    global_context = _create_global_context(services)

    # Get exercise
    exercise = services.exercise_service.get_exercise(exercise_name)

    # Add demo and exercise
    behaviors.extend(
        (
            _create_demo_behavior(global_context, exercise, exercise.station_tags[0]),
            _create_exercise_behavior(
                global_context, exercise, exercise.station_tags[0]
            ),
        )
    )

    return behaviors


def _create_global_context(services: ExecutionServices) -> GlobalBehaviourContext:
    """Create global behavior context from services."""
    return GlobalBehaviourContext(
        nao=services.robot_controller,
        speech_service=services.speech_service,
        pause_event=asyncio.Event(),  # This will be replaced with actual session pause_event
        exercise_executer=services.exercise_executor,
        button_service=services.button_service,
        exercise_coordinator=services.pose_coordinator,
        exercise_event_bus=services.exercise_event_bus,
        task_manager=services.task_manager,
    )


def _create_button_warmup(
    context: GlobalBehaviourContext, station: Station
) -> Behaviour:
    """Create button warmup behavior."""
    return ButtonWarmupBehaviour(
        ButtonWarmupBehaviourContext(
            global_context=context,
            station=station,
        )
    )


def _create_intro(
    context: GlobalBehaviourContext,
    regiment: Regiment,
    exercise_type: ExerciseType,
    services: ExecutionServices,
) -> Behaviour:
    """Create introduction behavior."""
    return IntroBehaviour(
        IntroBehaviourContext(
            global_context=context,
            station=regiment.station,
            iteration_count=regiment.iterations,
            exercise_count=len(regiment.moves or []),
            exercise_duration=regiment.exercise_duration,
            break_duration=regiment.short_break_duration,
            exercise=services.exercise_service.get_exercise(regiment.moves[0]),
            exercise_type=exercise_type,
        )
    )


def _create_demo_behavior(
    context: GlobalBehaviourContext, exercise: AnyExercise, station: Station
) -> Behaviour:
    """Create demo behavior for exercise."""
    return DemoBehaviour(
        DemoBehaviourContext(
            global_context=context,
            exercise=exercise,
            station=station,
            bookmark_timeout=30.0,
        )
    )


def _create_exercise_behavior(
    context: GlobalBehaviourContext, exercise: AnyExercise, station: Station
) -> Behaviour:
    """Create exercise execution behavior based on exercise type."""
    if isinstance(exercise, AlternatingExercise):
        return AlternatingExerciseBehaviour(
            AlternatingExerciseBehaviourContext(
                global_context=context,
                station=station,
                exercise_type=ExerciseType.MOVE,
                iteration=1,
                exercise=exercise,
            )
        )
    if isinstance(exercise, VariationExercise):
        return VariationExerciseBehaviour(
            VariationExerciseBehaviourContext(
                global_context=context,
                station=station,
                exercise_type=ExerciseType.MOVE,
                iteration=1,
                exercise=exercise,
            )
        )
    return ExerciseBehaviour(
        ExerciseBehaviourContext(
            global_context=context,
            station=station,
            exercise_type=ExerciseType.MOVE,
            iteration=1,
            exercise=exercise,
        )
    )


def _create_break_behavior(
    context: GlobalBehaviourContext,
    break_type: str,
    iteration: int,
    break_duration: int,
    station: Station,
    next_exercise: AnyExercise,
    prev_exercise: AnyExercise | None = None,
) -> Behaviour:
    """Create break behavior based on type.

    Args:
        context: Global behavior context
        break_type: Type of break ("long", "short", or "breath")
        iteration: Current iteration number
        break_duration: Duration of the break in seconds
        station: Station where the break occurs
        next_exercise: The exercise that follows this break
        prev_exercise: The exercise that preceded this break (optional)

    Returns:
        Configured break behavior
    """
    if break_type == "long":
        return BreakLongBehaviour(
            BreakLongBehaviourContext(
                global_context=context,
                iteration=iteration,
                break_duration=break_duration,
                station=station,
                next_exercise=next_exercise,
                prev_exercise=prev_exercise,
            )
        )
    if break_type == "short":
        return BreakShortBehaviour(
            BreakShortBehaviourContext(
                global_context=context,
                iteration=iteration,
                break_duration=break_duration,
                station=station,
                next_exercise=next_exercise,
                prev_exercise=prev_exercise,
            )
        )
    # breath
    return BreakBreathBehaviour(
        BreakBreathBehaviourContext(
            global_context=context,
            iteration=iteration,
            break_duration=break_duration,
            station=station,
            next_exercise=next_exercise,
            prev_exercise=prev_exercise,
        )
    )


def _create_outro(
    context: GlobalBehaviourContext, exercise_type: ExerciseType, station: Station
) -> Behaviour:
    """Create outro behavior.

    Args:
        context: Global behavior context
        exercise_type: Type of exercise that was performed
        station: Station where the session took place

    Returns:
        Configured outro behavior
    """
    return OutroBehaviour(
        OutroBehaviourContext(
            global_context=context,
            exercise_type=exercise_type,
            station=station,
        )
    )
