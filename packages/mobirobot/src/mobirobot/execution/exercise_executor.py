"""Exercise executor for robot movement and posture control.

Moved from src/exerciser/exercise_executor.py - contains specialized robot movement logic.
"""

import enum

from nao import NaoRobot
from nao.services.robot_posture import RobotPosture

from mobirobot.common.structured_logging import LoggerType, get_logger
from mobirobot.models import Movement, Turntype
from mobirobot.models.exercise import BaseExercise
from mobirobot.models.movement import ExerciseMovementSequence, MovementPhase

logger: LoggerType = get_logger(__name__)


class ExerciseExecuter:
    """Class to execute exercises on the robot."""

    DEFAULT_POSTURE_SPEED: float = 0.8

    def __init__(self, robot_controller: NaoRobot):
        """Initialize the exercise executor with a robot controller."""
        self._robot_controller: NaoRobot = robot_controller

    async def init(
        self,
        init_pose: RobotPosture | None = None,
        prev_exercise: BaseExercise | None = None,
        speed: float = DEFAULT_POSTURE_SPEED,
    ):
        """Initialize the robot's posture for the next exercise.

        Handles transitions including optional rotation based on the previous exercise.

        Args:
            init_pose: The target posture name (e.g., "Stand").
            prev_exercise: The previous exercise object, used to determine if rotation is needed.
            speed: The speed for posture changes (0.0 to 1.0).

        Raises:
            ValueError: If init_pose is not set.
        """
        if not init_pose:
            msg = "init_pose must be set for initialization"
            raise ValueError(msg)

        if prev_exercise is None or prev_exercise.turntype is Turntype.NONE:
            logger.info(
                "No rotation needed, going directly to posture",
                init_pose=init_pose,
                speed=speed,
            )
            await self._robot_controller.posture.go_to_posture(init_pose, speed)
            return

        logger.info(
            "Rotation required, transitioning via Stand posture",
            turntype=prev_exercise.turntype.value,
        )

        logger.debug("Moving to Stand posture", speed=speed)
        await self._robot_controller.posture.go_to_posture("Stand", speed)

        logger.debug("Performing rotation", turntype=prev_exercise.turntype.value)
        await self.rotate(prev_exercise)

        if init_pose != "Stand":
            logger.info(
                "Moving from Stand to final posture",
                init_pose=init_pose,
                speed=speed,
            )
            await self._robot_controller.posture.go_to_posture(init_pose, speed)
        else:
            logger.info("Target posture is Stand, already achieved after rotation")

    async def set_footsteps(
        self,
        bones: list[str],
        footsteps: list[list[float]],
        times: list[float],
        clear_footsteps: bool,
    ):
        """Set the footsteps for the robot.

        Args:
            bones: The bones to set the footsteps for.
            footsteps: The footsteps to set.
            times: The times to set the footsteps for.
            clear_footsteps: Whether to clear the footsteps or not.
        """
        await self._robot_controller.motion.set_footsteps(
            bones,
            footsteps,
            times,
            clear_footsteps,
        )

    async def rotate(self, prev_exercise: BaseExercise):
        """Turn the robot to the initial position.

        Args:
            prev_exercise: The previous exercise to turn to.
        """
        # Send robot to Pose Init
        if prev_exercise.turntype is not Turntype.NONE:
            if prev_exercise.turntype == Turntype.RIGHTTURN:
                footsteps = [[0.1, -0.15, 0.3]]
                await self.set_footsteps(["LLeg"], footsteps, [0.6], False)

                await self._robot_controller.posture.go_to_posture("Stand", 1)

                footsteps = [
                    [0.1, 0.1, 0.0],
                    [0.0, 0.1, 0.0],
                    [0.1, 0.1, 0.0],
                    [0.0, 0.1, 0.0],
                ]
                await self.set_footsteps(
                    ["RLeg", "LLeg", "RLeg", "LLeg"],
                    footsteps,
                    [0.6, 1.2, 1.8, 2.4],
                    False,
                )

            elif prev_exercise.turntype == Turntype.LEFTTURN:
                footsteps = [[0.065, 0.15, 0.3]]

                for _ in range(2):
                    await self.set_footsteps(["RLeg"], footsteps, [0.6], False)
                    await self._robot_controller.posture.go_to_posture("Stand", 1)

                await self._robot_controller.posture.go_to_posture("Stand", 1)

                footsteps: list[list[float]] = [
                    [-0.1, -0.1, 0.0],
                    [0.0, 0.1, 0.0],
                    [-0.1, -0.1, 0.0],
                    [0.0, 0.1, 0.0],
                ]
                await self.set_footsteps(
                    bones=["RLeg", "LLeg", "RLeg", "LLeg"],
                    footsteps=footsteps,
                    times=[0.6, 1.2, 1.8, 2.4],
                    clear_footsteps=False,
                )

            elif prev_exercise.turntype == Turntype.BACKWARDS:
                footsteps = [
                    [-0.1, -0.1, 0.0],
                    [0.0, 0.1, 0.0],
                    [-0.1, -0.1, 0.0],
                    [0.0, 0.1, 0.0],
                ]
                await self.set_footsteps(
                    ["RLeg", "LLeg", "RLeg", "LLeg"],
                    footsteps,
                    [0.6, 1.2, 1.8, 2.4],
                    False,
                )

            elif prev_exercise.turntype == Turntype.FORWARDS:
                footsteps = [
                    [0.1, 0.1, 0.0],
                    [0.0, 0.1, 0.0],
                    [0.1, 0.1, 0.0],
                    [0.0, 0.1, 0.0],
                ]
                await self.set_footsteps(
                    ["RLeg", "LLeg", "RLeg", "LLeg"],
                    footsteps,
                    [0.6, 1.2, 1.8, 2.4],
                    False,
                )

            # Go to rest position
            await self._robot_controller.posture.go_to_posture("Stand", 1)

    class ExerciseExecutionMode(enum.Enum):
        """Enum for the different modes of motion execution."""

        INIT = 1
        EXIT = 2
        DEMO = 3
        NORMAL = 4

    def get_correct_motion(
        self,
        movement_sequence: ExerciseMovementSequence,
        mode: ExerciseExecutionMode,
    ) -> Movement:
        """Gets the correct motion for the given mode.

        Args:
            movement_sequence: The exercise movement sequence.
            mode: The mode to get the correct motion for.

        Returns:
            Movement: The correct movement for the given mode.

        Raises:
            ValueError: If the mode is not valid.
        """
        match mode:
            case self.ExerciseExecutionMode.INIT:
                return (
                    movement_sequence.get_movement_by_phase(MovementPhase.INIT)
                    or movement_sequence.main_movement
                )
            case self.ExerciseExecutionMode.EXIT:
                return (
                    movement_sequence.get_movement_by_phase(MovementPhase.EXIT)
                    or movement_sequence.main_movement
                )
            case self.ExerciseExecutionMode.NORMAL:
                return movement_sequence.main_movement
            case _:
                pass
        msg = f"Invalid mode: {mode}. Must be one of {self.ExerciseExecutionMode.__members__}"
        raise ValueError(msg)

    async def execute_demo(self, demo_motion: Movement):
        """Executes the demo motion.

        Args:
            demo_motion (Movement): The demo motion to execute.
        """
        await self._robot_controller.motion.angle_interpolation_bezier(
            list(demo_motion.joint_names),
            [float(x) for sublist in demo_motion.times for x in sublist],
            [
                float(x)
                for sublist in demo_motion.angles
                for x in sublist
                if isinstance(x, (int, float))
            ],
        )

    async def execute_motion(
        self,
        movement_sequence: ExerciseMovementSequence,
        mode: ExerciseExecutionMode,
    ) -> None:
        """Executes the motion.

        Args:
            movement_sequence: The exercise movement sequence to execute.
            mode: The mode to execute the motion in.
        """
        motion = self.get_correct_motion(movement_sequence, mode)
        await self._robot_controller.motion.angle_interpolation_bezier(
            list(motion.joint_names),
            [float(x) for sublist in motion.times for x in sublist],
            [
                float(x)
                for sublist in motion.angles
                for x in sublist
                if isinstance(x, (int, float))
            ],
        )
