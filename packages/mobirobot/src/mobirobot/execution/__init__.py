"""Exercise execution coordination module.

This module provides the core coordination components for exercise execution,
integrating with all mobirobot services to provide a unified execution interface.
"""

from .behaviors import (
    BaseBehaviour,
    BaseSpecificBehaviourContext,
    Behaviour,
    ExerciseBehaviour,
    GlobalBehaviourContext,
    IntroBehaviour,
    OutroBehaviour,
)
from .exercise_executor import ExerciseExecuter
from .services import ExecutionServices, create_execution_services, setup_robot
from .session_manager import ExerciseSessionManager
from .session_state import ExecutionState, RobotState, SessionState
from .task_manager import TaskManager

__all__ = [
    "BaseBehaviour",
    "BaseSpecificBehaviourContext",
    "Behaviour",
    "ExecutionServices",
    "ExecutionState",
    "ExerciseBehaviour",
    "ExerciseExecuter",
    "ExerciseSessionManager",
    "GlobalBehaviourContext",
    "IntroBehaviour",
    "OutroBehaviour",
    "RobotState",
    "SessionState",
    "TaskManager",
    "create_execution_services",
    "setup_robot",
]
