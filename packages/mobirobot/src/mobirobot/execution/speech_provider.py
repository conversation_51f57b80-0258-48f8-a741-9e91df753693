from dataclasses import dataclass
from typing import override

from nao import NaoRobot

from mobirobot.speech import SpeechProviderProtocol


@dataclass
class SpeechProvider(SpeechProviderProtocol):
    """Speech provider for the robot."""

    robot_controller: <PERSON><PERSON><PERSON><PERSON><PERSON>

    @override
    async def say(self, string_to_say: str):
        await self.robot_controller.tts.say(string_to_say)

    @override
    async def animated_say(
        self, text: str, configuration: dict[str, str | float | int]
    ):
        await self.robot_controller.animated_speech.say(text, configuration)

    @override
    async def set_volume(self, volume: float):
        await self.robot_controller.tts.set_volume(volume)

    @override
    async def set_parameter(self, p_effect_name: str, p_effect_value: float):
        await self.robot_controller.tts.set_parameter(p_effect_name, p_effect_value)

    @override
    async def set_language(self, language: str):
        await self.robot_controller.tts.set_language(language)
