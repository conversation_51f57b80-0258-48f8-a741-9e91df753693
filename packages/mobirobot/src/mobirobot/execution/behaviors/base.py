"""Base behavior class with task management capabilities."""

import asyncio
from abc import ABC, abstractmethod

from mobirobot.common.structured_logging import LoggerType, get_logger

from .interfaces import BaseSpecificBehaviourContext, GlobalBehaviourContext

logger: LoggerType = get_logger(__name__)


class BaseBehaviour[TContext: BaseSpecificBehaviourContext](ABC):
    """Base class for all behaviours with task management capabilities."""

    def __init__(self, context: TContext):
        """Initialize the behaviour.

        Args:
            context: The specific context for the behaviour.
        """
        super().__init__()
        self.context: TContext = context

    @property
    def global_context(self) -> GlobalBehaviourContext:
        """Convenience accessor for the global context."""
        return self.context.global_context

    @abstractmethod
    async def setup(self) -> None:
        """Prepare the behaviour for execution (e.g., load resources, initial setup)."""

    @abstractmethod
    async def execute(self) -> None:
        """Perform the core logic of the behaviour."""

    @abstractmethod
    async def cleanup(self) -> None:
        """Clean up resources after execution or on cancellation."""

    async def run(self) -> None:
        """Manages the setup -> execute -> cleanup lifecycle and pause/resume.

        Raises:
            asyncio.CancelledError: If the behaviour is cancelled.
        """
        try:
            await self.global_context.pause_event.wait()
            await self.setup()
            await self.global_context.pause_event.wait()
            await self.execute()
        except asyncio.CancelledError:
            logger.info("Behaviour cancelled", behaviour_name=self.__class__.__name__)
            raise
        except Exception:
            logger.exception("Behaviour failed", behaviour_name=self.__class__.__name__)
            raise
        finally:
            await self.cleanup()
