import asyncio
import re
from dataclasses import dataclass
from typing import override

from mobirobot.common.structured_logging import LoggerType, get_logger
from mobirobot.execution.behaviors.base import BaseBehaviour
from mobirobot.execution.behaviors.interfaces import BaseSpecificBehaviourContext
from mobirobot.models import Movement
from mobirobot.models.exercise import AnyExercise
from mobirobot.models.station import Station

logger: LoggerType = get_logger(__name__)


def extract_bookmarks(explanation: str) -> list[int]:
    """Extracts the bookmarks from the explanation string.

    Args:
        explanation: The explanation string containing bookmarks.

    Returns:
        A list of integers representing the bookmarks.
    """
    pattern = r"\\mrk=(\d+)\\"
    markers: list[str] = re.findall(pattern, explanation)
    return [int(m) for m in markers]


@dataclass
class DemoBehaviourContext(BaseSpecificBehaviourContext):
    """Specific context for the DemoBehaviour."""

    station: Station
    exercise: AnyExercise
    bookmark_timeout: float = 30.0


class DemoBehaviour(BaseBehaviour[DemoBehaviourContext]):
    """This class performs the demo of an exercise.

    It starts in a standard pose of the exercise and ends in a finished exercise initiation
    """

    def __init__(self, context: DemoBehaviourContext):
        """Initializes the DemoBehaviour class."""
        super().__init__(context)
        self._demo_lock: asyncio.Lock = asyncio.Lock()
        self._demo_progress_tracker: list[int] | None = None
        self._last_bookmark_time: float = 0.0
        self._bookmark_event: asyncio.Event = asyncio.Event()
        self._demo_start: bool = False

    @property
    def next_exercise(self) -> AnyExercise:
        """Convenience accessor for the exercise in the context."""
        return self.context.exercise

    @override
    async def setup(self) -> None:
        """Prepare the behaviour for execution."""
        logger.info("Setting up DemoBehaviour for exercise %s", self.next_exercise.name)
        await self.global_context.nao.autonomous_life.set_autonomous_ability_enabled(
            "BackgroundMovement", False
        )

    @override
    async def execute(self) -> None:
        """Executes the demo behaviour."""
        logger.info("Executing DemoBehaviour for exercise %s", self.next_exercise.name)
        await self.global_context.speech_service.announce_exercise(
            station=self.context.station,
            next_exercise=self.next_exercise,
        )
        await self.explain_exercise()
        logger.info("Completed demo for %s", self.next_exercise.name)

    @override
    async def cleanup(self) -> None:
        """Cleans up resources after execution."""
        logger.info("Cleaning up DemoBehaviour for %s", self.next_exercise.name)

    async def explain_with_bookmarks(self):
        """Explains the exercise with synchronized movements based on TTS bookmarks."""
        self._demo_progress_tracker = extract_bookmarks(self.next_exercise.explanation)
        self._last_bookmark_time = asyncio.get_event_loop().time()
        self._bookmark_event.clear()
        self._demo_start = False

        logger.debug(
            "Starting bookmark explanation. Tracker: %s",
            self._demo_progress_tracker,
        )
        logger.debug(
            "Explanation text with bookmarks: %s",
            self.next_exercise.explanation,
        )

        # Create a separate callback just for debugging
        def debug_bookmark_callback(value: float | str) -> None:
            logger.debug(
                "DEBUG: Raw bookmark event received with value: %s (type: %s)",
                value,
                type(value),
            )

        # Subscribe to both the debug callback and the actual callback
        async with (
            self.global_context.nao.signals.subscribe(
                signal_name="ALTextToSpeech/CurrentBookMark",
                callback=self.bookmark_demo,
            ) as subscription,
            self.global_context.nao.signals.subscribe(
                signal_name="ALTextToSpeech/CurrentBookMark",
                callback=debug_bookmark_callback,
            ) as debug_subscription,
        ):
            logger.debug(
                "Subscribed to ALTextToSpeech/CurrentBookMark: %s (Active: %s)",
                subscription.name,
                subscription.is_active,
            )
            logger.debug(
                "Debug subscription: %s (Active: %s)",
                debug_subscription.name,
                debug_subscription.is_active,
            )

            await self.global_context.speech_service.explain_demo(
                station=self.context.station,
                explanation_text_or_key=self.next_exercise.explanation,
            )

            while self._demo_progress_tracker:
                expected_bookmark = self._demo_progress_tracker[0]
                logger.debug(
                    "Waiting for bookmark %s...",
                    expected_bookmark,
                )
                try:
                    await asyncio.wait_for(
                        self._bookmark_event.wait(),
                        timeout=self.context.bookmark_timeout,
                    )
                    self._bookmark_event.clear()
                except TimeoutError:
                    logger.warning(
                        "Timeout waiting for bookmark event after %s seconds",
                        self.context.bookmark_timeout,
                        expected_bookmark=expected_bookmark,
                        remaining_bookmarks=self._demo_progress_tracker,
                        fallback_action="manual_execution",
                    )
                    break
            logger.debug("Bookmark loop finished.")

        if self._demo_progress_tracker and len(self._demo_progress_tracker) > 0:
            logger.info(
                "Not all bookmarks executed successfully, replaying missing bookmarks: %s",
                self._demo_progress_tracker,
            )
            missing_bookmarks = list(self._demo_progress_tracker)
            for marker in missing_bookmarks:
                logger.debug(
                    "Manually triggering execution for missed bookmark: %s",
                    marker,
                )
                await self.bookmark_demo(marker)

    async def explain_exercise(self):
        """Explains the exercises according to a timed demo."""
        if "\\mrk" in self.next_exercise.explanation:
            await self.explain_with_bookmarks()
            return

        pending: list[asyncio.Task[None]] = []
        # perform a demo if given
        if self.next_exercise and self.next_exercise.demo:
            pending.append(
                asyncio.create_task(
                    self.global_context.exercise_executer.execute_demo(
                        self.next_exercise.demo
                    )
                )
            )

        await self.global_context.speech_service.explain(
            station=self.context.station,
            text_to_explain=self.next_exercise.explanation,
        )

        for task in pending:
            task.cancel()

    async def bookmark_demo(self, value: float | str) -> None:
        """Callback function for ALTextToSpeech/CurrentBookMark signal.

        Handles executing the corresponding part of the demo movement.

        Args:
            value: The bookmark value emitted by TTS (can be int, float, or str).
        """
        # Convert value to int at the beginning
        try:
            bookmark_id = int(value)
        except (ValueError, TypeError):
            logger.warning("Received non-integer bookmark value: %s. Ignoring.", value)
            return

        if not self.next_exercise.demo:
            logger.warning("bookmark_demo called but next_exercise.demo is None.")
            return
        if self._demo_progress_tracker is None:
            logger.warning("bookmark_demo called but _demo_progress_tracker is None.")
            return

        async with self._demo_lock:
            if not self._handle_bookmark_tracking(bookmark_id):
                return

            try:
                await self._execute_demo_segment(bookmark_id)
            except IndexError:
                logger.exception(
                    "IndexError accessing demo data for bookmark %s.",
                    bookmark=bookmark_id,
                )

    def _handle_bookmark_tracking(self, bookmark_id: int) -> bool:
        """Handles the logic for tracking and validating bookmarks.

        Returns:
            bool: True if the bookmark was successfully processed, False otherwise.
        """
        if self._demo_progress_tracker is None:  # Should be checked before calling
            logger.error(
                "_handle_bookmark_tracking called unexpectedly with None tracker."
            )
            return False

        logger.debug("Received Bookmark Int: %s", bookmark_id)

        if not self._demo_progress_tracker:  # Check if tracker is empty
            logger.debug("Tracker already empty, ignoring bookmark.")
            return False

        expected_bookmark = self._demo_progress_tracker[0]
        if bookmark_id != expected_bookmark:
            logger.error(
                "Reached bookmark %s did not match expected %s.",
                bookmark=bookmark_id,
                expected=expected_bookmark,
            )
            # Optionally: Should we attempt to recover or just stop? Stop for now.
            return False

        self._demo_progress_tracker.pop(0)
        self._last_bookmark_time = asyncio.get_event_loop().time()
        self._bookmark_event.set()
        logger.debug(
            "Processed bookmark %s. Remaining: %s",
            bookmark=bookmark_id,
            remaining=self._demo_progress_tracker,
        )
        return True

    async def _execute_demo_segment(self, bookmark_id: int) -> None:
        """Calculates and executes the demo movement segment for a given bookmark."""
        if not self.next_exercise.demo:
            logger.warning(
                "_execute_demo_segment called but next_exercise.demo is None."
            )
            return

        demo_data = self.next_exercise.demo

        if not self._validate_demo_data(demo_data, bookmark_id):
            return

        bookmark_movement = self._prepare_movement_for_bookmark(demo_data, bookmark_id)

        logger.debug("Executing demo segment for bookmark %s", bookmark_id)
        await self.global_context.exercise_executer.execute_demo(bookmark_movement)
        logger.debug("Finished demo segment for bookmark %s", bookmark_id)

    @staticmethod
    def _validate_demo_data(demo_data: Movement, bookmark_id: int) -> bool:
        """Validates the demo data structure and bookmark_id.

        Returns:
            bool: True if data is valid, False otherwise.
        """
        times = demo_data.times
        keys = demo_data.angles

        if not times or not times[0] or not keys:
            logger.error("Invalid or empty demo timing/key data.")
            return False

        num_frames = len(times[0])

        if not (0 <= bookmark_id < num_frames):
            logger.error(
                "Bookmark ID %s out of range (0-%s).",
                bookmark=bookmark_id,
                max_bookmark=num_frames - 1,
            )
            return False

        if not all(len(t) == num_frames for t in times) or not all(
            len(k) == num_frames for k in keys
        ):
            logger.error("Inconsistent lengths in demo times/keys data.")
            return False

        return True

    @staticmethod
    def _prepare_movement_for_bookmark(
        demo_data: Movement, bookmark_id: int
    ) -> Movement:
        """Prepares a Movement object for the given bookmark_id.

        Returns:
            Movement: The prepared movement data.
        """
        names = demo_data.joint_names
        times = demo_data.times
        keys = demo_data.angles

        # Calculate time delta
        prev_bookmark_time_val = times[0][bookmark_id - 1] if bookmark_id > 0 else 0.0
        current_bookmark_time_val = times[0][bookmark_id]
        time_delta = max(0.0, current_bookmark_time_val - prev_bookmark_time_val)

        # Extract keys for the current frame
        current_keys = [
            [keys[joint_idx][bookmark_id]] for joint_idx in range(len(names))
        ]
        segment_times = [[time_delta]] * len(names)

        return Movement(
            joint_names=names,
            times=segment_times,
            angles=current_keys,
        )
