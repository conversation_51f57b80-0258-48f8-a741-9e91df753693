from dataclasses import dataclass
from typing import override

from mobirobot.common.structured_logging import LoggerType, get_logger
from mobirobot.execution.behaviors.base import BaseBehaviour
from mobirobot.execution.behaviors.interfaces import (
    BaseSpecificBehaviourContext,
    Behaviour,
)
from mobirobot.models import Station
from mobirobot.models.common import ExerciseType
from mobirobot.models.exercise import BaseExercise
from mobirobot.speech.models import IntroConfig
from mobirobot.speech.tags import ExerciseTypeValue

"""
This class is for greeting the patient.
Nao ends in a standard init pose of the upcoming exercise.
"""

logger: LoggerType = get_logger(__name__)


@dataclass
class IntroBehaviourContext(BaseSpecificBehaviourContext):
    """Specific context for the IntroBehaviour."""

    station: Station
    iteration_count: int
    exercise_count: int
    exercise_duration: int
    break_duration: int
    exercise: BaseExercise
    exercise_type: ExerciseType

    # Fields specific to IntroBehaviourContext with defaults
    single_mode: bool = False
    next_state: Behaviour | None = None


class IntroBehaviour(BaseBehaviour[IntroBehaviourContext]):
    """Handles the introduction and setup before an exercise or sequence."""

    def __init__(self, context: IntroBehaviourContext):
        """Initializes the IntroBehaviour."""
        super().__init__(context)

    @override
    async def setup(self) -> None:
        """Prepare the behaviour for execution."""
        logger.info(
            "Setting up IntroBehaviour", exercise_name=self.context.exercise.name
        )
        await self.global_context.nao.autonomous_life.set_autonomous_ability_enabled(
            "BackgroundMovement", True
        )

    @override
    async def execute(self) -> None:
        """Runs the introduction sequence."""
        logger.info(
            "Executing IntroBehaviour", exercise_name=self.context.exercise.name
        )

        default_position = (
            "LyingBack" if self.context.station == Station.KC else "Stand"
        )
        logger.debug("Setting default posture", posture=default_position)
        await self.global_context.nao.posture.go_to_posture(default_position, speed=0.8)

        logger.info("Saying intro speech.")
        if not self.context.single_mode:
            intro_config = IntroConfig(
                exercise_type=ExerciseTypeValue[self.context.exercise_type.name],
                exercise_duration=self.context.exercise_duration,
                break_duration=self.context.break_duration,
                iteration_count=self.context.iteration_count,
                exercise_count=self.context.exercise_count,
            )
            await self.global_context.speech_service.say_intro(
                station=self.context.station, intro_config=intro_config
            )
        else:
            await self.global_context.speech_service.say_something(
                station=self.context.station,
                text=f"Okay, lass uns mit der Übung {self.context.exercise.name} beginnen.",
            )

        logger.info(
            "Initializing exercise",
            exercise_name=self.context.exercise.name,
            pose=self.context.exercise.init_pose,
        )

        await self.global_context.exercise_executer.init(
            init_pose=self.context.exercise.init_pose, prev_exercise=None
        )
        await self.global_context.nao.posture.go_to_posture(
            self.context.exercise.init_pose, 1.0
        )

    @override
    async def cleanup(self) -> None:
        """Cleans up resources after execution."""
        logger.info(
            "Cleaning up IntroBehaviour", exercise_name=self.context.exercise.name
        )
        await self.global_context.nao.autonomous_life.set_autonomous_ability_enabled(
            "BackgroundMovement", False
        )
        logger.info("Exiting IntroBehaviour")
