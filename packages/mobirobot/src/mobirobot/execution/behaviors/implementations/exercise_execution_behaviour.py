import asyncio
import datetime
import time
import zoneinfo
from dataclasses import dataclass
from typing import Type<PERSON><PERSON>, cast, override

from mobirobot.common.structured_logging import LoggerType, get_logger
from mobirobot.execution.behaviors.base import BaseBehaviour
from mobirobot.execution.behaviors.interfaces import BaseSpecificBehaviourContext
from mobirobot.execution.exercise_executor import ExerciseExecuter
from mobirobot.models import Station
from mobirobot.models.common import ExerciseType
from mobirobot.models.exercise import (
    AlternatingExercise,
    BaseExercise,
    StandardExercise,
    VariationExercise,
)
from mobirobot.models.movement import ExerciseMovementSequence
from mobirobot.pose.models import ExerciseConfig
from mobirobot.speech import SpeechController
from mobirobot.speech.models import InExerciseConfig
from mobirobot.speech.tags import ExerciseTypeValue

logger: LoggerType = get_logger(__name__)


@dataclass
class BaseExerciseExecutionContext(BaseSpecificBehaviourContext):
    """Base context for exercise execution behaviours."""

    station: Station
    exercise_type: ExerciseType
    iteration: int
    exercise: BaseExercise
    exercise_duration: int = 60


@dataclass
class ExerciseBehaviourContext(BaseExerciseExecutionContext):
    """Context for basic exercise behaviour."""

    # exercise is inherited and will be StandardExercise at runtime for this context


@dataclass
class AlternatingExerciseBehaviourContext(BaseExerciseExecutionContext):
    """Context for alternating exercise behaviour."""

    # exercise is inherited and will be AlternatingExercise at runtime for this context


@dataclass
class VariationExerciseBehaviourContext(BaseExerciseExecutionContext):
    """Context for variation exercise behaviour."""

    # exercise is inherited and will be VariationExercise at runtime for this context


TContext = TypeVar("TContext", bound=BaseExerciseExecutionContext)
# TExercise is bound to BaseExercise, but will be a specific subtype in practice.
# The consuming code (e.g., AlternatingExerciseBehaviour) will cast self.exercise
# to the specific Pydantic model type (e.g., AlternatingExercise) when accessing specific fields.
# This avoids making BaseExerciseExecutionContext itself generic over TExercise, simplifying context creation.
TExercise = TypeVar("TExercise", bound=BaseExercise)


class BaseExerciseExecutionBehaviour[
    TContext: BaseExerciseExecutionContext,
    TExercise: BaseExercise,
](BaseBehaviour[TContext]):
    """Base class for exercise execution behaviour.

    This class is responsible for executing the exercise and managing the exercise state.
    It handles the setup and teardown of the exercise, as well as the execution of the exercise movements.
    It also manages the speech output during the exercise.

    """

    def __init__(self, context: TContext):
        """Initializes the exercise execution behaviour.

        Args:
            context: The specific context for the behaviour.
        """
        super().__init__(context)
        self.times_count: int = 0
        self.times: int = 0
        self.repeat_duration: float = 0.0
        self.time_end: float = 0.0

    @property
    def exercise(self) -> TExercise:
        """Convenience accessor for the exercise in the context.

        Note: This returns the specific exercise type due to TExercise.
        """
        # The context holds BaseExercise, but TExercise ensures this property returns the specific type.
        # The actual instance in self.context.exercise should match TExercise at runtime.
        return cast("TExercise", self.context.exercise)

    @property
    def exercise_name(self) -> str:
        """Convenience accessor for the exercise name in the context."""
        return self.context.exercise.name  # BaseExercise has name

    @property
    def exercise_type(self) -> ExerciseType:
        """Convenience accessor for the exercise type in the context."""
        return self.context.exercise_type  # From BaseExerciseExecutionContext

    @property
    def exercise_duration(self) -> int:
        """Convenience accessor for the exercise duration in the context."""
        return self.context.exercise_duration  # From BaseExerciseExecutionContext

    @property
    def iteration(self) -> int:
        """Convenience accessor for the iteration in the context."""
        return self.context.iteration  # From BaseExerciseExecutionContext

    @property
    def speech_controller(self) -> SpeechController:
        """Convenience accessor for the speech controller in the global context."""
        return self.global_context.speech_service

    @property
    def exercise_executer(self) -> ExerciseExecuter:
        """Convenience accessor for the exercise executer in the global context."""
        return self.global_context.exercise_executer

    @property
    def exercise_coordinator(self):
        """Convenience accessor for the exercise coordinator in the global context."""
        return self.global_context.exercise_coordinator

    @property
    def exercise_event_bus(self):
        """Convenience accessor for the exercise event bus in the global context."""
        return self.global_context.exercise_event_bus

    @override
    async def setup(self) -> None:
        """Prepare the behaviour for execution."""
        logger.info("Setting up exercise execution for %s", self.exercise_name)
        await self.setup_model()
        await self.speech_controller.announce_exercise(
            station=self.context.station,
            next_exercise=self.context.exercise,  # Pass BaseExercise
        )

    async def setup_model(self) -> None:
        """Sets up the pose model for the exercise."""
        exercise = self.context.exercise  # This is BaseExercise
        if exercise.model_processing and self.exercise_coordinator:
            try:
                config = ExerciseConfig.from_exercise(exercise)
                success = await self.exercise_coordinator.start_exercise_tracking(
                    exercise_id=exercise.name, config=config
                )
                if success:
                    logger.info("Started pose tracking for exercise: %s", exercise.name)
                else:
                    logger.warning(
                        "Failed to start pose tracking for exercise: %s", exercise.name
                    )
            except Exception:
                logger.exception(
                    "Error setting up pose tracking for exercise: %s", exercise.name
                )

    @override
    async def execute(self) -> None:
        """Execute the exercise behaviour. Subclasses must implement this."""
        msg = "Subclasses must implement execute method"
        raise NotImplementedError(msg)

    @override
    async def cleanup(self) -> None:
        """Clean up resources after execution."""
        logger.info("Cleaning up exercise execution for %s", self.exercise_name)
        await self.finish_exercise()
        if self.exercise_coordinator:
            await self.exercise_coordinator.stop_exercise_tracking(self.exercise_name)

    async def run_exercise(  # noqa: C901 - Complex due to timing and movement logic
        self,
        movement_sequence: ExerciseMovementSequence,
        duration: int,
        do_init: bool = True,
        do_exit: bool = True,
    ):
        """Runs the exercise for a given duration.

        Args:
            movement_sequence: The exercise movement sequence to perform.
            duration: The duration of the exercise in seconds.
            do_init: Whether to perform the initial movement.
            do_exit: Whether to perform the exit movement.

        Raises:
            asyncio.CancelledError: If the exercise is cancelled.
        """
        if do_init:
            await self.exercise_executer.execute_motion(
                movement_sequence=movement_sequence,
                mode=ExerciseExecuter.ExerciseExecutionMode.INIT,
            )

        self.time_end = time.time() + duration
        max_timing: float = 0.0
        main_movement = movement_sequence.main_movement
        if main_movement and main_movement.times:
            for joint_time_sequence in main_movement.times:
                if joint_time_sequence and joint_time_sequence[-1] > max_timing:
                    max_timing = joint_time_sequence[-1]

        self.repeat_duration = max_timing

        if self.repeat_duration == 0 and duration > 0:
            logger.warning(
                "Repeat duration is 0 for exercise %s, but total duration is %s. Setting repeat_duration to total_duration.",
                self.exercise_name,
                duration,
            )
            self.times = 1
            self.repeat_duration = float(duration)
        elif self.repeat_duration > 0:
            self.times = int(duration / self.repeat_duration)
        else:
            self.times = 0

        logger.info(
            "Running exercise %s; Total duration: %s; Per repeat duration: %s; %s times",
            self.exercise_name,
            duration,
            self.repeat_duration,
            self.times,
        )
        self.times_count = 0

        tasks: list[asyncio.Task[None]] = []
        tasks.append(asyncio.create_task(self.exercise_talking()))

        movement_task = asyncio.create_task(self.perform_movements(movement_sequence))
        try:
            await movement_task
        except asyncio.CancelledError as e:
            # Ensure proper re-raising of CancelledError
            logger.info(
                "Movement task cancelled during run_exercise for %s", self.exercise_name
            )
            raise asyncio.CancelledError from e
        finally:
            for task in tasks:
                if not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        logger.debug("Task %s cancelled successfully.", task.get_name())
                    except Exception:
                        logger.exception(
                            "Error awaiting cancelled task %s",
                            task.get_name(),
                        )

        if do_exit:
            await self.exercise_executer.execute_motion(
                movement_sequence=movement_sequence,
                mode=ExerciseExecuter.ExerciseExecutionMode.EXIT,
            )
        logger.debug(
            "Exercise %s, planned end time: %s, actual end time: %s",
            self.exercise_name,
            datetime.datetime.fromtimestamp(
                self.time_end, tz=zoneinfo.ZoneInfo("UTC")
            ).astimezone(zoneinfo.ZoneInfo("Europe/Berlin")),
            datetime.datetime.fromtimestamp(
                time.time(), tz=zoneinfo.ZoneInfo("UTC")
            ).astimezone(zoneinfo.ZoneInfo("Europe/Berlin")),
        )

    async def perform_movements(self, movement_sequence: ExerciseMovementSequence):
        """Performs the movements of an exercise.

        Args:
            movement_sequence: The exercise movement sequence to perform.
        """
        for _ in range(self.times):
            await self.exercise_executer.execute_motion(
                movement_sequence, mode=ExerciseExecuter.ExerciseExecutionMode.NORMAL
            )
            self.times_count += 1

    async def exercise_talking(self):
        """Performs all talking from Nao during the exercise."""
        await asyncio.sleep(3)  # Initial delay
        try:
            while True:
                remaining_time = max(self.time_end - time.time(), 0)
                remaining_reps = 0
                if (
                    self.repeat_duration > 0
                    and remaining_time % self.repeat_duration > self.repeat_duration - 3
                    and remaining_time > 3  # only if more than 3s remaining in total
                ):
                    remaining_reps = self.times - (self.times_count + 1)
                remaining_time_rounded = round(remaining_time)

                # Get exercise state from new architecture
                exercise_state = None
                if self.exercise_coordinator:
                    exercise_state = self.exercise_coordinator.get_exercise_state(
                        self.exercise_name
                    )

                in_exercise_config = InExerciseConfig(
                    exercise_duration=self.exercise_duration,
                    exercise_type_enum=ExerciseTypeValue[
                        self.exercise_type.name.upper()
                    ],  # Use common ExerciseType
                    remaining_time=remaining_time_rounded,
                    hold_for=int(exercise_state.hold_duration)
                    if exercise_state
                    else None,
                    reps_done=int(exercise_state.reps) if exercise_state else None,
                    remaining_reps=remaining_reps,
                    probability=0.7,
                    interactive=self.exercise_coordinator is not None
                    and self.context.exercise.model_processing is not None,
                )

                await self.speech_controller.in_exercise(
                    station=self.context.station,
                    exercise=self.context.exercise,  # Pass BaseExercise
                    config=in_exercise_config,
                )
                await asyncio.sleep(1)
        except asyncio.CancelledError:
            logger.info("exercise_talking task for %s cancelled.", self.exercise_name)
            # No raise here, allow cleanup
        except Exception:
            logger.exception("Error during exercise_talking for %s", self.exercise_name)
            await asyncio.sleep(
                5
            )  # Wait before retrying or exiting loop if error persists

    @staticmethod
    async def compare_transition(
        prev_movement_seq: ExerciseMovementSequence,
        next_movement_seq: ExerciseMovementSequence,
    ) -> bool:
        """Compares and checks if init and exit movements within exercise sequences are equal.

        Args:
            prev_movement_seq: The previous exercise movement sequence.
            next_movement_seq: The next exercise movement sequence.

        Returns:
            bool: True if the movements are different, False otherwise.
        """
        if not prev_movement_seq or not next_movement_seq:
            return True  # Treat as different if either is empty/None

        # Compare init and exit movements
        prev_init = prev_movement_seq.init_movement
        next_init = next_movement_seq.init_movement
        prev_exit = prev_movement_seq.exit_movement
        next_exit = next_movement_seq.exit_movement

        # If both have init/exit movements, compare them
        # If one has and the other doesn't, they're different
        init_same = (prev_init is None and next_init is None) or (
            prev_init is not None and next_init is not None and prev_init == next_init
        )
        exit_same = (prev_exit is None and next_exit is None) or (
            prev_exit is not None and next_exit is not None and prev_exit == next_exit
        )

        return not (init_same and exit_same)

    async def finish_exercise(self):
        """Finishes the exercise and announces the end."""
        await self.speech_controller.after_exercise(
            station=self.context.station,
            exercise_type_enum=ExerciseTypeValue[
                self.exercise_type.name.upper()
            ],  # Use common ExerciseType
        )


class ExerciseBehaviour(
    BaseExerciseExecutionBehaviour[ExerciseBehaviourContext, StandardExercise]
):
    """Behaviour for executing a basic exercise.

    This class is responsible for executing a basic exercise and managing the exercise state.
    It handles the setup and teardown of the exercise, as well as the execution of the exercise movements.
    It also manages the speech output during the exercise.
    """

    @override
    async def execute(self) -> None:
        """Execute the basic exercise behaviour.

        Raises:
            asyncio.CancelledError: If the behaviour is cancelled.
        """
        logger.info("Performing an Exercise: %s", self.exercise_name)
        specific_exercise = cast("StandardExercise", self.context.exercise)
        try:
            await self.run_exercise(specific_exercise.movements, self.exercise_duration)
        except asyncio.CancelledError:
            logger.info(
                "ExerciseBehaviour %s cancelled during run.", self.exercise_name
            )
            raise
        except Exception:
            logger.exception("Error running exercise %s", self.exercise_name)


class AlternatingExerciseBehaviour(
    BaseExerciseExecutionBehaviour[
        AlternatingExerciseBehaviourContext, AlternatingExercise
    ]
):
    """Behaviour for executing an alternating exercise.

    This class is responsible for executing an alternating exercise and managing the exercise state.
    It handles the setup and teardown of the exercise, as well as the execution of the exercise movements.
    It also manages the speech output during the exercise.
    """

    @override
    async def execute(self) -> None:
        """Execute the alternating exercise behaviour.

        Raises:
            asyncio.CancelledError: If the behaviour is cancelled.
        """
        logger.info("Performing an Alternating Exercise: %s", self.exercise_name)
        try:
            await self.run_alternating_exercise()
        except asyncio.CancelledError:
            logger.info(
                "AlternatingExerciseBehaviour %s cancelled during run.",
                self.exercise_name,
            )
            raise
        except Exception:
            logger.exception(
                "Error running alternating exercise %s", self.exercise_name
            )

    async def run_alternating_exercise(self) -> None:
        """Runs an alternating exercise, switching between left and right movements."""
        specific_exercise = cast("AlternatingExercise", self.context.exercise)
        movements = [
            specific_exercise.left_movements,
            specific_exercise.right_movements,
        ]
        duration_per_side = self.exercise_duration // 2

        for i, movement_sequence in enumerate(movements):
            side = "left" if i == 0 else "right"
            logger.info("Running %s side for %ss.", side, duration_per_side)
            if not movement_sequence:
                logger.warning(
                    "No %s movement defined for %s, skipping.", side, self.exercise_name
                )
                continue
            try:
                do_init = i == 0
                do_exit = i == len(movements) - 1
                # Ensure movements[i-1] is valid before accessing
                prev_movement_seq = movements[i - 1] if i > 0 else None
                if prev_movement_seq and await self.compare_transition(
                    prev_movement_seq, movement_sequence
                ):
                    logger.debug("Transition needed between sides.")
                    do_init = True

                await self.run_exercise(
                    movement_sequence,
                    duration_per_side,
                    do_init=do_init,
                    do_exit=do_exit,
                )
            except asyncio.CancelledError:
                logger.info("Alternating run for %s side cancelled.", side)
                raise  # Re-raise to propagate cancellation
            except Exception:
                logger.exception(
                    "Error running %s side of %s", side, self.exercise_name
                )
                break


class VariationExerciseBehaviour(
    BaseExerciseExecutionBehaviour[VariationExerciseBehaviourContext, VariationExercise]
):
    """Behaviour for executing an exercise with variations."""

    @override
    async def execute(self) -> None:
        """Execute the variation exercise behaviour.

        Raises:
            asyncio.CancelledError: If the behaviour is cancelled.
        """
        logger.info("Performing an Exercise with Variations: %s", self.exercise_name)
        try:
            await self.run_variation_exercise()
        except asyncio.CancelledError:
            logger.info(
                "VariationExerciseBehaviour %s cancelled during run.",
                self.exercise_name,
            )
            raise
        except Exception:
            logger.exception("Error running variation exercise %s", self.exercise_name)

    async def run_variation_exercise(self) -> None:
        """Runs an exercise with variations."""
        specific_exercise = cast("VariationExercise", self.context.exercise)
        if not specific_exercise.variations:
            logger.error("Exercise %s has no variations defined.", self.exercise_name)
            return

        num_variations = len(specific_exercise.variations)
        duration_per_variation = 0
        if num_variations > 0:  # Avoid division by zero
            duration_per_variation = self.exercise_duration // num_variations

        if duration_per_variation == 0 and self.exercise_duration > 0:
            logger.warning(
                "Exercise duration %ss too short for %s variations. Each variation needs at least 1s. Setting to 1s.",
                self.exercise_duration,
                num_variations,
            )
            duration_per_variation = 1

        for i, movement_sequence in enumerate(specific_exercise.variations):
            if (
                duration_per_variation > 0
                and i * duration_per_variation >= self.exercise_duration
            ):
                break

            logger.info(
                "Running variation %s/%s for %ss.",
                i + 1,
                num_variations,
                duration_per_variation,
            )
            if not movement_sequence:
                logger.warning(
                    "Variation %s is not defined for %s, skipping.",
                    i + 1,
                    self.exercise_name,
                )
                continue
            try:
                do_init = i == 0
                do_exit = (i == num_variations - 1) or (
                    duration_per_variation > 0
                    and (i + 1) * duration_per_variation >= self.exercise_duration
                )
                # Ensure specific_exercise.variations[i-1] is valid
                prev_movement_seq = (
                    specific_exercise.variations[i - 1] if i > 0 else None
                )
                if prev_movement_seq and await self.compare_transition(
                    prev_movement_seq, movement_sequence
                ):
                    logger.debug("Transition needed between variations.")
                    do_init = True

                await self.run_exercise(
                    movement_sequence,
                    duration_per_variation,
                    do_init=do_init,
                    do_exit=do_exit,
                )
            except asyncio.CancelledError:
                logger.info("Variation run for variation %s cancelled.", i + 1)
                raise  # Re-raise to propagate cancellation
            except Exception:
                logger.exception(
                    "Error running variation %s of %s", i + 1, self.exercise_name
                )
                break
