from dataclasses import dataclass
from typing import override

from mobirobot.common.structured_logging import LoggerType, get_logger
from mobirobot.execution.behaviors.base import BaseBehaviour
from mobirobot.execution.behaviors.interfaces import (
    BaseSpecificBehaviourContext,
    Behaviour,
)
from mobirobot.models import Station
from mobirobot.models.common import ExerciseType
from mobirobot.speech.tags import ExerciseTypeValue

"""
This class is for ending the exercise program.
Nao ends in the exit pose of the last exercise.
"""

logger: LoggerType = get_logger(__name__)


@dataclass
class OutroBehaviourContext(BaseSpecificBehaviourContext):
    """Specific context for the OutroBehaviour."""

    exercise_type: ExerciseType
    station: Station
    next_state: Behaviour | None = None


class OutroBehaviour(BaseBehaviour[OutroBehaviourContext]):
    """Handles the final actions and speech at the end of the session."""

    def __init__(self, context: OutroBehaviourContext):
        """Initializes the OutroBehaviour."""
        super().__init__(context)

    @override
    async def setup(self) -> None:
        """Prepare the behaviour for execution."""
        logger.info("Setting up OutroBehaviour.")
        await self.global_context.nao.autonomous_life.set_autonomous_ability_enabled(
            "BackgroundMovement", True
        )

    @override
    async def execute(self) -> None:
        """Executes the outro sequence."""
        default_position = (
            "LyingBack" if self.context.station == Station.KC else "Stand"
        )
        logger.debug("Setting final posture", posture=default_position)
        await self.global_context.nao.posture.go_to_posture(default_position, speed=0.8)

        await self.global_context.speech_service.outro(
            station=self.context.station,
            exercise_type_enum=ExerciseTypeValue[self.context.exercise_type.name],
        )

    @override
    async def cleanup(self) -> None:
        """Cleans up resources after execution."""
        logger.info("Exiting OutroBehaviour. Session finished.")
