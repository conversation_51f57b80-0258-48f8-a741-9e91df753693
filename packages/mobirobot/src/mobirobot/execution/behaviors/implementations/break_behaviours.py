import asyncio
from dataclasses import dataclass
from typing import override

from mobirobot.common.structured_logging import LoggerType, get_logger
from mobirobot.execution.behaviors.base import BaseBehaviour
from mobirobot.execution.behaviors.interfaces import BaseSpecificBehaviourContext
from mobirobot.models import Station
from mobirobot.models.exercise import BaseExercise

logger: LoggerType = get_logger(__name__)


@dataclass
class BaseBreakBehaviourContext(BaseSpecificBehaviourContext):
    """Base context for break behaviours."""

    iteration: int
    break_duration: int
    station: Station
    next_exercise: BaseExercise
    prev_exercise: BaseExercise | None = None


@dataclass
class BreakLongBehaviourContext(BaseBreakBehaviourContext):
    """Context for long break behaviour."""


@dataclass
class BreakShortBehaviourContext(BaseBreakBehaviourContext):
    """Context for short break behaviour."""


@dataclass
class BreakBreathBehaviourContext(BaseBreakBehaviourContext):
    """Context for breathing break behaviour."""


class BaseBreakBehaviour[TContext: BaseBreakBehaviourContext](BaseBehaviour[TContext]):
    """Base class for different break behaviours."""

    @override
    async def setup(self) -> None:
        """Prepare the behaviour for execution."""
        logger.info("Setting up", behaviour_name=self.__class__.__name__)
        await self.global_context.nao.autonomous_life.set_autonomous_ability_enabled(
            "BackgroundMovement", True
        )

    @override
    async def execute(self) -> None:
        """Execute the break behaviour. Subclasses must implement this."""
        error_message = "Subclasses must implement execute method"
        raise NotImplementedError(error_message)

    @override
    async def cleanup(self) -> None:
        """Clean up resources after execution."""
        logger.info("Cleaning up", behaviour_name=self.__class__.__name__)

    async def init_exercise(self) -> None:
        """Handles pose change to the standard init pose of the next exercise."""
        await self.global_context.exercise_executer.init(
            init_pose=self.context.next_exercise.init_pose,
            prev_exercise=self.context.prev_exercise,
        )

    async def break_talking(self) -> None:
        """Handles the robot talking randomly during the break.

        Raises:
            asyncio.CancelledError: If the task is cancelled externally.
        """
        if self.context.break_duration <= 0:
            return
        try:
            while True:
                await self.global_context.speech_service.in_break(
                    station=self.context.station, probability=0.9
                )
                await asyncio.sleep(2)
        except asyncio.CancelledError:
            logger.debug("Break talking task cancelled.")
            raise


class BreakLongBehaviour(BaseBreakBehaviour[BreakLongBehaviourContext]):
    """Handles long breaks between exercise sets."""

    @override
    async def execute(self) -> None:
        """Execute the long break behaviour.

        Raises:
            asyncio.CancelledError: If the behaviour is cancelled.
        """
        logger.info(
            "Executing BreakLongBehaviour", behaviour_name=self.__class__.__name__
        )
        if self.context.break_duration > 0:
            await self.global_context.speech_service.long_break(
                station=self.context.station,
                iteration=self.context.iteration,
            )

        await self.global_context.speech_service.announce_exercise(
            station=self.context.station,
            next_exercise=self.context.next_exercise,
        )
        await self.init_exercise()

        if self.context.break_duration > 0:
            break_task = self.global_context.task_manager.create_task(
                self.break_talking(), name="break_talking"
            )
            try:
                await asyncio.wait_for(break_task, timeout=self.context.break_duration)
            except TimeoutError:
                logger.debug("Break duration finished.")
            except asyncio.CancelledError:
                logger.debug(
                    "BreakLongBehaviour execute cancelled during break_talking."
                )
                raise


class BreakShortBehaviour(BaseBreakBehaviour[BreakShortBehaviourContext]):
    """Handles short breaks between exercise repetitions or variations."""

    @override
    async def execute(self) -> None:
        """Execute the short break behaviour.

        Raises:
            asyncio.CancelledError: If the behaviour is cancelled.
        """
        logger.info(
            "Executing BreakShortBehaviour", behaviour_name=self.__class__.__name__
        )
        if self.context.break_duration > 0:
            await self.global_context.speech_service.short_break(
                station=self.context.station,
                break_duration=self.context.break_duration,
                next_exercise=self.context.next_exercise,
            )

        await self.init_exercise()

        if self.context.break_duration > 0:
            break_task = self.global_context.task_manager.create_task(
                self.break_talking(), name="break_talking"
            )
            try:
                await asyncio.wait_for(break_task, timeout=self.context.break_duration)
            except TimeoutError:
                logger.debug("Break duration finished.")
            except asyncio.CancelledError:
                logger.debug(
                    "BreakShortBehaviour execute cancelled during break_talking."
                )
                raise


class BreakBreathBehaviour(BaseBreakBehaviour[BreakBreathBehaviourContext]):
    """Handles a breathing exercise break."""

    @override
    async def execute(self) -> None:
        """Execute the breathing break behaviour."""
        logger.info(
            "Executing BreakBreathBehaviour", behaviour_name=self.__class__.__name__
        )
        await self.global_context.speech_service.kc_break(
            station=self.context.station,
        )

        try:
            audio_player = self.global_context.nao.audio_player
            await audio_player.play_sound_set_file("enu_ono_inspiration")
            await audio_player.play_sound_set_file("frf_ono_expi")

        except (RuntimeError, TypeError, AttributeError) as e:
            logger.warning(
                "Failed to play breathing sounds via ALAudioPlayer",
                error=str(e),
                error_type=type(e).__name__,
                exc_info=True,
            )

        await self.init_exercise()
