"""ButtonWarmup is responsible for the button warmup exercise."""

import asyncio
import random
from dataclasses import dataclass
from typing import TYPE_CHECKING, cast, override

from buttons import ButtonColor, ButtonPressWaiter

from mobirobot.common.structured_logging import LoggerType, get_logger
from mobirobot.execution.behaviors.base import <PERSON><PERSON><PERSON><PERSON>our
from mobirobot.execution.behaviors.interfaces import BaseSpecificBehaviourContext
from mobirobot.models.station import Station

if TYPE_CHECKING:
    from buttons.button_service import ButtonState

logger: LoggerType = get_logger(__name__)


@dataclass
class ButtonWarmupBehaviourContext(BaseSpecificBehaviourContext):
    """Specific context for the ButtonWarmupBehaviour."""

    station: Station
    num_rounds: int = 5
    timeout_per_round: float = 5.0


class ButtonWarmupBehaviour(BaseBehaviour[ButtonWarmupBehaviourContext]):
    """ButtonWarmup is responsible for the button warmup exercise.

    This behavior implements a simple button-pressing warmup exercise where
    the user needs to press buttons in response to audio cues.
    """

    def __init__(self, context: ButtonWarmupBehaviourContext):
        """Initializes the ButtonWarmupBehaviour.

        Args:
            context: The specific context for the behavior.
        """
        super().__init__(context)

    @override
    async def setup(self) -> None:
        """Prepare the behavior for execution."""
        logger.info("Setting up", behaviour_name=self.__class__.__name__)
        await self.global_context.speech_service.say_something(
            station=self.context.station,
            text="Hallo ich bin NAO, ich freue mich, dass wir heute gemeinsam Sport machen. Bevor es mit dem eigentlichen Training losgeht, müssen wir uns zunächst ein wenig aufwärmen.",
        )

    @override
    async def execute(self) -> None:
        """Execute the button warmup behavior."""
        logger.info("Executing", behaviour_name=self.__class__.__name__)
        try:
            await self.button_warmup(
                num_rounds=self.context.num_rounds,
                timeout_per_round=self.context.timeout_per_round,
            )
            await self.global_context.speech_service.say_something(
                station=self.context.station, text="Aufwärmen beendet. Gut gemacht!"
            )
            logger.info("Button warmup finished successfully.")
        except TimeoutError:
            logger.warning("Button warmup timed out globally.")
            await self.global_context.speech_service.say_something(
                station=self.context.station,
                text="Das Aufwärmen hat zu lange gedauert.",
            )

    @override
    async def cleanup(self) -> None:
        """Clean up resources after execution."""
        logger.info("Cleaning up", behaviour_name=self.__class__.__name__)

    async def button_warmup(self, num_rounds: int, timeout_per_round: float):
        """Runs multiple rounds of the button warmup exercise.

        Args:
            num_rounds: The number of rounds to play.
            timeout_per_round: The time in seconds the user has for each round.
        """
        if num_rounds <= 0:
            logger.warning("Button warmup called with zero or negative rounds.")
            return

        correct_rounds = 0
        for _ in range(num_rounds):
            logger.info(
                "Playing warmup round", round_number=_ + 1, total_rounds=num_rounds
            )
            result = await self.play_round(timeout_per_round)
            if result:
                correct_rounds += 1
                await self.global_context.speech_service.say_something(
                    station=self.context.station, text="Gut gemacht!"
                )
            await asyncio.sleep(1.0)

        logger.info(
            "Warmup complete",
            correct_rounds=correct_rounds,
            total_rounds=num_rounds,
        )

    async def play_round(self, timeout: float) -> bool:
        """Plays a single round of the button warmup exercise.

        Args:
            timeout: The time in seconds the user has to press the button after the sound.

        Returns:
            bool: True if the user pressed the correct button in time, False otherwise.

        Raises:
            asyncio.CancelledError: If the operation is cancelled.
        """
        if not self.global_context.button_service:
            logger.error("Button service is not available")
            return False

        available_colors: list[ButtonColor] = list(ButtonColor)
        if not available_colors:
            logger.error("No button colors found for warmup round.")
            return False

        target_color: ButtonColor = random.choice(available_colors)
        target_button: ButtonState | None = (
            self.global_context.button_service.buttons.get(target_color)
        )

        if not target_button:
            logger.error("Target button state for color %s not found.", target_color)
            return False

        # Play sound cue
        await target_button.hardware.play_sound(filename="bell")
        logger.debug(
            "Target color",
            target_color=target_color.name,
            timeout=timeout,
        )

        pressed_button_color: ButtonColor | None = None
        try:
            async with ButtonPressWaiter(
                self.global_context.button_service,
                target_colors=None,
            ) as waiter:
                pressed_button_color, reaction_time = await asyncio.wait_for(
                    waiter.wait(), timeout=timeout
                )

        except TimeoutError:
            logger.info(
                "Timeout occurred, no button pressed for target",
                target_color=target_color.name,
            )
            await self.global_context.speech_service.say_something(
                station=self.context.station,
                text="Das Aufwärmen hat zu lange gedauert.",
            )
            await target_button.hardware.play_sound("wrong")
            return False
        except asyncio.CancelledError:
            logger.info("play_round cancelled.")
            raise
        else:
            if pressed_button_color == target_color:
                logger.info(
                    "Correct button pressed",
                    target_color=target_color.name,
                    reaction_time=reaction_time,
                )
                await target_button.hardware.play_sound(filename="success")
                return True

            logger.info(
                "Incorrect button pressed",
                pressed_button_color=pressed_button_color.name
                if pressed_button_color
                else "None",
                target_color=target_color.name,
            )
            wrong_button = self.global_context.button_service.buttons.get(
                cast("ButtonColor", pressed_button_color)
            )
            if wrong_button:
                await wrong_button.hardware.play_sound(filename="wrong")
            await self.global_context.speech_service.say_something(
                station=self.context.station,
                text="Das Aufwärmen hat zu lange gedauert.",
            )
            return False
