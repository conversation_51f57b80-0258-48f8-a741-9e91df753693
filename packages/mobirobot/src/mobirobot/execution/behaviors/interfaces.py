"""Behavior interfaces and context classes."""

import asyncio
from dataclasses import dataclass
from typing import Protocol, runtime_checkable

from buttons import ButtonService
from nao import <PERSON>oR<PERSON><PERSON>

from mobirobot.execution.exercise_executor import ExerciseExecuter
from mobirobot.execution.task_manager import TaskManager
from mobirobot.models.exercise import BaseExercise
from mobirobot.pose.events import ExerciseEventBus
from mobirobot.pose.exercise_tracker import ExerciseCoordinator
from mobirobot.speech import SpeechService


@runtime_checkable
class Behaviour(Protocol):
    """Defines the interface for a state machine behaviour."""

    async def run(self) -> None:
        """Manages the setup -> execute -> cleanup lifecycle and pause/resume."""
        ...

    async def setup(self) -> None:
        """Prepare the behaviour for execution (e.g., load resources, initial setup)."""
        ...

    async def execute(self) -> None:
        """Perform the core logic of the behaviour."""
        ...


@runtime_checkable
class ExerciseBehaviour(Behaviour, Protocol):
    """A specific type of behaviour related to an exercise."""

    @property
    def exercise(self) -> BaseExercise:
        """The exercise associated with this behaviour."""
        ...


@dataclass
class GlobalBehaviourContext:
    """Context for a behaviour."""

    nao: NaoRobot
    speech_service: SpeechService
    pause_event: asyncio.Event
    exercise_executer: ExerciseExecuter
    button_service: ButtonService | None
    task_manager: TaskManager
    # New pose detection architecture
    exercise_coordinator: ExerciseCoordinator | None = None
    exercise_event_bus: ExerciseEventBus | None = None

    def __post_init__(self):
        self.pause_event.set()


@dataclass
class BaseSpecificBehaviourContext:
    """Base class for specific behaviour contexts, ensuring global context is present."""

    global_context: GlobalBehaviourContext
