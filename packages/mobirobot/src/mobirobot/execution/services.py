"""Service coordination for exercise execution with mobirobot integration."""

from dataclasses import dataclass

from buttons import ButtonService
from nao import NaoRobot

from mobirobot.common.config import settings
from mobirobot.execution.exercise_executor import ExerciseExecuter
from mobirobot.execution.speech_provider import Speech<PERSON><PERSON>ider
from mobirobot.exercises.exercise_service import ExerciseService
from mobirobot.monitoring.service import MonitoringService
from mobirobot.pose.events import ExerciseEventBus
from mobirobot.pose.exercise_tracker import ExerciseCoordinator
from mobirobot.pose.pose_detector import PoseDetector
from mobirobot.speech.service import SpeechService
from mobirobot.stream.stream_service import StreamService
from mobirobot.webcam.webcam_service import WebcamService

from .task_manager import TaskManager


@dataclass(frozen=True)
class ExecutionServices:
    """Container for all execution-related services, integrating with mobirobot architecture."""

    # Core robot and exercise services
    robot_controller: NaoRobot
    speech_service: SpeechService
    exercise_service: ExerciseService
    exercise_executor: ExerciseExecuter
    task_manager: <PERSON><PERSON>anager

    # Integrated mobirobot services
    monitoring_service: MonitoringService | None = None
    stream_service: StreamService | None = None
    pose_detector: PoseDetector | None = None
    pose_coordinator: ExerciseCoordinator | None = None
    button_service: ButtonService | None = None
    exercise_event_bus: ExerciseEventBus | None = None


async def setup_robot(robot: NaoRobot) -> None:
    """Configure robot for exercise operations (extracted from Application.setup_robot)."""
    await robot.autonomous_life.set_state("disabled")
    configs: dict[str, bool] = {
        "AutonomousBlinking": True,
        "BackgroundMovement": True,
        "BasicAwareness": False,
        "ListeningMovement": False,
        "SpeakingMovement": False,
    }
    for config, value in configs.items():
        await robot.autonomous_life.set_autonomous_ability_enabled(config, value)
    await robot.tts.set_language("German")


async def create_execution_services(
    robot: NaoRobot,
    exercise_service: ExerciseService,
    button_service: ButtonService | None = None,
    enable_pose_detection: bool = False,
    enable_monitoring: bool = True,
    enable_streaming: bool = False,
) -> ExecutionServices:
    """Create and initialize all services required for exercise execution.

    Args:
        robot: NAO robot controller
        exercise_service: Exercise service for exercise definitions
        button_service: Optional button service for interaction
        enable_pose_detection: Enable pose detection and exercise tracking
        enable_monitoring: Enable robot monitoring service
        enable_streaming: Enable video streaming service

    Returns:
        Configured ExecutionServices container
    """
    # Create core services
    task_manager = TaskManager()

    # Create speech service
    speech_service = SpeechService(SpeechProvider(robot))

    # Create exercise executor
    exercise_executor = ExerciseExecuter(robot)

    # Initialize optional services
    monitoring_service = None
    if enable_monitoring:
        monitoring_service = await MonitoringService.create(
            robot=robot, button_service=button_service
        )

    # Initialize pose detection if requested
    pose_detector = None
    pose_coordinator = None
    exercise_event_bus = None

    if enable_pose_detection:
        # Create webcam service for pose detection
        webcam_service = WebcamService(settings=settings.stream_settings)
        pose_detector = PoseDetector.create(webcam_service)
        exercise_event_bus = ExerciseEventBus()
        pose_coordinator = ExerciseCoordinator(pose_detector, exercise_event_bus)

    # Initialize streaming service if requested
    stream_service = None
    if enable_streaming and enable_pose_detection:
        # Stream service requires webcam service from pose detection
        webcam_service = WebcamService(settings=settings.stream_settings)
        stream_service = StreamService(webcam_service)

    return ExecutionServices(
        robot_controller=robot,
        speech_service=speech_service,
        exercise_service=exercise_service,
        exercise_executor=exercise_executor,
        task_manager=task_manager,
        monitoring_service=monitoring_service,
        stream_service=stream_service,
        pose_detector=pose_detector,
        button_service=button_service,
        pose_coordinator=pose_coordinator,
        exercise_event_bus=exercise_event_bus,
    )
