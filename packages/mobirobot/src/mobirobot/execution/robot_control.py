"""Robot control functions extracted from ExecutionManager."""

from nao import <PERSON>o<PERSON><PERSON><PERSON>

from mobirobot.common.structured_logging import LoggerType, get_logger
from mobirobot.llm_interaction.chat import NaoChat
from mobirobot.llm_interaction.nao_audio import AudioFormat, NAOAudioSource

from .session_state import RobotState
from .task_manager import TaskManager

logger: LoggerType = get_logger(__name__)


async def set_robot_stiffness(
    robot: NaoRobot, robot_state: RobotState, stiffness: float
) -> str:
    """Set robot stiffness and update state (extracted from ExecutionManager)."""
    logger.info("Setting robot stiffness", stiffness=stiffness)

    # Set stiffness on robot
    await robot.motion.set_stiffnesses("Body", stiffness)

    # Update state
    robot_state.update_stiffness(stiffness)

    return f"Robot stiffness set to {int(stiffness * 100)}%"


async def set_robot_volume(
    robot: NaoRobot, robot_state: RobotState, volume: float
) -> None:
    """Set robot volume and update state (extracted from ExecutionManager)."""
    logger.info("Setting robot volume", volume=volume)

    # Set volume on robot
    await robot.tts.set_volume(volume)

    # Update state
    robot_state.update_volume(volume)


async def toggle_follow_me(robot: NaoRobot, robot_state: RobotState) -> str:
    """Toggle follow-me behavior (extracted from ExecutionManager)."""
    if robot_state.follow_me_active:
        logger.info("Stopping follow-me behavior")
        await robot.behavior_manager.stop_behavior("FollowMe")
        robot_state.set_follow_me(False)
        return "Follow-me disabled"

    logger.info("Starting follow-me behavior")

    await robot.motion.set_stiffnesses("Body", 1.0)
    robot_state.update_stiffness(1.0)

    await robot.behavior_manager.start_behavior("FollowMe")

    robot_state.set_follow_me(True)
    return "Follow-me enabled"


def start_llm_chat(
    robot: NaoRobot, robot_state: RobotState, task_manager: TaskManager
) -> None:
    """Start LLM chat interaction (extracted from ExecutionManager)."""
    if robot_state.llm_chat_active:
        logger.warning("LLM chat is already active")
        return

    logger.info("Starting LLM chat interaction")

    # Create NAO audio source
    audio_source = NAOAudioSource(
        robot, format=AudioFormat(sample_rate=16000, channels=1)
    )

    # Create and start NaoChat
    nao_chat = NaoChat(audio_source, say=robot.tts.say)

    # Start LLM chat as background task
    task_manager.create_background_task(nao_chat.listen_and_respond(), name="llm_chat")

    robot_state.set_llm_chat(True)


async def stop_all_activities(
    robot: NaoRobot,
    robot_state: RobotState,
    task_manager: TaskManager,
    go_limp: bool = False,
) -> None:
    """Stop all robot activities and reset state (extracted from ExecutionManager)."""
    try:
        logger.info("Stopping all robot activities", go_limp=go_limp)

        # Cancel all background tasks
        task_manager.cancel_all_tasks()

        # Reset robot state
        robot_state.set_follow_me(False)
        robot_state.set_llm_chat(False)

        # Set limp if requested
        if go_limp:
            await robot.motion.set_stiffnesses("Body", 0.0)
            robot_state.update_stiffness(0.0)

        logger.info("All robot activities stopped")

    except Exception as error:
        logger.exception("Failed to stop robot activities", error=str(error))
        raise


async def emergency_stop(
    robot: NaoRobot, robot_state: RobotState, task_manager: TaskManager
) -> None:
    """Execute emergency stop with immediate effect."""
    logger.warning("Emergency stop initiated")

    try:
        # Immediate task cancellation
        task_manager.cancel_all_tasks()

        # Set robot to limp
        await robot.motion.set_stiffnesses("Body", 0.0)

        # Reset all state
        robot_state.set_follow_me(False)
        robot_state.set_llm_chat(False)
        robot_state.update_stiffness(0.0)

        logger.warning("Emergency stop completed")

    except Exception as error:
        logger.exception("Error during emergency stop", error=str(error))
        # Don't re-raise - emergency stop should always succeed
