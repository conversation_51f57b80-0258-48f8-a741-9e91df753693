"""Utility functions for robot operations."""

import asyncio
import contextlib

from nao import <PERSON>oR<PERSON>ot

from mobirobot.common.structured_logging import LoggerType, get_logger
from mobirobot.pose.events import ExerciseEventBus
from mobirobot.pose.exercise_tracker import ExerciseCoordinator
from mobirobot.pose.pose_detector import PoseDetector
from mobirobot.webcam.webcam_service import WebcamService

from .task_manager import TaskManager

logger: LoggerType = get_logger(__name__)


def start_blinking_task(robot: <PERSON>oR<PERSON><PERSON>, task_manager: TaskManager) -> None:
    """Start autonomous blinking task."""
    logger.info("Starting blinking task")

    # Start blinking as background task
    task_manager.create_background_task(
        _run_blinking_behavior(robot), name="autonomous_blinking"
    )


def stop_blinking_task(task_manager: TaskManager) -> None:
    """Stop autonomous blinking task."""
    logger.info("Stopping blinking task")
    success = task_manager.cancel_background_task("autonomous_blinking")
    if not success:
        logger.warning("Blinking task was not running")


def setup_pose_detection(
    webcam_service: WebcamService,
) -> tuple[ExerciseCoordinator, ExerciseEventBus]:
    """Setup pose detection services (extracted from <PERSON><PERSON>)."""
    logger.info("Setting up pose detection")

    # Create exercise event bus
    exercise_event_bus = ExerciseEventBus()

    # Create pose detector
    pose_detector = PoseDetector.create(webcam_service)

    # Create exercise coordinator
    exercise_coordinator = ExerciseCoordinator(pose_detector, exercise_event_bus)

    logger.info("Pose detection setup completed")
    return exercise_coordinator, exercise_event_bus


async def initialize_robot_for_exercise(robot: NaoRobot) -> None:
    """Initialize robot for exercise execution with proper settings."""
    try:
        logger.info("Initializing robot for exercise execution")

        # Set robot to standing position
        await robot.posture.go_to_posture("Stand", 0.8)

        # Enable stiffness for movement
        await robot.motion.set_stiffnesses("Body", 1.0)

        # Set appropriate volume
        await robot.tts.set_volume(0.6)

        # Configure autonomous abilities for exercise
        await robot.autonomous_life.set_state("disabled")

        configs = {
            "AutonomousBlinking": True,
            "BackgroundMovement": True,
            "BasicAwareness": False,
            "ListeningMovement": False,
            "SpeakingMovement": False,
        }

        for config, value in configs.items():
            await robot.autonomous_life.set_autonomous_ability_enabled(config, value)

        # Set language
        await robot.tts.set_language("German")

        logger.info("Robot initialization completed")

    except Exception as error:
        logger.exception("Failed to initialize robot", error=str(error))
        raise


async def prepare_robot_for_shutdown(robot: "NaoRobot") -> None:
    """Prepare robot for safe shutdown."""
    try:
        logger.info("Preparing robot for shutdown")

        # Go to rest position
        await robot.posture.go_to_posture("Crouch", 0.8)

        # Reduce stiffness
        await robot.motion.set_stiffnesses("Body", 0.3)

        logger.info("Robot shutdown preparation completed")

    except Exception as error:
        logger.exception("Failed to prepare robot for shutdown", error=str(error))


# Helper functions


async def _run_blinking_behavior(robot: "NaoRobot") -> None:
    """Run autonomous blinking behavior in background."""
    try:
        logger.info("Starting autonomous blinking behavior")

        while True:
            # Blink interval (3-8 seconds is natural)
            await asyncio.sleep(5.0)

            # Perform blink
            await robot.leds.off("FaceLeds")  # Close eyes
            await asyncio.sleep(0.1)
            await robot.leds.on("FaceLeds")  # Open eyes

    except asyncio.CancelledError:
        logger.info("Autonomous blinking behavior cancelled")
        # Ensure eyes are open when cancelled
        with contextlib.suppress(RuntimeError):
            await robot.leds.on("FaceLeds")
        raise
    except Exception as error:
        logger.exception("Error in blinking behavior", error=str(error))
        raise
