"""Event system for pose detection and exercise tracking."""

import asyncio
import time
from collections import defaultdict
from collections.abc import Awaitable, Callable
from dataclasses import dataclass
from typing import Self

from mobirobot.common.structured_logging import LoggerType, get_logger

logger: LoggerType = get_logger(__name__)


@dataclass(frozen=True)
class ExerciseEvent:
    """Base class for exercise events."""

    timestamp: float
    exercise_id: str


@dataclass(frozen=True)
class RepCompletedEvent(ExerciseEvent):
    """Event fired when a repetition is completed."""

    rep_count: int
    previous_count: int

    @classmethod
    def create(cls, exercise_id: str, rep_count: int, previous_count: int) -> Self:
        """Create rep completed event."""
        return cls(
            timestamp=time.time(),
            exercise_id=exercise_id,
            rep_count=rep_count,
            previous_count=previous_count,
        )


@dataclass(frozen=True)
class HoldStartedEvent(ExerciseEvent):
    """Event fired when a hold pose is entered."""

    pose_name: str

    @classmethod
    def create(cls, exercise_id: str, pose_name: str) -> Self:
        """Create hold started event."""
        return cls(
            timestamp=time.time(),
            exercise_id=exercise_id,
            pose_name=pose_name,
        )


@dataclass(frozen=True)
class HoldProgressEvent(ExerciseEvent):
    """Event fired during hold pose progress."""

    hold_duration: float
    pose_name: str

    @classmethod
    def create(cls, exercise_id: str, hold_duration: float, pose_name: str) -> Self:
        """Create hold progress event."""
        return cls(
            timestamp=time.time(),
            exercise_id=exercise_id,
            hold_duration=hold_duration,
            pose_name=pose_name,
        )


@dataclass(frozen=True)
class HoldCompletedEvent(ExerciseEvent):
    """Event fired when a hold pose is exited."""

    final_duration: float
    pose_name: str

    @classmethod
    def create(cls, exercise_id: str, final_duration: float, pose_name: str) -> Self:
        """Create hold completed event."""
        return cls(
            timestamp=time.time(),
            exercise_id=exercise_id,
            final_duration=final_duration,
            pose_name=pose_name,
        )


@dataclass(frozen=True)
class PhaseTransitionEvent(ExerciseEvent):
    """Event fired when exercise phase changes."""

    from_phase: str | None
    to_phase: str | None

    @classmethod
    def create(
        cls, exercise_id: str, from_phase: str | None, to_phase: str | None
    ) -> Self:
        """Create phase transition event."""
        return cls(
            timestamp=time.time(),
            exercise_id=exercise_id,
            from_phase=from_phase,
            to_phase=to_phase,
        )


@dataclass(frozen=True)
class ExerciseStartedEvent(ExerciseEvent):
    """Event fired when exercise tracking starts."""

    @classmethod
    def create(cls, exercise_id: str) -> Self:
        """Create exercise started event."""
        return cls(timestamp=time.time(), exercise_id=exercise_id)


@dataclass(frozen=True)
class ExerciseStoppedEvent(ExerciseEvent):
    """Event fired when exercise tracking stops."""

    final_reps: int
    final_hold_duration: float

    @classmethod
    def create(
        cls, exercise_id: str, final_reps: int, final_hold_duration: float
    ) -> "ExerciseStoppedEvent":
        """Create exercise stopped event."""
        return cls(
            timestamp=time.time(),
            exercise_id=exercise_id,
            final_reps=final_reps,
            final_hold_duration=final_hold_duration,
        )


EventHandler = Callable[[ExerciseEvent], Awaitable[None]]


class ExerciseEventBus:
    """Event bus for exercise-related events."""

    def __init__(self) -> None:
        """Initialize the event bus."""
        self._handlers: dict[type[ExerciseEvent], list[EventHandler]] = defaultdict(
            list
        )
        self._global_handlers: list[EventHandler] = []

    def subscribe(
        self,
        event_type: type[ExerciseEvent],
        handler: EventHandler,
    ) -> None:
        """Subscribe to specific event types.

        Args:
            event_type: The type of event to subscribe to.
            handler: The async function to call when event occurs.
        """
        self._handlers[event_type].append(handler)
        logger.debug(
            "Subscribed handler for event type", event_type=event_type.__name__
        )

    def subscribe_all(self, handler: EventHandler) -> None:
        """Subscribe to all event types.

        Args:
            handler: The async function to call for any event.
        """
        self._global_handlers.append(handler)
        logger.debug("Subscribed global event handler")

    def unsubscribe(
        self,
        event_type: type[ExerciseEvent],
        handler: EventHandler,
    ) -> None:
        """Unsubscribe from specific event types.

        Args:
            event_type: The type of event to unsubscribe from.
            handler: The handler function to remove.
        """
        if handler in self._handlers[event_type]:
            self._handlers[event_type].remove(handler)
            logger.debug(
                "Unsubscribed handler for event type", event_type=event_type.__name__
            )

    def unsubscribe_all(self, handler: EventHandler) -> None:
        """Unsubscribe from all events.

        Args:
            handler: The handler function to remove.
        """
        if handler in self._global_handlers:
            self._global_handlers.remove(handler)
            logger.debug("Unsubscribed global event handler")

    async def publish(self, event: ExerciseEvent) -> None:
        """Publish an event to all subscribers.

        Args:
            event: The event to publish.
        """
        logger.debug(
            "Publishing event for exercise",
            event_type=type(event).__name__,
            exercise_id=event.exercise_id,
        )

        # Call specific handlers
        handlers = self._handlers.get(type(event), [])

        # Call global handlers
        all_handlers = handlers + self._global_handlers

        if all_handlers:
            # Execute all handlers concurrently
            tasks = [handler(event) for handler in all_handlers]
            try:
                await asyncio.gather(*tasks, return_exceptions=True)
            except Exception:
                logger.exception(
                    "Error in event handler for event",
                    event_type=type(event).__name__,
                )

    def clear(self) -> None:
        """Clear all event handlers."""
        self._handlers.clear()
        self._global_handlers.clear()
        logger.debug("Cleared all event handlers")
