"""Exercise tracking logic for pose-based exercises."""

import asyncio
import time

from mobirobot.common.structured_logging import LoggerType, get_logger
from mobirobot.pose.events import (
    ExerciseEventBus,
    HoldCompletedEvent,
    HoldProgressEvent,
    HoldStartedEvent,
    PhaseTransitionEvent,
    RepCompletedEvent,
)
from mobirobot.pose.models import ExerciseConfig, ExerciseState, PoseDetection
from mobirobot.pose.pose_detector import PoseDetector

logger: LoggerType = get_logger(__name__)


class ExerciseTracker:
    """Handles exercise-specific tracking logic."""

    config: ExerciseConfig
    event_bus: ExerciseEventBus | None
    _state: ExerciseState
    _last_reset_time: float
    _hold_start_time: float | None
    _last_hold_pose: str | None

    def __init__(
        self, config: ExerciseConfig, event_bus: ExerciseEventBus | None = None
    ) -> None:
        """Initialize exercise tracker.

        Args:
            config: Exercise configuration.
            event_bus: Optional event bus for publishing events.
        """
        config.validate()
        self.config = config
        self.event_bus = event_bus

        # State management
        self._state = ExerciseState()
        self._last_reset_time = time.time()

        # Hold tracking state
        self._hold_start_time = None
        self._last_hold_pose = None

        logger.debug("Exercise tracker initialized", exercise_id=config.exercise_id)

    def get_state(self) -> ExerciseState:
        """Get current exercise state.

        Returns:
            Current immutable exercise state.
        """
        return self._state

    async def process_detection(self, detection: PoseDetection) -> ExerciseState:
        """Process a pose detection and update state.

        Args:
            detection: Pose detection result.

        Returns:
            Updated exercise state.
        """
        if not self._state.is_active:
            return self._state

        old_state = self._state

        if self.config.is_hold_mode:
            self._state = await self._process_hold_detection(detection, old_state)
        else:
            self._state = await self._process_rep_detection(detection, old_state)

        return self._state

    async def _process_hold_detection(
        self, detection: PoseDetection, old_state: ExerciseState
    ) -> ExerciseState:
        """Process detection for hold-based exercises.

        Args:
            detection: Pose detection result.
            old_state: Previous exercise state.

        Returns:
            Updated exercise state.
        """
        if not detection.classification:
            # No pose detected, end hold if active
            if self._hold_start_time is not None:
                final_duration = time.time() - self._hold_start_time
                await self._publish_hold_completed(final_duration)
                self._hold_start_time = None
                self._last_hold_pose = None
            return old_state

        hold_pose = self.config.classifications[0]

        if detection.classification == hold_pose:
            # Holding the target pose
            if self._hold_start_time is None:
                # Start new hold
                self._hold_start_time = time.time()
                self._last_hold_pose = hold_pose
                await self._publish_hold_started(hold_pose)
            else:
                # Continue hold - update duration
                current_duration = time.time() - self._hold_start_time
                await self._publish_hold_progress(current_duration, hold_pose)

            # Update state with current hold duration
            current_duration = time.time() - self._hold_start_time
            return old_state.with_hold_duration(current_duration)
        # Different pose detected, end hold if active
        if self._hold_start_time is not None:
            final_duration = time.time() - self._hold_start_time
            await self._publish_hold_completed(final_duration)
            self._hold_start_time = None
            self._last_hold_pose = None
        return old_state

    async def _process_rep_detection(
        self, detection: PoseDetection, old_state: ExerciseState
    ) -> ExerciseState:
        """Process detection for repetition-based exercises.

        Args:
            detection: Pose detection result.
            old_state: Previous exercise state.

        Returns:
            Updated exercise state.
        """
        if not detection.classification:
            return old_state

        start_pose = self.config.classifications[0]
        rep_pose = self.config.classifications[1]

        current_phase = old_state.current_phase
        new_phase = detection.classification

        # Check for phase transition with debouncing
        if current_phase != new_phase:
            time_since_last_transition = (
                detection.timestamp - old_state.last_transition_time
                if old_state.last_transition_time
                else float("inf")
            )

            if time_since_last_transition > self.config.debounce_threshold:
                # Valid phase transition
                await self._publish_phase_transition(current_phase, new_phase)

                # Check for rep completion (transition from rep_pose back to start_pose)
                if current_phase == rep_pose and new_phase == start_pose:
                    new_reps = old_state.reps + 1
                    await self._publish_rep_completed(new_reps, old_state.reps)
                    return old_state.with_reps(new_reps).with_phase(new_phase)
                return old_state.with_phase(new_phase)

        return old_state

    async def start(self) -> None:
        """Start exercise tracking."""
        self._state = self._state.with_active(True)
        logger.info("Exercise tracking started", exercise_id=self.config.exercise_id)

    async def stop(self) -> ExerciseState:
        """Stop exercise tracking.

        Returns:
            Final exercise state.
        """
        final_state = self._state.with_active(False)

        # End any active hold
        if self._hold_start_time is not None:
            final_duration = time.time() - self._hold_start_time
            await self._publish_hold_completed(final_duration)
            final_state = final_state.with_hold_duration(final_duration)

        self._state = final_state
        self._hold_start_time = None
        self._last_hold_pose = None

        logger.info("Exercise tracking stopped", exercise_id=self.config.exercise_id)
        return final_state

    def reset(self) -> None:
        """Reset exercise state."""
        self._state = ExerciseState()
        self._last_reset_time = time.time()
        self._hold_start_time = None
        self._last_hold_pose = None
        logger.debug("Exercise state reset", exercise_id=self.config.exercise_id)

    # Event publishing methods
    async def _publish_rep_completed(self, rep_count: int, previous_count: int) -> None:
        """Publish rep completed event."""
        if self.event_bus:
            event = RepCompletedEvent.create(
                exercise_id=self.config.exercise_id,
                rep_count=rep_count,
                previous_count=previous_count,
            )
            await self.event_bus.publish(event)

    async def _publish_hold_started(self, pose_name: str) -> None:
        """Publish hold started event."""
        if self.event_bus:
            event = HoldStartedEvent.create(
                exercise_id=self.config.exercise_id,
                pose_name=pose_name,
            )
            await self.event_bus.publish(event)

    async def _publish_hold_progress(self, duration: float, pose_name: str) -> None:
        """Publish hold progress event."""
        if self.event_bus:
            event = HoldProgressEvent.create(
                exercise_id=self.config.exercise_id,
                hold_duration=duration,
                pose_name=pose_name,
            )
            await self.event_bus.publish(event)

    async def _publish_hold_completed(self, final_duration: float) -> None:
        """Publish hold completed event."""
        if self.event_bus and self._last_hold_pose:
            event = HoldCompletedEvent.create(
                exercise_id=self.config.exercise_id,
                final_duration=final_duration,
                pose_name=self._last_hold_pose,
            )
            await self.event_bus.publish(event)

    async def _publish_phase_transition(
        self, from_phase: str | None, to_phase: str | None
    ) -> None:
        """Publish phase transition event."""
        if self.event_bus:
            event = PhaseTransitionEvent.create(
                exercise_id=self.config.exercise_id,
                from_phase=from_phase,
                to_phase=to_phase,
            )
            await self.event_bus.publish(event)


class ExerciseCoordinator:
    """Coordinates pose detection with exercise tracking."""

    def __init__(
        self, pose_detector: PoseDetector, event_bus: ExerciseEventBus
    ) -> None:
        """Initialize exercise coordinator.

        Args:
            pose_detector: Pose detection service.
            event_bus: Event bus for publishing events.
        """
        self.pose_detector: PoseDetector = pose_detector
        self.event_bus: ExerciseEventBus = event_bus
        self.active_trackers: dict[str, ExerciseTracker] = {}
        self._detection_task: asyncio.Task[None] | None = None

        logger.debug("Exercise coordinator initialized")

    async def start_exercise_tracking(
        self, exercise_id: str, config: ExerciseConfig
    ) -> bool:
        """Start tracking for a specific exercise.

        Args:
            exercise_id: Unique identifier for the exercise.
            config: Exercise configuration.

        Returns:
            True if tracking started successfully, False otherwise.
        """
        if exercise_id in self.active_trackers:
            logger.warning("Exercise tracking already active", exercise_id=exercise_id)
            return False

        # Update pose detector model if specified
        pose_classification_available = False
        if not config.model_path:
            logger.info(
                "No pose model specified for exercise - using timing-based tracking only",
                exercise_id=exercise_id,
            )
        success = self.pose_detector.update_model(
            config.model_path, config.classifications
        )
        if not success:
            logger.warning(
                "Pose classification unavailable for exercise - continuing without model",
                exercise_id=exercise_id,
                model_file=config.model_path.name if config.model_path else "None",
                fallback_mode="exercise_timing_only",
            )

        pose_classification_available = True
        logger.info(
            "Pose classification enabled for exercise",
            exercise_id=exercise_id,
            model_file=config.model_path.name if config.model_path else "None",
        )

        # Create and start tracker (will work with or without pose classification)
        tracker = ExerciseTracker(config, self.event_bus)
        await tracker.start()
        self.active_trackers[exercise_id] = tracker

        # Start detection processing if not already running and pose classification is available
        if pose_classification_available and (
            self._detection_task is None or self._detection_task.done()
        ):
            self._detection_task = asyncio.create_task(self._process_detections())
            logger.debug("Started pose detection processing task")

        logger.info(
            "Started exercise tracking",
            exercise_id=exercise_id,
            pose_classification=pose_classification_available,
        )
        return True

    async def stop_exercise_tracking(self, exercise_id: str) -> ExerciseState | None:
        """Stop tracking for a specific exercise.

        Args:
            exercise_id: Unique identifier for the exercise.

        Returns:
            Final exercise state, or None if exercise was not being tracked.
        """
        tracker = self.active_trackers.pop(exercise_id, None)
        if tracker is None:
            logger.warning(
                "No active tracking found for exercise", exercise_id=exercise_id
            )
            return None

        final_state = await tracker.stop()

        # Stop detection processing if no active trackers
        if not self.active_trackers and self._detection_task:
            self.pose_detector.stop()

        logger.info("Stopped exercise tracking", exercise_id=exercise_id)
        return final_state

    def get_exercise_state(self, exercise_id: str) -> ExerciseState | None:
        """Get current state for a specific exercise.

        Args:
            exercise_id: Unique identifier for the exercise.

        Returns:
            Current exercise state, or None if exercise is not being tracked.
        """
        tracker = self.active_trackers.get(exercise_id)
        return tracker.get_state() if tracker else None

    async def _process_detections(self) -> None:
        """Process pose detections for all active exercises."""
        try:
            async for detection in self.pose_detector.detect_poses():
                # Process detection for all active trackers
                for tracker in self.active_trackers.values():
                    try:
                        await tracker.process_detection(detection)
                    except Exception:
                        logger.exception("Error processing detection for tracker")

        except Exception:
            logger.exception("Error in detection processing loop")
        finally:
            self._detection_task = None

    async def stop_all(self) -> dict[str, ExerciseState]:
        """Stop all active exercise tracking.

        Returns:
            Dictionary mapping exercise IDs to their final states.
        """
        final_states: dict[str, ExerciseState] = {}

        for exercise_id in list(self.active_trackers.keys()):
            final_state = await self.stop_exercise_tracking(exercise_id)
            if final_state:
                final_states[exercise_id] = final_state

        return final_states

    @property
    def active_exercise_ids(self) -> list[str]:
        """Get list of currently tracked exercise IDs."""
        return list(self.active_trackers.keys())
