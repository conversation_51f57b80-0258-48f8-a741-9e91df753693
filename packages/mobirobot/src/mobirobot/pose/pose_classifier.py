import asyncio
import pickle
import time
from collections.abc import Iterable
from pathlib import Path
from typing import TYPE_CHECKING, Any, Self

import cv2
import numpy as np
from cv2.typing import MatLike
from mediapipe.framework.formats.landmark_pb2 import Landmark
from mediapipe.python.solutions.holistic import Holistic
from sklearn.ensemble import RandomForestClassifier
from sklearn.exceptions import NotFittedError

from mobirobot.common.config import settings
from mobirobot.common.structured_logging import LoggerType, get_logger
from mobirobot.webcam.webcam_service import WebcamService

if TYPE_CHECKING:
    from collections.abc import Callable

    from numpy.typing import NDArray


logger: LoggerType = get_logger(__name__)

# Constants
DEFAULT_MIN_DETECTION_CONFIDENCE = 0.5
DEFAULT_MIN_TRACKING_CONFIDENCE = 0.5
DEBOUNCE_THRESHOLD_SECONDS = 0.5
FRAME_TIMEOUT_SECONDS = 1.0
MIN_CLASSIFICATIONS_FOR_REP_COUNT = 2


def _load_model(model_path: Path) -> RandomForestClassifier | None:
    """Loads a model from a file.

    Args:
        model_path: The path to the model file.

    Returns:
        The loaded model, or None if loading fails.
    """
    if not model_path.exists():
        logger.warning("Model file does not exist", model_path=str(model_path))
        return None

    if not model_path.is_file():
        logger.warning("Model file is not a file", model_path=str(model_path))
        return None

    try:
        with model_path.open("rb") as f:
            model: object = pickle.load(f)
            if not isinstance(model, RandomForestClassifier):
                logger.error(
                    "Loaded object is not a RandomForestClassifier",
                    object_type=type(model).__name__,
                    model_path=str(model_path),
                )
                return None
            return model
    except (pickle.PickleError, OSError):
        logger.exception("Failed to load model", model_path=str(model_path))
        return None


class PoseClassifier:
    """Classifies human poses from a video stream using a trained model."""

    def __init__(self, webcam_service: WebcamService, model_dir: Path):
        """Initializes the PoseClassifier with a webcam service."""
        self.webcam_service: WebcamService = webcam_service
        self.model_dir: Path = model_dir
        self._frame_queue: asyncio.Queue[cv2.typing.MatLike] | None = None

        # Model and classification state
        self.model: RandomForestClassifier | None = None
        self.classifications: list[str] | None = None
        self.prev_classification: str | None = ""
        self.classification: str | None = ""

        # Exercise tracking state
        self.reps: int = 0
        self.hold_time_start: float = 0.0
        self.hold_time_elapsed: float = 0.0
        self.duration: int = 60

        # Handler for pose processing
        self.poseHandler: Callable[..., None] | None = None

    @classmethod
    def create(cls, webcam_service: WebcamService | None = None) -> Self | None:
        """Factory method to create a PoseClassifier instance with a webcam service.

        Args:
            webcam_service: An existing WebcamService instance to use.

        Returns:
            A PoseClassifier instance if the webcam service is provided,
            otherwise None.
        """
        if webcam_service is None:
            return None
        logger.debug("WebcamService provided for pose classification")
        return cls(webcam_service, settings.models_dir)

    def update_model(
        self,
        model_name: str | None = None,
        classifications: list[str] | None = None,
        hold: bool = False,
    ) -> bool:
        """Loads a new classification model and resets state.

        Args:
            model_name: Name of the model file to load.
            classifications: List of classification labels.
            hold: Whether to use hold time tracking instead of rep counting.

        Returns:
            True if model was loaded successfully, False otherwise.
        """
        if not model_name:
            self.model = None
            self._reset_state()
            logger.debug("Model cleared for pose classification")
            return True

        model_path = self.model_dir / model_name
        self.model = _load_model(model_path)

        if self.model is None:
            logger.warning("Failed to load model, pose classification disabled")
            return False

        self._reset_state()
        self.classifications = classifications or []
        self.poseHandler = self.handle_hold_time if hold else self.handle_rep_count
        logger.debug("Model for pose classification set", model_name=model_name)
        return True

    def _reset_state(self) -> None:
        """Reset exercise tracking state."""
        self.reps = 0
        self.hold_time_start = 0.0
        self.hold_time_elapsed = 0.0
        self.prev_classification = ""
        self.classification = ""

    @staticmethod
    def _extract_pose_features(landmarks: Iterable[Landmark]) -> "NDArray[np.float64]":
        """Extract pose features from landmarks for model prediction.

        Args:
            landmarks: Pose landmarks from MediaPipe.

        Returns:
            Flattened numpy array of pose features.
        """
        features: list[float] = []
        for landmark in landmarks:
            features.extend([landmark.x, landmark.y, landmark.z, landmark.visibility])
        return np.array(features, dtype=np.float64).reshape(1, -1)

    @staticmethod
    def process_image(holistic: Holistic, image: MatLike) -> Any:
        """Processes a single image frame using the MediaPipe Holistic model.

        Args:
            holistic: The MediaPipe Holistic model.
            image: The image to process.

        Returns:
            The processed image.
        """
        return holistic.process(image)

    async def classify(self):
        """Continuously reads frames from webcam service, detects landmarks, and classifies poses."""
        if not self.webcam_service.is_active:
            logger.warning("WebcamService is not active, cannot start classification")
            return

        # Subscribe to the webcam service to receive frames
        self._frame_queue = await self.webcam_service.subscribe(max_queue_size=1)
        self.hold_time_start = time.time()

        try:
            with Holistic(
                min_detection_confidence=DEFAULT_MIN_DETECTION_CONFIDENCE,
                min_tracking_confidence=DEFAULT_MIN_TRACKING_CONFIDENCE,
            ) as holistic:
                while self.webcam_service.is_active:
                    try:
                        # Get frame from the webcam service queue
                        frame = await asyncio.wait_for(
                            self._frame_queue.get(), timeout=FRAME_TIMEOUT_SECONDS
                        )
                    except TimeoutError:
                        logger.debug(
                            "No frame received from webcam service, continuing"
                        )
                        continue

                    image = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                    image.flags.writeable = False
                    results = await asyncio.get_event_loop().run_in_executor(
                        None, lambda img=image: self.process_image(holistic, img)
                    )

                    # Check if landmarks were detected
                    if results.pose_landmarks is None:
                        logger.debug(
                            "No pose landmarks detected in frame, skipping classification"
                        )
                        continue  # Skip to next frame

                    pose_features = self._extract_pose_features(
                        results.pose_landmarks.landmark
                    )

                    self.prev_classification = self.classification
                    if self.model is not None:
                        try:
                            prediction: NDArray[np.float64] = self.model.predict(
                                pose_features
                            )
                            self.classification = (
                                str(prediction[0]) if len(prediction) > 0 else None
                            )
                        except (NotFittedError, ValueError) as e:
                            logger.warning("Model prediction failed", error=str(e))
                            self.classification = None

                    # Handling the movement of the patient
                    if self.poseHandler:
                        self.poseHandler()

        finally:
            # Unsubscribe from the webcam service when done
            if self._frame_queue:
                await self.webcam_service.unsubscribe(self._frame_queue)
                self._frame_queue = None

    def handle_hold_time(self) -> None:
        """Handles pose classification logic based on holding a pose."""
        if self.classifications is None or len(self.classifications) == 0:
            return
        hold_pose = self.classifications[0]
        # Check if we are currently in the hold pose
        if self.classification == hold_pose:
            # If we just entered the hold pose, start the timer
            if self.prev_classification != hold_pose:
                self.hold_time_start = time.time()
                self.hold_time_elapsed = 0.0  # Reset elapsed time on entering hold
                logger.debug("Entered hold pose, starting timer", hold_pose=hold_pose)
            else:
                # If we are still in the hold pose, update the elapsed time
                self.hold_time_elapsed = time.time() - self.hold_time_start
        # Check if we just exited the hold pose
        elif self.prev_classification == hold_pose:
            logger.debug(
                "Exited hold pose",
                hold_pose=hold_pose,
                final_hold_duration=self.hold_time_elapsed,
            )
            # Timer will be reset when/if entering hold pose again

    def handle_rep_count(self) -> None:
        """Handles pose classification logic based on counting repetitions."""
        if (
            self.classifications is None
            or len(self.classifications) < MIN_CLASSIFICATIONS_FOR_REP_COUNT
        ):
            return
        start_pose = self.classifications[0]
        rep_pose = self.classifications[1]

        # Transition from start to rep pose
        if self.prev_classification == start_pose and self.classification == rep_pose:
            # Check if enough time has passed since last reset (debounce)
            time_since_reset = time.time() - self.hold_time_start
            if time_since_reset > DEBOUNCE_THRESHOLD_SECONDS:
                self.reps += 1
                self.hold_time_start = time.time()  # Reset timer *after* counting rep
                logger.debug("Updated Rep count", reps=self.reps)
        # Transition back from rep to start pose
        elif self.prev_classification == rep_pose and self.classification == start_pose:
            # Reset timer when returning to start pose, ready for next rep detection
            self.hold_time_start = time.time()
