"""Pose detection and exercise tracking module."""

from .events import (
    ExerciseEvent,
    ExerciseEventBus,
    HoldCompletedEvent,
    HoldProgressEvent,
    HoldStartedEvent,
    PhaseTransitionEvent,
    RepCompletedEvent,
)
from .exercise_tracker import ExerciseCoordinator, ExerciseTracker
from .models import ExerciseConfig, ExerciseState, PoseDetection
from .pose_classifier import PoseClassifier
from .pose_detector import PoseDetector

__all__ = [
    "ExerciseConfig",
    "ExerciseCoordinator",
    "ExerciseEvent",
    "ExerciseEventBus",
    "ExerciseState",
    "ExerciseTracker",
    "HoldCompletedEvent",
    "HoldProgressEvent",
    "HoldStartedEvent",
    "PhaseTransitionEvent",
    "PoseClassifier",
    "PoseDetection",
    "PoseDetector",
    "RepCompletedEvent",
]
