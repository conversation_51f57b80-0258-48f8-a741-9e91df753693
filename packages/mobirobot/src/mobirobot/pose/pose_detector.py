"""Pure pose detection component using MediaPipe and ML models."""

import asyncio
import pickle
from collections.abc import Async<PERSON>terator, Iterable
from pathlib import Path
from typing import TYPE_CHECKING

import cv2
import numpy as np
from mediapipe.framework.formats.landmark_pb2 import Landmark
from mediapipe.python.solutions.holistic import Holistic
from sklearn.ensemble import RandomForestClassifier
from sklearn.exceptions import NotFittedError

from mobirobot.common.structured_logging import LoggerType, get_logger
from mobirobot.pose.models import PoseDetection
from mobirobot.webcam.webcam_service import WebcamService

if TYPE_CHECKING:
    from numpy.typing import NDArray

logger: LoggerType = get_logger(__name__)

# Constants
DEFAULT_MIN_DETECTION_CONFIDENCE = 0.5
DEFAULT_MIN_TRACKING_CONFIDENCE = 0.5
FRAME_TIMEOUT_SECONDS = 1.0


def _load_model(model_path: Path) -> RandomForestClassifier | None:
    """Load a RandomForest model from a file.

    Args:
        model_path: Path to the model file.

    Returns:
        The loaded model, or None if loading fails.
    """
    if not model_path.exists():
        logger.warning("Model file does not exist", model_path=str(model_path))
        return None

    if not model_path.is_file():
        logger.warning("Model file is not a file", model_path=str(model_path))
        return None

    try:
        with model_path.open("rb") as f:
            model: object = pickle.load(f)
            if not isinstance(model, RandomForestClassifier):
                logger.error(
                    "Loaded object is not a RandomForestClassifier",
                    object_type=type(model).__name__,
                    model_path=str(model_path),
                )
                return None
            return model
    except (pickle.PickleError, OSError):
        logger.exception("Failed to load model", model_path=str(model_path))
        return None


class PoseDetector:
    """Handles pose detection and classification using MediaPipe and ML models."""

    def __init__(
        self,
        webcam_service: WebcamService,
        min_detection_confidence: float = DEFAULT_MIN_DETECTION_CONFIDENCE,
        min_tracking_confidence: float = DEFAULT_MIN_TRACKING_CONFIDENCE,
    ) -> None:
        """Initialize the pose detector.

        Args:
            webcam_service: Service providing video frames.
            min_detection_confidence: Minimum confidence for pose detection.
            min_tracking_confidence: Minimum confidence for pose tracking.
        """
        self.webcam_service: WebcamService = webcam_service
        self.min_detection_confidence: float = min_detection_confidence
        self.min_tracking_confidence: float = min_tracking_confidence

        # Model state
        self.model: RandomForestClassifier | None = None
        self.classifications: list[str] = []

        # Frame processing
        self._frame_queue: asyncio.Queue[cv2.typing.MatLike] | None = None
        self._is_running: bool = False

    @classmethod
    def create(
        cls,
        webcam_service: WebcamService,
        min_detection_confidence: float = DEFAULT_MIN_DETECTION_CONFIDENCE,
        min_tracking_confidence: float = DEFAULT_MIN_TRACKING_CONFIDENCE,
    ) -> "PoseDetector":
        """Create a new PoseDetector instance.

        Args:
            webcam_service: Service providing video frames.
            min_detection_confidence: Minimum confidence for pose detection.
            min_tracking_confidence: Minimum confidence for pose tracking.

        Returns:
            New PoseDetector instance.
        """
        return cls(
            webcam_service=webcam_service,
            min_detection_confidence=min_detection_confidence,
            min_tracking_confidence=min_tracking_confidence,
        )

    def update_model(
        self, model_path: Path | None, classifications: list[str] | None = None
    ) -> bool:
        """Update the classification model.

        Args:
            model_path: Path to the model file, or None to disable classification.
            classifications: List of classification labels.

        Returns:
            True if model was loaded successfully, False otherwise.
        """
        if model_path is None:
            self.model = None
            self.classifications = []
            logger.debug("Model cleared for pose classification")
            return True

        self.model = _load_model(model_path)

        if self.model is None:
            logger.warning("Failed to load model, pose classification disabled")
            return False

        self.classifications = classifications or []
        logger.debug("Model for pose classification set", model_name=model_path.name)
        return True

    @staticmethod
    def _extract_pose_features(
        landmarks: Iterable[Landmark],
    ) -> "NDArray[np.float64]":
        """Extract pose features from landmarks for model prediction.

        Args:
            landmarks: Pose landmarks from MediaPipe.

        Returns:
            Flattened numpy array of pose features.
        """
        features: list[float] = []
        for landmark in landmarks:
            features.extend([landmark.x, landmark.y, landmark.z, landmark.visibility])
        return np.array(features, dtype=np.float64).reshape(1, -1)

    def _classify_pose(self, landmarks: Iterable[Landmark]) -> str | None:
        """Classify pose from landmarks.

        Args:
            landmarks: Pose landmarks from MediaPipe.

        Returns:
            Classification result or None if classification fails.
        """
        if self.model is None:
            return None

        try:
            pose_features = self._extract_pose_features(landmarks)
            prediction: NDArray[np.float64] = self.model.predict(pose_features)
            return str(prediction[0]) if len(prediction) > 0 else None
        except (NotFittedError, ValueError) as e:
            logger.warning("Model prediction failed", error=str(e))
            return None

    async def detect_poses(self) -> AsyncIterator[PoseDetection]:
        """Detect poses from video stream.

        Yields:
            PoseDetection objects as they are detected.

        Raises:
            RuntimeError: If pose detection is already running.
        """
        if self._is_running:
            msg = "Pose detection is already running"
            raise RuntimeError(msg)

        self._is_running = True

        try:
            # Subscribe to webcam frames
            self._frame_queue = await self.webcam_service.subscribe(max_queue_size=10)

            with Holistic(
                min_detection_confidence=self.min_detection_confidence,
                min_tracking_confidence=self.min_tracking_confidence,
            ) as holistic:
                while self._is_running:
                    try:
                        frame = await asyncio.wait_for(
                            self._frame_queue.get(), timeout=FRAME_TIMEOUT_SECONDS
                        )
                    except TimeoutError:
                        continue

                    # Process frame with MediaPipe
                    rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                    results = holistic.process(rgb_frame)

                    # Extract pose and classify if landmarks detected
                    classification = None
                    landmarks = None

                    if results.pose_landmarks:
                        landmark_list: Iterable[Landmark] = (
                            results.pose_landmarks.landmark
                        )
                        landmarks = self._extract_pose_features(landmark_list)
                        classification = self._classify_pose(landmark_list)

                    # Yield detection result
                    detection = PoseDetection.create(
                        classification=classification,
                        confidence=1.0,
                        landmarks=landmarks,
                    )

                    yield detection

        finally:
            # Cleanup
            if self._frame_queue:
                await self.webcam_service.unsubscribe(self._frame_queue)
            self._frame_queue = None
            self._is_running = False

    def stop(self) -> None:
        """Stop pose detection."""
        self._is_running = False
        logger.debug("Pose detection stopped")

    @property
    def is_running(self) -> bool:
        """Check if pose detection is currently running."""
        return self._is_running
