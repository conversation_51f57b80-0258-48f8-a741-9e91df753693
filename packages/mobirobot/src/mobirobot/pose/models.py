"""Data models for pose detection and exercise tracking."""

import time
from dataclasses import dataclass
from pathlib import Path
from typing import TYPE_CHECKING, Literal, Self

from mobirobot.models.exercise import BaseExercise

if TYPE_CHECKING:
    import numpy as np
    from numpy.typing import NDArray


@dataclass(frozen=True)
class PoseDetection:
    """Immutable pose detection result."""

    classification: str | None
    confidence: float
    timestamp: float
    landmarks: "NDArray[np.float64] | None" = None

    @classmethod
    def create(
        cls,
        classification: str | None,
        confidence: float = 1.0,
        landmarks: "NDArray[np.float64] | None" = None,
    ) -> "PoseDetection":
        """Create a new pose detection with current timestamp."""
        return cls(
            classification=classification,
            confidence=confidence,
            timestamp=time.time(),
            landmarks=landmarks,
        )


@dataclass(frozen=True)
class ExerciseState:
    """Immutable exercise state snapshot."""

    reps: int = 0
    hold_duration: float = 0.0
    current_phase: str | None = None
    last_transition_time: float | None = None
    is_active: bool = False

    def with_reps(self, reps: int) -> "ExerciseState":
        """Create new state with updated rep count."""
        return ExerciseState(
            reps=reps,
            hold_duration=self.hold_duration,
            current_phase=self.current_phase,
            last_transition_time=time.time(),
            is_active=self.is_active,
        )

    def with_hold_duration(self, duration: float) -> "ExerciseState":
        """Create new state with updated hold duration."""
        return ExerciseState(
            reps=self.reps,
            hold_duration=duration,
            current_phase=self.current_phase,
            last_transition_time=self.last_transition_time,
            is_active=self.is_active,
        )

    def with_phase(self, phase: str | None) -> "ExerciseState":
        """Create new state with updated phase."""
        return ExerciseState(
            reps=self.reps,
            hold_duration=self.hold_duration,
            current_phase=phase,
            last_transition_time=time.time(),
            is_active=self.is_active,
        )

    def with_active(self, is_active: bool) -> "ExerciseState":
        """Create new state with updated active status."""
        return ExerciseState(
            reps=self.reps,
            hold_duration=self.hold_duration,
            current_phase=self.current_phase,
            last_transition_time=self.last_transition_time,
            is_active=is_active,
        )


@dataclass(frozen=True)
class ExerciseConfig:
    """Configuration for exercise tracking."""

    exercise_id: str
    tracking_mode: Literal["reps", "hold"]
    classifications: list[str]
    model_path: Path | None = None
    debounce_threshold: float = 0.5
    target_reps: int | None = None
    target_hold_duration: float | None = None

    @classmethod
    def from_exercise(cls, exercise: BaseExercise) -> Self:
        """Create config from exercise model.

        Args:
            exercise: BaseExercise instance with model_processing configuration.

        Returns:
            ExerciseConfig instance.

        Raises:
            ValueError: If exercise has no model processing configuration.
        """
        if not exercise.model_processing:
            msg = "Exercise has no model processing configuration"
            raise ValueError(msg)

        model_path = None
        if exercise.model_processing.model_files:
            model_path = Path(exercise.model_processing.model_files[0])

        return cls(
            exercise_id=exercise.name,
            tracking_mode="hold" if exercise.model_processing.hold else "reps",
            classifications=exercise.model_processing.classifications,
            model_path=model_path,
        )

    @property
    def is_hold_mode(self) -> bool:
        """Check if this is hold tracking mode."""
        return self.tracking_mode == "hold"

    @property
    def is_reps_mode(self) -> bool:
        """Check if this is reps tracking mode."""
        return self.tracking_mode == "reps"

    @property
    def min_classifications_required(self) -> int:
        """Get minimum number of classifications required."""
        return 1 if self.is_hold_mode else 2

    def validate(self) -> None:
        """Validate the configuration.

        Raises:
            ValueError: If configuration is invalid.
        """
        if not self.exercise_id:
            msg = "Exercise ID cannot be empty"
            raise ValueError(msg)

        if len(self.classifications) < self.min_classifications_required:
            msg = f"Need at least {self.min_classifications_required} classifications for {self.tracking_mode} mode"
            raise ValueError(msg)

        if self.debounce_threshold < 0:
            msg = "Debounce threshold must be non-negative"
            raise ValueError(msg)
