"""Speech service for managing text-to-speech functionality."""

from __future__ import annotations

from dataclasses import dataclass, field
from typing import TYPE_CHECKING, Self

from mobirobot.common.result_types import Err, Ok, Result
from mobirobot.common.structured_logging import LoggerType, get_logger

from .config import SpeechConfig
from .formatting import format_sentence_template
from .sentence_randomizer import SentenceRandomizer
from .sentence_repository import (
    get_matching_sentences,
    load_sentences_for_station_with_base_logic,
)
from .tag_builders import (
    build_in_exercise_query_tags,
    build_simple_query_tags,
)
from .tags import CategoryValue, EventTypeValue, ExerciseTypeValue, QueryTags

if TYPE_CHECKING:
    from mobirobot.models import Station
    from mobirobot.models.exercise import BaseExercise

    from .formatting import FormattingKwargs
    from .models import InExerciseConfig, IntroConfig
    from .protocols import SpeechProviderProtocol

logger: LoggerType = get_logger(__name__)


@dataclass
class SpeechService:
    """Service for speech synthesis and sentence selection based on context tags.

    Station context is provided per-call to speech methods.
    """

    _provider: SpeechProviderProtocol
    _config: SpeechConfig = field(default_factory=SpeechConfig)
    _sentence_randomizer: SentenceRandomizer = field(
        default_factory=SentenceRandomizer.create
    )

    @classmethod
    async def create(
        cls,
        provider: SpeechProviderProtocol,
        config: SpeechConfig | None = None,
        sentence_randomizer: SentenceRandomizer | None = None,
    ) -> Self:
        """Asynchronously create and configure a new SpeechService instance."""
        logger.info("Creating SpeechService")

        final_config = config or SpeechConfig()
        final_randomizer = sentence_randomizer or SentenceRandomizer.create()

        instance = cls(
            _provider=provider,
            _config=final_config,
            _sentence_randomizer=final_randomizer,
        )
        logger.debug("Configuring speech provider parameters")
        await instance._configure_provider()

        logger.info("SpeechService created and configured successfully")
        return instance

    async def _configure_provider(self) -> None:
        """Configure speech provider with current settings."""
        await self._provider.set_parameter("pitchShift", self._config.pitch_shift)
        await self._provider.set_parameter("speed", self._config.speed)
        await self._provider.set_volume(self._config.volume)

    async def _say_with_tags(
        self,
        station: Station,
        query_tags: QueryTags,
        probability: float | None = None,
        use_animated_say: bool = False,
        **kwargs: object,
    ) -> Result[None, str]:
        """Load sentences for the station, get a template, and say it."""
        # Get the sentence template first
        template_result = await self._get_sentence_template(
            station, query_tags, probability
        )

        # Chain the operations: template -> format -> say
        # Using match for async operations since and_then doesn't work well with async
        match template_result:
            case Ok(sentence_template):
                return await self._format_and_say_sentence(
                    sentence_template, kwargs, use_animated_say
                )
            case Err(error_msg):
                # Error already logged in helper methods
                return Err(error_msg)

    async def _get_sentence_template(
        self,
        station: Station,
        query_tags: QueryTags,
        probability: float | None,
    ) -> Result[str, str]:
        """Load sentences and get a template."""
        # Load sentences
        sentences_result = load_sentences_for_station_with_base_logic(station)
        if isinstance(sentences_result, Err):
            return Err(sentences_result.err_value)

        all_sentences = sentences_result.ok_value
        if not all_sentences:
            return Err("No sentences loaded")

        # Get matching sentences
        matching_sentences = get_matching_sentences(all_sentences, query_tags)
        if not matching_sentences:
            logger.debug(
                "No matching sentences found for station %s with tags: %s",
                station_name=station.value,
                tags=dict(query_tags),
            )
            return Err("No matching sentences")

        # Get template with or without probability
        if probability is not None:
            result = self._sentence_randomizer.maybe_get_sentence_template(
                query_tags, matching_sentences, probability
            )
            return (
                result
                if isinstance(result, Err)
                else (
                    Ok(result.ok_value)
                    if result.ok_value
                    else Err("Probability check failed")
                )
            )

        return self._sentence_randomizer.get_sentence_template(
            query_tags, matching_sentences
        )

    async def _format_and_say_sentence(
        self,
        sentence_template: str,
        kwargs: FormattingKwargs,
        use_animated_say: bool = False,
    ) -> Result[None, str]:
        """Format and say a sentence template."""
        # Format the template
        format_result = format_sentence_template(sentence_template, kwargs)
        if isinstance(format_result, Err):
            logger.warning(
                "Failed to format sentence template: %s", format_result.err_value
            )
            return Err(format_result.err_value)

        formatted_sentence = format_result.ok_value
        logger.debug(
            "Speaking: %s (animated=%s)",
            sentence=formatted_sentence[:50] + "..."
            if len(formatted_sentence) > 50
            else formatted_sentence,
            animated=use_animated_say,
        )

        # Say the formatted sentence - let exceptions bubble up as they represent system errors
        try:
            if use_animated_say:
                await self._provider.animated_say(
                    formatted_sentence,
                    {"bodyLanguageMode": self._config.body_language_mode},
                )
            else:
                await self._provider.say(formatted_sentence)
            return Ok(None)
        except RuntimeError as e:
            logger.exception("Speech synthesis failed")
            return Err(f"Speech synthesis failed: {e}")

    # Public methods for different speech scenarios
    async def in_exercise(
        self,
        station: Station,
        exercise: BaseExercise,
        config: InExerciseConfig,
    ) -> Result[None, str]:
        """Handle in-exercise speech logic."""
        dont_talk_threshold = config.exercise_duration * (
            1 - config.dont_talk_until_percentage
        )
        if config.remaining_time > dont_talk_threshold:
            logger.debug(
                "Skipping in-exercise speech: remaining_time=%.1fs > threshold=%.1fs",
                remaining_time=config.remaining_time,
                threshold=dont_talk_threshold,
            )
            return Ok(None)  # Early return as success, just no speech

        query_tags = build_in_exercise_query_tags(exercise, config)
        formatting_kwargs = config.get_formatting_kwargs()

        logger.debug(
            "In-exercise speech for %s: remaining_time=%.1fs, probability=%.2f",
            exercise_name=exercise.name,
            remaining_time=config.remaining_time,
            probability=config.probability,
        )

        return await self._say_with_tags(
            station,
            query_tags,
            probability=config.probability,
            use_animated_say=False,
            **formatting_kwargs,
        )

    async def after_exercise(
        self, station: Station, exercise_type_enum: ExerciseTypeValue
    ) -> Result[None, str]:
        """Say something after an exercise."""
        logger.debug(
            "After-exercise speech for station %s, type %s",
            station_name=station.value,
            exercise_type=exercise_type_enum.value,
        )
        query_tags = build_simple_query_tags(
            CategoryValue.AFTER_EXERCISE, exercise_type=exercise_type_enum
        )
        return await self._say_with_tags(
            station, query_tags, probability=None, use_animated_say=False
        )

    async def announce_exercise(
        self, station: Station, next_exercise: BaseExercise
    ) -> Result[None, str]:
        """Announce the next exercise."""
        logger.debug(
            "Announcing exercise: %s for station %s",
            exercise_name=next_exercise.name,
            station_name=station.value,
        )
        query_tags = build_simple_query_tags(CategoryValue.ANNOUNCE_EXERCISE)
        return await self._say_with_tags(
            station,
            query_tags,
            probability=None,
            use_animated_say=False,
            exercise_name=next_exercise.name,
        )

    async def short_break(
        self,
        station: Station,
        break_duration: int,
        next_exercise: BaseExercise | None = None,
    ) -> Result[None, str]:
        """Say something during a short break."""
        logger.debug(
            "Short break speech: station=%s, duration=%ds, next_exercise=%s",
            station_name=station.value,
            break_duration=break_duration,
            next_exercise_name=next_exercise.name if next_exercise else "None",
        )
        query_tags = build_simple_query_tags(CategoryValue.SHORT_BREAK)
        kwargs: FormattingKwargs = {"break_duration": break_duration}
        if next_exercise:
            kwargs["next_exercise_name"] = next_exercise.name
        return await self._say_with_tags(
            station, query_tags, probability=None, use_animated_say=False, **kwargs
        )

    async def long_break(self, station: Station, iteration: int) -> Result[None, str]:
        """Say something during a long break."""
        logger.debug(
            "Long break speech: station=%s, iteration=%d", station.value, iteration
        )
        query_tags = build_simple_query_tags(CategoryValue.LONG_BREAK)
        return await self._say_with_tags(
            station,
            query_tags,
            probability=None,
            use_animated_say=False,
            completed_rounds=iteration,
        )

    async def in_break(
        self, station: Station, probability: float = 0.1
    ) -> Result[None, str]:
        """Say something during a break, with a probability."""
        logger.debug(
            "In-break speech: station=%s, probability=%.2f", station.value, probability
        )
        query_tags = build_simple_query_tags(CategoryValue.IN_BREAK)
        return await self._say_with_tags(
            station, query_tags, probability=probability, use_animated_say=True
        )

    async def kc_break(self, station: Station) -> Result[None, str]:
        """Say something during a KC break."""
        logger.debug("KC break speech for station %s", station.value)
        query_tags = build_simple_query_tags(CategoryValue.KC_BREAK)
        return await self._say_with_tags(
            station, query_tags, probability=None, use_animated_say=False
        )

    async def fallen(self, station: Station) -> Result[None, str]:
        """Say something if the robot has fallen."""
        logger.info("Robot fallen event for station %s", station.value)
        query_tags = build_simple_query_tags(
            CategoryValue.SYSTEM_EVENT, event_type=EventTypeValue.FALLEN
        )
        return await self._say_with_tags(
            station, query_tags, probability=None, use_animated_say=False
        )

    async def robot_cant_move(self, station: Station) -> Result[None, str]:
        """Say something if the robot can't move."""
        logger.warning("Robot can't move event for station %s", station.value)
        query_tags = build_simple_query_tags(
            CategoryValue.SYSTEM_EVENT, event_type=EventTypeValue.CANT_MOVE
        )
        return await self._say_with_tags(
            station, query_tags, probability=None, use_animated_say=False
        )

    async def outro(
        self,
        station: Station,
        exercise_type_enum: ExerciseTypeValue = ExerciseTypeValue.MOVE,
    ) -> Result[None, str]:
        """Say an outro sentence."""
        logger.debug(
            "Outro speech: station=%s, type=%s", station.value, exercise_type_enum.value
        )
        query_tags = build_simple_query_tags(
            CategoryValue.OUTRO, exercise_type=exercise_type_enum
        )
        return await self._say_with_tags(
            station, query_tags, probability=None, use_animated_say=True
        )

    async def say_intro(
        self, station: Station, intro_config: IntroConfig
    ) -> Result[None, str]:
        """Say an intro sentence."""
        logger.info(
            "Intro speech: station=%s, exercise_type=%s, duration=%ds, iterations=%d",
            station_name=station.value,
            exercise_type=intro_config.exercise_type.value,
            duration=intro_config.exercise_duration,
            iterations=intro_config.iteration_count,
        )
        query_tags = build_simple_query_tags(
            CategoryValue.INTRO, exercise_type=intro_config.exercise_type
        )
        kwargs: FormattingKwargs = {
            "exercise_duration": intro_config.exercise_duration,
            "break_duration": intro_config.break_duration,
            "iteration_count": intro_config.iteration_count,
            "exercise_count": intro_config.exercise_count,
        }
        return await self._say_with_tags(
            station, query_tags, probability=None, use_animated_say=True, **kwargs
        )

    async def explain(
        self,
        station: Station,
        text_to_explain: str,
        is_explanation_dynamic: bool = True,
        **kwargs_for_template: object,
    ) -> Result[None, str]:
        """Explain something. Can use a template or say dynamic text with modified speed."""
        if is_explanation_dynamic:
            logger.debug(
                "Explaining dynamic text for station %s (length=%d chars)",
                station_name=station.value,
                length=len(text_to_explain),
            )
            current_speed = self._config.speed
            try:
                await self._provider.set_parameter("speed", 80)
                await self._provider.say(text_to_explain)
                return Ok(None)
            except RuntimeError as e:
                logger.exception("Dynamic explanation failed")
                return Err(f"Dynamic explanation failed: {e}")
            finally:
                await self._provider.set_parameter("speed", current_speed)

        logger.debug("Explaining using template for station %s", station.value)
        query_tags = build_simple_query_tags(CategoryValue.EXPLAIN)
        final_kwargs: FormattingKwargs = {
            "explanation_content": text_to_explain,
            **kwargs_for_template,
        }
        return await self._say_with_tags(
            station,
            query_tags,
            probability=None,
            use_animated_say=False,
            **final_kwargs,
        )

    async def say_something(self, station: Station, text: str) -> Result[None, str]:
        """Say the given text using the provider (station context for logging/future use)."""
        logger.debug(
            "General speech for station %s (length=%d chars): %s...",
            station_name=station.value,
            length=len(text),
            text=text[:50],
        )
        try:
            await self._provider.say(text)
            return Ok(None)
        except RuntimeError as e:
            logger.exception("General speech failed")
            return Err(f"Speech failed: {e}")

    async def explain_demo(
        self,
        station: Station,
        explanation_text_or_key: str,
        is_dynamic: bool = True,
        **kwargs_for_template: object,
    ) -> Result[None, str]:
        """Explain a demo. Can use a template or say dynamic text."""
        if is_dynamic:
            has_bookmarks = "\\mrk" in explanation_text_or_key
            logger.debug(
                "Demo explanation for station %s (length=%d chars, bookmarks=%s)",
                station_name=station.value,
                length=len(explanation_text_or_key),
                bookmarks=has_bookmarks,
            )

            try:
                # Use regular TTS for bookmark support instead of animated speech
                await self._provider.say(explanation_text_or_key)
                return Ok(None)
            except RuntimeError as e:
                logger.exception("Demo explanation failed")
                return Err(f"Demo explanation failed: {e}")

        logger.debug("Demo explanation using template for station %s", station.value)
        query_tags = build_simple_query_tags(CategoryValue.EXPLAIN_DEMO)
        final_kwargs: FormattingKwargs = {
            "demo_explanation": explanation_text_or_key,
            **kwargs_for_template,
        }
        return await self._say_with_tags(
            station,
            query_tags,
            probability=None,
            use_animated_say=False,
            **final_kwargs,
        )
