"""Template formatting utilities for the speech module."""

from mobirobot.common.result_types import Err, Ok, Result

FormattingKwargs = dict[str, object]


def format_sentence_template(
    sentence_template: str,
    kwargs: FormattingKwargs,
) -> Result[str, str]:
    """Format a sentence template with kwargs."""
    if not kwargs or "{" not in sentence_template:
        return Ok(sentence_template)

    try:
        formatted = sentence_template.format(**kwargs)
        return Ok(formatted)
    except KeyError as e:
        return Err(f"Failed to format template: missing key {e}")
