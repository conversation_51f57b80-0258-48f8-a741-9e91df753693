"""Configuration classes for the speech module."""

from typing import Annotated

from pydantic import BaseModel, Field


class SpeechConfig(BaseModel):
    """Configuration for speech system."""

    pitch_shift: Annotated[float, Field(default=1.1, gt=0, lt=2)] = 1.1
    speed: Annotated[float, Field(default=90.0, gt=0, lt=200)] = 90.0
    volume: Annotated[float, Field(default=0.5, gt=0, le=1.0)] = 0.5
    body_language_mode: Annotated[str, Field(default="disabled")] = "disabled"
