"""Speech module for text-to-speech functionality and sentence management."""

from .config import SpeechConfig
from .formatting import FormattingKwargs, format_sentence_template
from .models import InExerciseConfig, IntroConfig, Sentence
from .protocols import SpeechProviderProtocol, temporary_provider_param
from .service import SpeechService
from .tag_builders import (
    build_in_exercise_query_tags,
    build_simple_query_tags,
    mobirobot_exercise_type_to_enum,
)
from .tags import (
    CategoryValue,
    EventTypeValue,
    ExerciseTypeValue,
    QueryTags,
)

# Backward compatibility alias
SpeechController = SpeechService

__all__ = [
    # Tag enums and types
    "CategoryValue",
    "EventTypeValue",
    "ExerciseTypeValue",
    "FormattingKwargs",
    # Core classes
    "InExerciseConfig",
    "IntroConfig",
    "QueryTags",
    "Sentence",
    "SpeechConfig",
    "SpeechController",  # Backward compatibility
    "SpeechProviderProtocol",
    "SpeechService",
    # Utility functions
    "build_in_exercise_query_tags",
    "build_simple_query_tags",
    "format_sentence_template",
    "mobirobot_exercise_type_to_enum",
    "temporary_provider_param",
]
