"""Data models and configuration classes for the speech module."""

from dataclasses import dataclass
from typing import Ann<PERSON><PERSON>, ClassVar

from pydantic import BaseModel, ConfigDict, Field

from mobirobot.common.result_types import Err, Ok
from mobirobot.common.structured_logging import LoggerType, get_logger

from .formatting import FormattingKwargs
from .tags import ExerciseTypeValue, SpecificTagValue, <PERSON><PERSON>ey, parse_tag_string

logger: LoggerType = get_logger(__name__)


@dataclass(frozen=True)
class IntroConfig:
    """Configuration for the intro sentence parameters."""

    exercise_type: ExerciseTypeValue
    exercise_duration: int
    break_duration: int
    iteration_count: int
    exercise_count: int


@dataclass(frozen=True)
class InExerciseConfig:
    """Configuration for in-exercise speech parameters."""

    exercise_duration: int
    exercise_type_enum: ExerciseTypeValue
    remaining_time: int
    hold_for: int | None = None
    reps_done: int | None = None
    remaining_reps: int | None = None
    dont_talk_until_percentage: float = 0.16
    probability: float = 0.1
    interactive: bool = False
    end_duration: int = 10

    def get_formatting_kwargs(self) -> FormattingKwargs:
        """Build formatting kwargs for in-exercise speech."""
        kwargs: dict[str, object] = {}
        if self.remaining_reps and self.remaining_reps > 1:
            kwargs["reps"] = self.remaining_reps
        if self.remaining_time > 0:
            kwargs["duration"] = self.remaining_time
        if self.hold_for and self.hold_for > 0:
            kwargs["hold_duration"] = self.hold_for
        return kwargs


class Sentence(BaseModel):
    """Represents a sentence with its template and associated tags."""

    model_config: ClassVar[ConfigDict] = ConfigDict(frozen=True)

    template: str
    tags_raw: Annotated[list[str], Field(default_factory=list, alias="tags")]
    sentence_id: Annotated[str | None, Field(default=None)]

    @property
    def tags(self) -> frozenset[tuple[TagKey, SpecificTagValue]]:
        """Parse and return the tags as enum tuples."""
        parsed_tags: set[tuple[TagKey, SpecificTagValue]] = set()
        for tag_str in self.tags_raw:
            match parse_tag_string(tag_str):
                case Ok(tag_tuple):
                    parsed_tags.add(tag_tuple)
                case Err(error_msg):
                    logger.debug(
                        "Unparseable tag '%s' in sentence '%s': %s",
                        tag=tag_str,
                        sentence=self.sentence_id or self.template[:30],
                        error=error_msg,
                    )
        return frozenset(parsed_tags)
