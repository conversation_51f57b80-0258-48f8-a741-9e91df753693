"""Tag definitions and parsing utilities for the speech module."""

from enum import Enum
from typing import cast

from mobirobot.common.result_types import Err, Ok, Result

# --- Constants ---
KEY_VALUE_SPLIT_PARTS: int = 2


# --- Tag Enum Definitions ---
class TagKey(Enum):
    """A tag is a key-value pair. This enum defines the keys."""

    CATEGORY = "category"
    EXERCISE_TYPE = "exercise_type"
    INTENT = "intent"
    PHASE = "phase"
    EVENT_TYPE = "event_type"
    TIMING = "timing"


class CategoryValue(Enum):
    """A value for the Category tag."""

    INTRO = "intro"
    OUTRO = "outro"
    ANNOUNCE_EXERCISE = "announce_exercise"
    IN_EXERCISE = "in_exercise"
    AFTER_EXERCISE = "after_exercise"
    SHORT_BREAK = "short_break"
    LONG_BREAK = "long_break"
    IN_BREAK = "in_break"
    KC_BREAK = "kc_break"
    SYSTEM_EVENT = "system_event"
    EXPLAIN = "explain"
    EXPLAIN_DEMO = "explain_demo"
    GENERAL_SAY = "general_say"


class ExerciseTypeValue(Enum):
    """A value for the ExerciseType tag."""

    MOVE = "move"
    STRETCH = "stretch"


class IntentValue(Enum):
    """A value for the Intent tag."""

    PRAISE = "praise"
    MOTIVATE = "motivate"
    INFO = "info"


class PhaseValue(Enum):
    """A value for the Phase tag."""

    HOLD = "hold"
    REPS = "reps"
    OTHER = "other"


class EventTypeValue(Enum):
    """A value for the EventType tag."""

    FALLEN = "fallen"
    CANT_MOVE = "cant_move"


class TimingValue(Enum):
    """A value for the Timing tag."""

    START = "start"
    MIDDLE = "middle"
    END = "end"
    COUNTDOWN = "countdown"


# --- Type Definitions ---
SpecificTagValue = (
    CategoryValue
    | ExerciseTypeValue
    | IntentValue
    | PhaseValue
    | EventTypeValue
    | TimingValue
)

TagTuple = tuple[TagKey, SpecificTagValue]
TagTupleSet = set[TagTuple]
TagTupleFrozenSet = frozenset[TagTuple]
QueryTags = set[TagTuple]

TAG_KEY_TO_VALUE_ENUM: dict[TagKey, type[Enum]] = {
    TagKey.CATEGORY: CategoryValue,
    TagKey.EXERCISE_TYPE: ExerciseTypeValue,
    TagKey.INTENT: IntentValue,
    TagKey.PHASE: PhaseValue,
    TagKey.EVENT_TYPE: EventTypeValue,
    TagKey.TIMING: TimingValue,
}


# --- Tag Parsing Functions ---
def parse_tag_string(tag_str: str) -> Result[TagTuple, str]:
    """Parse a 'KEY:VALUE' string into a (TagKey, SpecificTagValue Enum member) tuple."""
    parts = tag_str.split(":", 1)
    if len(parts) != KEY_VALUE_SPLIT_PARTS:
        return Err(f"Tag string '{tag_str}' is not in 'KEY:VALUE' format.")

    key_str, value_str = parts[0].strip().upper(), parts[1].strip().upper()

    # Parse tag key with case-insensitive fallback
    try:
        tag_key = TagKey[key_str]
    except KeyError:
        for k in TagKey:
            if k.name.lower() == key_str.lower():
                tag_key = k
                break
        else:
            return Err(f"Invalid tag key '{key_str}' in tag string '{tag_str}'")

    # Parse tag value with case-insensitive fallback
    value_enum_type = TAG_KEY_TO_VALUE_ENUM.get(tag_key)
    if not value_enum_type:
        return Err(f"No value Enum defined for TagKey.{tag_key.name}")

    try:
        tag_value = cast("SpecificTagValue", value_enum_type[value_str])
    except KeyError:
        for v in value_enum_type:
            if (
                v.name.lower() == value_str.lower()
                or v.value.lower() == value_str.lower()  # pyright: ignore[reportAny]
            ):
                tag_value = cast("SpecificTagValue", v)
                break
        else:
            return Err(f"Invalid tag value '{value_str}' for key '{tag_key.name}'")

    return Ok((tag_key, tag_value))
