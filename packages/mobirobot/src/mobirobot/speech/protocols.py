"""Protocol definitions for the speech module."""

import asyncio
from collections.abc import Iterator
from contextlib import contextmanager
from typing import Protocol


class SpeechProviderProtocol(Protocol):
    """Protocol for the speech provider."""

    async def say(self, string_to_say: str) -> None:
        """Make the robot say the given text."""

    async def set_parameter(self, p_effect_name: str, p_effect_value: float) -> None:
        """Set a speech parameter (e.g., pitchShift, speed)."""

    async def set_volume(self, volume: float) -> None:
        """Set the speech volume."""

    async def animated_say(
        self, text: str, configuration: dict[str, str | float]
    ) -> None:
        """Say text with animations based on the configuration."""

    async def set_language(self, language: str) -> None:
        """Set the speech language."""


@contextmanager
def temporary_provider_param(
    provider: SpeechProviderProtocol, param: str, value: float
) -> Iterator[None]:
    """Temporarily set a provider parameter for the context.

    Note: Restoration is not supported (provider has no get_parameter).

    Args:
        provider: The speech provider to set the parameter for.
        param: The parameter to set.
        value: The value to set the parameter to.

    Yields:
        None
    """
    asyncio.run(provider.set_parameter(param, value))
    yield
