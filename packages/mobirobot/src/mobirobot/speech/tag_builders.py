"""Tag building utilities for the speech module."""

from mobirobot.common.result_types import <PERSON><PERSON>, Ok, Result
from mobirobot.models.common import ExerciseType
from mobirobot.models.exercise import BaseExercise

from .models import InExerciseConfig
from .tags import (
    CategoryValue,
    EventTypeValue,
    ExerciseTypeValue,
    IntentValue,
    PhaseValue,
    QueryTags,
    TagKey,
    TimingValue,
)


def build_simple_query_tags(
    category: CategoryValue,
    exercise_type: ExerciseTypeValue | None = None,
    event_type: EventTypeValue | None = None,
) -> QueryTags:
    """Build simple query tags with category and optional exercise/event type."""
    query_tags: QueryTags = {(TagKey.CATEGORY, category)}

    if exercise_type is not None:
        query_tags.add((TagKey.EXERCISE_TYPE, exercise_type))

    if event_type is not None:
        query_tags.add((TagKey.EVENT_TYPE, event_type))

    return query_tags


def mobirobot_exercise_type_to_enum(
    mobirobot_ex_type: ExerciseType,
) -> Result[ExerciseTypeValue, str]:
    """Convert mobirobot.ExerciseType to local ExerciseTypeValue Enum."""
    try:
        return Ok(ExerciseTypeValue[mobirobot_ex_type.value.upper()])
    except KeyError:
        return Err(f"Could not map ExerciseType '{mobirobot_ex_type.value}'")


def build_in_exercise_query_tags(
    exercise: BaseExercise, config: InExerciseConfig
) -> QueryTags:
    """Build query tags for in-exercise speech."""
    query_tags: QueryTags = {
        (TagKey.CATEGORY, CategoryValue.IN_EXERCISE),
        (TagKey.EXERCISE_TYPE, config.exercise_type_enum),
    }

    if config.exercise_type_enum != ExerciseTypeValue.STRETCH:
        current_hold_value = False
        if exercise.model_processing:
            current_hold_value = exercise.model_processing.hold
        phase_value = PhaseValue.HOLD if current_hold_value else PhaseValue.REPS
    else:
        phase_value = PhaseValue.OTHER
    query_tags.add((TagKey.PHASE, phase_value))

    should_praise = False

    current_model_files_value = None
    if exercise.model_processing:
        current_model_files_value = exercise.model_processing.model_files

    if config.interactive and current_model_files_value:
        hold_condition = (
            phase_value == PhaseValue.HOLD and config.hold_for and config.hold_for > 0
        )
        reps_condition = (
            phase_value == PhaseValue.REPS and config.reps_done and config.reps_done > 0
        )
        should_praise = hold_condition or reps_condition
    else:
        hold_condition = config.hold_for and config.hold_for > 0
        reps_condition = config.reps_done and config.reps_done > 0
        should_praise = hold_condition or reps_condition

    intent_value = IntentValue.PRAISE if should_praise else IntentValue.MOTIVATE
    query_tags.add((TagKey.INTENT, intent_value))

    if (
        config.exercise_type_enum != ExerciseTypeValue.STRETCH
        and config.remaining_time < config.end_duration
    ):
        query_tags.add((TagKey.TIMING, TimingValue.END))

    return query_tags
