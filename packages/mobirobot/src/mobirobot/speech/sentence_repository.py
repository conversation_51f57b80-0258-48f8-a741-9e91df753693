"""Sentence repository for loading and managing sentences from data files."""

from pydantic import TypeAdapter, ValidationError

from mobirobot.common.config import settings
from mobirobot.common.result_types import Err, Ok, Result
from mobirobot.common.structured_logging import LoggerType, get_logger
from mobirobot.models.station import Station

from .models import Sentence
from .tags import SpecificTagValue, TagKey

logger: LoggerType = get_logger(__name__)


def load_sentences_from_file(station: Station) -> Result[list[Sentence], str]:
    """Load sentences from a JSON file for a given station.

    Args:
        station: The station to load sentences for

    Returns:
        Result containing list of sentences or error message

    Raises:
        No exceptions are raised; all errors are returned as Result[T, str]
    """
    json_path = settings.sentences_dir / f"{station.value.lower()}.json"

    if not json_path.exists():
        logger.warning("No sentence file found for station", station_name=station.value)
        return Ok([])

    try:
        sentences = TypeAdapter(list[Sentence]).validate_json(
            json_path.read_text(), strict=True
        )
        logger.debug(
            "Loaded sentences from file",
            total_sentences=len(sentences),
            file_path=str(json_path),
        )
        return Ok(sentences)
    except ValidationError as e:
        logger.exception("Validation error in file", file_path=str(json_path))
        return Err(f"Validation error in {json_path}: {e}")


def get_matching_sentences(
    all_sentences_for_station: list[Sentence],
    query_tags: set[tuple[TagKey, SpecificTagValue]],
) -> list[Sentence]:
    """Get sentences whose tags are a superset of the query_tags from a given list.

    Args:
        all_sentences_for_station: List of all available sentences for the station
        query_tags: Set of tag tuples to match against

    Returns:
        List of sentences that match the query tags
    """
    if not query_tags:
        logger.debug("No query tags provided, returning empty list")
        return []

    if not all_sentences_for_station:
        logger.debug("No sentences available for matching")
        return []

    matching_sentences = [
        sentence
        for sentence in all_sentences_for_station
        if query_tags.issubset(sentence.tags)
    ]

    logger.debug(
        "Found matching sentences",
        matching_sentences=len(matching_sentences),
        total_sentences=len(all_sentences_for_station),
        query_tags=query_tags,
    )

    return matching_sentences


def consolidate_sentences(
    base_sentences: list[Sentence], station_sentences: list[Sentence]
) -> list[Sentence]:
    """Consolidate base and station sentences, with station sentences overriding base ones.

    Args:
        base_sentences: Sentences from the base station
        station_sentences: Sentences from the specific station

    Returns:
        Consolidated list of sentences with station sentences taking precedence
    """
    sentences_by_id: dict[str, Sentence] = {}

    # Add all sentences, with station sentences overriding base ones
    for sentences, prefix in [(base_sentences, "base"), (station_sentences, "station")]:
        for sentence in sentences:
            key = sentence.sentence_id or f"__{prefix}_{id(sentence)}"
            sentences_by_id[key] = sentence

    consolidated = list(sentences_by_id.values())
    logger.debug(
        "Consolidated sentences",
        base_sentences=len(base_sentences),
        station_sentences=len(station_sentences),
        total_sentences=len(consolidated),
    )

    return consolidated


def load_sentences_for_station_with_base_logic(
    station: Station,
) -> Result[list[Sentence], str]:
    """Load sentences for a station, handling base station inheritance.

    Args:
        station: The station to load sentences for

    Returns:
        Result containing consolidated sentences or error message

    Raises:
        No exceptions are raised; all errors are returned as Result[T, str]
    """
    # Load base sentences recursively if base station exists
    base_result = (
        load_sentences_for_station_with_base_logic(station.base_station)
        if station.base_station
        else Ok([])
    )

    # Load station-specific sentences
    station_result = load_sentences_from_file(station)

    # Handle results with simplified logic
    match (base_result, station_result):
        case (Ok(base_sentences), Ok(station_sentences)):
            final_sentences = consolidate_sentences(base_sentences, station_sentences)
            logger.info(
                "Loaded sentences for station",
                total_sentences=len(final_sentences),
                station_name=station.value,
                base_sentences=len(base_sentences),
                station_sentences=len(station_sentences),
            )
            return Ok(final_sentences)
        case (Err(_), Ok(station_sentences)):
            logger.warning("Using only station sentences", station_name=station.value)
            return Ok(station_sentences)
        case (Ok(_), Err(station_error)):
            return Err(
                f"Failed to load sentences for station {station.value}: {station_error}"
            )
        case (Err(base_error), Err(station_error)):
            return Err(
                f"Failed to load sentences for {station.value}: base={base_error}, station={station_error}"
            )
