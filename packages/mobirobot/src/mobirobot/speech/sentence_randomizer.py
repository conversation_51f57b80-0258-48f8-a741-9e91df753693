import random
from dataclasses import dataclass, field
from typing import Protocol, Self

from mobirobot.common.result_types import <PERSON><PERSON>, Ok, Result
from mobirobot.common.structured_logging import LoggerType, get_logger

from .models import Sentence
from .tags import SpecificTagValue, TagKey

logger: LoggerType = get_logger(__name__)

# --- Type Aliases ---
TagTuple = tuple[TagKey, SpecificTagValue]
type UsedSentencesKey = frozenset[TagTuple]


class RandomGenerator(Protocol):
    """Protocol defining an interface for random number generation."""

    def random(self) -> float:
        """Return a random float between 0.0 and 1.0."""
        ...

    def choice(self, seq: list[Sentence]) -> Sentence:
        """Return a random element from a sequence of Sentences."""
        ...


@dataclass
class SentenceRandomizer:
    """Provides randomized sentence templates based on tags, avoiding immediate repetition."""

    _random_generator: RandomGenerator = field(default_factory=lambda: random)
    _used_sentences: dict[UsedSentencesKey, list[str]] = field(default_factory=dict)

    @classmethod
    def create(
        cls,
        random_generator: RandomGenerator | None = None,
        initial_used_sentences: dict[UsedSentencesKey, list[str]] | None = None,
    ) -> Self:
        """Creates a SentenceRandomizer instance."""
        return cls(
            _random_generator=random_generator or random,
            _used_sentences=initial_used_sentences or {},
        )

    @staticmethod
    def _get_tags_key(query_tags: set[TagTuple]) -> UsedSentencesKey:
        """Convert a set of query_tags to a hashable key."""
        return frozenset(query_tags)

    def _get_used_sentence_templates(self, tags_key: UsedSentencesKey) -> list[str]:
        """Get the list of used sentence templates for a given tags_key."""
        return self._used_sentences.setdefault(tags_key, [])

    def _add_used_sentence_template(
        self,
        tags_key: UsedSentencesKey,
        sentence_template: str,
        all_templates: list[str],
    ) -> None:
        """Add a sentence template to the used sentences tracking."""
        used_templates = self._get_used_sentence_templates(tags_key)

        # Reset if all templates have been used
        if len(used_templates) >= len(all_templates):
            used_templates.clear()

        # Add template if not already used
        if sentence_template not in used_templates:
            used_templates.append(sentence_template)

    def reset_used_sentences(self):
        """Resets all used sentences tracking. For testing purposes only."""
        self._used_sentences = {}

    def get_used_sentences_map(self) -> dict[UsedSentencesKey, list[str]]:
        """Returns the entire map of used sentences. For testing purposes only."""
        return self._used_sentences

    def get_sentence_template(
        self,
        query_tags: set[TagTuple],
        available_matching_sentences: list[Sentence],
    ) -> Result[str, str]:
        """Returns a random sentence template from the provided list, avoiding repetition."""
        if not query_tags or not available_matching_sentences:
            return Err("Query tags and available sentences cannot be empty")

        tags_key = self._get_tags_key(query_tags)
        all_templates = [s.template for s in available_matching_sentences]
        used_templates = self._get_used_sentence_templates(tags_key)

        # Get unused sentences
        unused_sentences = [
            s for s in available_matching_sentences if s.template not in used_templates
        ]

        # Reset if all have been used
        if not unused_sentences:
            used_templates.clear()
            unused_sentences = available_matching_sentences

        try:
            selected_sentence = self._random_generator.choice(unused_sentences)
            self._add_used_sentence_template(
                tags_key, selected_sentence.template, all_templates
            )
            return Ok(selected_sentence.template)
        except (IndexError, ValueError) as e:
            return Err(f"Error selecting sentence: {e}")

    def maybe_get_sentence_template(
        self,
        query_tags: set[TagTuple],
        available_matching_sentences: list[Sentence],
        probability: float = 0.5,
    ) -> Result[str | None, str]:
        """Returns a sentence template with probability, decreasing as more sentences are used."""
        if not query_tags:
            return Err("Query tags cannot be empty")
        if not 0 <= probability <= 1:
            return Err(f"Probability {probability} must be between 0 and 1")
        if not available_matching_sentences:
            return Ok(None)

        tags_key = self._get_tags_key(query_tags)
        used_count = len(self._get_used_sentence_templates(tags_key))
        adjusted_probability = probability / (used_count + 1)

        if self._random_generator.random() <= adjusted_probability:
            result = self.get_sentence_template(
                query_tags, available_matching_sentences
            )
            return result if isinstance(result, Err) else Ok(result.ok_value)
        return Ok(None)
