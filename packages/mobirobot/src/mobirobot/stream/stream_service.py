"""Stream service for video processing and WebSocket streaming using WebcamService."""

import asyncio
import base64
import zoneinfo
from collections.abc import Awaitable
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime
from pathlib import Path
from typing import Self

import cv2
import mediapipe.python.solutions.drawing_utils as mp_drawing
import mediapipe.python.solutions.holistic as mp_holistic
from fastapi import WebSocket, status
from mediapipe.python.solutions.pose_connections import POSE_CONNECTIONS

from mobirobot.common import settings
from mobirobot.common.config import StreamSettings
from mobirobot.common.structured_logging import LoggerType, get_logger
from mobirobot.webcam.webcam_service import WebcamService

from .schemas import ClientStreamData

logger: LoggerType = get_logger(__name__)

# Constants that are not part of StreamSettings or are specific to drawing
POSE_LANDMARK_DRAWING_SPEC = mp_drawing.DrawingSpec(
    color=(245, 117, 66), thickness=2, circle_radius=4
)
POSE_CONNECTION_DRAWING_SPEC = mp_drawing.DrawingSpec(
    color=(245, 66, 230), thickness=2, circle_radius=2
)


class StreamService:
    """Stream service for handling video capture and processing using WebcamService."""

    def __init__(
        self, webcam_service: WebcamService, config: StreamSettings | None = None
    ):
        """Initialize the StreamService.

        Args:
            webcam_service: The webcam service to use for frame capture.
            config: The stream settings.
        """
        self.settings: StreamSettings = config or settings.stream_settings
        self.webcam_service: WebcamService = webcam_service
        self._frame_queue: asyncio.Queue[cv2.typing.MatLike] | None = None

        self.detector: mp_holistic.Holistic = mp_holistic.Holistic(
            min_detection_confidence=self.settings.min_detection_confidence,
            min_tracking_confidence=self.settings.min_tracking_confidence,
            model_complexity=self.settings.model_complexity,
            static_image_mode=self.settings.static_image_mode,
        )

        self.size: tuple[int, int] = (
            self.settings.output_size[0],
            self.settings.output_size[1],
        )

        self.recording: cv2.VideoWriter | None = None
        self.pose_processing_enabled: bool = self.settings.pose_processing_on_start

        self.quality: int = self.settings.default_jpeg_quality
        self.executor: ThreadPoolExecutor = ThreadPoolExecutor(max_workers=1)
        self.last_fps: int = self.settings.stream_target_fps

    @classmethod
    async def create(
        cls,
        config: StreamSettings | None = None,
    ) -> Self | None:
        """Create a StreamService object.

        Args:
            config: The stream settings.

        Returns:
            The StreamService object or None if initialization fails.
        """
        stream_settings = config or settings.stream_settings

        # Create and start the webcam service
        webcam_service = WebcamService.create(stream_settings)

        if not await webcam_service.start():
            logger.error("Failed to start webcam service")
            return None

        stream_instance = cls(webcam_service, stream_settings)

        # Setup recording
        try:
            stream_instance.setup_recording()
        except RuntimeError:
            logger.exception("Failed to setup recording for stream")
            await webcam_service.stop()
            return None

        # Subscribe to webcam service
        try:
            frame_queue = await webcam_service.subscribe(max_queue_size=1)
            stream_instance._frame_queue = frame_queue
        except TimeoutError:
            logger.exception("Timeout subscribing to webcam service")
            await webcam_service.stop()
            return None

        return stream_instance

    def setup_recording(self) -> None:
        """Setup the recording.

        Raises:
            RuntimeError: If recording setup fails.
        """
        timestamp = str(datetime.now(tz=zoneinfo.ZoneInfo("UTC"))).split(".")[0]
        path = Path.resolve(Path(f"{self.settings.recording_dir}/{timestamp}.avi"))
        if not Path(path.parent).exists():
            Path(path.parent).mkdir(parents=True, exist_ok=True)

        try:
            self.recording = cv2.VideoWriter(
                str(path),
                cv2.VideoWriter.fourcc(*self.settings.video_fourcc),
                self.settings.stream_target_fps,
                self.size,
            )
        except cv2.error as e:
            logger.exception("Failed to setup video recording", error=str(e))
            msg = "Failed to setup video recording"
            raise RuntimeError(msg) from e

    def _write_recording_frame(self, frame: cv2.typing.MatLike) -> None:
        """Write a frame to the recording.

        Args:
            frame: The frame to write.
        """
        if not self.recording:
            return
        frame = cv2.resize(frame, self.size)
        self.recording.write(frame)

    def process_pose(self, frame: cv2.typing.MatLike) -> cv2.typing.MatLike:
        """Process the pose.

        Args:
            frame: The frame to process.

        Returns:
            The processed frame with pose landmarks drawn.
        """
        temp_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        detections = self.detector.process(temp_frame)
        mp_drawing.draw_landmarks(  # pyright: ignore[reportUnknownMemberType]
            frame,
            detections.pose_landmarks,
            list(POSE_CONNECTIONS),
            POSE_LANDMARK_DRAWING_SPEC,
            POSE_CONNECTION_DRAWING_SPEC,
        )
        return frame

    async def _get_frame(self) -> cv2.typing.MatLike:
        """Gets a frame from webcam service, records it, and optionally processes pose.

        Returns:
            The processed frame.

        Raises:
            RuntimeError: If frame queue is not available or timeout occurs.
        """
        if not self._frame_queue:
            msg = "Frame queue not available"
            raise RuntimeError(msg)

        try:
            # Get frame from webcam service with a timeout
            raw_frame = await asyncio.wait_for(
                self._frame_queue.get(),
                timeout=self.settings.frame_acquisition_timeout,
            )
        except TimeoutError as e:
            msg = "Timeout waiting for frame from webcam service"
            raise RuntimeError(msg) from e

        # Write to recording
        self._write_recording_frame(raw_frame)

        # Process pose if enabled
        if self.pose_processing_enabled:
            # Always use a copy for pose processing to avoid modifying the original frame
            frame_for_pose = raw_frame.copy()
            return self.process_pose(frame_for_pose)

        # If pose processing is not enabled, return the raw frame
        return raw_frame

    def _frame_to_base64(self, frame: cv2.typing.MatLike) -> str:
        """Convert frame to base64 encoded string.

        Args:
            frame: The frame to convert.

        Returns:
            Base64 encoded string of the frame.
        """
        _, buffer = cv2.imencode(
            ".jpg", frame, [cv2.IMWRITE_JPEG_QUALITY, self.quality]
        )
        return base64.b64encode(buffer.tobytes()).decode("utf-8")

    async def _send_handler(self, websocket: WebSocket):
        """Handle sending frames to WebSocket.

        Args:
            websocket: The WebSocket connection.

        Raises:
            RuntimeError: If timeout occurs while waiting for frame from webcam service or WebSocket is not connected.
        """
        try:
            frame = await self._get_frame()
            frame_text = await asyncio.get_event_loop().run_in_executor(
                self.executor, self._frame_to_base64, frame
            )
            await websocket.send_text(frame_text)
        except RuntimeError as e:
            if "Timeout waiting for frame" in str(e):
                logger.exception("Error getting frame for WebSocket")
                await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
            else:
                raise

        await asyncio.sleep(self.settings.websocket_send_interval)

    async def handler(self, websocket: WebSocket):
        """Handle the stream WebSocket connection.

        Args:
            websocket: The WebSocket connection.

        """
        while True:
            await self._send_handler(websocket)
            try:
                data = await asyncio.wait_for(
                    websocket.receive_text(),
                    timeout=self.settings.websocket_receive_timeout,
                )
            except TimeoutError:
                continue

            client_data: ClientStreamData = ClientStreamData.model_validate_json(data)
            client_fps = client_data.fps

            if (
                client_fps < self.last_fps
                or client_fps < self.settings.client_fps_low_threshold
            ) and self.quality > self.settings.min_jpeg_quality:
                self.quality = max(
                    self.settings.min_jpeg_quality,
                    self.quality - self.settings.jpeg_quality_adjust_step,
                )
            elif (
                client_fps > self.last_fps
                or client_fps > self.settings.client_fps_high_threshold
            ) and self.quality < self.settings.max_jpeg_quality:
                self.quality = min(
                    self.settings.max_jpeg_quality,
                    self.quality + self.settings.jpeg_quality_adjust_step,
                )

            self.last_fps = client_fps

    def stop_recording(self):
        """Stop the recording."""
        if self.recording:
            self.recording.release()
            self.recording = None

    async def save_video_callback(self, coroutine: Awaitable[None]):
        """Callback to save video.

        Args:
            coroutine: The coroutine to run.
        """
        try:
            await coroutine
        except RuntimeError:
            logger.exception("Error in coroutine")
        finally:
            if self.recording and self.recording.isOpened():
                self.recording.release()
                logger.info("Recording released")

    async def close(self) -> None:
        """Release resources held by the stream."""
        logger.info("Closing stream and releasing resources.")

        # Unsubscribe from webcam service
        if self._frame_queue and self.webcam_service:
            await self.webcam_service.unsubscribe(self._frame_queue)
            self._frame_queue = None

        # Stop webcam service
        if self.webcam_service:
            await self.webcam_service.stop()

        self.stop_recording()

        self.executor.shutdown(wait=True)
        logger.info("Thread pool executor shut down.")

        if self.detector:
            self.detector.close()
            logger.info("MediaPipe Holistic detector closed.")
