import json
from pathlib import Path
from typing import Self

from pydantic import Type<PERSON><PERSON>pter, ValidationError
from pydantic.dataclasses import dataclass

from mobirobot.common.structured_logging import LoggerType, get_logger
from mobirobot.regiments.regiment import Regiment

logger: LoggerType = get_logger(__name__)


type RegimentDict = dict[int, Regiment]


@dataclass(frozen=True)
class RegimentService:
    """Manages regiment data.

    This class handles loading, saving, and updating regiment data
    in JSON files. It provides methods to create new regiments,
    update existing ones, and retrieve a list of all regiments.
    """

    folder: Path
    regiments: RegimentDict

    @classmethod
    def create(cls, folder: str | Path) -> Self:
        """Factory method to create a RegimentService instance.

        Args:
            folder: The folder path where regiment files are stored.

        Returns:
            RegimentService: An instance of the RegimentService.
        """
        folder_path = Path(folder)
        folder_path.mkdir(parents=True, exist_ok=True)

        regiments = cls._load_regiments(folder_path)
        logger.debug(
            "RegimentService created",
            total_regiments=len(regiments),
            folder=str(folder_path),
        )

        return cls(folder=folder_path, regiments=regiments)

    @staticmethod
    def _load_regiments(folder: Path) -> RegimentDict:
        """Load regiment data from JSON files in the specified folder.

        Args:
            folder (Path): The folder path where regiment files are stored.

        Returns:
            RegimentDict: A dictionary of regiment IDs to Regiment objects.
        """
        regiments: RegimentDict = {}
        regiment_adapter = TypeAdapter(Regiment)

        json_files = list(folder.glob("*.json"))
        logger.debug(
            "Found JSON files for regiment loading",
            total_files=len(json_files),
            folder=str(folder),
        )

        for file_path in json_files:
            try:
                with file_path.open("r", encoding="utf-8") as f:
                    regiment = regiment_adapter.validate_json(f.read(), strict=True)
                regiments[regiment.id] = regiment
                logger.debug(
                    "Loaded regiment from file",
                    regiment_name=regiment.name,
                    regiment_id=regiment.id,
                    file_path=str(file_path),
                )
            except ValidationError as e:
                logger.exception(
                    "Validation error loading regiment from file",
                    file_path=str(file_path),
                    validation_errors=e.errors(),
                )
                continue
            except json.JSONDecodeError:
                logger.exception(
                    "JSON decode error loading regiment from file",
                    file_path=str(file_path),
                )
                continue
            except OSError:
                logger.exception(
                    "File system error loading regiment from file",
                    file_path=str(file_path),
                )
                continue

        logger.debug("Completed regiment loading", total_regiments=len(regiments))
        return regiments

    def get_regiment_list(self) -> list[Regiment]:
        """Return a list of all regiments.

        Returns:
            list[Regiment]: A list of Regiment objects.
        """
        return list(self.regiments.values())

    def save_regiment(self, regiment: Regiment) -> None:
        """Save a regiment to a JSON file.

        Args:
            regiment (Regiment): The regiment to save.

        Raises:
            OSError: If the file cannot be written.
            ValidationError: If the regiment data is invalid.
        """
        file_path = self.folder / f"{regiment.id}_{regiment.name}.json"

        try:
            json_data = TypeAdapter(Regiment).dump_json(regiment, indent=4)
            # Write atomically using a temporary file
            temp_path = file_path.with_suffix(".tmp")
            temp_path.write_text(json_data.decode("utf-8"), encoding="utf-8")
            temp_path.replace(file_path)
            logger.info(
                "Saved regiment to file",
                regiment_name=regiment.name,
                regiment_id=regiment.id,
                file_path=file_path.name,
            )
        except (OSError, ValidationError):
            logger.exception(
                "Failed to save regiment to file",
                regiment_name=regiment.name,
                regiment_id=regiment.id,
            )
            raise

    def create_regiment(self, name: str) -> Regiment:
        """Create a new regiment with a unique ID.

        Args:
            name (str): The name of the new regiment.

        Returns:
            Regiment: The newly created regiment.
        """
        new_id = max(self.regiments.keys(), default=0) + 1
        regiment = Regiment(id=new_id, name=name, moves=[], cooldown_duration=60)

        self.regiments[new_id] = regiment
        self.save_regiment(regiment)
        logger.info(
            "Created new regiment",
            regiment_name=name,
            regiment_id=new_id,
        )
        return regiment

    def update_regiment(self, regiment_id: int, regiment: Regiment) -> Regiment:
        """Update an existing regiment with the given ID.

        Args:
            regiment_id (int): The ID of the regiment to update.
            regiment (Regiment): The updated regiment data.

        Returns:
            Regiment: The updated regiment.

        Raises:
            ValueError: If the regiment ID does not match the provided regiment.
            KeyError: If the regiment ID is not found.
        """
        if regiment_id != regiment.id:
            msg = f"Regiment ID mismatch: expected {regiment_id}, got {regiment.id}"
            logger.error(
                "Regiment ID mismatch",
                expected_id=regiment_id,
                provided_id=regiment.id,
            )
            raise ValueError(msg)
        if regiment_id not in self.regiments:
            logger.error("Regiment not found", regiment_id=regiment_id)
            msg = f"Regiment {regiment_id} not found"
            raise KeyError(msg)

        self.regiments[regiment_id] = regiment
        self.save_regiment(regiment)
        logger.info(
            "Updated regiment",
            regiment_name=regiment.name,
            regiment_id=regiment_id,
        )
        return regiment
