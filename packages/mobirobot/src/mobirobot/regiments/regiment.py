from enum import Str<PERSON><PERSON>
from typing import Annotated

from pydantic import Field
from pydantic.config import ConfigDict
from pydantic.dataclasses import dataclass

from mobirobot.models.station import Station


def clamp(value: int, min_val: int, max_val: int) -> int:
    """Clamp a value to a specified range.

    Args:
        value: The value to clamp.
        min_val: The minimum allowed value.
        max_val: The maximum allowed value.

    Returns:
        int: The clamped value.
    """
    return max(min(value, max_val), min_val)


class BreakType(StrEnum):
    """Type of break to use."""

    BREAK_SHORT = "BreakShort"
    BREAK_BREATH = "BreakBreath"


@dataclass(config=ConfigDict(extra="forbid"))
class Regiment:
    """Represents a regiment for exercise sessions.

    Attributes:
        id: Unique identifier for the regiment.
        name: Name of the regiment.
        moves: List of exercise names.
        station: Station type for the regiment.
        iterations: Number of iterations for the regiment.
        short_break_duration: Duration of short breaks in seconds.
        long_break_duration: Duration of long breaks in seconds.
        exercise_duration: Duration of each exercise in seconds.
        cooldown_duration: Duration of cooldown exercises in seconds.
        cooldown_break_duration: Duration of breaks during cooldown in seconds.
        cooldown_moves: List of cooldown exercise names.
        break_type: Type of break to use.
        button_warmup: Whether to include a button warmup.
    """

    id: int
    name: str
    moves: list[str] = Field(default_factory=list)
    station: Station = Station.KJP
    iterations: Annotated[int, Field(ge=1, le=5)] = 1
    short_break_duration: Annotated[int, Field(ge=15, le=60)] = 30
    long_break_duration: Annotated[int, Field(ge=15, le=180)] = 60
    exercise_duration: Annotated[int, Field(ge=15, le=120)] = 60
    cooldown_duration: Annotated[int | None, Field(ge=15, le=120)] = None
    cooldown_break_duration: Annotated[int | None, Field(ge=15, le=120)] = None
    cooldown_moves: list[str] | None = None
    break_type: BreakType = BreakType.BREAK_SHORT
    button_warmup: bool | None = False

    def _approximate_iteration_duration(
        self, is_first_iteration: bool = False, approx_demo_duration: int = 20
    ) -> int:
        """Calculate the approximate duration of an iteration.

        Args:
            is_first_iteration: Whether this is the first iteration.
            approx_demo_duration: Approximate duration for demo.

        Returns:
            int: Approximate duration of the iteration.
        """
        if self.station == Station.KJP and is_first_iteration:
            return (
                len(self.moves) * (self.exercise_duration + self.short_break_duration)
                + approx_demo_duration
            )
        return len(self.moves) * (self.exercise_duration + self.short_break_duration)

    @property
    def approximate_total_duration(self) -> int:
        """Calculate the approximate total duration of the regiment.

        This includes the intro, iterations, breaks, and outro.

        Returns:
            int: Approximate total duration in seconds.
        """
        approx_intro_duration: int = 20
        approx_outro_duration: int = 10
        return (
            approx_intro_duration
            + self._approximate_iteration_duration(True)
            + (self.iterations - 1) * self._approximate_iteration_duration(False)
            + (self.iterations - 1) * self.long_break_duration
            + approx_outro_duration
        )

    def check_regiment_properties(self):
        """Checks and corrects regiment properties for consistency.

        Sets default values for properties based on station type and validity.
        """
        self.break_type = (
            BreakType.BREAK_SHORT
            if self.station == Station.KJP
            else BreakType.BREAK_BREATH
        )

        # First, check lower and upper limits

        self.exercise_duration = clamp(self.exercise_duration, 15, 120)
        self.short_break_duration = clamp(self.short_break_duration, 15, 60)
        self.long_break_duration = clamp(self.long_break_duration, 15, 180)
        self.iterations = clamp(self.iterations, 1, 5)
        if self.cooldown_duration:
            self.cooldown_duration = clamp(self.cooldown_duration, 15, 120)

        # Second, check station specific attributes
        if self.station in {Station.HUF, Station.KC}:
            self.short_break_duration = 0
            self.long_break_duration = 0
            self.iterations = 1
            self.cooldown_duration = 0
            self.button_warmup = False
