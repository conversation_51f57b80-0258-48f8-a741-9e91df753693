"""Simplified monitoring module for NAO robot status."""

from .aggregators.nao_aggregator import NaoMonitor
from .config import MonitoringConfig
from .exceptions import MonitoringServiceError
from .models import CombinedStatus, HostStatus, MobiRobotStatus, NaoStatus
from .service import MonitoringService

__all__ = [
    "CombinedStatus",
    "HostStatus",
    "MobiRobotStatus",
    "MonitoringConfig",
    "MonitoringService",
    "MonitoringServiceError",
    "NaoMonitor",
    "NaoStatus",
]
