# MobiRobot Monitoring

A modular monitoring and status collection system for MobiRobot applications.

## Features

- **Domain-specific aggregators**: NAO hardware, MobiRobot application, and host system monitoring
- **Modular collectors**: Individual collectors for different status types
- **Event-driven architecture**: Loose coupling through event system
- **Configuration-driven**: Fine-grained control over monitoring behavior
- **Error resilience**: Failure in one collector doesn't affect others
- **Type-safe**: Strong typing throughout with generic collectors

## Architecture

### Aggregators

- **NAO Aggregator**: Monitors robot hardware (battery, volume, joints, buttons)
- **MobiRobot Aggregator**: Monitors application state (exercises, speech, streaming)
- **Host Aggregator**: Monitors system resources (CPU, memory, disk, network)

### Collectors

Each aggregator uses specialized collectors that handle specific monitoring tasks:

- Signal-based collectors for real-time events
- Polling-based collectors for periodic status checks
- Configurable retry logic and error handling

## Usage

```python
from mobirobot.monitoring import MonitoringService, MonitoringConfig

# Create monitoring service
monitoring = await MonitoringService.create(
    robot=nao_robot,
    config=MonitoringConfig()
)

# Start monitoring
await monitoring.start()

# Get status
nao_status = monitoring.get_nao_status()
combined_status = monitoring.get_combined_status()

# Stop monitoring
await monitoring.stop()
```

## Configuration

Monitoring behavior can be configured with simple settings:

```python
config = MonitoringConfig(
    battery_poll_interval=15.0,  # Check battery every 15 seconds
    volume_poll_interval=10.0,   # Check volume every 10 seconds
    buttons_poll_interval=1.0,   # Check buttons every second
    joints_enabled=True,         # Enable signal-based joint monitoring
    timeout=5.0,                 # 5 second timeout for operations
)
```
