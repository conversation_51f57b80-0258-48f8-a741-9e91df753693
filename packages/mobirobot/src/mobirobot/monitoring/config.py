"""Configuration models for monitoring system."""

from dataclasses import dataclass


@dataclass
class MonitoringConfig:
    """Simplified monitoring configuration with sensible defaults."""

    # NAO monitoring settings
    battery_poll_interval: float = 10.0
    volume_poll_interval: float = 5.0
    buttons_poll_interval: float = 2.0
    joints_enabled: bool = True  # Signal-based, no polling

    # Optional NAO collectors (disabled by default)
    stiffness_enabled: bool = False
    stiffness_poll_interval: float = 15.0
    temperature_enabled: bool = False
    temperature_poll_interval: float = 30.0

    # Host monitoring settings
    host_poll_interval: float = 5.0

    # Global settings
    retry_attempts: int = 3
    retry_delay: float = 1.0
    timeout: float = 10.0
    event_queue_size: int = 1000
    shutdown_timeout: float = 30.0


@dataclass
class HostMonitoringConfig:
    """Host system monitoring configuration."""

    poll_interval: float = 5.0
    retry_attempts: int = 3
    retry_delay: float = 1.0
    timeout: float = 10.0
