"""Event system for status monitoring."""

import asyncio
import contextlib
from collections.abc import Awaitable, Callable
from datetime import datetime
from typing import Any, TypeVar

from pydantic import Field
from pydantic.dataclasses import dataclass

from mobirobot.common.structured_logging import LoggerType, get_logger

logger: LoggerType = get_logger(__name__)

T = TypeVar("T")


@dataclass
class StatusChangeEvent[T]:
    """Event representing a status change."""

    collector_name: str
    aggregator_name: str
    old_value: T | None
    new_value: T | None
    timestamp: datetime = Field(default_factory=datetime.now)


class StatusEventBus:
    """Simple event bus for status changes."""

    def __init__(self, max_queue_size: int = 1000):
        """Initialize the event bus.

        Args:
            max_queue_size: Maximum size of the event queue.
        """
        self._subscribers: list[
            Callable[[StatusChangeEvent[Any]], Awaitable[None]]
        ] = []
        self._event_queue: asyncio.Queue[StatusChangeEvent[Any]] = asyncio.Queue(
            maxsize=max_queue_size
        )
        self._processor_task: asyncio.Task[None] | None = None
        self._running: bool = False

    async def start(self) -> None:
        """Start the event bus processor."""
        if self._running:
            return

        self._running = True
        self._processor_task = asyncio.create_task(self._process_events())
        logger.debug("StatusEventBus started")

    async def stop(self) -> None:
        """Stop the event bus processor."""
        if not self._running:
            return

        self._running = False

        if self._processor_task and not self._processor_task.done():
            self._processor_task.cancel()
            with contextlib.suppress(asyncio.CancelledError):
                await self._processor_task

        logger.debug("StatusEventBus stopped")

    def subscribe(
        self, callback: Callable[[StatusChangeEvent[Any]], Awaitable[None]]
    ) -> None:
        """Subscribe to status change events.

        Args:
            callback: Async callback function to handle events.
        """
        if callback not in self._subscribers:
            self._subscribers.append(callback)
            logger.debug(
                "Added subscriber",
                subscriber_name=callback.__name__,
                subscriber_type=type(callback).__name__,
            )

    def unsubscribe(
        self, callback: Callable[[StatusChangeEvent[Any]], Awaitable[None]]
    ) -> None:
        """Unsubscribe from status change events.

        Args:
            callback: Callback function to remove.
        """
        if callback in self._subscribers:
            self._subscribers.remove(callback)
            logger.debug(
                "Removed subscriber",
                subscriber_name=callback.__name__,
                subscriber_type=type(callback).__name__,
            )

    async def emit(self, event: StatusChangeEvent[Any]) -> None:
        """Emit a status change event.

        Args:
            event: The event to emit.
        """
        if not self._running:
            logger.warning(
                "Event bus not running, dropping event",
                event_name=event.collector_name,
                event_type=type(event).__name__,
            )
            return

        try:
            self._event_queue.put_nowait(event)
        except asyncio.QueueFull:
            logger.warning(
                "Event queue full, dropping event",
                event_name=event.collector_name,
                event_type=type(event).__name__,
            )

    async def _process_events(self) -> None:
        """Process events from the queue."""
        while self._running:
            try:
                # Wait for event with timeout to allow checking _running flag
                event = await asyncio.wait_for(self._event_queue.get(), timeout=1.0)
                await self._handle_event(event)
            except TimeoutError:
                # Timeout is expected, continue loop
                continue
            except asyncio.CancelledError:
                # Task was cancelled, exit
                break
            except Exception:
                logger.exception("Error processing event")

    async def _handle_event(self, event: StatusChangeEvent[Any]) -> None:
        """Handle a single event by notifying all subscribers.

        Args:
            event: The event to handle.
        """
        for subscriber in self._subscribers:
            try:
                await subscriber(event)
            except Exception:
                logger.exception(
                    "Error in event subscriber for event",
                    subscriber_name=subscriber.__name__,
                    event_name=event.collector_name,
                    event_type=type(event).__name__,
                )
