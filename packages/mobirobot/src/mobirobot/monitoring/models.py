"""Status data models for monitoring system."""

from datetime import UTC, datetime
from enum import Enum

from pydantic import Field
from pydantic.dataclasses import dataclass


class ConnectionState(Enum):
    """Connection state enum (mirrored from nao package)."""

    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"


class RunningState(Enum):
    """Running state enum (mirrored from web types)."""

    NOT_STARTED = "Not Started"
    RUNNING = "Running"
    CONNECTING = "Connecting"
    PAUSED = "Paused"
    STOPPED = "Stopped"
    NAOLLM = "LLM"
    LIMP = "Limp"
    FOLLOWME = "Follow Me"
    SINGLEEXERCISE = "Single Exercise"
    COMPLETED = "Completed"
    ERROR = "Error"


@dataclass
class NaoStatus:
    """NAO robot hardware status."""

    battery: float = 0.0
    volume: float = 0.0
    hot_joints: list[str] = Field(default_factory=list)
    buttons: dict[str, bool] = Field(default_factory=dict)
    connection_state: ConnectionState = ConnectionState.DISCONNECTED
    autonomous_life_state: str = "unknown"
    stiffness: dict[str, float] = Field(default_factory=dict)
    temperature: dict[str, float] = Field(default_factory=dict)
    last_updated: datetime = Field(default_factory=lambda: datetime.now(UTC))


@dataclass
class MobiRobotStatus:
    """MobiRobot application status."""

    current_exercise: str | None = None
    exercise_progress: float = 0.0
    regiment_status: str = "idle"
    speech_queue_size: int = 0
    stream_active: bool = False
    stream_clients: int = 0
    execution_state: RunningState = RunningState.NOT_STARTED
    last_error: str | None = None
    last_updated: datetime = Field(default_factory=lambda: datetime.now(UTC))


@dataclass
class HostStatus:
    """Host system status."""

    cpu_usage: float = 0.0
    memory_usage: float = 0.0
    disk_usage: float = 0.0
    network_active: bool = False
    uptime: float = 0.0
    load_average: tuple[float, float, float] = (0.0, 0.0, 0.0)
    processes: int = 0
    last_updated: datetime = Field(default_factory=lambda: datetime.now(UTC))


@dataclass
class CombinedStatus:
    """Combined status from all aggregators."""

    nao: NaoStatus | None = None
    mobirobot: MobiRobotStatus | None = None
    host: HostStatus | None = None
    last_updated: datetime = Field(default_factory=lambda: datetime.now(UTC))
