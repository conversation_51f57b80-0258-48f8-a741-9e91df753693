"""Monitoring-specific exceptions."""


class MonitoringError(Exception):
    """Base exception for monitoring system errors."""


class MonitoringServiceError(MonitoringError):
    """Error in the main monitoring service."""


class CollectorError(MonitoringError):
    """Error in a status collector."""

    def __init__(
        self, collector_name: str, message: str, original_error: Exception | None = None
    ):
        """Initialize collector error.

        Args:
            collector_name: Name of the collector that failed.
            message: Error message.
            original_error: Original exception that caused this error.
        """
        self.collector_name: str = collector_name
        self.original_error: Exception | None = original_error

        full_message = f"Collector '{collector_name}': {message}"
        if original_error:
            full_message += f" (caused by: {original_error!r})"

        super().__init__(full_message)


class AggregatorError(MonitoringError):
    """Error in a status aggregator."""

    def __init__(
        self,
        aggregator_name: str,
        message: str,
        original_error: Exception | None = None,
    ):
        """Initialize aggregator error.

        Args:
            aggregator_name: Name of the aggregator that failed.
            message: Error message.
            original_error: Original exception that caused this error.
        """
        self.aggregator_name: str = aggregator_name
        self.original_error: Exception | None = original_error

        full_message = f"Aggregator '{aggregator_name}': {message}"
        if original_error:
            full_message += f" (caused by: {original_error!r})"

        super().__init__(full_message)


class ConfigurationError(MonitoringError):
    """Error in monitoring configuration."""
