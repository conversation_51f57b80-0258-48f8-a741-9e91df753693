"""Base aggregator class for status monitoring."""

from abc import ABC, abstractmethod
from datetime import UTC, datetime
from typing import Any, TypeVar

from mobirobot.common.structured_logging import LoggerType, get_logger
from mobirobot.monitoring.events import StatusChangeEvent, StatusEventBus
from mobirobot.monitoring.exceptions import AggregatorError

logger: LoggerType = get_logger(__name__)

T = TypeVar("T")


class BaseAggregator[T](ABC):
    """Base class for status aggregators."""

    name: str
    event_bus: StatusEventBus
    _status: T | None
    _running: bool
    _last_updated: datetime

    def __init__(self, name: str, event_bus: StatusEventBus):
        """Initialize the aggregator.

        Args:
            name: Name of this aggregator.
            event_bus: Event bus for status change events.
        """
        self.name = name
        self.event_bus = event_bus

        self._status = None
        self._running = False
        self._last_updated = datetime.now(UTC)

    @property
    def status(self) -> T | None:
        """Get the current aggregated status."""
        return self._status

    @property
    def last_updated(self) -> datetime:
        """Get the last update timestamp."""
        return self._last_updated

    @property
    def is_running(self) -> bool:
        """Check if the aggregator is running."""
        return self._running

    async def start(self) -> None:
        """Start the aggregator and all its collectors."""
        if self._running:
            logger.warning("Aggregator already running", aggregator_name=self.name)
            return

        logger.info("Starting aggregator", aggregator_name=self.name)

        try:
            # Subscribe to event bus
            self.event_bus.subscribe(self._handle_status_change)

            # Initialize status
            self._status = await self.create_initial_status()
            self._last_updated = datetime.now(UTC)

            self._running = True
            logger.info("Aggregator started", aggregator_name=self.name)

        except Exception as e:
            raise AggregatorError(self.name, "Failed to start aggregator", e) from e

    async def stop(self) -> None:
        """Stop the aggregator."""
        if not self._running:
            return

        logger.info("Stopping aggregator", aggregator_name=self.name)
        self._running = False

        # Unsubscribe from event bus
        self.event_bus.unsubscribe(self._handle_status_change)

        logger.debug("Aggregator stopped", aggregator_name=self.name)

    @abstractmethod
    async def create_initial_status(self) -> T:
        """Create the initial status object.

        Returns:
            Initial status object.
        """

    @abstractmethod
    async def update_status(self, event: StatusChangeEvent[Any]) -> T:
        """Update the status based on a collector event.

        Args:
            event: The status change event.

        Returns:
            Updated status object.
        """

    async def _handle_status_change(self, event: StatusChangeEvent[Any]) -> None:
        """Handle status change events from collectors.

        Args:
            event: The status change event.
        """
        # Only handle events from our collectors
        if event.aggregator_name != self.name:
            return

        try:
            self._status = await self.update_status(event)
            self._last_updated = datetime.now(UTC)

            logger.debug(
                "Status updated for aggregator due to change",
                aggregator_name=self.name,
                collector_name=event.collector_name,
            )

            # Optionally emit aggregator-level events here
            # For now, we just update the internal status

        except Exception:
            logger.exception(
                "Error updating status for aggregator from event",
                aggregator_name=self.name,
                collector_name=event.collector_name,
                event_name=event.collector_name,
                event_type=type(event).__name__,
            )
