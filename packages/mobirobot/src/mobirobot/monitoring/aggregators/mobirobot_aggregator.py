"""Simplified MobiRobot application status monitor."""

import asyncio
from datetime import UTC, datetime

from mobirobot.common.structured_logging import LoggerType, get_logger
from mobirobot.monitoring.config import MonitoringConfig
from mobirobot.monitoring.models import MobiRobotStatus, RunningState

logger: LoggerType = get_logger(__name__)


class MobiRobotMonitor:
    """Simplified MobiRobot application status monitor that combines collection and aggregation."""

    _config: MonitoringConfig
    _status: MobiRobotStatus
    _running: bool
    _tasks: list[asyncio.Task[None]]

    def __init__(
        self,
        config: MonitoringConfig,
    ):
        """Initialize MobiRobot monitor.

        Args:
            config: MobiRobot monitoring configuration.
        """
        self._config = config
        self._status = MobiRobotStatus()
        self._running = False
        self._tasks = []

    @property
    def status(self) -> MobiRobotStatus:
        """Get the current MobiRobot status."""
        return self._status

    @property
    def is_running(self) -> bool:
        """Check if the monitor is running."""
        return self._running

    async def start(self) -> None:
        """Start the MobiRobot monitor."""
        if self._running:
            logger.warning("MobiRobot monitor already running")
            return

        logger.info("Starting MobiRobot monitor...")
        self._running = True

        # Initialize status with current timestamp
        self._status.last_updated = datetime.now(UTC)

        # Start monitoring tasks based on configuration
        self._tasks = []

        logger.info(
            "MobiRobot monitor started with %d monitoring tasks",
            total_tasks=len(self._tasks),
        )

    async def stop(self) -> None:
        """Stop the MobiRobot monitor."""
        if not self._running:
            return

        logger.info("Stopping MobiRobot monitor...")
        self._running = False
        await self._cleanup()
        logger.info("MobiRobot monitor stopped")

    async def _cleanup(self) -> None:
        """Cleanup all tasks and resources."""
        for task in self._tasks:
            if not task.done():
                task.cancel()

        if self._tasks:
            await asyncio.gather(*self._tasks, return_exceptions=True)
        self._tasks.clear()

    def update_execution_state(self, state: RunningState) -> None:
        """Update the execution state.

        Args:
            state: New execution state.
        """
        self._status.execution_state = state
        self._status.last_updated = datetime.now(UTC)

    def update_exercise_info(
        self, exercise_name: str | None, progress: float = 0.0
    ) -> None:
        """Update current exercise information.

        Args:
            exercise_name: Name of current exercise or None if no exercise.
            progress: Exercise progress (0.0 to 1.0).
        """
        self._status.current_exercise = exercise_name
        self._status.exercise_progress = max(0.0, min(1.0, progress))
        self._status.last_updated = datetime.now(UTC)

    def update_speech_status(self, queue_size: int) -> None:
        """Update speech queue status.

        Args:
            queue_size: Current size of speech queue.
        """
        self._status.speech_queue_size = max(0, queue_size)
        self._status.last_updated = datetime.now(UTC)

    def update_stream_status(self, active: bool, client_count: int = 0) -> None:
        """Update stream status.

        Args:
            active: Whether streaming is active.
            client_count: Number of connected stream clients.
        """
        self._status.stream_active = active
        self._status.stream_clients = max(0, client_count)
        self._status.last_updated = datetime.now(UTC)

    def update_error(self, error_message: str | None) -> None:
        """Update last error information.

        Args:
            error_message: Error message or None to clear error.
        """
        self._status.last_error = error_message
        self._status.last_updated = datetime.now(UTC)
