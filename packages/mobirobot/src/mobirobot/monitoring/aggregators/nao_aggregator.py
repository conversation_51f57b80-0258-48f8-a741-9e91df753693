"""Simplified NAO hardware status monitor."""

import asyncio
from contextlib import AsyncExitStack
from datetime import UTC, datetime

from buttons import ButtonService
from nao import Nao<PERSON><PERSON><PERSON>

from mobirobot.common.structured_logging import LoggerType, get_logger
from mobirobot.monitoring.config import MonitoringConfig
from mobirobot.monitoring.models import ConnectionState, NaoStatus

logger: LoggerType = get_logger(__name__)


class NaoMonitor:
    """Simplified NAO hardware status monitor that combines collection and aggregation."""

    _robot: NaoRobot
    _config: MonitoringConfig
    _button_service: ButtonService | None
    _status: NaoStatus
    _running: bool
    _tasks: list[asyncio.Task[None]]
    _exit_stack: AsyncExitStack
    _hot_joints: set[str]

    def __init__(
        self,
        robot: NaoRobot,
        config: MonitoringConfig,
        button_service: ButtonService | None = None,
    ):
        """Initialize NAO monitor.

        Args:
            robot: NAO robot instance.
            config: Monitoring configuration.
            button_service: Optional button service for button monitoring.
        """
        self._robot = robot
        self._config = config
        self._button_service = button_service

        self._status = NaoStatus()
        self._running = False
        self._tasks = []
        self._exit_stack = AsyncExitStack()
        self._hot_joints = set()

    @property
    def status(self) -> NaoStatus:
        """Get the current NAO status."""
        return self._status

    @property
    def is_running(self) -> bool:
        """Check if the monitor is running."""
        return self._running

    async def start(self) -> None:
        """Start the NAO monitor."""
        if self._running:
            logger.warning("NAO monitor already running")
            return

        logger.info("Starting NAO monitor...")
        self._running = True

        await self._update_connection_state()

        if self._config.joints_enabled:
            await self._setup_joints_monitoring()

        self._tasks = [
            asyncio.create_task(self._battery_polling_loop()),
            asyncio.create_task(self._volume_polling_loop()),
        ]

        if self._button_service:
            self._tasks.append(asyncio.create_task(self._buttons_polling_loop()))

        logger.info(
            "NAO monitor started with %d polling tasks", total_tasks=len(self._tasks)
        )

    async def stop(self) -> None:
        """Stop the NAO monitor."""
        if not self._running:
            return

        logger.info("Stopping NAO monitor...")
        self._running = False
        await self._cleanup()
        logger.info("NAO monitor stopped")

    async def _cleanup(self) -> None:
        """Cleanup all tasks and resources."""
        for task in self._tasks:
            if not task.done():
                task.cancel()

        if self._tasks:
            await asyncio.gather(*self._tasks, return_exceptions=True)
        self._tasks.clear()

        await self._exit_stack.aclose()

    async def _update_connection_state(self) -> None:
        """Update connection state from robot."""
        nao_state = self._robot.connection_manager.state
        if str(nao_state) == "CONNECTED":
            self._status.connection_state = ConnectionState.CONNECTED
        elif str(nao_state) == "CONNECTING":
            self._status.connection_state = ConnectionState.CONNECTING
        else:
            self._status.connection_state = ConnectionState.DISCONNECTED

    async def _setup_joints_monitoring(self) -> None:
        """Set up signal-based joints monitoring."""
        await self._exit_stack.enter_async_context(
            self._robot.signals.subscribe("HotDeviceDetected", self._handle_hot_device)
        )
        await self._exit_stack.enter_async_context(
            self._robot.signals.subscribe(
                "DeviceNoLongerHotDetected", self._handle_not_hot_device
            )
        )
        logger.debug("Joints monitoring signals subscribed")

    async def _handle_hot_device(self, value: list[str]) -> None:
        """Handle HotDeviceDetected signal."""
        if not self._running:
            return
        logger.info("Joints became hot", joints=value)
        self._hot_joints.update(value)
        self._status.hot_joints = sorted(self._hot_joints)
        self._status.last_updated = datetime.now(UTC)

    async def _handle_not_hot_device(self, value: list[str]) -> None:
        """Handle DeviceNoLongerHotDetected signal."""
        if not self._running:
            return
        logger.info("Joints no longer hot", joints=value)
        self._hot_joints.difference_update(value)
        self._status.hot_joints = sorted(self._hot_joints)
        self._status.last_updated = datetime.now(UTC)

    async def _battery_polling_loop(self) -> None:
        """Polling loop for battery status."""
        while self._running:
            try:
                battery_value = await asyncio.wait_for(
                    self._robot.memory.get_data(
                        "Device/SubDeviceList/Battery/Charge/Sensor/Value"
                    ),
                    timeout=self._config.timeout,
                )

                if isinstance(battery_value, int | float):
                    # Clamp value to valid range and convert to 0.0-1.0
                    clamped_value = max(0, min(100, battery_value))
                    self._status.battery = clamped_value / 100.0
                    self._status.last_updated = datetime.now(UTC)

                await asyncio.sleep(self._config.battery_poll_interval)

            except asyncio.CancelledError:
                break

    async def _volume_polling_loop(self) -> None:
        """Polling loop for volume status."""
        while self._running:
            try:
                volume_value = await asyncio.wait_for(
                    self._robot.tts.get_volume(),
                    timeout=self._config.timeout,
                )

                self._status.volume = max(0.0, min(1.0, float(volume_value)))
                self._status.last_updated = datetime.now(UTC)

                await asyncio.sleep(self._config.volume_poll_interval)

            except asyncio.CancelledError:
                break

    async def _buttons_polling_loop(self) -> None:
        """Polling loop for button status."""
        if not self._button_service:
            return

        while self._running:
            try:
                self._status.buttons = {}
                self._status.last_updated = datetime.now(UTC)

                await asyncio.sleep(self._config.buttons_poll_interval)

            except asyncio.CancelledError:
                break
