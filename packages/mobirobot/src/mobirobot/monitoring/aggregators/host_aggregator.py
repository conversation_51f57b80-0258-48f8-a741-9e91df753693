"""Simplified host system status monitor."""

import asyncio
import os
from datetime import UTC, datetime

import psutil

from mobirobot.common.structured_logging import LoggerType, get_logger
from mobirobot.monitoring.config import HostMonitoringConfig
from mobirobot.monitoring.models import HostStatus

logger: LoggerType = get_logger(__name__)


class HostMonitor:
    """Simplified host system status monitor that combines collection and aggregation."""

    _config: HostMonitoringConfig
    _status: HostStatus
    _running: bool
    _tasks: list[asyncio.Task[None]]

    def __init__(
        self,
        config: HostMonitoringConfig,
    ):
        """Initialize host monitor.

        Args:
            config: Host monitoring configuration.
        """
        self._config = config
        self._status = HostStatus()
        self._running = False
        self._tasks = []

    @property
    def status(self) -> HostStatus:
        """Get the current host status."""
        return self._status

    @property
    def is_running(self) -> bool:
        """Check if the monitor is running."""
        return self._running

    async def start(self) -> None:
        """Start the host monitor."""
        if self._running:
            logger.warning("Host monitor already running")
            return

        logger.info("Starting host monitor...")
        self._running = True

        # Initialize status with current timestamp
        self._status.last_updated = datetime.now(UTC)

        # Start monitoring tasks
        self._tasks = [
            asyncio.create_task(self._system_stats_polling_loop()),
        ]

        logger.info("Host monitor started", total_tasks=len(self._tasks))

    async def stop(self) -> None:
        """Stop the host monitor."""
        if not self._running:
            return

        logger.info("Stopping host monitor...")
        self._running = False
        await self._cleanup()
        logger.info("Host monitor stopped")

    async def _cleanup(self) -> None:
        """Cleanup all tasks and resources."""
        for task in self._tasks:
            if not task.done():
                task.cancel()

        if self._tasks:
            await asyncio.gather(*self._tasks, return_exceptions=True)
        self._tasks.clear()

    async def _system_stats_polling_loop(self) -> None:
        """Polling loop for system statistics."""
        while self._running:
            try:
                # CPU usage
                self._status.cpu_usage = psutil.cpu_percent(interval=None)

                # Memory usage
                memory = psutil.virtual_memory()
                self._status.memory_usage = float(memory.percent)

                # Disk usage (root filesystem)
                disk = psutil.disk_usage("/")
                self._status.disk_usage = (disk.used / disk.total) * 100

                # Network activity (simplified check)
                net_io = psutil.net_io_counters()
                self._status.network_active = (
                    net_io.bytes_sent > 0 or net_io.bytes_recv > 0
                )

                # System uptime
                boot_time = psutil.boot_time()
                self._status.uptime = datetime.now(UTC).timestamp() - boot_time

                # Load average
                if hasattr(os, "getloadavg"):
                    self._status.load_average = os.getloadavg()

                # Process count
                self._status.processes = len(psutil.pids())

                # Update timestamp
                self._status.last_updated = datetime.now(UTC)

                await asyncio.sleep(self._config.poll_interval)

            except asyncio.CancelledError:
                break
            except Exception:
                logger.exception("Error collecting host system stats")
                await asyncio.sleep(self._config.poll_interval)
