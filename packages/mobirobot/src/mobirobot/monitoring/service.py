"""Simplified monitoring service for NAO robot status."""

from typing import TYPE_CHECKING, Self

from mobirobot.common.structured_logging import LoggerType, get_logger

from .aggregators.nao_aggregator import NaoMonitor
from .config import MonitoringConfig
from .exceptions import MonitoringServiceError
from .models import CombinedStatus, HostStatus, MobiRobotStatus, NaoStatus

if TYPE_CHECKING:
    from buttons import ButtonService
    from nao import NaoRobot

logger: LoggerType = get_logger(__name__)


class MonitoringService:
    """Simplified monitoring service for NAO robot status."""

    def __init__(
        self,
        nao_monitor: NaoMonitor | None = None,
    ):
        """Initialize monitoring service.

        Args:
            nao_monitor: Optional NAO hardware monitor.
        """
        self._nao_monitor: NaoMonitor | None = nao_monitor
        self._running: bool = False

    @classmethod
    async def create(
        cls,
        robot: "NaoRobot | None" = None,
        button_service: "ButtonService | None" = None,
        config: MonitoringConfig | None = None,
    ) -> Self:
        """Factory method to create configured monitoring service.

        Args:
            robot: Optional NAO robot for hardware monitoring.
            button_service: Optional button service for button monitoring.
            config: Optional monitoring configuration.

        Returns:
            Configured monitoring service instance.
        """
        final_config = config or MonitoringConfig()

        # Create NAO monitor if robot is available
        nao_monitor = None
        if robot:
            nao_monitor = NaoMonitor(
                robot=robot,
                config=final_config,
                button_service=button_service,
            )

        return cls(nao_monitor=nao_monitor)

    async def start(self) -> None:
        """Start the monitoring service."""
        if self._running:
            logger.warning("Monitoring service already running")
            return

        logger.info("Starting monitoring service...")

        try:
            # Start NAO monitor if available
            if self._nao_monitor:
                await self._nao_monitor.start()

            self._running = True
            monitor_count = 1 if self._nao_monitor else 0
            logger.info(
                "Monitoring service started with %d monitors",
                total_monitors=monitor_count,
            )

        except Exception as e:
            # Cleanup on failure
            await self._cleanup()
            msg = "Failed to start monitoring service"
            raise MonitoringServiceError(msg, e) from e

    async def stop(self) -> None:
        """Stop the monitoring service."""
        if not self._running:
            return

        logger.info("Stopping monitoring service...")
        self._running = False

        await self._cleanup()
        logger.info("Monitoring service stopped")

    async def _cleanup(self) -> None:
        """Cleanup all monitors."""
        if self._nao_monitor:
            await self._nao_monitor.stop()

    @property
    def is_running(self) -> bool:
        """Check if the monitoring service is running."""
        return self._running

    def get_nao_status(self) -> NaoStatus | None:
        """Get current NAO hardware status.

        Returns:
            NAO status or None if NAO monitor not available.
        """
        if self._nao_monitor:
            return self._nao_monitor.status
        return None

    @staticmethod
    def get_mobirobot_status() -> MobiRobotStatus | None:
        """Get current MobiRobot application status.

        Returns:
            None - MobiRobot monitoring not implemented yet.
        """
        return None

    @staticmethod
    def get_host_status() -> HostStatus | None:
        """Get current host system status.

        Returns:
            None - Host monitoring not implemented yet.
        """
        return None

    def get_combined_status(self) -> CombinedStatus:
        """Get all status information combined.

        Returns:
            Combined status from all available monitors.
        """
        return CombinedStatus(
            nao=self.get_nao_status(),
            mobirobot=self.get_mobirobot_status(),
            host=self.get_host_status(),
        )

    def get_service_info(self) -> dict[str, object]:
        """Get information about the monitoring service.

        Returns:
            Dictionary with service status and monitor information.
        """
        monitors: dict[str, object] = {}

        if self._nao_monitor:
            monitors["nao"] = {
                "running": self._nao_monitor.is_running,
                "last_updated": self._nao_monitor.status.last_updated.isoformat(),
            }

        return {
            "running": self._running,
            "monitors": monitors,
        }
