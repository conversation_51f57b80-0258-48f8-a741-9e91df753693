from typing import Annotated, ClassVar, Literal

from mobirobot.models.movement import ExerciseMovementSequence, Movement
from mobirobot.models.station import Station
from pydantic import BaseModel, ConfigDict, Field

from nao.nao_types import RobotPosture
from mobirobot.models.common import Intensity, Turntype


class AudioSettings(BaseModel):
    """Settings related to audio output for the exercise."""

    sounds: list[str]
    model_config: ClassVar[ConfigDict] = ConfigDict(frozen=True)


class ModelProcessingSettings(BaseModel):
    """Settings related to model processing and classification for the exercise."""

    model_files: Annotated[list[str], Field(alias="model")]
    classifications: list[str]
    hold: bool = False
    model_config: ClassVar[ConfigDict] = ConfigDict(populate_by_name=True, frozen=True)


class ExerciseMetadata(BaseModel):
    """Additional metadata for the exercise."""

    intensity: Intensity
    tags: list[str]
    model_config: ClassVar[ConfigDict] = ConfigDict(frozen=True)


class BaseExercise(BaseModel):
    """Base model for an exercise."""

    name: str
    explanation: str
    init_pose: RobotPosture
    station_tags: list[Station]
    turntype: Turntype = Turntype.NONE

    # Modular optional sections
    audio: AudioSettings | None = None
    model_processing: ModelProcessingSettings | None = None
    metadata: ExerciseMetadata | None = None
    
    model_config: ClassVar[ConfigDict] = ConfigDict(populate_by_name=True, frozen=True)


class StandardExercise(BaseExercise):
    """Defines a standard exercise with a sequence of movements."""

    exercise_type: Literal["standard"] = "standard"
    movements: ExerciseMovementSequence
    demo: Movement | None = None

    @property
    def first_movement(self) -> Movement:
        """Returns the first movement of the exercise."""
        return self.movements.main_movement


class AlternatingExercise(BaseExercise):
    """Defines an exercise with alternating left and right movements."""

    exercise_type: Literal["alternating"] = "alternating"
    left_movements: ExerciseMovementSequence
    right_movements: ExerciseMovementSequence
    alternating_explanation: str
    demo: Movement | None = None

    @property
    def first_movement(self) -> Movement:
        """Returns the first movement of the exercise."""
        return self.left_movements.main_movement


class VariationExercise(BaseExercise):
    """Defines an exercise with multiple variations, each a sequence of movements."""

    exercise_type: Literal["variation"] = "variation"
    variations: list[ExerciseMovementSequence]
    variation_explanations: list[str]
    demo: Movement | None = None

    @property
    def first_movement(self) -> Movement:
        """Returns the first movement of the exercise."""
        return self.variations[0].main_movement


AnyExercise = StandardExercise | AlternatingExercise | VariationExercise