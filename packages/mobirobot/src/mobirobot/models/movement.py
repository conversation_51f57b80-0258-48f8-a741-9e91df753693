from collections.abc import Sequence
from enum import Enum
from typing import Any

from pydantic.dataclasses import dataclass

from mobirobot.models.nao_types import NaoJointName


class MovementPhase(Enum):
    """Enum representing the different phases of an exercise movement sequence."""

    INIT = "init"
    MAIN = "main"
    EXIT = "exit"


@dataclass
class Movement:
    """Represents a single movement sequence for the Nao robot.

    Attributes:
        joint_names: A sequence of Nao joint names involved in the movement.
        times: A sequence of sequences, where each inner sequence represents
               time points for the corresponding joint.
        angles: A sequence of sequences, where each inner sequence represents
                angle values (or sequences of angle values for bezier curves)
                for the corresponding joint at the specified times.
    """

    joint_names: Sequence[NaoJointName]
    # For 'times', the original type is Sequence[Sequence[float]].
    # This suggests a list of lists of floats.
    times: Sequence[Sequence[float]]
    # For 'angles', the structure is complex with Bézier control points.
    # Each angle entry can be either a float or a list containing:
    # [angle_value, [interpolation_type, in_tangent, out_tangent], [interpolation_type, in_tangent, out_tangent]]
    # Using Any to avoid Pydantic validation issues with the deeply nested structure.
    angles: Sequence[Sequence[Any]]


@dataclass
class ExerciseMovementSequence:
    """Represents a complete exercise movement sequence with up to 3 phases.

    This model captures the common pattern in the exercise system where exercises
    can have up to 3 individual movements:
    - init_movement: Optional initialization/setup movement
    - main_movement: Required main exercise movement (repeated during execution)
    - exit_movement: Optional cleanup/exit movement

    This structure aligns with the ExerciseExecutor.get_correct_motion() method
    which expects movements at specific indices:
    - motions[0] for INIT phase
    - motions[1] for MAIN/NORMAL phase
    - motions[2] for EXIT phase

    Attributes:
        init_movement: Optional movement to initialize/setup the exercise position.
        main_movement: Required main movement that gets repeated during exercise execution.
        exit_movement: Optional movement to clean up or return to neutral position.
    """

    init_movement: Movement | None
    main_movement: Movement
    exit_movement: Movement | None

    def get_movement_by_phase(self, phase: MovementPhase) -> Movement | None:
        """Get movement for a specific phase.

        Args:
            phase: The movement phase to retrieve.

        Returns:
            The movement for the specified phase, or None if not defined.
        """
        match phase:
            case MovementPhase.INIT:
                return self.init_movement
            case MovementPhase.MAIN:
                return self.main_movement
            case MovementPhase.EXIT:
                return self.exit_movement

    def to_sequence(self) -> Sequence[Movement]:
        """Convert to a sequence format compatible with existing exercise executor.

        Returns a sequence of movements in the expected order:
        [init_movement, main_movement, exit_movement]

        None movements are replaced with the main_movement to maintain
        compatibility with code that expects exactly 3 movements.

        Returns:
            A sequence of 3 movements in [init, main, exit] order.
        """
        return [
            self.init_movement or self.main_movement,
            self.main_movement,
            self.exit_movement or self.main_movement,
        ]

    def has_init_phase(self) -> bool:
        """Check if the sequence has a distinct initialization movement.

        Returns:
            True if init_movement is defined and different from main_movement.
        """
        return self.init_movement is not None

    def has_exit_phase(self) -> bool:
        """Check if the sequence has a distinct exit movement.

        Returns:
            True if exit_movement is defined and different from main_movement.
        """
        return self.exit_movement is not None

    def get_phase_count(self) -> int:
        """Get the number of distinct movement phases.

        Returns:
            The number of distinct movements (1-3).
        """
        count = 1  # main_movement is always present
        if self.has_init_phase():
            count += 1
        if self.has_exit_phase():
            count += 1
        return count