from typing import Literal


NaoJointName = Literal[
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "LAnklePitch",
    "LAnkleRoll",
    "LElbowRoll",
    "LElbowYaw",
    "LHand",
    "LHipPitch",
    "LHipRoll",
    "LHipYaw<PERSON>itch",
    "L<PERSON>neePitch",
    "LShoulder<PERSON>itch",
    "LShoulderRoll",
    "L<PERSON>ristYaw",
    "RAnklePitch",
    "RAnkleRoll",
    "RElbowRoll",
    "RE<PERSON><PERSON><PERSON>aw",
    "RHand",
    "RHipPitch",
    "RHipRoll",
    "RHipYawPitch",
    "R<PERSON>nee<PERSON><PERSON>",
    "<PERSON>houlder<PERSON><PERSON>",
    "RShoulderRoll",
    "R<PERSON>rist<PERSON>aw",
]
