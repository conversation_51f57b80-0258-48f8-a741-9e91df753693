from enum import Enum, StrEnum


class Turntype(Enum):
    """Different types of turns to be performed by Na<PERSON>."""

    NONE = 0
    LEFTTURN = 1
    RIGHTTURN = 2
    BACKWARDS = 3
    FORWARDS = 4 


class ExerciseType(StrEnum):
    """Different types of exercises."""

    MOVE = "move"
    STRETCH = "stretch"
    BUTTON = "button"


class Intensity(StrEnum):
    """Different intensities of exercises."""

    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"