
from enum import StrEnum


class Station(StrEnum):
    """Enum representing different stations.

    Each station has a unique identifier and may have a base station.
    """

    KJP = "KJP"
    KC = "KC"
    HUF = "HUF"
    LIPPE = "LIPPE"

    @property
    def base_station(self) -> "Station | None":
        """Returns the base station for the current station."""
        match self:
            case Station.HUF:
                return Station.KC
            case _:
                return None
