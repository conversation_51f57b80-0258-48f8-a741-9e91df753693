"""Structured logging configuration using structlog.

This module provides a high-performance, structured logging setup that follows
structlog best practices for optimal performance and type safety.

Key benefits:
- Structured data in logs (JSON for files, human-readable for console)
- Automatic context extraction (component, function, line numbers)
- High performance with caching and efficient filtering
- Consistent formatting across all components
- Easy integration with log aggregation systems
- Full type safety with proper annotations
- Unified logging configuration that works with both structlog and stdlib logging
"""

import datetime
import logging
import logging.handlers
import os
import sys
import zoneinfo
from pathlib import Path
from typing import ClassVar, Protocol, override

import structlog
import structlog.stdlib
import structlog.typing

from mobirobot.common.config import Environment, settings
from mobirobot.common.result_types import Err, Ok, Result

# Type aliases for better readability
LoggerType = structlog.stdlib.BoundLogger
ProcessorType = structlog.typing.Processor
EventDict = structlog.typing.EventDict
WrapperClass = (
    type[structlog.stdlib.BoundLogger] | type[structlog.typing.FilteringBoundLogger]
)  # Type for wrapper classes
LoggerFactory = structlog.stdlib.LoggerFactory


class JSONSerializer(Protocol):
    """Protocol for JSON serialization functions."""

    def __call__(self, obj: object, /) -> str | bytes:
        """Serialize object to JSON."""
        ...


class LoggingSetupError(Exception):
    """Exception raised when logging setup fails."""

    def __init__(self, message: str, cause: Exception | None = None) -> None:
        """Initialize the logging setup error.

        Args:
            message: Error message describing what went wrong.
            cause: The underlying exception that caused this error.
        """
        super().__init__(message)
        self.cause: Exception | None = cause


# Performance: Try to import faster JSON serializer
try:
    import orjson

    def _orjson_serializer(obj: object) -> bytes:
        """Fast JSON serializer using orjson."""
        return orjson.dumps(obj)

    _json_serializer: JSONSerializer = _orjson_serializer
    _has_orjson = True
except ImportError:
    import json

    def _stdlib_json_serializer(obj: object) -> str:
        """Fallback JSON serializer using stdlib."""
        return json.dumps(obj, default=str, separators=(",", ":"))

    _json_serializer = _stdlib_json_serializer
    _has_orjson = False


def _add_app_context(
    _logger: object, _method_name: str, event_dict: EventDict
) -> EventDict:
    """Add application-specific context to log entries.

    Args:
        _logger: The wrapped logger object (unused but required by structlog).
        _method_name: The name of the method being called (unused but required by structlog).
        event_dict: Current event dictionary.

    Returns:
        Enhanced event dictionary with app context.
    """
    event_dict["app"] = "mobirobot"
    event_dict["environment"] = settings.environment.value
    if hasattr(settings, "station") and settings.station:
        event_dict["station"] = settings.station.value
    return event_dict


def _add_performance_context(
    _logger: object, _method_name: str, event_dict: EventDict
) -> EventDict:
    """Add performance and system context to log entries.

    Args:
        _logger: The wrapped logger object (unused but required by structlog).
        _method_name: The name of the method being called (unused but required by structlog).
        event_dict: Current event dictionary.

    Returns:
        Enhanced event dictionary with performance context.
    """
    # Add process ID for debugging multi-process scenarios
    event_dict["pid"] = os.getpid()

    # Add timestamp with timezone info for better debugging
    if settings.environment == Environment.DEVELOPMENT:
        event_dict["local_time"] = datetime.datetime.now(
            tz=zoneinfo.ZoneInfo("UTC")
        ).isoformat()

    return event_dict


class AsyncioShutdownFilter(logging.Filter):
    """Filter to suppress known harmless asyncio errors during shutdown."""

    SUPPRESSED_PATTERNS: ClassVar[list[str]] = [
        "Future exception was never retrieved",
        "Session closed during execution",
        "QiConnectionError",
        "goToPosture",
        "Connection lost during execution",
    ]

    @override
    def filter(self, record: logging.LogRecord) -> bool:  # type: ignore[override]
        """Filter out known shutdown-related asyncio errors.

        Args:
            record: The log record to filter.

        Returns:
            False if the record should be suppressed, True otherwise.
        """
        if record.name != "asyncio" or record.levelno != logging.ERROR:
            return True

        message = record.getMessage()
        return not any(pattern in message for pattern in self.SUPPRESSED_PATTERNS)


class ThirdPartyNoiseFilter(logging.Filter):
    """Filter to suppress noisy third-party warnings and messages."""

    SUPPRESSED_MESSAGES: ClassVar[list[str]] = [
        "libEGL warning:",
        "MESA: error:",
        "WARNING: All log messages before absl::InitializeLog()",
        "No Application was created, trying to deduce paths",
        "Feedback manager requires a model with a single signature",
        "DRI3: Screen seems not DRI3 capable",
        "egl: failed to create dri2 screen",
        "Successfully initialized EGL",
        "GL version:",
        "Created TensorFlow Lite XNNPACK delegate",
        "ZINK: failed to choose pdev",
    ]

    @override
    def filter(self, record: logging.LogRecord) -> bool:  # type: ignore[override]
        """Filter out noisy third-party messages.

        Args:
            record: The log record to filter.

        Returns:
            False if the record should be suppressed, True otherwise.
        """
        message = record.getMessage()
        return not any(suppressed in message for suppressed in self.SUPPRESSED_MESSAGES)


def setup_structured_logging() -> Result[None, LoggingSetupError]:
    """Configure high-performance structured logging for the entire application.

    This function sets up structlog with optimal performance settings:
    - Logger caching for better performance
    - Level-based filtering for early rejection of debug logs
    - Fast JSON serialization when available
    - Proper integration with stdlib logging
    - Unified configuration that replaces traditional logging setup

    Returns:
        Result indicating success or failure with error details.
    """
    try:
        # Clear any existing configuration to avoid conflicts
        _clear_existing_handlers()
        structlog.reset_defaults()

        # Use structlog's recommended stdlib integration
        structlog.stdlib.recreate_defaults()

        # Setup processors and configuration based on environment
        config_result = _configure_logging_environment()
        if isinstance(config_result, Err):
            return config_result

        processors, wrapper_class, logger_factory = config_result.unwrap()

        # Configure structlog with performance optimizations
        structlog.configure(
            processors=processors,
            wrapper_class=wrapper_class,
            logger_factory=logger_factory,
            # PERFORMANCE: Cache loggers for significant speed improvement
            cache_logger_on_first_use=True,
        )

        # Configure third-party loggers
        third_party_result = _configure_third_party_loggers()
        if isinstance(third_party_result, Err):
            return third_party_result

        # Setup file logging for non-development environments
        if settings.environment != Environment.DEVELOPMENT:
            file_result = _setup_file_handler()
            if isinstance(file_result, Err):
                return file_result

        # Add filters to reduce noise
        _add_logging_filters()

        # Log successful setup with performance info
        logger = get_logger(__name__)
        logger.info(
            "Structured logging configured successfully",
            environment=settings.environment.value,
            log_level="DEBUG"
            if settings.environment == Environment.DEVELOPMENT
            else "INFO",
            performance_optimized=True,
            json_serializer="orjson" if _has_orjson else "stdlib",
            logger_caching=True,
            level_filtering=True,
            unified_logging=True,
        )

        return Ok(None)

    except Exception as e:
        # Fallback to basic logging if structlog setup fails
        logging.basicConfig(level=logging.INFO, format="%(levelname)s: %(message)s")
        error = LoggingSetupError(f"Failed to setup structured logging: {e}", e)
        logger = logging.getLogger(__name__)
        logger.exception("Structured logging setup failed, using fallback")
        return Err(error)


def _clear_existing_handlers() -> None:
    """Clear any existing handlers to avoid conflicts and duplication."""
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
        handler.close()


def _configure_logging_environment() -> Result[
    tuple[list[ProcessorType], WrapperClass, LoggerFactory], LoggingSetupError
]:
    """Configure logging based on the current environment.

    Returns:
        Result containing tuple of (processors, wrapper_class, logger_factory) or error.
    """
    try:
        # Always use stdlib logger factory for better compatibility
        # The performance gain from BytesLoggerFactory isn't worth the compatibility issues
        logger_factory = structlog.stdlib.LoggerFactory()
        json_renderer = structlog.processors.JSONRenderer(serializer=_json_serializer)

        # Configure processors based on environment
        if settings.environment == Environment.DEVELOPMENT:
            # Development: clean, readable console output
            processors: list[ProcessorType] = [
                structlog.contextvars.merge_contextvars,
                structlog.stdlib.filter_by_level,
                _add_app_context,
                _add_performance_context,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.stdlib.PositionalArgumentsFormatter(),
                structlog.processors.TimeStamper(fmt="%H:%M:%S", utc=False),
                structlog.processors.StackInfoRenderer(),
                structlog.processors.format_exc_info,
                structlog.processors.UnicodeDecoder(),
                # Clean up the context for readability BEFORE rendering
                _clean_dev_context,
                _create_enhanced_console_renderer(),
            ]

            # Configure stdlib logging for development
            logging.basicConfig(
                format="%(message)s",
                stream=sys.stderr,
                level=logging.DEBUG,
                force=True,  # Override existing configuration
            )

            # Use debug-level filtering for development
            wrapper_class = structlog.make_filtering_bound_logger(logging.DEBUG)

        else:
            # Production: JSON output with performance optimizations
            processors = [
                structlog.contextvars.merge_contextvars,
                structlog.stdlib.filter_by_level,
                _add_app_context,
                _add_performance_context,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.stdlib.PositionalArgumentsFormatter(),
                # Use UTC timestamps for production
                structlog.processors.TimeStamper(fmt="iso", utc=True),
                structlog.processors.StackInfoRenderer(),
                structlog.processors.format_exc_info,
                structlog.processors.UnicodeDecoder(),
                structlog.processors.CallsiteParameterAdder(
                    parameters=[
                        structlog.processors.CallsiteParameter.FILENAME,
                        structlog.processors.CallsiteParameter.FUNC_NAME,
                        structlog.processors.CallsiteParameter.LINENO,
                    ]
                ),
                json_renderer,
            ]

            # Configure stdlib logging for production
            logging.basicConfig(
                format="%(message)s",
                stream=sys.stdout,
                level=logging.INFO,
                force=True,  # Override existing configuration
            )

            # Use info-level filtering for production (better performance)
            wrapper_class = structlog.make_filtering_bound_logger(logging.INFO)

        return Ok((processors, wrapper_class, logger_factory))

    except (OSError, ValueError, TypeError, ImportError) as e:
        return Err(
            LoggingSetupError(
                f"Failed to configure environment-specific logging: {e}", e
            )
        )


def _clean_dev_context(
    _logger: object, _method_name: str, event_dict: EventDict
) -> EventDict:
    """Clean up context for readable development logs.

    Args:
        _logger: The wrapped logger object (unused but required by structlog).
        _method_name: The name of the method being called (unused but required by structlog).
        event_dict: Current event dictionary.

    Returns:
        Cleaned event dictionary with reduced metadata for readability.
    """
    # Skip cleaning if verbose logging is enabled
    if os.getenv("STRUCTLOG_VERBOSE") == "1":
        return event_dict

    # Remove noisy fields that clutter development logs
    fields_to_remove = [
        "app",
        "environment",
        "filename",
        "func_name",
        "lineno",
        "local_time",
        "pid",
        "performance_optimized",
        "json_serializer",
        "logger_caching",
        "level_filtering",
        "unified_logging",
    ]

    for field in fields_to_remove:
        event_dict.pop(field, None)

    # Keep the logger name but make it shorter and cleaner
    if "logger" in event_dict:
        logger_name: object = event_dict["logger"]
        if isinstance(logger_name, str):
            # Map common logger patterns to cleaner names
            logger_mappings = {
                "mobirobot.": "",  # Remove mobirobot prefix
                "src.": "",  # Remove src prefix
                "nao.": "🤖 ",  # NAO services with robot emoji
                "webcam.webcam_service": "📹 webcam",
                "stream.stream_service": "📺 stream",
                "speech.service": "🗣️ speech",
                "speech.sentence_repository": "📚 sentences",
                "pose.exercise_tracker": "🏃 tracker",
                "pose.pose_detector": "👁️ pose",
                "exerciser.": "💪 ",
                "monitoring.": "📊 ",
                "web.": "🌐 ",
                "regiments.regiment_service": "📋 regiments",
                "common.logging_setup": "⚙️ logging",
                "common.structured_logging": "⚙️ logging",
            }

            # Apply mappings in order of specificity (longer patterns first)
            sorted_mappings = sorted(
                logger_mappings.items(), key=lambda x: len(x[0]), reverse=True
            )

            for pattern, replacement in sorted_mappings:
                if logger_name.startswith(pattern):
                    event_dict["logger"] = replacement + logger_name[len(pattern) :]
                    break
            else:
                # If no mapping found, keep original but clean up common prefixes
                if "." in logger_name:
                    # For unmapped dotted names, just use the last component
                    event_dict["logger"] = logger_name.split(".")[-1]

    return event_dict


def _create_enhanced_console_renderer() -> structlog.dev.ConsoleRenderer:
    """Create an enhanced console renderer for development.

    Returns:
        Configured console renderer with optimal settings.
    """
    return structlog.dev.ConsoleRenderer(
        colors=True,
        force_colors=os.getenv("FORCE_COLOR") is not None,
        # Only show essential fields in development for readability
        repr_native_str=False,
        level_styles={
            "critical": "\033[1;37;41m",  # Bold white on red
            "exception": "\033[1;37;41m",  # Bold white on red
            "error": "\033[1;31m",  # Bold red
            "warn": "\033[1;33m",  # Bold yellow
            "warning": "\033[1;33m",  # Bold yellow
            "info": "\033[1;32m",  # Bold green
            "debug": "\033[1;34m",  # Bold blue
        },
    )


def _setup_file_handler() -> Result[None, LoggingSetupError]:
    """Set up rotating file handler with structured JSON formatting.

    Returns:
        Result indicating success or failure.
    """
    try:
        # Ensure logging directory exists
        logging_path = Path(settings.logging_path)
        logging_path.mkdir(parents=True, exist_ok=True)

        # Use timezone-aware datetime for filename
        now_utc = datetime.datetime.now(tz=zoneinfo.ZoneInfo("UTC"))
        log_filename = f"mobirobot-{now_utc.strftime('%Y-%m-%d_%H-%M-%S')}.log"
        log_file_path = logging_path / log_filename

        # Create file handler with rotation
        file_handler = logging.handlers.RotatingFileHandler(
            filename=log_file_path,
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=5,
            encoding="utf-8",
        )
        file_handler.setLevel(logging.DEBUG)

        # Use the same JSON formatter for consistency
        json_formatter = structlog.stdlib.ProcessorFormatter(
            processor=structlog.processors.JSONRenderer(serializer=_json_serializer),
            foreign_pre_chain=[
                structlog.contextvars.merge_contextvars,
                structlog.stdlib.add_log_level,
                structlog.processors.TimeStamper(fmt="iso", utc=True),
                _add_app_context,
                _add_performance_context,
            ],
        )

        file_handler.setFormatter(json_formatter)

        # Add to root logger
        root_logger = logging.getLogger()
        root_logger.addHandler(file_handler)

        return Ok(None)

    except (OSError, PermissionError, ValueError) as e:
        return Err(LoggingSetupError(f"Failed to setup file handler: {e}", e))


def _add_logging_filters() -> None:
    """Add filters to reduce noise from third-party libraries."""
    root_logger = logging.getLogger()

    # Add filters to all handlers
    asyncio_filter = AsyncioShutdownFilter()
    noise_filter = ThirdPartyNoiseFilter()

    for handler in root_logger.handlers:
        handler.addFilter(asyncio_filter)
        handler.addFilter(noise_filter)


def _configure_nao_loggers() -> None:
    """Configure NAO package loggers."""
    nao_loggers = [
        "nao",
        "nao.nao_robot",
        "nao.connection_manager",
        "nao.signal_manager",
        "nao.task_manager",
        "nao.services",
        "nao.services.base_service",
        "nao.services.audio_player",
        "nao.helpers",
    ]
    for logger_name in nao_loggers:
        logging.getLogger(logger_name).setLevel(logging.INFO)


def _configure_web_loggers() -> None:
    """Configure web framework loggers."""
    web_loggers = {
        "urllib3": logging.WARNING,
        "websockets": logging.INFO,
        "uvicorn": logging.INFO,
        "uvicorn.access": logging.WARNING,
        "uvicorn.error": logging.INFO,
        "fastapi": logging.INFO,
    }
    for logger_name, level in web_loggers.items():
        logging.getLogger(logger_name).setLevel(level)


def _configure_system_loggers() -> None:
    """Configure system and async loggers."""
    system_loggers = {
        "asyncio": logging.WARNING,
        "concurrent.futures": logging.WARNING,
        "multiprocessing": logging.WARNING,
    }
    for logger_name, level in system_loggers.items():
        logging.getLogger(logger_name).setLevel(level)


def _configure_verbose_loggers() -> None:
    """Configure verbose third-party loggers to reduce noise."""
    verbose_loggers = {
        # MediaPipe warnings are very noisy and not actionable
        "absl": logging.WARNING,
        "mediapipe": logging.WARNING,
        # TensorFlow/TFLite inference warnings
        "tensorflow": logging.ERROR,
        "tflite": logging.ERROR,
        # OpenCV/MESA warnings about hardware acceleration
        "opencv": logging.ERROR,
        # Qt/NAOqi internal logging
        "qi": logging.WARNING,
        "qi.path": logging.ERROR,  # "No Application was created" messages
        # Sentry SDK noise
        "sentry_sdk": logging.WARNING,
        # Threading and async noise
        "concurrent.futures": logging.WARNING,
        "asyncio": logging.WARNING,
    }
    for logger_name, level in verbose_loggers.items():
        logging.getLogger(logger_name).setLevel(level)


def _configure_development_loggers() -> None:
    """Configure development-specific loggers."""
    if settings.environment == Environment.DEVELOPMENT:
        dev_loggers = {
            "sentry_sdk": logging.WARNING,
            "watchfiles": logging.WARNING,
        }
        for logger_name, level in dev_loggers.items():
            logging.getLogger(logger_name).setLevel(level)


def _configure_noisy_loggers() -> None:
    """Configure loggers that generate excessive noise in development."""
    # These loggers are particularly noisy and should be quieted in all environments
    noisy_loggers = {
        # MESA/OpenGL warnings about virtualization/containers
        "libEGL": logging.ERROR,
        "MESA": logging.ERROR,
        # MediaPipe inference feedback warnings
        "inference_feedback_manager": logging.ERROR,
        # NAOqi path warnings in simulation
        "qi.path.sdklayout": logging.ERROR,
    }
    for logger_name, level in noisy_loggers.items():
        logging.getLogger(logger_name).setLevel(level)


def _configure_third_party_loggers() -> Result[None, LoggingSetupError]:
    """Configure logging levels for third-party packages.

    Returns:
        Result indicating success or failure.
    """
    try:
        _configure_nao_loggers()
        _configure_web_loggers()
        _configure_system_loggers()
        _configure_verbose_loggers()
        _configure_development_loggers()
        _configure_noisy_loggers()

        return Ok(None)

    except (ValueError, TypeError, AttributeError) as e:
        return Err(
            LoggingSetupError(f"Failed to configure third-party loggers: {e}", e)
        )


def get_logger(name: str | None = None) -> LoggerType:
    """Get a high-performance structured logger instance.

    This function follows structlog best practices by using proper typing
    and avoiding the Any return type issue.

    Args:
        name: Logger name. If None, uses the caller's module name.

    Returns:
        A bound structlog logger instance with optimal performance.

    Note:
        The returned logger is cached for performance when
        cache_logger_on_first_use=True is set in configuration.
    """
    # Use stdlib.get_logger for proper typing as recommended by structlog docs
    return structlog.stdlib.get_logger(name)


def get_bound_logger(name: str | None = None, **initial_context: object) -> LoggerType:
    """Get a logger with initial context bound for performance.

    This is more efficient than calling .bind() repeatedly when you
    know the context upfront.

    Args:
        name: Logger name. If None, uses the caller's module name.
        **initial_context: Initial context to bind to the logger.

    Returns:
        A bound logger with the initial context.

    Example:
        >>> logger = get_bound_logger(__name__, service="exercise", user_id="123")
        >>> logger.info("Exercise started", exercise_id="arm_raise")
    """
    base_logger = get_logger(name)
    return base_logger.bind(**initial_context)


def get_service_logger(service_name: str, **context: object) -> LoggerType:
    """Get a logger specifically configured for a service.

    This provides a standardized way to create service-specific loggers
    with consistent context.

    Args:
        service_name: Name of the service (e.g., "exercise", "nao", "webcam").
        **context: Additional context to bind to the logger.

    Returns:
        A bound logger with service context.
    """
    return get_bound_logger(
        f"mobirobot.{service_name}", service=service_name, **context
    )


# Legacy functions for backward compatibility
def get_structured_logger(name: str | None = None) -> LoggerType:
    """Get a structured logger (alias for get_logger).

    Deprecated: Use get_logger() instead for better performance.

    Args:
        name: Logger name. If None, uses the caller's module name.

    Returns:
        A structured logger instance.
    """
    return get_logger(name)


# Public API for external integration
__all__ = [
    "LoggerType",
    "LoggingSetupError",
    "get_bound_logger",
    "get_logger",
    "get_service_logger",
    "setup_structured_logging",
]
