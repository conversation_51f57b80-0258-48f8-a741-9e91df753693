"""Provides common utilities for the project.

This module contains utility functions and classes that are used across
the project. It includes functions for data processing, logging, and
configuration management.
It also includes custom exceptions for error handling.
This module is designed to be imported by other modules in the project
"""

from .config import settings
from .logging_setup import setup_logging, setup_sentry
from .observable import Observable
from .result_types import Err, Ok, Result

__all__ = [
    "Err",
    "Observable",
    "Ok",
    "Result",
    "settings",
    "setup_logging",
    "setup_sentry",
]
