"""Traditional logging configuration - legacy compatibility wrapper.

This module provides backwards compatibility for traditional logging setups
while leveraging the new structured logging infrastructure. It's recommended
to migrate to structured logging using get_logger() from structured_logging.py.

For new code, prefer:
    from mobirobot.common.structured_logging import get_logger
    logger = get_logger(__name__)
"""

import datetime
import logging
import logging.handlers
import os
import zoneinfo
from pathlib import Path
from typing import ClassVar, override

import sentry_sdk
from sentry_sdk.integrations.logging import LoggingIntegration

from mobirobot.common.config import Environment, settings
from mobirobot.common.result_types import Err, Ok, Result
from mobirobot.common.structured_logging import (
    LoggingSetupError,
    get_logger,
    setup_structured_logging,
)


def setup_logging() -> Result[None, LoggingSetupError]:
    """Configure logging for the application.

    This function now serves as a wrapper around the structured logging setup
    for backwards compatibility. New code should use setup_structured_logging() directly.

    Returns:
        Result indicating success or failure with error details.
    """
    # Use the new structured logging setup
    setup_result = setup_structured_logging()
    if isinstance(setup_result, Err):
        return setup_result

    # Setup Sentry if configured
    sentry_result = setup_sentry()
    if isinstance(sentry_result, Err):
        # Log warning but don't fail the whole setup
        logger = get_logger(__name__)
        logger.warning(
            "Sentry setup failed, continuing without error tracking",
            error=str(sentry_result.unwrap_err()),
        )

    # Print startup banner for development
    if settings.environment == Environment.DEVELOPMENT:
        _print_startup_banner()

    # Log successful setup
    logger = get_logger(__name__)
    logger.info(
        "Legacy logging setup completed successfully",
        environment=settings.environment.value,
        structured_logging=True,
        sentry_enabled=bool(settings.sentry_dsn),
    )

    return Ok(None)


def setup_file_logging() -> logging.handlers.RotatingFileHandler:
    """Configure file logging for the application with rotation.

    Note: This function is deprecated. File logging is now handled
    automatically by setup_structured_logging() in production environments.

    Returns:
        A configured RotatingFileHandler instance.
    """
    logger = get_logger(__name__)
    logger.warning(
        "setup_file_logging() is deprecated. File logging is handled automatically in production."
    )

    # Create a basic file handler for compatibility
    now_utc = datetime.datetime.now(tz=zoneinfo.ZoneInfo("UTC"))
    log_filename = f"legacy-backend-{now_utc.strftime('%Y-%m-%d_%H-%M-%S')}.log"
    log_file_path = Path(settings.logging_path) / log_filename

    # Create rotating file handler (10MB max, keep 5 backups)
    file_handler = logging.handlers.RotatingFileHandler(
        filename=log_file_path,
        maxBytes=10 * 1024 * 1024,  # 10MB
        backupCount=5,
        encoding="utf-8",
    )
    file_handler.setLevel(logging.DEBUG)

    # Use structured formatter for file logs
    file_formatter = logging.Formatter(
        fmt="%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )
    file_handler.setFormatter(file_formatter)

    return file_handler


def setup_sentry() -> Result[None, LoggingSetupError]:
    """Configure Sentry integration if DSN is provided.

    Returns:
        Result indicating success or failure with error details.
    """
    if not settings.sentry_dsn:
        logger = get_logger(__name__)
        logger.debug("No Sentry DSN provided, skipping Sentry setup")
        return Ok(None)

    try:
        sentry_sdk.init(
            dsn=settings.sentry_dsn,
            environment=settings.environment.value,
            integrations=[
                LoggingIntegration(
                    level=logging.DEBUG,  # Capture debug logs
                    event_level=logging.WARNING,  # Send warnings and above as events
                )
            ],
            # Sample rate for performance monitoring (adjust as needed)
            traces_sample_rate=0.1
            if settings.environment == Environment.PRODUCTION
            else 1.0,
            # Release tracking
            release=None,  # Could be set from environment variable
        )

        # Set contextual tags
        if hasattr(settings, "station") and settings.station:
            sentry_sdk.set_tag("station", settings.station.value)
        sentry_sdk.set_tag("environment", settings.environment.value)

        logger = get_logger(__name__)
        logger.info("Sentry integration configured successfully")
        return Ok(None)

    except Exception as e:
        return Err(LoggingSetupError(f"Failed to setup Sentry: {e}", e))


def _print_startup_banner() -> None:
    """Print a startup banner with application information."""
    logger = get_logger("mobirobot.startup")

    # Only show banner in development mode to avoid cluttering production logs
    if settings.environment != Environment.DEVELOPMENT:
        return

    banner_lines = [
        "",
        "╭─────────────────────────────────────────────────────────────╮",
        "│                    🤖 MobiRobot Backend                     │",
        "│                                                             │",
        f"│  Environment: {settings.environment.value.upper():10}                              │",
        f"│  Station:     {getattr(settings.station, 'value', 'Not Set'):10}                              │",
        "│  Logging:     Structured (structlog) + Legacy Support      │",
        "│                                                             │",
        "│  Ready to connect to NAO robot and serve web interface     │",
        "╰─────────────────────────────────────────────────────────────╯",
        "",
    ]

    for line in banner_lines:
        logger.info(line)


class EnhancedColoredFormatter(logging.Formatter):
    """An enhanced formatter that adds color and better structure to log messages.

    Note: This formatter is primarily kept for backwards compatibility.
    The structured logging system provides better formatting and performance.

    This formatter:
    - Adds ANSI color codes for different log levels when output is a terminal
    - Uses consistent formatting with timestamps, levels, and module context
    - Shows service/component hierarchy for better navigation
    - Provides visual separation between log entries
    - Automatically detects terminal capability and disables colors when appropriate
    - Preserves original record attributes to avoid side effects
    """

    # Constants for magic value replacements
    MIN_NAME_PARTS_FOR_HIERARCHY: ClassVar[int] = 2
    COMPONENT_WIDTH: ClassVar[int] = 30
    LEVEL_WIDTH: ClassVar[int] = 10

    COLORS: ClassVar[dict[str, str]] = {
        "DEBUG": "\033[36m",  # Cyan
        "INFO": "\033[32m",  # Green
        "WARNING": "\033[33m",  # Yellow
        "ERROR": "\033[31m",  # Red
        "CRITICAL": "\033[41m",  # Red background
    }

    LEVEL_ICONS: ClassVar[dict[str, str]] = {
        "DEBUG": "🔍",
        "INFO": "ℹ️ ",  # noqa: RUF001
        "WARNING": "⚠️ ",
        "ERROR": "❌",
        "CRITICAL": "🚨",
    }

    RESET: ClassVar[str] = "\033[0m"
    BOLD: ClassVar[str] = "\033[1m"
    DIM: ClassVar[str] = "\033[2m"

    use_colors: bool
    use_icons: bool

    def __init__(
        self, *, use_colors: bool | None = None, use_icons: bool = True
    ) -> None:
        """Initialize the EnhancedColoredFormatter.

        Args:
            use_colors: Whether to use colors. If None, auto-detect based on terminal.
            use_icons: Whether to use emoji icons for log levels.
        """
        super().__init__(
            fmt="%(asctime)s %(levelname_colored)s %(component)s %(message)s",
            datefmt="%H:%M:%S",
        )

        # Auto-detect color support if not explicitly set
        if use_colors is None:
            self.use_colors = os.isatty(2) and os.getenv("NO_COLOR") is None
        else:
            self.use_colors = use_colors

        self.use_icons = use_icons and self.use_colors

    @override
    def format(self, record: logging.LogRecord) -> str:
        """Format the log record with colors and improved structure.

        Args:
            record: The log record to format.

        Returns:
            The formatted log message string.
        """
        # Store original values to restore later - these are all string attributes
        orig_values: dict[str, str] = {}
        for attr in ["levelname", "name", "funcName"]:
            if hasattr(record, attr):
                value = getattr(record, attr)
                if isinstance(value, str):
                    orig_values[attr] = value

        try:
            # Create enhanced component display
            record.component = self._format_component(record)

            # Create colored level name
            record.levelname_colored = self._format_level(record)

            # Format the message
            formatted = super().format(record)

            # Add visual separation for errors and warnings
            if record.levelno >= logging.WARNING and self.use_colors:
                formatted = f"{self.DIM}{'─' * 80}{self.RESET}\n{formatted}\n{self.DIM}{'─' * 80}{self.RESET}"

            return formatted

        finally:
            # Always restore original values to avoid side effects
            for attr, value in orig_values.items():
                setattr(record, attr, value)

    def _format_component(self, record: logging.LogRecord) -> str:
        """Format the component/service name for better readability.

        Args:
            record: The log record to format.

        Returns:
            Formatted component string.
        """
        name_parts = record.name.split(".")
        component = self._extract_component_name(name_parts)
        component = self._add_function_context(component, record)
        return self._apply_component_styling(component, record)

    def _extract_component_name(self, name_parts: list[str]) -> str:
        """Extract the meaningful component name from logger name parts.

        Args:
            name_parts: List of parts from the logger name.

        Returns:
            The extracted component name.
        """
        if len(name_parts) < self.MIN_NAME_PARTS_FOR_HIERARCHY:
            return name_parts[-1] if name_parts else "unknown"

        if "mobirobot" in name_parts:
            return self._handle_mobirobot_component(name_parts)
        if name_parts[0] in {"uvicorn", "fastapi"}:
            return f"web.{name_parts[-1]}"
        if name_parts[0] == "nao":
            return self._handle_nao_component(name_parts)
        if name_parts[0] in {"qi", "tensorflow", "mediapipe"}:
            return f"ext.{name_parts[0]}"
        return name_parts[-1]

    @staticmethod
    def _handle_mobirobot_component(name_parts: list[str]) -> str:
        """Handle component extraction for mobirobot modules.

        Args:
            name_parts: List of parts from the logger name.

        Returns:
            The extracted mobirobot component name.
        """
        relevant_parts = [p for p in name_parts if not p.startswith("mobirobot")]
        return relevant_parts[-1] if relevant_parts else name_parts[-1]

    def _handle_nao_component(self, name_parts: list[str]) -> str:
        """Handle component extraction for NAO modules.

        Args:
            name_parts: List of parts from the logger name.

        Returns:
            The extracted NAO component name.
        """
        if len(name_parts) > self.MIN_NAME_PARTS_FOR_HIERARCHY:
            return f"nao.{'.'.join(name_parts[1:])}"
        return f"nao.{name_parts[-1]}"

    @staticmethod
    def _add_function_context(component: str, record: logging.LogRecord) -> str:
        """Add function context for debug logs and errors.

        Args:
            component: The base component name.
            record: The log record.

        Returns:
            Component name with optional function context.
        """
        should_add_function = (
            record.levelno == logging.DEBUG or record.levelno >= logging.WARNING
        ) and hasattr(record, "funcName")

        if should_add_function:
            return f"{component}.{record.funcName}"
        return component

    def _apply_component_styling(
        self, component: str, record: logging.LogRecord
    ) -> str:
        """Apply appropriate styling to the component name.

        Args:
            component: The component name.
            record: The log record.

        Returns:
            Styled component string with fixed width.
        """
        if not self.use_colors:
            return f"[{component}]".ljust(self.COMPONENT_WIDTH)

        if record.levelno >= logging.ERROR:
            styled = f"{self.COLORS['ERROR']}[{component}]{self.RESET}"
        elif record.levelno == logging.WARNING:
            styled = f"{self.COLORS['WARNING']}[{component}]{self.RESET}"
        elif record.levelno == logging.DEBUG:
            styled = f"{self.DIM}[{component}]{self.RESET}"
        else:
            styled = f"{self.DIM}[{component}]{self.RESET}"

        return styled.ljust(self.COMPONENT_WIDTH)

    def _format_level(self, record: logging.LogRecord) -> str:
        """Format the log level with colors and icons.

        Args:
            record: The log record to format.

        Returns:
            Formatted level string.
        """
        level = record.levelname

        if self.use_icons:
            icon = self.LEVEL_ICONS.get(level, "  ")
            level_display = f"{icon} {level}"
        else:
            level_display = level

        if self.use_colors:
            color = self.COLORS.get(level, "")
            if color:
                level_display = f"{color}{self.BOLD}{level_display}{self.RESET}"

        return f"{level_display:12}"  # Fixed width for alignment


# Legacy alias for backwards compatibility
ColoredFormatter = EnhancedColoredFormatter


# Public API for external integration (legacy compatibility)
__all__ = [
    "ColoredFormatter",
    "EnhancedColoredFormatter",
    "setup_file_logging",
    "setup_logging",
    "setup_sentry",
]
