# ruff: noqa: PLR0904, PLR6301
"""Result types for control flow without exceptions.

This module provides a Result type as an alternative to exceptions
for normal program flow control. Use this when the operation might fail
but it's not an exceptional circumstance.

Examples:
    # Instead of raising exceptions for validation
    def validate_email(email: str) -> Result[str, str]:
        if "@" not in email:
            return Err("Invalid email format")
        return Ok(email.lower())

    # Usage
    result = validate_email("<EMAIL>")
    if result.is_ok():
        clean_email = result.unwrap()
    else:
        error_msg = result.unwrap_err()

    # Or use pattern matching (Python 3.10+)
    match validate_email("<EMAIL>"):
        case Ok(email):
            print(f"Valid email: {email}")
        case Err(error):
            print(f"Invalid email: {error}")
"""

from __future__ import annotations

from typing import (
    TYPE_CHECKING,
    NoReturn,
    Protocol,
    TypeGuard,
    TypeVar,
    final,
    override,
)

if TYPE_CHECKING:
    from collections.abc import Callable, Iterator

T = TypeVar("T", covariant=True)  # noqa: PLC0105
E = TypeVar("E", covariant=True)  # noqa: PLC0105
U = TypeVar("U")
F = TypeVar("F")


class ResultError(Exception):
    """Exception raised when Result operations fail."""

    def __init__(self, message: str) -> None:
        """Initialize the result error."""
        super().__init__(message)


class ResultProtocol[T, E](Protocol):
    """Protocol that defines the interface for Result types."""

    def is_ok(self) -> bool:
        """Return True if the result is Ok."""
        ...

    def is_err(self) -> bool:
        """Return True if the result is Err."""
        ...

    def unwrap(self) -> T:
        """Return the contained Ok value or raise an exception."""
        ...

    def unwrap_err(self) -> E:
        """Return the contained Err value or raise an exception."""
        ...

    def unwrap_or(self, default: T) -> T:
        """Return the contained Ok value or the provided default."""
        ...

    def map[U](self, op: Callable[[T], U]) -> ResultProtocol[U, E]:
        """Map the Ok value using the provided function."""
        ...

    def map_err[F](self, op: Callable[[E], F]) -> ResultProtocol[T, F]:
        """Map the Err value using the provided function."""
        ...


@final
class Ok[T]:  # ruff: noqa: PLR0904
    """A successful Result."""

    __match_args__ = ("ok_value",)
    __slots__ = ("_value",)
    _value: T

    def __init__(self, value: T) -> None:
        """Initialize the Ok Result."""
        self._value = value

    def __iter__(self) -> Iterator[T]:
        """Iterate over the Ok value (yields the value once)."""
        yield self._value

    @override
    def __eq__(self, other: object) -> bool:
        """Check if the Ok Result is equal to another object."""
        if not isinstance(other, Ok):
            return False
        return self._value == other._value  # pyright: ignore[reportUnknownMemberType, reportUnknownVariableType]

    @override
    def __hash__(self) -> int:
        """Hash the Ok Result."""
        return hash(self._value)

    @override
    def __repr__(self) -> str:
        """Return a string representation of the Ok Result."""
        return f"Ok({self._value!r})"

    def is_ok(self) -> bool:
        """Return True if the result is Ok."""
        return True

    def is_err(self) -> bool:
        """Return True if the result is Err."""
        return False

    def unwrap(self) -> T:
        """Return the contained Ok value."""
        return self._value

    def unwrap_err(self) -> NoReturn:
        """Raise an exception if called on Ok."""
        msg = "Called unwrap_err() on an Ok value"
        raise ResultError(msg)

    def expect(self, _msg: str) -> T:
        """Return the contained Ok value."""
        return self._value

    def expect_err(self, msg: str) -> NoReturn:
        """Raise an exception with a custom message if called on Ok."""
        raise ResultError(msg)

    def unwrap_or(self, _default: T) -> T:
        """Return the contained Ok value or the provided default."""
        return self._value

    def unwrap_or_else(self, _op: Callable[[E], T]) -> T:
        """Return the contained Ok value or compute it from a closure."""
        return self._value

    def ok(self) -> T | None:
        """Convert from Result<T, E> to Option<T>."""
        return self._value

    def err(self) -> object | None:
        """Convert from Result<T, E> to Option<E>."""
        return None

    @property
    def ok_value(self) -> T:
        """Get the Ok value."""
        return self._value

    def and_then[U, E](self, op: Callable[[T], Result[U, E]]) -> Result[U, E]:
        """The contained result is `Ok`, so return the result of `op` with the original value passed in.

        Parameters:
            op (Callable[[T], Result[U, E]]): The function to apply to the Ok value.

        Returns:
            Result[U, E]: The result of the function applied to the Ok value.
        """
        return op(self._value)

    def map[U, E](self, op: Callable[[T], U]) -> Result[U, E]:
        """Maps a `Result[T, E]` to `Result[U, E]` by applying a function to a contained `Ok` value, leaving an `Err` value untouched.

        Parameters:
            op (Callable[[T], U]): The function to apply to the Ok value.

        Returns:
            Result[U, E]: The result of the function applied to the Ok value.
        """
        return Ok(op(self._value))

    def map_or[U](self, _default: U, f: Callable[[T], U]) -> U:
        """Returns the provided default (if Err), or applies a function to the contained value (if Ok).

        Parameters:
            _default (U): The default value to return if the result is Err.
            f (Callable[[T], U]): The function to apply to the Ok value.

        Returns:
            U: The result of the function applied to the Ok value.
        """
        return f(self._value)

    def map_or_else[U](self, _default: Callable[[object], U], f: Callable[[T], U]) -> U:
        """Maps a Result<T, E> to U by applying fallback function default to a contained Err value, or function f to a contained Ok value.

        Parameters:
            _default (Callable[[object], U]): The default function to apply if the result is Err.
            f (Callable[[T], U]): The function to apply to the Ok value.

        Returns:
            U: The result of the function applied to the Ok value.
        """
        return f(self._value)

    def map_err[F](self, _op: Callable[[object], F]) -> Result[T, F]:
        """Maps a `Result[T, E]` to `Result[T, F]` by applying a function to a contained `Err` value, leaving an `Ok` value untouched.

        Parameters:
            _op (Callable[[object], F]): The function to apply to the Err value.

        Returns:
            Result[T, F]: The result of the function applied to the Err value.
        """
        return Ok(self._value)

    def or_else[E, F](self, _op: Callable[[E], Result[T, F]]) -> Result[T, F]:
        """Returns the result if it is `Ok`, otherwise calls `op` with the error value and returns the result.

        Parameters:
            _op (Callable[[E], Result[T, F]]): The function to apply to the Err value.

        Returns:
            Result[T, F]: The result of the function applied to the Err value.
        """
        return Ok(self._value)

    def inspect[E](self, f: Callable[[T], None]) -> Result[T, E]:
        """Calls a function with a reference to the contained value if Ok. Returns the original result.

        Parameters:
            f (Callable[[T], None]): The function to apply to the Ok value.

        Returns:
            Result[T, E]: The original result.
        """
        f(self._value)
        return self

    def inspect_err[E](self, _f: Callable[[object], None]) -> Result[T, E]:
        """Calls a function with a reference to the contained value if Err. Returns the original result.

        Parameters:
            _f (Callable[[object], None]): The function to apply to the Err value.

        Returns:
            Result[T, E]: The original result.
        """
        return self


@final
class Err[E]:  # ruff: noqa: PLR0904
    """A failed Result."""

    __match_args__ = ("err_value",)
    __slots__ = ("_error",)
    _error: E

    def __init__(self, error: E) -> None:
        """Initialize the Err Result."""
        self._error = error

    def __iter__(self) -> Iterator[T]:
        """Iterate over the Err value (yields nothing)."""
        return iter([])

    @override
    def __repr__(self) -> str:
        """Return a string representation of the Err Result."""
        return f"Err({self._error!r})"

    def is_ok(self) -> bool:
        """Return True if the result is Ok."""
        return False

    def is_err(self) -> bool:
        """Return True if the result is Err."""
        return True

    def unwrap(self) -> NoReturn:
        """Raise an exception if called on Err."""
        msg = f"Called unwrap() on an Err value: {self._error}"
        raise ResultError(msg)

    def unwrap_err(self) -> E:
        """Return the contained Err value."""
        return self._error

    def expect(self, msg: str) -> NoReturn:
        """Raise an exception with a custom message if called on Err."""
        raise ResultError(msg)

    def expect_err(self, _msg: str) -> E:
        """Return the contained Err value."""
        return self._error

    def unwrap_or(self, default: T) -> T:
        """Return the provided default since this is an Err."""
        return default

    def unwrap_or_else(self, op: Callable[[E], T]) -> T:
        """Compute the value from a closure since this is an Err."""
        return op(self._error)

    def ok(self) -> object | None:
        """Convert from Result<T, E> to Option<T>."""
        return None

    def err(self) -> E | None:
        """Convert from Result<T, E> to Option<E>."""
        return self._error

    @property
    def err_value(self) -> E:
        """Get the Err value."""
        return self._error

    @override
    def __eq__(self, other: object) -> bool:
        """Check if the Err Result is equal to another object."""
        if not isinstance(other, Err):
            return False
        return self._error == other._error

    @override
    def __hash__(self) -> int:
        """Hash the Err Result."""
        return hash(self._error)

    def and_then(self, _op: object) -> Err[E]:
        """The contained result is `Err`, so return `Err` with the original value."""
        return self

    def map[T, U](self, _op: Callable[[T], U]) -> Result[U, E]:
        """Maps a `Result[T, E]` to `Result[U, E]` by applying a function to a contained `Ok` value, leaving an `Err` value untouched.

        Parameters:
            _op (Callable[[T], U]): The function to apply to the Ok value.

        Returns:
            Result[U, E]: The result of the function applied to the Ok value.
        """
        return Err(self._error)

    def map_or[T, U](self, default: U, _f: Callable[[T], U]) -> U:
        """Returns the provided default (if Err), or applies a function to the contained value (if Ok).

        Parameters:
            default (U): The default value to return if the result is Err.
            _f (Callable[[T], U]): The function to apply to the Ok value.

        Returns:
            U: The result of the function applied to the Ok value.
        """
        return default

    def map_or_else[T, U](self, default: Callable[[E], U], _f: Callable[[T], U]) -> U:
        """Maps a Result<T, E> to U by applying fallback function default to a contained Err value, or function f to a contained Ok value.

        Parameters:
            default (Callable[[E], U]): The default function to apply if the result is Err.
            _f (Callable[[T], U]): The function to apply to the Ok value.

        Returns:
            U: The result of the function applied to the Ok value.
        """
        return default(self._error)

    def map_err[T, F](self, op: Callable[[E], F]) -> Result[T, F]:
        """Maps a `Result[T, E]` to `Result[T, F]` by applying a function to a contained `Err` value, leaving an `Ok` value untouched.

        Parameters:
            op (Callable[[E], F]): The function to apply to the Err value.

        Returns:
            Result[T, F]: The result of the function applied to the Err value.
        """
        return Err(op(self._error))

    def or_else[T, F](self, op: Callable[[E], Result[T, F]]) -> Result[T, F]:
        """Returns the result if it is `Ok`, otherwise calls `op` with the error value and returns the result.

        Parameters:
            op (Callable[[E], Result[T, F]]): The function to apply to the Err value.

        Returns:
            Result[T, F]: The result of the function applied to the Err value.
        """
        return op(self._error)

    def inspect[T](self, _f: Callable[[T], None]) -> Result[T, E]:
        """Calls a function with a reference to the contained value if Ok. Returns the original result.

        Parameters:
            _f (Callable[[T], None]): The function to apply to the Ok value.

        Returns:
            Result[T, E]: The original result.
        """
        return self

    def inspect_err[T](self, f: Callable[[E], None]) -> Result[T, E]:
        """Calls a function with a reference to the contained value if Err. Returns the original result.

        Parameters:
            f (Callable[[E], None]): The function to apply to the Err value.

        Returns:
            Result[T, E]: The original result.
        """
        f(self._error)
        return self


Result = Ok[T] | Err[E]


# Type guards for better type narrowing
def is_ok[T, E](result: Result[T, E]) -> TypeGuard[Ok[T]]:
    """Type guard to check if result is Ok."""
    return result.is_ok()


def is_err[T, E](result: Result[T, E]) -> TypeGuard[Err[E]]:
    """Type guard to check if result is Err."""
    return result.is_err()


# Type aliases for common patterns
type ValidationResult[T] = Result[T, str]  # Success value or error message
type ServiceResult[T] = Result[T, Exception]  # Success value or exception
type DatabaseResult[T] = Result[T, str]  # Success value or database error message


# Utility functions for common patterns
def try_parse_int(value: str) -> Result[int, str]:
    """Try to parse a string as an integer."""
    try:
        return Ok(int(value))
    except ValueError:
        return Err(f"Cannot parse '{value}' as integer")


def try_parse_float(value: str) -> Result[float, str]:
    """Try to parse a string as a float."""
    try:
        return Ok(float(value))
    except ValueError:
        return Err(f"Cannot parse '{value}' as float")


def safe_divide(a: float, b: float) -> Result[float, str]:
    """Safely divide two numbers."""
    if b == 0:
        return Err("Division by zero")
    return Ok(a / b)


def flatten[T, E](result: Result[Result[T, E], E]) -> Result[T, E]:
    """Flatten a nested Result."""
    if result.is_ok():
        return result.unwrap()
    return Err(result.unwrap_err())


def transpose[T, E](result: Result[T | None, E]) -> Result[T, E] | None:
    """Convert Result[Option[T], E] to Option[Result[T, E]]."""
    if result.is_ok():
        value = result.unwrap()
        if value is None:
            return None
        return Ok(value)
    return Err(result.unwrap_err())


def collect_results[T, E](results: list[Result[T, E]]) -> Result[list[T], E]:
    """Collect a list of Results into a Result of list.

    Returns:
        Ok(list) if all Results are Ok, otherwise returns the first Err.
    """
    values: list[T] = []
    for result in results:
        if result.is_err():
            return Err(result.unwrap_err())
        values.append(result.unwrap())
    return Ok(values)


def partition_results[T, E](results: list[Result[T, E]]) -> tuple[list[T], list[E]]:
    """Partition a list of Results into separate lists of Ok and Err values."""
    oks: list[T] = []
    errs: list[E] = []

    for result in results:
        if result.is_ok():
            oks.append(result.unwrap())
        else:
            errs.append(result.unwrap_err())

    return oks, errs


def unwrap_or_default[T](result: Result[T, object], default: T) -> T:
    """Unwrap a Result or return a default value."""
    return result.unwrap_or(default)


def unwrap_or_none[T](result: Result[T, object]) -> T | None:
    """Unwrap a Result or return None if it's an Err."""
    if result.is_ok():
        return result.unwrap()
    return None


def chain_results[T, U, E](
    first: Result[T, E], second: Result[U, E]
) -> Result[tuple[T, U], E]:
    """Chain two Results together, returning a tuple if both are Ok."""
    if first.is_ok() and second.is_ok():
        return Ok((first.unwrap(), second.unwrap()))
    if first.is_err():
        return Err(first.unwrap_err())
    return Err(second.unwrap_err())


def first_ok[T, E](results: list[Result[T, E]]) -> Result[T, list[E]]:
    """Return the first Ok result, or all errors if none are Ok."""
    errors: list[E] = []

    for result in results:
        if result.is_ok():
            return Ok(result.unwrap())
        errors.append(result.unwrap_err())

    return Err(errors)


def all_ok[T, E](results: list[Result[T, E]]) -> bool:
    """Check if all Results in a list are Ok."""
    return all(result.is_ok() for result in results)


def any_ok[T, E](results: list[Result[T, E]]) -> bool:
    """Check if any Result in a list is Ok."""
    return any(result.is_ok() for result in results)
