import asyncio
from collections.abc import Awaitable, Callable
from typing import TypeVar

T = TypeVar("T")

Observer = Callable[[T], Awaitable[None]]


class Observable[T]:
    """An observable class that allows subscribing to events.

    The observers are called when the notify method is called.
    """

    def __init__(self):
        """Initialize the observable with an empty list of observers."""
        self._observers: list[Observer[T]] = []

    def subscribe(self, observer: Observer[T]) -> None:
        """Subscribe an observer to the observable.

        Args:
            observer: The observer to subscribe.
        """
        self._observers.append(observer)

    def unsubscribe(self, observer: Observer[T]) -> None:
        """Unsubscribe an observer from the observable.

        Args:
            observer: The observer to unsubscribe.
        """
        self._observers.remove(observer)

    def clear(self):
        """Clear all observers from the observable."""
        self._observers.clear()

    async def notify(self, value: T):
        """Notify all observers with the given value.

        Args:
            value: The value to notify the observers with.
        """
        await asyncio.gather(*[observer(value) for observer in self._observers])
