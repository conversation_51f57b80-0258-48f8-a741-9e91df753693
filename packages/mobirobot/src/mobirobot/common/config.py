from enum import <PERSON>r<PERSON><PERSON>
from pathlib import Path
from typing import Annotated, ClassVar

from pydantic import BaseModel, Field, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict

from mobirobot.models.station import Station


class Environment(StrEnum):
    """Environment for the application."""

    DEVELOPMENT = "development"
    PRODUCTION = "production"


class StreamSettings(BaseModel):
    """Stream settings."""

    camera_name_query: str = "MX_Brio"
    stream_resolution: tuple[int, int] = (1280, 720)
    output_size: tuple[int, int] = (1280, 720)

    # Legacy compatibility properties
    @property
    def stream_width(self) -> int:
        """Legacy property for stream width."""
        return self.output_size[0]

    @property
    def stream_height(self) -> int:
        """Legacy property for stream height."""
        return self.output_size[1]

    stream_target_fps: int = 30
    recording_dir: Path = Path(".mobirobot/videos")

    # Mock camera settings
    use_mock_camera: bool = False
    mock_camera_image_path: Path | None = None

    # Detailed stream processing and quality settings
    min_detection_confidence: Annotated[float, Field(ge=0.0, le=1.0)] = 0.5
    min_tracking_confidence: Annotated[float, Field(ge=0.0, le=1.0)] = 0.5
    model_complexity: Annotated[int, Field(ge=0, le=2)] = (
        0  # Typically 0, 1, or 2 for MediaPipe
    )
    static_image_mode: bool = False

    video_fourcc: str = "MPEG"  # Codec for video recording
    default_jpeg_quality: Annotated[int, Field(ge=1, le=100)] = 80
    min_jpeg_quality: Annotated[int, Field(ge=1, le=100)] = 20
    max_jpeg_quality: Annotated[int, Field(ge=1, le=100)] = 95
    jpeg_quality_adjust_step: Annotated[int, Field(ge=1)] = 5

    # FPS thresholds for quality adjustment
    client_fps_low_threshold: Annotated[int, Field(ge=1)] = 24
    client_fps_high_threshold: Annotated[int, Field(ge=1)] = 24

    websocket_receive_timeout: Annotated[float, Field(gt=0)] = 0.001  # Seconds
    websocket_send_interval: Annotated[float, Field(gt=0)] = (
        1 / 60
    )  # Seconds, derived from target FPS

    # Frame acquisition timeout - separate from WebSocket timeout
    frame_acquisition_timeout: Annotated[float, Field(gt=0)] = (
        5.0  # Increased from 2.0 seconds for more stability
    )

    pose_processing_on_start: bool = False

    camera_retry_delay: Annotated[float, Field(gt=0)] = (
        2.0  # Increased from 1.0 for better reconnection stability
    )

    @field_validator("recording_dir")
    @classmethod
    def ensure_recording_dir_exists(cls, v: Path) -> Path:
        """Ensure the recording directory exists."""
        v.mkdir(parents=True, exist_ok=True)
        return v


class Settings(BaseSettings):
    """Settings for the application."""

    environment: Environment = Environment.DEVELOPMENT
    sentry_dsn: str | None = None
    station: Station | None = None
    robot_ip: str = "nao.local"
    robot_port: Annotated[int, Field(ge=1, le=65535)] = 9559
    mobirobot_buttons: bool = False
    mobirobot_record: bool = False
    mobirobot_nrof_buttons: int = 4
    logging_path: Path = Path(".mobirobot/logs")
    regiment_folder: Path = Path(".mobirobot/regiments")
    stream_settings: StreamSettings = StreamSettings()
    exercises_dir: Path = Path("data/exercises/toml/")
    movements_dir: Path = Path("data/exercises/movement_data/")
    models_dir: Path = Path("data/exercises/models/")
    sentences_dir: Path = Path("data/sentences/")

    model_config: ClassVar[SettingsConfigDict] = SettingsConfigDict(
        env_file=(".env", ".env.prod"),
        env_file_encoding="utf-8",
        nested_model_default_partial_update=True,
        env_nested_delimiter=".",
    )

    @field_validator(
        "logging_path",
        "regiment_folder",
        "exercises_dir",
        "movements_dir",
        "models_dir",
        "sentences_dir",
    )
    @classmethod
    def ensure_data_dirs_exist(cls, v: Path) -> Path:
        """Ensure data directories exist."""
        v.mkdir(parents=True, exist_ok=True)
        return v


settings: Settings = Settings()
