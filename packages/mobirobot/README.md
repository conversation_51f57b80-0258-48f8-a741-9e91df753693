# MobiRobot Core Package

Core services and execution engine for the MobiRobot robot-assisted physical therapy system.

## Overview

The `mobirobot` package provides the core functionality for exercise execution, robot monitoring, pose detection, and speech processing in the MobiRobot system. It replaces the legacy `src/exerciser` module with a modular, maintainable architecture.

## Key Modules

### Execution (`mobirobot.execution`)

Central exercise session management and behavior coordination:

- **`session_manager`**: Main coordinator replacing Application/Orchestrator/ExecutionManager
- **`behavior_builder`**: Modular behavior construction from regiment definitions
- **`behavior_executor`**: Behavior sequence execution with pause/resume support
- **`robot_control`**: Robot command functions (stiffness, volume, follow-me, LLM)
- **`task_manager`**: Enhanced async task management with graceful cancellation
- **`services`**: Dependency injection container for all execution services

### Monitoring (`mobirobot.monitoring`)

Robot status and performance tracking:

- Real-time system monitoring (CPU, memory, disk)
- NAO robot health tracking (battery, temperature, stiffness)
- Event-driven monitoring with aggregation
- WebSocket streaming for live updates

### Pose Detection (`mobirobot.pose`)

Computer vision and exercise validation:

- MediaPipe-based pose detection
- Exercise-specific pose classification
- Real-time feedback for exercise correctness
- Integration with exercise execution system

### Speech (`mobirobot.speech`)

Text-to-speech and language processing:

- Station-specific speech content (HUF, KJP, KC)
- Dynamic sentence generation with variables
- Multilingual support with translation files
- Speech formatting and timing control

### Exercise Management (`mobirobot.exercises`)

Exercise definitions and movement data:

- TOML-based exercise configuration
- Python movement module loading
- Exercise schema validation
- Station filtering and categorization

### Regiment Management (`mobirobot.regiments`)

Exercise program coordination:

- Regiment definition and validation
- Exercise sequencing and timing
- Station-specific customization
- Auto-save and persistence

### LLM Interaction (`mobirobot.llm_interaction`)

AI-powered patient engagement:

- LLM chat integration for patient interaction
- NAO robot audio processing
- Context-aware conversation management
- Therapeutic communication patterns

## Installation

From the project root:

```bash
# Install all dependencies
uv sync

# The mobirobot package is automatically available
python -c "import mobirobot; print('Package installed successfully')"
```

## Basic Usage

### Exercise Session Management

```python
from mobirobot.execution.session_manager import SessionManager
from mobirobot.execution.services import create_execution_services

# Create services
services = await create_execution_services(
    robot=nao_robot,
    exercise_service=exercise_service,
    enable_monitoring=True
)

# Create session manager
session_manager = SessionManager(services)

# Execute a regiment
await session_manager.execute_regiment(regiment)

# Execute a single exercise
await session_manager.execute_single_exercise("Squat")

# Robot control
await session_manager.set_stiffness(0.8)
await session_manager.set_volume(0.6)
result = await session_manager.toggle_follow_me()
```

### Monitoring Services

```python
from mobirobot.monitoring.service import MonitoringService

# Start monitoring
monitoring = MonitoringService()
await monitoring.start()

# Get current status
status = await monitoring.get_status()
print(f"CPU: {status.system.cpu_percent}%")
print(f"Battery: {status.robot.battery_charge}%")
```

### Pose Detection

```python
from mobirobot.pose.pose_detector import PoseDetector
from mobirobot.pose.exercise_tracker import ExerciseTracker

# Initialize pose detection
detector = PoseDetector()
tracker = ExerciseTracker("Squat")

# Process frame
pose = detector.detect_pose(frame)
feedback = tracker.analyze_pose(pose)
print(f"Exercise correctness: {feedback.correctness}%")
```

## Architecture

### Service Integration

The package uses dependency injection via `ExecutionServices` to coordinate:

- NAO robot controller
- Exercise and regiment services  
- Monitoring and pose detection
- Speech and LLM services
- Task and stream management

### Async Design

All services are async-first for real-time robot communication:

- Non-blocking robot operations
- Concurrent task execution
- Graceful shutdown and cleanup
- Real-time WebSocket updates

### Event-Driven Architecture

Services communicate via events:

- Exercise lifecycle events
- Robot status updates
- Pose detection feedback
- Monitoring alerts

## Configuration

Configure via environment variables:

```bash
# Robot connection
export ROBOT_IP=***************

# Data paths
export MOBIROBOT_DATA_DIR=/path/to/data

# Feature flags
export MOBIROBOT_ENABLE_POSE_DETECTION=true
export MOBIROBOT_ENABLE_MONITORING=true
```

## Development

### Adding New Behaviors

1. Create behavior class in `execution/behaviors/implementations/`
2. Register in `execution/behavior_builder.py`
3. Test with session manager

### Extending Services

1. Add service to `execution/services.py`
2. Update `ExecutionServices` container
3. Integrate with session manager

### Testing

```bash
# Run package tests
pytest packages/mobirobot/

# Run with robot simulation
uv run poe simulation
pytest packages/mobirobot/ -m simulation
```

## Dependencies

- **Core**: `pydantic`, `psutil`
- **Robot**: `nao` package for NAO communication
- **Vision**: `mediapipe`, `opencv-python`
- **Audio**: `faster-whisper`, `speechrecognition`
- **AI**: `openai` for LLM integration

## Migration from Legacy

Replacing legacy `src/exerciser/` components:

- `Application` → `execution.session_manager.SessionManager`
- `Orchestrator` → `execution.behavior_builder` + `execution.behavior_executor`  
- `ExecutionManager` → Direct service usage via `execution.services`
- `exerciser/behaviours/` → `execution.behaviors.implementations/`

See `CHANGELOG.md` for complete migration details.