# MobiRobot API Package

FastAPI web backend and HTTP/WebSocket routers for the MobiRobot robot-assisted physical therapy system.

## Overview

The `mobirobot-api` package provides the web interface for controlling and monitoring NAO robots during therapeutic exercise sessions. It offers REST APIs, WebSocket connections, and real-time streaming capabilities for therapist interfaces.

## Features

- **Exercise Execution**: Start/stop regiments and individual exercises
- **Robot Control**: Manage robot stiffness, volume, and behaviors
- **Real-time Monitoring**: Live robot status and system metrics
- **Video Streaming**: Real-time camera feed from robot
- **LLM Integration**: AI-powered patient interaction
- **WebSocket Support**: Live updates for frontend applications

## API Structure

### Core Routers

- **`command_router`**: Robot control commands (stiffness, volume, follow-me)
- **`exercise_backend`**: Exercise and regiment management
- **`regiment_router`**: Exercise program operations
- **`llm_router`**: LLM chat and interaction endpoints
- **`stream_router`**: Video streaming and media endpoints
- **`websocket_router`**: Real-time WebSocket connections

### Backend Components

- **`backend.py`**: Main FastAPI application with dependency injection
- **`dependencies.py`**: Service initialization and dependency management
- **`schemas.py`**: Pydantic models for API request/response
- **`web_types.py`**: Type definitions for web operations

## Installation

From the project root:

```bash
# Install all dependencies
uv sync

# Run development server
uv run poe dev:backend
# or
uv run fastapi run packages/mobirobot-api/src/mobirobot_api/backend.py
```

## API Endpoints

### Exercise Control

```http
POST /api/v1/exercise/regiment/start
POST /api/v1/exercise/regiment/stop
POST /api/v1/exercise/single/{exercise_name}/start
GET  /api/v1/exercise/available
```

### Robot Commands

```http
POST /api/v1/robot/stiffness
POST /api/v1/robot/volume  
POST /api/v1/robot/follow-me/toggle
POST /api/v1/robot/emergency-stop
```

### Monitoring

```http
GET  /api/v1/status/robot
GET  /api/v1/status/system
WS   /ws/status
```

### Streaming

```http
GET  /api/v1/stream/video
WS   /ws/stream
```

### LLM Integration

```http
POST /api/v1/llm/chat/start
POST /api/v1/llm/chat/stop
WS   /ws/llm
```

## Usage Examples

### Starting an Exercise Regiment

```python
import httpx

async with httpx.AsyncClient() as client:
    # Load a regiment
    regiment_data = {
        "name": "Morning Routine",
        "exercises": ["Squat", "ArmStrecken", "Plank"],
        "station": "HUF"
    }
    
    # Start execution
    response = await client.post(
        "http://localhost:8000/api/v1/exercise/regiment/start",
        json=regiment_data
    )
    print(f"Status: {response.status_code}")
```

### Robot Control

```python
# Set robot stiffness
response = await client.post(
    "http://localhost:8000/api/v1/robot/stiffness",
    json={"stiffness": 0.8}
)

# Toggle follow-me behavior
response = await client.post(
    "http://localhost:8000/api/v1/robot/follow-me/toggle"
)
```

### WebSocket Monitoring

```python
import websockets

async with websockets.connect("ws://localhost:8000/ws/status") as ws:
    async for message in ws:
        status = json.loads(message)
        print(f"Battery: {status['robot']['battery_charge']}%")
```

## Frontend Integration

### TypeScript Types

Generate TypeScript types from OpenAPI schema:

```bash
cd gui/
npm run schema  # Generates types from API
```

### WebSocket Client

```typescript
// Connect to robot status updates
const ws = new WebSocket('ws://localhost:8000/ws/status');

ws.onmessage = (event) => {
  const status = JSON.parse(event.data);
  updateRobotStatus(status);
};
```

### API Client

```typescript
import { api } from './lib/api';

// Start exercise
await api.post('/api/v1/exercise/single/Squat/start');

// Get robot status
const status = await api.get('/api/v1/status/robot');
```

## Configuration

### Environment Variables

```bash
# Robot connection
export ROBOT_IP=***************

# API settings
export MOBIROBOT_API_HOST=0.0.0.0
export MOBIROBOT_API_PORT=8000

# CORS settings for frontend
export MOBIROBOT_CORS_ORIGINS="http://localhost:5173"

# Feature flags
export MOBIROBOT_ENABLE_LLM=true
export MOBIROBOT_ENABLE_STREAMING=true
```

### Dependency Injection

The API uses dependency injection for services:

```python
# backend.py
from mobirobot_api.dependencies import get_session_manager

@app.post("/api/v1/exercise/single/{exercise_name}/start")
async def start_single_exercise(
    exercise_name: str,
    session_manager: SessionManager = Depends(get_session_manager)
):
    await session_manager.execute_single_exercise(exercise_name)
    return {"status": "started"}
```

## Development

### Adding New Endpoints

1. Create router in `src/mobirobot_api/routers/`
2. Define Pydantic schemas in `schemas.py`
3. Add router to main app in `backend.py`
4. Update OpenAPI documentation

### WebSocket Handlers

```python
@router.websocket("/ws/custom")
async def custom_websocket(websocket: WebSocket):
    await websocket.accept()
    try:
        while True:
            # Handle incoming messages
            data = await websocket.receive_text()
            # Send responses
            await websocket.send_text(response)
    except WebSocketDisconnect:
        # Cleanup on disconnect
        pass
```

### Testing

```bash
# Run API tests
pytest packages/mobirobot-api/

# Test with robot simulation
uv run poe simulation
pytest packages/mobirobot-api/ -m integration
```

## Error Handling

### HTTP Error Responses

```json
{
  "error": "ROBOT_NOT_CONNECTED",
  "message": "Unable to connect to robot at ***************",
  "details": {
    "robot_ip": "***************",
    "connection_timeout": 5.0
  }
}
```

### WebSocket Error Messages

```json
{
  "type": "error",
  "error": "EXERCISE_FAILED", 
  "message": "Exercise execution failed",
  "timestamp": "2024-06-06T18:00:00Z"
}
```

## Security

### CORS Configuration

Configured for frontend development:

```python
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173"],  # SvelteKit dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

### Rate Limiting

Critical endpoints have rate limiting:

- Robot commands: 10 requests/minute
- Exercise execution: 5 requests/minute
- Emergency stop: Unlimited

## Deployment

### Docker

```dockerfile
FROM python:3.12-slim

WORKDIR /app
COPY packages/mobirobot-api/ .
RUN pip install .

CMD ["fastapi", "run", "src/mobirobot_api/backend.py", "--host", "0.0.0.0"]
```

### Production Settings

```bash
export MOBIROBOT_LOG_LEVEL=INFO
export MOBIROBOT_ENABLE_DOCS=false  # Disable OpenAPI docs
export MOBIROBOT_CORS_ORIGINS="https://therapy.example.com"
```

## Dependencies

- **Core**: `mobirobot` package for all robot services
- **Web**: `fastapi`, `uvicorn`, `websockets`
- **Validation**: `pydantic` for request/response schemas
- **Streaming**: Integration with `mobirobot.stream` services