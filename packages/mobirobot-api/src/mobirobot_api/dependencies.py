import queue
from dataclasses import dataclass
from functools import lru_cache
from typing import TYPE_CHECKING, Annotated, cast

from buttons import ButtonService
from fastapi import Depends, Request, WebSocket
from pydantic import BaseModel

from mobirobot.common.config import settings
from mobirobot.common.structured_logging import LoggerType, get_logger
from mobirobot.execution import ExerciseSessionManager
from mobirobot.exercises.exercise_service import ExerciseService
from mobirobot.models import Station
from mobirobot.monitoring import MonitoringService
from mobirobot.regiments.regiment_service import RegimentService
from mobirobot.stream.stream_service import StreamService

if TYPE_CHECKING:
    from starlette.datastructures import State

logger: LoggerType = get_logger(__name__)


@dataclass
class WebAppState:
    """Simplified state container for web layer dependencies."""

    exercise_session_manager: ExerciseSessionManager
    monitoring_service: MonitoringService | None = None
    stream_service: StreamService | None = None


# --- Centralized App State Getters ---


@lru_cache
def get_button_service() -> ButtonService | None:
    """Get the button service.

    Returns:
        ButtonService | None: The button service instance, or None if not available.
    """
    if settings.mobirobot_buttons:
        return ButtonService()
    return None


@lru_cache
def regiment_dep() -> RegimentService:
    """Get the regiment service.

    Returns:
        RegimentService: The regiment service instance.
    """
    return RegimentService.create(settings.regiment_folder)


RegimentServiceDep = Annotated[RegimentService, Depends(regiment_dep)]


def get_web_app_state(request: Request) -> WebAppState:
    """Dependency to get the WebAppState from a Request.

    Args:
        request: The FastAPI request object.

    Returns:
        WebAppState: The web app state instance.

    Raises:
        RuntimeError: If the web app state is not found in the request.
    """
    state = cast("State", request.app.state)  # pyright: ignore[reportAny]
    if not hasattr(state, "web_app_state"):
        logger.critical(
            "WebAppState not found in request",
            location="request.app.state.web_app_state",
        )
        msg = "WebAppState not found in request"
        raise RuntimeError(msg)
    return cast("WebAppState", state.web_app_state)


def get_web_app_state_ws(websocket: WebSocket) -> WebAppState:
    """Dependency to get the WebAppState from a WebSocket.

    Args:
        websocket: The FastAPI websocket object.

    Returns:
        WebAppState: The web app state instance.

    Raises:
        RuntimeError: If the web app state is not found in the websocket.
    """
    state = cast("State", websocket.app.state)  # pyright: ignore[reportAny]
    if not hasattr(state, "web_app_state"):
        msg = "WebAppState not found in websocket.app.state.web_app_state"
        logger.critical(
            "WebAppState not found in websocket",
            location="websocket.app.state.web_app_state",
        )
        raise RuntimeError(msg)
    return cast("WebAppState", state.web_app_state)


# --- Refactored Service Getters ---


def get_status_monitor_service(
    web_app_state: Annotated[WebAppState, Depends(get_web_app_state)],
) -> MonitoringService:
    """Get the status monitor service from web app state.

    Args:
        web_app_state: The web app state instance.

    Returns:
        MonitoringService: The status monitor service instance.

    Raises:
        RuntimeError: If monitoring service is not available.
    """
    if web_app_state.monitoring_service is None:
        msg = "Monitoring service not available"
        raise RuntimeError(msg)
    return web_app_state.monitoring_service


def get_exercise_session_manager(
    web_app_state: Annotated[WebAppState, Depends(get_web_app_state)],
) -> ExerciseSessionManager:
    """Get the exercise session manager from web app state.

    Args:
        web_app_state: The web app state instance.

    Returns:
        ExerciseSessionManager: The exercise session manager instance.
    """
    return web_app_state.exercise_session_manager


def get_stream(
    web_app_state: Annotated[WebAppState, Depends(get_web_app_state)],
) -> StreamService | None:
    """Get the stream from web app state.

    Args:
        web_app_state: The web app state instance.

    Returns:
        StreamService | None: The stream instance, or None if not available.
    """
    return web_app_state.stream_service


# --- Refactored WebSocket Service Getters ---


def get_status_monitor_service_ws(
    web_app_state: Annotated[WebAppState, Depends(get_web_app_state_ws)],
) -> MonitoringService:
    """Get the status monitor service via websocket web app state.

    Args:
        web_app_state: The web app state instance.

    Returns:
        MonitoringService: The status monitor service instance.

    Raises:
        RuntimeError: If monitoring service is not available.
    """
    if web_app_state.monitoring_service is None:
        msg = "Monitoring service not available"
        raise RuntimeError(msg)
    return web_app_state.monitoring_service


def get_exercise_session_manager_ws(
    web_app_state: Annotated[WebAppState, Depends(get_web_app_state_ws)],
) -> ExerciseSessionManager:
    """Get the exercise session manager via websocket web app state.

    Args:
        web_app_state: The web app state instance.

    Returns:
        ExerciseSessionManager: The exercise session manager instance.
    """
    return web_app_state.exercise_session_manager


def get_stream_ws(
    web_app_state: Annotated[WebAppState, Depends(get_web_app_state_ws)],
) -> StreamService | None:
    """Get the stream via websocket web app state.

    Args:
        web_app_state: The web app state instance.

    Returns:
        StreamService | None: The stream instance, or None if not available.
    """
    return web_app_state.stream_service


# --- Other Dependencies (May need similar refactoring if they use request directly) ---


def audio_queue() -> queue.Queue[bytes]:
    """Get the audio queue.

    Returns:
        queue.Queue[bytes]: A new queue for audio bytes.
    """
    return queue.Queue()


# Robot controller and other dependencies now managed by mobirobot.execution
# Audio and chat services are handled internally by the session manager


StreamDep = Annotated[StreamService | None, Depends(get_stream)]

StreamDepWs = Annotated[StreamService | None, Depends(get_stream_ws)]


@lru_cache
def exercise_service() -> ExerciseService:
    """Load and cache exercise behaviors.

    Returns:
            ExerciseService: The exercise service instance.
    """
    return ExerciseService(exercises_dir=settings.exercises_dir)


# Application class no longer needed - functionality moved to mobirobot.execution


class ExerciseData(BaseModel):
    """Contains the exercise data for a single exercise request."""

    exercise_name: str
    exercise_duration: int
    station: Station
