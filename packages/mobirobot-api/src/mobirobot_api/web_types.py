
from pydantic import BaseModel, Field

from mobirobot.monitoring.models import RunningState


# Pydantic model for application status
class AppStatusModel(BaseModel):
    """Pydantic model for application status."""

    battery: float = 0.0
    volume: float = 0.0
    hot_joints: list[str] = Field(default_factory=list)
    running: RunningState = RunningState.NOT_STARTED
    buttons: dict[str, bool] = Field(default_factory=dict)
