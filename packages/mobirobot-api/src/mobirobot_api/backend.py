import asyncio
from contextlib import asynccontextmanager, suppress
from typing import TYPE_CHECKING

from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from nao import ConnectionConfig, NaoRobot

from mobirobot.common import settings, setup_logging, setup_sentry
from mobirobot.common.structured_logging import LoggerType, get_logger
from mobirobot.execution import ExerciseSessionManager, create_execution_services
from mobirobot.stream.stream_service import StreamService
from mobirobot_api.dependencies import WebAppState, exercise_service, get_button_service
from mobirobot_api.routers.command_router import router as command_router
from mobirobot_api.routers.exercise_backend import exercise_router
from mobirobot_api.routers.llm_router import router as llm_router
from mobirobot_api.routers.regiment_router import router as regiment_router
from mobirobot_api.routers.stream_router import stream_router
from mobirobot_api.routers.websocket_router import router as websocket_router

if TYPE_CHECKING:
    from buttons import ButtonService

logger: LoggerType = get_logger(__name__)


async def _cancel_task_safely(task: asyncio.Task[None] | None) -> None:
    """Safely cancel an asyncio task."""
    if task and not task.done():
        task.cancel()
        with suppress(asyncio.TimeoutError, asyncio.CancelledError):
            await asyncio.wait_for(task, timeout=1.0)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Simplified lifespan manager focused on core service coordination."""
    # Setup logging and Sentry
    setup_logging()
    setup_sentry()

    logger.info("Starting application services")

    # Initialize core services
    robot_controller = NaoRobot.create(
        ConnectionConfig(ip=settings.robot_ip, port=settings.robot_port)
    )
    button_service = get_button_service()
    stream_service = await StreamService.create()

    # Start background tasks
    robot_task = asyncio.create_task(robot_controller.start())
    button_task = (
        asyncio.create_task(button_service.connect_all()) if button_service else None
    )

    session_manager = None
    try:
        # Create and initialize session manager (handles all business logic)
        execution_services = await create_execution_services(
            robot=robot_controller,
            exercise_service=exercise_service(),
            button_service=button_service,
            enable_monitoring=True,
            enable_streaming=bool(stream_service),
            enable_pose_detection=False,
        )

        session_manager = ExerciseSessionManager(execution_services)
        await session_manager.initialize()

        # Store minimal state for web layer dependencies
        app.state.web_app_state = WebAppState(
            exercise_session_manager=session_manager,
            monitoring_service=execution_services.monitoring_service,
            stream_service=stream_service,
        )

        logger.info("Application startup complete")
        yield

    finally:
        logger.info("Starting application shutdown")

        # Shutdown session manager (handles all business logic cleanup)
        if session_manager:
            try:
                await session_manager.shutdown()
            except Exception:
                logger.exception("Error during session manager shutdown")

        # Cleanup remaining services
        await _cleanup_background_services(stream_service, button_service, button_task)
        await _cleanup_robot_controller(robot_controller, robot_task)

        logger.info("Application shutdown complete")


async def _cleanup_background_services(
    stream_service: StreamService | None,
    button_service: "ButtonService | None",
    button_task: asyncio.Task[None] | None,
) -> None:
    """Clean up background services."""
    # Shutdown stream service
    if stream_service:
        logger.debug("Shutting down stream service")
        try:
            await stream_service.close()
        except Exception:
            logger.exception("Error shutting down stream service")

    # Shutdown button service
    await _cancel_task_safely(button_task)
    if button_service:
        logger.debug("Disconnecting button service")
        try:
            await button_service.disconnect_all()
        except Exception:
            logger.exception("Error disconnecting button service")


async def _cleanup_robot_controller(
    robot_controller: NaoRobot, robot_task: asyncio.Task[None]
) -> None:
    """Clean up robot controller and its task."""
    logger.debug("Stopping robot controller")
    try:
        await robot_controller.stop()
    except Exception:
        logger.exception("Error stopping robot controller")

    await _cancel_task_safely(robot_task)


app = FastAPI(lifespan=lifespan)
app.include_router(regiment_router)
app.include_router(exercise_router)
app.include_router(command_router)
app.include_router(llm_router)
app.include_router(websocket_router)
app.include_router(stream_router)

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
