from typing import Annotated

from fastapi import APIRouter, Depends
from mobirobot.exercises.exercise_service import ExerciseService
from mobirobot.models.exercise import AnyExercise

from mobirobot_api.dependencies import exercise_service
from mobirobot_api.schemas import ExerciseDisplay


def convert_exercise_to_display(exercise: AnyExercise) -> ExerciseDisplay:
    """Convert an exercise model to display format.

    Args:
        exercise: The exercise model to convert.

    Returns:
        ExerciseDisplay model with properly extracted metadata.
    """
    # Extract intensity and tags from metadata if present
    intensity = None
    tags = None
    if exercise.metadata:
        intensity = (
            exercise.metadata.intensity.value if exercise.metadata.intensity else None
        )
        tags = exercise.metadata.tags or None

    # Convert station tags to strings
    station_tags = [station.value for station in exercise.station_tags]

    return ExerciseDisplay(
        name=exercise.name,
        explanation=exercise.explanation,
        intensity=intensity,
        tags=tags,
        station_tags=station_tags,
    )


exercise_router = APIRouter()


@exercise_router.get("/exercise")
async def get_exercise(
    exercise_service: Annotated[ExerciseService, Depends(exercise_service)],
) -> list[ExerciseDisplay]:
    """Get a list of exercises."""
    return [
        convert_exercise_to_display(exercise)
        for exercise in exercise_service.load_all_exercises().values()
    ]
