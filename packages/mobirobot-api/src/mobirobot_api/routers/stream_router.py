from fastapi import APIRouter, HTTPException, Response, WebSocket, status
from fastapi.websockets import WebSocketDisconnect

from mobirobot.common.structured_logging import LoggerType, get_logger
from mobirobot.stream.schemas import PoseData, StreamData
from mobirobot_api.dependencies import StreamDep, StreamDepWs

stream_router = APIRouter(prefix="/stream", tags=["stream"])

logger: LoggerType = get_logger(__name__)


@stream_router.get(
    "/info",
    responses={
        400: {"description": "Stream not available"},
        200: {"model": StreamData},
    },
)
async def info(stream_provider: StreamDep) -> StreamData:
    """Get the stream info.

    Args:
        stream_provider (StreamDep): The stream provider.

    Returns:
        StreamData: The stream info.

    Raises:
        HTTPException: If the stream is not available.
    """
    if stream_provider is None:
        raise HTTPException(status_code=400, detail="Stream not available")
    return StreamData(
        poses_enabled=stream_provider.pose_processing_enabled,
        is_recording=stream_provider.recording is not None,
    )


@stream_router.websocket("/ws")
async def stream(
    websocket: WebSocket,
    stream_provider: StreamDepWs,
) -> None:
    """Handle the stream websocket.

    Args:
        websocket (WebSocket): The websocket.
        stream_provider (Stream | None): The stream provider from app state.
    """
    await websocket.accept()
    if stream_provider is None:
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
        return

    try:
        await stream_provider.handler(websocket)
    except WebSocketDisconnect:
        logger.info("Client disconnected from stream WebSocket")
    except Exception:
        logger.exception("Error in stream WebSocket handler")
        try:
            await websocket.close(code=status.WS_1011_INTERNAL_ERROR)
        except RuntimeError:
            # WebSocket is already closed
            logger.debug("WebSocket already closed, cannot send close message")


@stream_router.post("/pose")
async def pose(pose_data: PoseData, stream_provider: StreamDep) -> Response:
    """Set the pose data.

    Args:
        pose_data (PoseData): The pose data.
        stream_provider (StreamDep): The stream provider.

    Returns:
        Response: The response

    Raises:
        HTTPException: If the stream is not available.
    """
    if stream_provider is None:
        raise HTTPException(status_code=400, detail="Stream not available")
    stream_provider.pose_processing_enabled = pose_data.enabled
    return Response(status_code=status.HTTP_204_NO_CONTENT)


@stream_router.post("/stop-recording")
async def stop_recording(stream_provider: StreamDep) -> StreamData:
    """Stop the recording.

    Args:
        stream_provider (StreamDep): The stream provider.

    Returns:
        StreamData: The stream data.

    Raises:
        HTTPException: If the stream is not available.
    """
    if stream_provider is None:
        raise HTTPException(status_code=400, detail="Stream not available")

    stream_provider.stop_recording()
    logger.info("Recording stopped via API")
    return StreamData(
        poses_enabled=stream_provider.pose_processing_enabled,
        is_recording=stream_provider.recording is not None,
    )


@stream_router.post("/start-recording")
async def start_recording(stream_provider: StreamDep) -> StreamData:
    """Start the recording.

    Args:
        stream_provider (StreamDep): The stream provider.

    Returns:
        StreamData: The stream data.

    Raises:
        HTTPException: If the stream is not available or recording fails to start.
    """
    if stream_provider is None:
        raise HTTPException(status_code=400, detail="Stream not available")

    if stream_provider.recording is not None:
        logger.warning("Start recording called but recording is already in progress")
        return StreamData(
            poses_enabled=stream_provider.pose_processing_enabled,
            is_recording=True,
        )

    try:
        stream_provider.setup_recording()
        logger.info("Recording started via API")
        return StreamData(
            poses_enabled=stream_provider.pose_processing_enabled,
            is_recording=True,
        )
    except RuntimeError as e:
        logger.exception("Failed to start recording via API")
        raise HTTPException(status_code=500, detail="Failed to start recording") from e
