#:schema https://raw.githubusercontent.com/SchemaStore/schemastore/master/src/schemas/json/uv.json
[project]
name = "mobirobot-cli"
version = "0.1.0"
description = "Command-line interface for mobirobot exercise validation and management"
readme = "README.md"
authors = [
    { name = "<PERSON> Noller", email = "<EMAIL>" }
]
requires-python = ">=3.12"
dependencies = [
    "typer>=0.15.2",
    "rich>=13.0.0",
    "pydantic>=2.0.0",
    "mobirobot",
    "docker>=7.1.0",
    "uvicorn>=0.34.1",
    "nao-simulation",
]

[project.scripts]
mobirobot-cli = "mobirobot_cli:main"
mobirobot = "mobirobot_cli:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
