"""Backend management commands."""

import os
import socket
import subprocess
import sys
import time
from pathlib import Path
from typing import Annotated

import docker
import docker.errors
import qi
import typer
from rich.console import Console

console = Console()
backend_app = typer.Typer(help="Backend management commands")


def _is_port_open(host: str, port: int, timeout: int = 2) -> bool:
    """Check if a TCP port is open and accepting connections.

    Args:
        host: The hostname or IP address to check
        port: The port number to check
        timeout: Connection timeout in seconds

    Returns:
        True if the port is open, False otherwise
    """
    try:
        with socket.create_connection((host, port), timeout=timeout):
            return True
    except (OSError, TimeoutError):
        return False


def _wait_for_naoqi(
    host: str = "localhost",
    port: int = 8432,
    timeout: int = 300,
    retry_interval: int = 5,
) -> bool:
    """Wait for the NAOqi service to be available and initialized.

    Args:
        host: NAOqi host
        port: NAOqi port
        timeout: Maximum time to wait in seconds
        retry_interval: Time between retries in seconds

    Returns:
        True if NAOqi initialized successfully within the timeout, False otherwise
    """
    start_time = time.time()
    session = None

    console.print(
        f"[cyan]Waiting for NAOqi to be available at {host}:{port} (timeout: {timeout}s)[/cyan]"
    )

    while time.time() - start_time < timeout:
        if not _is_port_open(host, port):
            console.print("[yellow]Port not open yet, NAOqi not ready...[/yellow]")
            time.sleep(retry_interval)
            continue

        try:
            session = qi.Session()
            session.connect(f"tcp://{host}:{port}")

            # Try to get ALAutonomousLife service to verify full initialization
            session.service("ALAutonomousLife")

            console.print("[green]NAOqi is fully initialized![/green]")

        except (RuntimeError, ConnectionRefusedError) as e:
            console.print(f"[yellow]Waiting for NAOqi to initialize... ({e})[/yellow]")
            time.sleep(retry_interval)

        else:
            return True

    console.print("[red]Timeout waiting for NAOqi to initialize[/red]")
    return False


def _start_simulation_container(container_name: str = "naoqi-simulation") -> bool:
    """Start the NAOqi simulation Docker container.

    Args:
        container_name: Name for the Docker container

    Returns:
        True if container started successfully, False otherwise
    """
    try:
        client = docker.from_env()

        # Check if container already exists and is running
        try:
            existing_container = client.containers.get(container_name)
            if existing_container.status == "running":
                console.print(
                    f"[green]Container '{container_name}' is already running[/green]"
                )
                return True
            console.print(
                f"[yellow]Starting existing container '{container_name}'[/yellow]"
            )
            existing_container.start()
        except docker.errors.NotFound:
            pass  # Container doesn't exist yet
        else:
            return True

        # Start new container
        console.print(
            f"[cyan]Starting NAOqi simulation container '{container_name}'...[/cyan]"
        )
        container = client.containers.run(
            "naoqi-simulation:latest",
            name=container_name,
            ports={"8432/tcp": 8432},
            detach=True,
            remove=False,  # Don't auto-remove so we can reuse it
        )

        console.print(
            f"[green]Container '{container_name}' started with ID: {container.short_id}[/green]"
        )

    except docker.errors.ImageNotFound:
        console.print("[red]Docker image 'naoqi-simulation:latest' not found[/red]")
        console.print("[yellow]Please ensure the image is built or available[/yellow]")
        return False
    except docker.errors.APIError as e:
        console.print(f"[red]Docker API error: {e}[/red]")
        return False
    else:
        return True


def _stop_simulation_container(container_name: str = "naoqi-simulation") -> None:
    """Stop the NAOqi simulation Docker container.

    Args:
        container_name: Name of the Docker container to stop
    """
    client = docker.from_env()

    try:
        container = client.containers.get(container_name)
    except docker.errors.NotFound:
        console.print(f"[yellow]Container '{container_name}' not found[/yellow]")
        return

    if container.status == "running":
        console.print(f"[yellow]Stopping container '{container_name}'...[/yellow]")
        container.stop()
        console.print(f"[green]Container '{container_name}' stopped[/green]")
    else:
        console.print(f"[yellow]Container '{container_name}' is not running[/yellow]")


def _set_simulation_environment() -> None:
    """Set environment variables for simulation mode."""
    os.environ["MOBIROBOT_ROBOT_IP"] = "localhost"
    os.environ["MOBIROBOT_ROBOT_PORT"] = "8432"
    # Set to development environment if not already set
    if "MOBIROBOT_ENVIRONMENT" not in os.environ:
        os.environ["MOBIROBOT_ENVIRONMENT"] = "development"


def _run_backend() -> int:
    """Run the backend server.

    Returns:
        Exit code from the backend process
    """
    # Find the backend.py file relative to the workspace
    backend_path = Path("src/web/backend.py")
    if not backend_path.exists():
        console.print(f"[red]Backend file not found at: {backend_path}[/red]")
        return 1

    console.print("[cyan]Starting backend server...[/cyan]")

    try:
        # Run the backend using python -m uvicorn
        result = subprocess.run(
            [
                sys.executable,
                "-m",
                "uvicorn",
                "src.web.backend:app",
                "--host",
                "0.0.0.0",
                "--port",
                "8000",
            ],
            check=False,
        )
    except KeyboardInterrupt:
        console.print("\n[yellow]Backend server stopped by user[/yellow]")
        return 0
    else:
        return result.returncode


@backend_app.command("run")
def run_backend(
    simulation: Annotated[
        bool,
        typer.Option(
            "--simulation", help="Run with NAOqi simulation in Docker container"
        ),
    ] = False,
    container_name: Annotated[
        str,
        typer.Option(
            "--container-name", help="Name for the simulation Docker container"
        ),
    ] = "naoqi-simulation",
    wait_timeout: Annotated[
        int,
        typer.Option(
            "--wait-timeout", help="Timeout in seconds to wait for simulation startup"
        ),
    ] = 300,
) -> None:
    """Run the backend server.

    Args:
        simulation: Whether to start with simulation mode
        container_name: Name for the Docker container
        wait_timeout: Timeout for waiting for simulation startup

    Raises:
        typer.Exit: If the backend server fails to start
    """
    container_started = False

    try:
        if simulation:
            console.print("[cyan]Starting in simulation mode...[/cyan]")

            # Set environment variables for simulation
            _set_simulation_environment()

            # Start the simulation container
            if not _start_simulation_container(container_name):
                console.print("[red]Failed to start simulation container[/red]")
                raise typer.Exit(1)

            container_started = True

            # Wait for NAOqi to be ready
            if not _wait_for_naoqi(timeout=wait_timeout):
                console.print(
                    "[red]NAOqi simulation failed to initialize within timeout[/red]"
                )
                raise typer.Exit(1)

            console.print("[green]Simulation is ready![/green]")

        # Run the backend
        exit_code = _run_backend()
        raise typer.Exit(exit_code)

    except typer.Exit:
        raise
    except KeyboardInterrupt:
        console.print("\n[yellow]Interrupted by user[/yellow]")
    finally:
        # Cleanup: optionally stop the container
        if simulation and container_started:
            console.print("[cyan]Cleaning up simulation container...[/cyan]")
            _stop_simulation_container(container_name)


@backend_app.command("stop-simulation")
def stop_simulation(
    container_name: Annotated[
        str,
        typer.Option(
            "--container-name", help="Name of the simulation Docker container to stop"
        ),
    ] = "naoqi-simulation",
) -> None:
    """Stop the NAOqi simulation container.

    Args:
        container_name: Name of the Docker container to stop
    """
    _stop_simulation_container(container_name)
