"""Validation commands for exercise TOML files."""

import json
import tomllib
from enum import StrEnum
from pathlib import Path
from typing import Annotated, TypedDict

import typer
from pydantic import TypeAdapter, ValidationError
from rich.console import Console

# Import mobirobot modules
from mobirobot.common.config import settings
from mobirobot.exercises.schemas import AnyTomlExercise

console = Console()
validate_app = typer.Typer(help="Exercise validation commands")


class ValidationIssue(TypedDict):
    """Type definition for validation issues (errors and warnings)."""

    file: str
    type: str  # "error" or "warning"
    message: str
    details: str | None


class OutputFormat(StrEnum):
    """Output format options."""

    TEXT = "text"
    JSON = "json"
    JUNIT = "junit"


class ValidationResult:
    """Container for validation results."""

    def __init__(self) -> None:
        """Initialize the validation result."""
        self.errors: list[ValidationIssue] = []
        self.warnings: list[ValidationIssue] = []
        self.valid_files: list[str] = []
        self.total_files: int = 0

    def add_error(self, file_path: str, error: str, details: str | None = None) -> None:
        """Add an error to the results."""
        self.errors.append(
            {
                "file": file_path,
                "type": "error",
                "message": error,
                "details": details,
            }
        )

    def add_warning(
        self, file_path: str, warning: str, details: str | None = None
    ) -> None:
        """Add a warning to the results."""
        self.warnings.append(
            {
                "file": file_path,
                "type": "warning",
                "message": warning,
                "details": details,
            }
        )

    def add_valid_file(self, file_path: str) -> None:
        """Add a valid file to the results."""
        self.valid_files.append(file_path)

    @property
    def has_errors(self) -> bool:
        """Check if there are any errors."""
        return len(self.errors) > 0

    @property
    def has_warnings(self) -> bool:
        """Check if there are any warnings."""
        return len(self.warnings) > 0

    @property
    def success_rate(self) -> float:
        """Calculate the success rate."""
        if self.total_files == 0:
            return 0.0
        return len(self.valid_files) / self.total_files


def validate_single_exercise_file(file_path: Path) -> ValidationResult:
    """Validate a single exercise TOML file.

    Args:
        file_path: Path to the TOML file to validate

    Returns:
        ValidationResult containing any errors or warnings
    """
    result = ValidationResult()
    result.total_files = 1

    file_str = str(file_path)

    # Check if file exists
    if not file_path.exists():
        result.add_error(file_str, "File does not exist")
        return result

    # Check if it's a TOML file
    if file_path.suffix.lower() != ".toml":
        result.add_warning(file_str, "File does not have .toml extension")

    try:
        # Try to parse TOML
        with file_path.open("rb") as f:
            data = tomllib.load(f)
    except tomllib.TOMLDecodeError as e:
        result.add_error(file_str, f"TOML parsing error: {e}")
        return result
    except OSError as e:
        result.add_error(file_str, f"File reading error: {e}")
        return result

    # Validate against Pydantic schema
    try:
        adapter: TypeAdapter[AnyTomlExercise] = TypeAdapter(AnyTomlExercise)
        exercise_model: AnyTomlExercise = adapter.validate_python(data)
        result.add_valid_file(file_str)

        # Additional semantic validations/warnings
        _check_semantic_warnings(exercise_model, result, file_str)

    except ValidationError as e:
        error_details: list[str] = []
        for error in e.errors():
            loc = " -> ".join(str(x) for x in error["loc"])
            error_details.append(f"{loc}: {error['msg']}")

        result.add_error(file_str, "Schema validation failed", "\n".join(error_details))

    return result


def _check_semantic_warnings(
    exercise: AnyTomlExercise, result: ValidationResult, file_path: str
) -> None:
    """Check for semantic warnings in a valid exercise.

    Args:
        exercise: The validated exercise model
        result: ValidationResult to add warnings to
        file_path: Path to the file being validated
    """
    # Check for empty movements lists (only for standard exercises)
    if hasattr(exercise.exercise, "movements"):
        movements = getattr(exercise.exercise, "movements", None)
        if movements is not None and not movements:
            result.add_warning(file_path, "Exercise has empty movements list")

    # Check for missing metadata
    if exercise.metadata is None:
        result.add_warning(file_path, "Exercise is missing metadata section")


@validate_app.command("exercises")
def validate_exercises(
    exercises_dir: Annotated[
        Path | None,
        typer.Argument(
            help="Path to the directory containing exercise TOML files (default: from config)",
        ),
    ] = None,
    output_format: Annotated[
        OutputFormat,
        typer.Option("--format", "-f", help="Output format for validation results"),
    ] = OutputFormat.TEXT,
    output_file: Annotated[
        Path | None,
        typer.Option("--output", "-o", help="Output file path (default: stdout)"),
    ] = None,
    strict: Annotated[
        bool,
        typer.Option(
            "--strict", help="Treat warnings as errors (exit with non-zero code)"
        ),
    ] = False,
) -> None:
    """Validate exercise TOML files in the specified directory.

    This command loads and validates all .toml files in the given directory
    using the exercise schemas. It reports errors, warnings, and provides
    a summary of the validation results.

    Args:
        exercises_dir: Path to directory containing TOML files (default: from config)
        output_format: Format for output (text, json, junit)
        output_file: Optional file to write results to
        strict: Whether to treat warnings as errors

    Raises:
        typer.Exit: With exit codes 0 (success), 1 (validation errors), or 2 (no files found)
    """
    # Use default exercises directory from config if not provided
    if exercises_dir is None:
        exercises_dir = settings.exercises_dir

    # Validate that the directory exists
    if not exercises_dir.exists():
        console.print(f"[red]Exercises directory does not exist: {exercises_dir}[/red]")
        raise typer.Exit(2)

    if not exercises_dir.is_dir():
        console.print(f"[red]Path is not a directory: {exercises_dir}[/red]")
        raise typer.Exit(2)

    # Find all TOML files
    toml_files = list(exercises_dir.glob("*.toml"))

    if not toml_files:
        console.print(f"[yellow]No TOML files found in {exercises_dir}[/yellow]")
        raise typer.Exit(2)

    # Validate all files
    overall_result = ValidationResult()
    overall_result.total_files = len(toml_files)

    for toml_file in toml_files:
        file_result = validate_single_exercise_file(toml_file)
        overall_result.errors.extend(file_result.errors)
        overall_result.warnings.extend(file_result.warnings)
        overall_result.valid_files.extend(file_result.valid_files)

    # Output results
    output_content = _format_output(overall_result, output_format, exercises_dir)

    if output_file:
        output_file.write_text(output_content, encoding="utf-8")
        console.print(f"Results written to {output_file}")
    elif output_format == OutputFormat.TEXT:
        console.print(output_content)
    else:
        typer.echo(output_content)

    # Determine exit code
    exit_code = 0
    if overall_result.has_errors or (strict and overall_result.has_warnings):
        exit_code = 1

    if exit_code != 0:
        raise typer.Exit(exit_code)


def _format_output(
    result: ValidationResult, output_format: OutputFormat, exercises_dir: Path
) -> str:
    """Format validation results according to the specified output format.

    Args:
        result: ValidationResult containing validation data
        output_format: Desired output format
        exercises_dir: Directory that was validated

    Returns:
        Formatted output string
    """
    if output_format == OutputFormat.JSON:
        return _format_json_output(result, exercises_dir)
    if output_format == OutputFormat.JUNIT:
        return _format_junit_output(result, exercises_dir)
    # TEXT
    return _format_text_output(result, exercises_dir)


def _format_text_output(result: ValidationResult, exercises_dir: Path) -> str:
    """Format results as human-readable text."""
    lines: list[str] = []
    lines.extend(
        [
            f"Exercise Validation Report for {exercises_dir}",
            "=" * 50,
            "",
            # Summary
            f"Total files: {result.total_files}",
            f"Valid files: {len(result.valid_files)}",
            f"Files with errors: {len(result.errors)}",
            f"Files with warnings: {len(result.warnings)}",
            f"Success rate: {result.success_rate:.1%}",
            "",
        ]
    )

    # Errors
    if result.errors:
        lines.extend(("ERRORS:", "-" * 20))
        for error in result.errors:
            lines.append(f"❌ {error['file']}: {error['message']}")
            if error.get("details"):
                details = error["details"]
                if details:
                    lines.extend(
                        f"   {detail_line}" for detail_line in details.split("\n")
                    )
            lines.append("")

    # Warnings
    if result.warnings:
        lines.extend(("WARNINGS:", "-" * 20))
        for warning in result.warnings:
            lines.append(f"⚠️  {warning['file']}: {warning['message']}")
            if warning.get("details"):
                details = warning["details"]
                if details:
                    lines.extend(
                        f"   {detail_line}" for detail_line in details.split("\n")
                    )
            lines.append("")

    # Valid files summary (count only)
    if result.valid_files:
        lines.extend((f"✅ {len(result.valid_files)} file(s) passed validation", ""))

    return "\n".join(lines)


def _format_json_output(result: ValidationResult, exercises_dir: Path) -> str:
    """Format results as JSON for structured processing."""
    output_data = {
        "exercises_dir": str(exercises_dir),
        "summary": {
            "total_files": result.total_files,
            "valid_files": len(result.valid_files),
            "error_count": len(result.errors),
            "warning_count": len(result.warnings),
            "success_rate": result.success_rate,
        },
        "errors": result.errors,
        "warnings": result.warnings,
        "valid_files": result.valid_files,
    }
    return json.dumps(output_data, indent=2)


def _format_junit_output(result: ValidationResult, _exercises_dir: Path) -> str:
    """Format results as JUnit XML for GitLab CI integration."""
    # Basic JUnit XML structure
    lines: list[str] = ['<?xml version="1.0" encoding="UTF-8"?>']
    testsuite_tag = (
        f'<testsuite name="exercise-validation" '
        f'tests="{result.total_files}" '
        f'failures="{len(result.errors)}" '
        f'errors="0" '
        f'time="0">'
    )
    lines.append(testsuite_tag)

    # Add test cases for each file
    all_files: set[str] = set()
    all_files.update(result.valid_files)
    all_files.update(error["file"] for error in result.errors)
    all_files.update(warning["file"] for warning in result.warnings)

    for file_path in sorted(all_files):
        file_errors = [e for e in result.errors if e["file"] == file_path]
        file_warnings = [w for w in result.warnings if w["file"] == file_path]

        lines.append(
            f'  <testcase name="{Path(file_path).name}" classname="exercise-validation">'
        )

        if file_errors:
            for error in file_errors:
                lines.append(f'    <failure message="{error["message"]}">')
                if error.get("details"):
                    lines.append(f"      {error['details']}")
                lines.append("    </failure>")

        if file_warnings:
            lines.extend(
                f"    <system-out>{warning['message']}</system-out>"
                for warning in file_warnings
            )

        lines.append("  </testcase>")

    lines.append("</testsuite>")
    return "\n".join(lines)
