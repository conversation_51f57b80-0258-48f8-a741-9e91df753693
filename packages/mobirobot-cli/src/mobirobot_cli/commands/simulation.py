"""Simulation container management commands."""

import asyncio
import subprocess
from pathlib import Path
from typing import Annotated

import qi
import typer
from nao_simulation.builder import BuildConfig, ContainerBuilder
from nao_simulation.container import NaoSimulationContainer
from rich.console import Console
from rich.table import Table

console = Console()

simulation_app = typer.Typer(
    name="simulation",
    help="NAO simulation container management",
    no_args_is_help=True,
)


@simulation_app.command()
def build(
    tag: Annotated[
        str, typer.Option("--tag", "-t", help="Container tag")
    ] = "nao-simulation:latest",
    push: Annotated[bool, typer.Option("--push", help="Push to registry")] = False,
    registry: Annotated[
        str | None, typer.Option("--registry", help="Registry URL")
    ] = None,
    no_cache: Annotated[
        bool, typer.Option("--no-cache", help="Build without cache")
    ] = False,
    platform: Annotated[
        str | None,
        typer.Option("--platform", help="Target platform (e.g., linux/amd64)"),
    ] = None,
    optimize: Annotated[
        bool, typer.Option("--optimize/--no-optimize", help="Optimize build")
    ] = True,
) -> None:
    """Build NAO simulation container."""
    try:
        # Get dockerfile path from nao-simulation package
        package_root = Path(__file__).parent.parent.parent.parent.parent
        simulation_package = package_root / "nao-simulation"
        dockerfile_path = simulation_package / "Dockerfile"

        if not dockerfile_path.exists():
            console.print(f"[red]Dockerfile not found at {dockerfile_path}[/red]")
            raise typer.Exit(1)

        config = BuildConfig(
            tag=tag,
            push=push,
            registry=registry,
            no_cache=no_cache,
            platform=platform,
            optimize=optimize,
        )

        builder = ContainerBuilder(dockerfile_path=dockerfile_path)
        builder.build(config)

        # Show build summary
        size = builder.get_image_size(tag)
        table = Table(title="Build Summary")
        table.add_column("Property", style="cyan")
        table.add_column("Value", style="green")

        table.add_row("Tag", tag)
        table.add_row("Size", size)
        table.add_row("Platform", platform or "default")
        table.add_row("Optimized", "Yes" if optimize else "No")

        if push and registry:
            table.add_row("Registry", registry)
            table.add_row("Pushed", "Yes")

        console.print(table)

    except Exception as e:
        console.print(f"[red]Build failed: {e}[/red]")
        raise typer.Exit(1) from e


@simulation_app.command()
async def test(
    tag: Annotated[
        str, typer.Option("--tag", "-t", help="Container tag")
    ] = "nao-simulation:latest",
    timeout: Annotated[
        int, typer.Option("--timeout", help="Test timeout in seconds")
    ] = 60,
) -> None:
    """Test NAO simulation container."""
    try:
        console.print(f"[yellow]Testing {tag}...[/yellow]")

        async with NaoSimulationContainer(image=tag, timeout=timeout) as container:
            nao_url = container.get_nao_url()
            host_port = container.get_host_port()

            console.print("[green]✅ Container started successfully[/green]")
            console.print(f"[dim]NAO URL: {nao_url}[/dim]")
            console.print(f"[dim]Host port: {host_port}[/dim]")

            session = qi.Session()
            session.connect(nao_url)
            session.service("ALAutonomousLife")

            console.print("[green]✅ NAOqi services accessible[/green]")

        console.print(f"[green]✅ All tests passed for {tag}[/green]")

    except Exception as e:
        console.print(f"[red]Test failed: {e}[/red]")
        raise typer.Exit(1) from e


@simulation_app.command()
def push(
    tag: Annotated[
        str, typer.Option("--tag", "-t", help="Container tag")
    ] = "nao-simulation:latest",
    registry: Annotated[
        str, typer.Option("--registry", help="Registry URL")
    ] = "registry.gitlab.com",
    project: Annotated[
        str | None, typer.Option("--project", help="GitLab project path")
    ] = None,
) -> None:
    """Push container to GitLab registry."""
    if not project:
        console.print("[red]Project path is required (e.g., group/project)[/red]")
        raise typer.Exit(1)

    full_registry = f"{registry}/{project}"

    try:
        config = BuildConfig(
            tag=tag,
            push=True,
            registry=full_registry,
        )

        builder = ContainerBuilder()
        builder._push_image(config)

        console.print(f"[green]✅ Pushed to {full_registry}/{tag}[/green]")

    except Exception as e:
        console.print(f"[red]Push failed: {e}[/red]")
        raise typer.Exit(1) from e


@simulation_app.command()
def start(
    tag: Annotated[
        str, typer.Option("--tag", "-t", help="Container tag")
    ] = "nao-simulation:latest",
    port: Annotated[
        int | None,
        typer.Option("--port", "-p", help="Host port (auto if not specified)"),
    ] = None,
    detach: Annotated[
        bool, typer.Option("--detach", "-d", help="Run in background")
    ] = True,
) -> None:
    """Start NAO simulation container."""

    async def _start() -> None:
        try:
            container = NaoSimulationContainer(
                image=tag,
                port=8432,  # Internal port is always 8432
                timeout=120,
            )

            console.print(
                f"[yellow]Starting NAO simulation container ({tag})...[/yellow]"
            )
            await container.start()

            nao_url = container.get_nao_url()
            host_port = container.get_host_port()

            table = Table(title="Container Started")
            table.add_column("Property", style="cyan")
            table.add_column("Value", style="green")

            table.add_row("Image", tag)
            table.add_row("NAO URL", nao_url)
            table.add_row("Host Port", str(host_port))
            table.add_row("Container Port", "8432")

            console.print(table)

            if not detach:
                console.print("[yellow]Press Ctrl+C to stop the container...[/yellow]")
                try:
                    while container.is_running:
                        await asyncio.sleep(1)
                except KeyboardInterrupt:
                    console.print("[yellow]Stopping container...[/yellow]")
                finally:
                    await container.stop()
                    console.print("[green]Container stopped[/green]")
            else:
                console.print("[green]Container running in background[/green]")
                console.print(
                    "[dim]Use 'mobirobot-cli simulation stop' to stop it[/dim]"
                )

        except Exception as e:
            console.print(f"[red]Failed to start container: {e}[/red]")
            raise typer.Exit(1) from e

    asyncio.run(_start())


@simulation_app.command()
def stop(
    tag: Annotated[
        str, typer.Option("--tag", "-t", help="Container tag")
    ] = "nao-simulation:latest",
    all_containers: Annotated[
        bool, typer.Option("--all", help="Stop all simulation containers")
    ] = False,
) -> None:
    """Stop NAO simulation containers."""
    try:
        if all_containers:
            # Stop all simulation containers
            console.print("[yellow]Stopping all NAO simulation containers...[/yellow]")
            result = subprocess.run(
                [
                    "docker",
                    "ps",
                    "--filter",
                    "name=nao-simulation",
                    "--format",
                    "{{.ID}}",
                ],
                capture_output=True,
                text=True,
                check=True,
            )
        else:
            # Stop containers with specific tag
            console.print(f"[yellow]Stopping containers with image {tag}...[/yellow]")
            result = subprocess.run(
                ["docker", "ps", "--filter", f"ancestor={tag}", "--format", "{{.ID}}"],
                capture_output=True,
                text=True,
                check=True,
            )

        container_ids = result.stdout.strip().split("\n")
        container_ids = [cid for cid in container_ids if cid]

        if not container_ids:
            console.print("[yellow]No running containers found[/yellow]")
            return

        for container_id in container_ids:
            subprocess.run(["docker", "stop", container_id], check=True)
            console.print(f"[green]Stopped container {container_id}[/green]")

    except subprocess.CalledProcessError as e:
        console.print(f"[red]Failed to stop containers: {e}[/red]")
        raise typer.Exit(1) from e


@simulation_app.command()
def cleanup() -> None:
    """Clean up simulation build artifacts."""
    try:
        ContainerBuilder.cleanup()

        console.print("[green]✅ Cleanup completed[/green]")

    except Exception as e:
        console.print(f"[red]Cleanup failed: {e}[/red]")
        raise typer.Exit(1) from e


@simulation_app.command()
def status() -> None:
    """Show status of simulation containers."""
    try:
        # Get running simulation containers
        result = subprocess.run(
            [
                "docker",
                "ps",
                "--filter",
                "ancestor=nao-simulation",
                "--format",
                "table {{.ID}}\t{{.Image}}\t{{.Status}}\t{{.Ports}}",
            ],
            capture_output=True,
            text=True,
            check=True,
        )

        if result.stdout.strip():
            console.print("[cyan]Running NAO Simulation Containers:[/cyan]")
            console.print(result.stdout)
        else:
            console.print("[yellow]No running NAO simulation containers found[/yellow]")

    except subprocess.CalledProcessError as e:
        console.print(f"[red]Failed to get container status: {e}[/red]")
        raise typer.Exit(1) from e
