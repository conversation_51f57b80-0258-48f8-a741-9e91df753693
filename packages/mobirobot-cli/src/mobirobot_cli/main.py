"""Main CLI application module."""

import typer

from .commands.backend import backend_app
from .commands.simulation import simulation_app
from .commands.validate import validate_app

app = typer.Typer(
    name="mobirobot-cli",
    help="Command-line interface for mobirobot exercise validation and management",
    no_args_is_help=True,
)

# Add subcommands
app.add_typer(validate_app, name="validate", help="Validation commands")
app.add_typer(backend_app, name="backend", help="Backend management commands")
app.add_typer(simulation_app, name="simulation", help="NAO simulation container management")


def main() -> None:
    """Main entry point for the CLI application."""
    app()


if __name__ == "__main__":
    main()
