# MobiRobot CLI

Command-line interface for MobiRobot exercise validation, backend management, and system utilities.

## Overview

The `mobirobot-cli` package provides command-line tools for managing the MobiRobot system, including exercise validation, backend server management, and development utilities. It's designed for developers, therapists, and system administrators.

## Installation

From the project root:

```bash
# Install dependencies
uv sync

# Run CLI commands
uv run mobirobot --help
```

## Available Commands

### Exercise Validation

Validate exercise TOML files for correctness and completeness:

```bash
# Basic validation
uv run mobirobot validate exercises data/exercises/toml/

# With different output formats
uv run mobirobot validate exercises data/exercises/toml/ --format json
uv run mobirobot validate exercises data/exercises/toml/ --format junit --output results.xml

# Strict mode (treat warnings as errors)
uv run mobirobot validate exercises data/exercises/toml/ --strict

# Validate specific file
uv run mobirobot validate exercises data/exercises/toml/Squat.toml
```

### Backend Management

Start and manage the MobiRobot backend server:

```bash
# Start development server
uv run mobirobot backend start --dev

# Start production server
uv run mobirobot backend start --host 0.0.0.0 --port 8000

# Start with specific robot IP
uv run mobirobot backend start --robot-ip ***************

# Start with monitoring enabled
uv run mobirobot backend start --enable-monitoring

# Check backend status
uv run mobirobot backend status

# Stop running backend
uv run mobirobot backend stop
```

## Command Reference

### Validation Commands

#### `validate exercises`

Validates exercise TOML files against the schema:

```bash
uv run mobirobot validate exercises [PATH] [OPTIONS]
```

**Arguments:**
- `PATH`: Directory or file path to validate (default: `data/exercises/toml/`)

**Options:**
- `--format {text,json,junit}`: Output format (default: text)
- `--output FILE`: Write output to file instead of stdout
- `--strict`: Treat warnings as errors
- `--quiet`: Suppress non-error output
- `--verbose`: Show detailed validation information

**Exit Codes:**
- `0`: All files valid (or only warnings in non-strict mode)
- `1`: Validation errors found (or warnings in strict mode)
- `2`: No TOML files found in directory
- `3`: Invalid command arguments

#### `validate regiments`

Validates regiment JSON files:

```bash
uv run mobirobot validate regiments [PATH] [OPTIONS]
```

### Backend Commands

#### `backend start`

Starts the MobiRobot API server:

```bash
uv run mobirobot backend start [OPTIONS]
```

**Options:**
- `--host TEXT`: Bind host (default: 127.0.0.1)
- `--port INTEGER`: Bind port (default: 8000)
- `--robot-ip TEXT`: NAO robot IP address
- `--dev`: Development mode with auto-reload
- `--enable-monitoring`: Enable system monitoring
- `--enable-streaming`: Enable video streaming
- `--log-level {DEBUG,INFO,WARNING,ERROR}`: Set log level

#### `backend status`

Check backend server status:

```bash
uv run mobirobot backend status [OPTIONS]
```

**Options:**
- `--host TEXT`: Server host to check (default: 127.0.0.1)
- `--port INTEGER`: Server port to check (default: 8000)
- `--timeout FLOAT`: Request timeout in seconds (default: 5.0)

#### `backend stop`

Stop running backend server:

```bash
uv run mobirobot backend stop [OPTIONS]
```

## Output Formats

### Text Format (Default)

Human-readable output with colors and formatting:

```
✅ data/exercises/toml/Squat.toml
   - Valid exercise definition
   - Station: HUF, KJP, KC
   - Movement files: 3 found

❌ data/exercises/toml/InvalidExercise.toml
   - Error: Missing required field 'explanation'
   - Warning: No movement files found

Summary: 1 valid, 1 error, 1 warning
```

### JSON Format

Structured output for programmatic processing:

```json
{
  "summary": {
    "total_files": 2,
    "valid_files": 1,
    "error_count": 1,
    "warning_count": 1
  },
  "results": [
    {
      "file": "data/exercises/toml/Squat.toml",
      "valid": true,
      "errors": [],
      "warnings": []
    },
    {
      "file": "data/exercises/toml/InvalidExercise.toml", 
      "valid": false,
      "errors": ["Missing required field 'explanation'"],
      "warnings": ["No movement files found"]
    }
  ]
}
```

### JUnit XML Format

For CI/CD integration:

```xml
<?xml version="1.0" encoding="utf-8"?>
<testsuites name="Exercise Validation">
  <testsuite name="data/exercises/toml" tests="2" failures="1" errors="0">
    <testcase classname="exercises" name="Squat.toml" />
    <testcase classname="exercises" name="InvalidExercise.toml">
      <failure message="Missing required field 'explanation'" />
    </testcase>
  </testsuite>
</testsuites>
```

## Usage Examples

### Development Workflow

```bash
# Start development server with auto-reload
uv run mobirobot backend start --dev --robot-ip ***************

# In another terminal, validate exercises
uv run mobirobot validate exercises data/exercises/toml/ --verbose

# Check server status
uv run mobirobot backend status
```

### CI/CD Integration

**GitLab CI Example:**

```yaml
stages:
  - validate
  - test
  - deploy

validate-exercises:
  stage: validate
  script:
    - uv run mobirobot validate exercises data/exercises/toml/ --format junit --output exercise-validation.xml
  artifacts:
    reports:
      junit: exercise-validation.xml
    when: always
  rules:
    - changes:
        - data/exercises/toml/**/*.toml

start-backend-test:
  stage: test
  services:
    - name: naoqi-simulation:latest
      alias: naoqi
  script:
    - uv run mobirobot backend start --robot-ip naoqi --port 8000 &
    - sleep 5
    - uv run mobirobot backend status --port 8000
    - # Run integration tests here
  after_script:
    - uv run mobirobot backend stop --port 8000
```

**GitHub Actions Example:**

```yaml
name: Validate Exercises

on:
  push:
    paths:
      - 'data/exercises/toml/**/*.toml'

jobs:
  validate:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: astral-sh/setup-uv@v1
      - name: Install dependencies
        run: uv sync
      - name: Validate exercises
        run: uv run mobirobot validate exercises data/exercises/toml/ --format json --output validation-results.json
      - name: Upload results
        uses: actions/upload-artifact@v4
        with:
          name: validation-results
          path: validation-results.json
```

### Production Deployment

```bash
# Start production server
uv run mobirobot backend start \
  --host 0.0.0.0 \
  --port 8000 \
  --robot-ip *************** \
  --enable-monitoring \
  --log-level INFO

# Monitor server health
while true; do
  uv run mobirobot backend status
  sleep 30
done
```

## Configuration

### Environment Variables

```bash
# Default robot IP
export ROBOT_IP=***************

# CLI defaults
export MOBIROBOT_CLI_HOST=127.0.0.1
export MOBIROBOT_CLI_PORT=8000
export MOBIROBOT_CLI_LOG_LEVEL=INFO

# Validation settings
export MOBIROBOT_CLI_STRICT_VALIDATION=false
export MOBIROBOT_CLI_VERBOSE_OUTPUT=false
```

### Configuration File

Create `~/.mobirobot/config.toml`:

```toml
[cli]
default_host = "127.0.0.1"
default_port = 8000
log_level = "INFO"

[validation]
strict_mode = false
show_warnings = true
color_output = true

[backend]
default_robot_ip = "***************"
enable_monitoring = true
enable_streaming = true
```

## Development

### Project Structure

```
src/mobirobot_cli/
├── __init__.py          # Main CLI app and command routing
├── main.py             # Entry point and application setup
└── commands/
    ├── __init__.py     # Command module exports
    ├── backend.py      # Backend server management commands
    └── validate.py     # Validation commands
```

### Adding New Commands

1. **Create command module:**

```python
# src/mobirobot_cli/commands/new_command.py
import typer
from typing import Optional

app = typer.Typer(help="New command functionality")

@app.command()
def new_action(
    param: str = typer.Argument(..., help="Required parameter"),
    option: Optional[str] = typer.Option(None, help="Optional parameter")
) -> None:
    """Execute new action with parameters."""
    typer.echo(f"Executing action with {param}")
```

2. **Register in main CLI:**

```python
# src/mobirobot_cli/__init__.py
from .commands.new_command import app as new_command_app

app.add_typer(new_command_app, name="new-command")
```

3. **Test the command:**

```bash
uv run mobirobot new-command new-action "test-param" --option "value"
```

### Testing

```bash
# Run CLI tests
pytest packages/mobirobot-cli/tests/

# Test specific command
pytest packages/mobirobot-cli/tests/test_validate.py

# Integration tests with backend
pytest packages/mobirobot-cli/tests/ -m integration
```

### Debugging

```bash
# Enable debug logging
uv run mobirobot --log-level DEBUG validate exercises data/exercises/toml/

# Verbose output
uv run mobirobot validate exercises data/exercises/toml/ --verbose

# Check CLI help
uv run mobirobot --help
uv run mobirobot validate --help
```

## Troubleshooting

### Common Issues

**Command not found:**
```bash
# Ensure project is installed
uv sync

# Check mobirobot is available
uv run which mobirobot
```

**Backend won't start:**
```bash
# Check if port is already in use
lsof -i :8000

# Try different port
uv run mobirobot backend start --port 8001

# Check robot connectivity
ping ***************
```

**Validation errors:**
```bash
# Check file permissions
ls -la data/exercises/toml/

# Validate specific file
uv run mobirobot validate exercises data/exercises/toml/Squat.toml --verbose
```

## Dependencies

- **CLI Framework**: `typer` for command-line interface
- **Core Services**: `mobirobot` package for backend functionality
- **HTTP Client**: `httpx` for backend status checking
- **Output Formatting**: `rich` for colored terminal output
- **Configuration**: `pydantic` for settings management

## Contributing

### Development Setup

```bash
# Clone and setup
git clone <repository>
cd bewegungstherapie-mit-roboter
uv sync

# Install in development mode
pip install -e packages/mobirobot-cli/

# Run tests
pytest packages/mobirobot-cli/tests/
```

### Code Style

- Follow Python type hints
- Use `typer` for command definitions
- Add comprehensive help text
- Include examples in docstrings
- Test on multiple platforms