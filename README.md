[![pipeline status](https://gitlab.ub.uni-bielefeld.de/IR/projects/**********************roboter/badges/develop/pipeline.svg)](https://gitlab.ub.uni-bielefeld.de/IR/projects/**********************roboter/-/commits/develop) [![coverage report](https://gitlab.ub.uni-bielefeld.de/IR/projects/**********************roboter/badges/develop/coverage.svg)](https://gitlab.ub.uni-bielefeld.de/IR/projects/**********************roboter/-/commits/develop) [![Latest Release](https://gitlab.ub.uni-bielefeld.de/IR/projects/**********************roboter/-/badges/release.svg)](https://gitlab.ub.uni-bielefeld.de/IR/projects/**********************roboter/-/releases)

# Nao Program

This README explains the Installation and usage of the project. For an overview of the architecture please look into the wiki. \\
For simpler instructions on this program look into Bedienungsdokument.pdf (german only).

## Installation

### Step 1: Install uv

Install the uv package manager using the official installer:

```sh
curl -LsSf https://astral.sh/uv/install.sh | sh
```

For more detailed installation instructions and documentation, visit the [uv documentation](https://docs.astral.sh/uv/getting-started/installation/).

### Step 2: Install PortAudio

PortAudio is required for handling audio processing. Install it using the command below:

```bash
sudo apt-get install libasound-dev portaudio19-dev libportaudio2 libportaudiocpp0
```

### Step 3: Install Project Requirements

Install dependencies using uv:

```bash
uv sync
```

### Step 4: Install Node v20
Download and install Node 20 from the [Official Nodejs Site](https://nodejs.org/en/download/package-manager)

Navigate to the `gui/` folder and run the following command to install all dependencies:
```bash
npm i --legacy-peer-deps
```


### (Optional) 5. NAO Simulation

#### Option A: Docker Container (Recommended)

Build and run the NAO simulation using the integrated container:

```bash
# Build the simulation container
uv run mobirobot-cli simulation build

# Start the simulation
uv run mobirobot-cli simulation start

# Stop the simulation
uv run mobirobot-cli simulation stop

# Check container status
uv run mobirobot-cli simulation status
```

#### Option B: Manual Installation

Download Choreographe from [Aldebaran](https://www.aldebaran.com/en/support/nao-6/downloads-softwares)

Go to Choreographe installation folder, enter subfolder bin and enter the following command to start the simulation:
```bash
./naoqi-bin
```


## Start for testing

For testing the robot programm do the following:

- If working with simulation, start the simulation as described in the installation. When using the Docker container, the simulation will be available at `tcp://localhost:XXXX` (port shown in output). For manual installation, set environment variable `ROBOT_IP` to `127.0.0.1`.

- Start the backend with the following command:
```bash
uv run poe dev:backend
```

- Start gui server with:
```bash
cd gui/
npm run dev
```

If you want to access the gui from annother device run the following command instead:
```bash
npm run dev --host "0.0.0.0"
```


## Enviroment Variables

If you have multiple Naos in the network you are operating in you can set the following command to set the enviroment variable to the IP of the robot you want to use:

´´´
export ROBOT_IP=***************
´´´


## FAQ
<details><summary>The program can not connect to the real robot</summary>
By default the program assumes it can reach nao by the url `nao.local` if this doesnt work for some reason (or you have multiple NAO robots in the same network) please set the environment variable `ROBOT_IP` to the ip address of your Nao. If you dont know the ip address press NAOs chest button.
</details>

## Preparation of Laptops for Studies

### Install git and clone repository:

```
sudo apt install git-all
```

Before starting the program for the first time, run the following command:
```
git config --global credential.helper store
```

This will store the credentials for the gitlab so it is able to update without passing in credentials every time. (should be changed tho, the credentials are not encrypted)

Clone this repository into the home directory (~)

### Install [linux-wifi-hotspot](https://github.com/lakinduakash/linux-wifi-hotspot): 
```
sudo add-apt-repository ppa:lakinduakash/lwh
sudo apt update
sudo apt install linux-wifi-hotspot
```

### Install Cuda and other dependencies for NaoChat (WORK IN PROGRESS):

#### Install Nvidia Drivers (535):

Open "Additional Drivers/Zusätzliche Treiber" and chose the nvidia-driver-535 driver.

#### Install CuDNN

Go to the [CuDNN Download Page](https://developer.nvidia.com/cudnn-downloads?target_os=Linux&target_arch=x86_64&Distribution=Ubuntu&target_version=24.04&target_type=deb_local) and follow the instructions to install CuDNN for Cuda 12.

#### Install Cuda12.1

Go to the [Cuda Download Page](https://developer.nvidia.com/cuda-12-1-0-download-archive) and follow their instructions based on your system.

#### Install OLama for LLM

Go to the [Ollama Download Page](https://ollama.com/download) and install Ollama.

After the installation is done you need to pull the LLM. Pull Model Phi3 from Ollama with the following command:

```
ollama pull phi3
```

#### Download Whisper Model
Start the application and start NAOChat one time to begin the download of the whisper model


