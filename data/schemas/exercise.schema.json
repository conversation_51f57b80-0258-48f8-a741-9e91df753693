{"$defs": {"Intensity": {"description": "Different intensities of exercises.", "enum": ["low", "medium", "high"], "title": "Intensity", "type": "string"}, "Station": {"description": "Enum representing different stations.\n\nEach station has a unique identifier and may have a base station.", "enum": ["KJP", "KC", "HUF", "LIPPE"], "title": "Station", "type": "string"}, "TomlAlternatingExercise": {"description": "Alternating exercise TOML configuration.", "properties": {"exercise_type": {"const": "alternating", "default": "alternating", "title": "Exercise Type", "type": "string"}, "demo": {"$ref": "#/$defs/TomlDemoSection", "description": "Demo section containing explanation and optional demo movement"}, "exercise": {"$ref": "#/$defs/TomlAlternatingExerciseSection", "description": "Core exercise configuration with left/right movements"}, "audio": {"anyOf": [{"$ref": "#/$defs/TomlAudioSettings"}, {"type": "null"}], "default": null, "description": "Optional audio settings"}, "model_processing": {"anyOf": [{"$ref": "#/$defs/TomlModelProcessingSettings"}, {"type": "null"}], "default": null, "description": "Optional model processing settings"}, "metadata": {"anyOf": [{"$ref": "#/$defs/TomlExerciseMetadata"}, {"type": "null"}], "default": null, "description": "Optional exercise metadata"}}, "required": ["demo", "exercise"], "title": "TomlAlternatingExercise", "type": "object"}, "TomlAlternatingExerciseSection": {"description": "Alternating exercise section with left/right movements.", "properties": {"name": {"description": "Display name of the exercise as shown to users", "title": "Name", "type": "string"}, "init_pose": {"description": "Initial robot posture before starting the exercise", "enum": ["<PERSON><PERSON>", "LyingBack", "<PERSON><PERSON><PERSON><PERSON>", "Sit", "SitRelax", "Stand", "StandInit", "StandZero"], "title": "Init Pose", "type": "string"}, "station_tags": {"description": "List of therapy stations where this exercise can be performed", "items": {"$ref": "#/$defs/Station"}, "title": "Station Tags", "type": "array"}, "turntype": {"$ref": "#/$defs/Turntype", "default": 0, "description": "Type of turn the robot should perform during the exercise"}, "left_movements": {"description": "Paths to left movement Python files", "items": {"type": "string"}, "title": "Left Movements", "type": "array"}, "right_movements": {"description": "Paths to right movement Python files", "items": {"type": "string"}, "title": "Right Movements", "type": "array"}, "alternating_explanation": {"description": "Explanation text used when switching between left and right sides", "title": "Alternating Explanation", "type": "string"}}, "required": ["name", "init_pose", "station_tags", "left_movements", "right_movements", "alternating_explanation"], "title": "TomlAlternatingExerciseSection", "type": "object"}, "TomlAudioSettings": {"description": "Audio settings for TOML configuration.", "properties": {"sounds": {"description": "List of audio file paths to play during the exercise", "items": {"type": "string"}, "title": "Sounds", "type": "array"}}, "required": ["sounds"], "title": "TomlAudioSettings", "type": "object"}, "TomlDemoSection": {"description": "Demo section for TOML configuration.", "properties": {"explanation": {"description": "Detailed explanation of how to perform the exercise, including speech markers and pauses for robot narration", "title": "Explanation", "type": "string"}, "demo": {"anyOf": [{"type": "string"}, {"type": "null"}], "default": null, "description": "Optional path to demo movement Python file that demonstrates the exercise", "title": "Demo"}}, "required": ["explanation"], "title": "TomlDemoSection", "type": "object"}, "TomlExerciseMetadata": {"description": "Exercise metadata for TOML configuration.", "properties": {"intensity": {"$ref": "#/$defs/Intensity", "description": "Physical intensity level of the exercise (low, medium, high)"}, "tags": {"description": "Categorization tags for filtering and organizing exercises", "items": {"type": "string"}, "title": "Tags", "type": "array"}}, "required": ["intensity", "tags"], "title": "TomlExerciseMetadata", "type": "object"}, "TomlModelProcessingSettings": {"description": "Model processing settings for TOML configuration.", "properties": {"model": {"description": "List of machine learning model files for movement classification", "items": {"type": "string"}, "title": "Model", "type": "array"}, "classifications": {"description": "List of movement classifications that the model can detect", "items": {"type": "string"}, "title": "Classifications", "type": "array"}, "hold": {"default": false, "description": "Whether to hold/pause execution while processing model predictions", "title": "Hold", "type": "boolean"}}, "required": ["model", "classifications"], "title": "TomlModelProcessingSettings", "type": "object"}, "TomlStandardExercise": {"description": "Standard exercise TOML configuration.\n\nMovements are specified as file paths that will be dynamically loaded.", "properties": {"exercise_type": {"const": "standard", "default": "standard", "title": "Exercise Type", "type": "string"}, "demo": {"$ref": "#/$defs/TomlDemoSection", "description": "Demo section containing explanation and optional demo movement"}, "exercise": {"$ref": "#/$defs/TomlStandardExerciseSection", "description": "Core exercise configuration with movements"}, "audio": {"anyOf": [{"$ref": "#/$defs/TomlAudioSettings"}, {"type": "null"}], "default": null, "description": "Optional audio settings"}, "model_processing": {"anyOf": [{"$ref": "#/$defs/TomlModelProcessingSettings"}, {"type": "null"}], "default": null, "description": "Optional model processing settings"}, "metadata": {"anyOf": [{"$ref": "#/$defs/TomlExerciseMetadata"}, {"type": "null"}], "default": null, "description": "Optional exercise metadata"}}, "required": ["demo", "exercise"], "title": "TomlStandardExercise", "type": "object"}, "TomlStandardExerciseSection": {"description": "Standard exercise section with movements.", "properties": {"name": {"description": "Display name of the exercise as shown to users", "title": "Name", "type": "string"}, "init_pose": {"description": "Initial robot posture before starting the exercise", "enum": ["<PERSON><PERSON>", "LyingBack", "<PERSON><PERSON><PERSON><PERSON>", "Sit", "SitRelax", "Stand", "StandInit", "StandZero"], "title": "Init Pose", "type": "string"}, "station_tags": {"description": "List of therapy stations where this exercise can be performed", "items": {"$ref": "#/$defs/Station"}, "title": "Station Tags", "type": "array"}, "turntype": {"$ref": "#/$defs/Turntype", "default": 0, "description": "Type of turn the robot should perform during the exercise"}, "movements": {"description": "Paths to movement Python files", "items": {"type": "string"}, "title": "Movements", "type": "array"}}, "required": ["name", "init_pose", "station_tags", "movements"], "title": "TomlStandardExerciseSection", "type": "object"}, "TomlVariationExercise": {"description": "Variation exercise TOML configuration.", "properties": {"exercise_type": {"const": "variation", "default": "variation", "title": "Exercise Type", "type": "string"}, "demo": {"$ref": "#/$defs/TomlDemoSection", "description": "Demo section containing explanation and optional demo movement"}, "exercise": {"$ref": "#/$defs/TomlVariationExerciseSection", "description": "Core exercise configuration with variations"}, "audio": {"anyOf": [{"$ref": "#/$defs/TomlAudioSettings"}, {"type": "null"}], "default": null, "description": "Optional audio settings"}, "model_processing": {"anyOf": [{"$ref": "#/$defs/TomlModelProcessingSettings"}, {"type": "null"}], "default": null, "description": "Optional model processing settings"}, "metadata": {"anyOf": [{"$ref": "#/$defs/TomlExerciseMetadata"}, {"type": "null"}], "default": null, "description": "Optional exercise metadata"}}, "required": ["demo", "exercise"], "title": "TomlVariationExercise", "type": "object"}, "TomlVariationExerciseSection": {"description": "Variation exercise section with variations.", "properties": {"name": {"description": "Display name of the exercise as shown to users", "title": "Name", "type": "string"}, "init_pose": {"description": "Initial robot posture before starting the exercise", "enum": ["<PERSON><PERSON>", "LyingBack", "<PERSON><PERSON><PERSON><PERSON>", "Sit", "SitRelax", "Stand", "StandInit", "StandZero"], "title": "Init Pose", "type": "string"}, "station_tags": {"description": "List of therapy stations where this exercise can be performed", "items": {"$ref": "#/$defs/Station"}, "title": "Station Tags", "type": "array"}, "turntype": {"$ref": "#/$defs/Turntype", "default": 0, "description": "Type of turn the robot should perform during the exercise"}, "variations": {"description": "Lists of movement file paths for each variation", "items": {"items": {"type": "string"}, "type": "array"}, "title": "Variations", "type": "array"}, "variation_explanations": {"description": "Explanation texts for each variation", "items": {"type": "string"}, "title": "Variation Explanations", "type": "array"}}, "required": ["name", "init_pose", "station_tags", "variations", "variation_explanations"], "title": "TomlVariationExerciseSection", "type": "object"}, "Turntype": {"description": "Different types of turns to be performed by <PERSON><PERSON>.", "enum": [0, 1, 2, 3, 4], "title": "Turntype", "type": "integer"}}, "anyOf": [{"$ref": "#/$defs/TomlStandardExercise"}, {"$ref": "#/$defs/TomlAlternatingExercise"}, {"$ref": "#/$defs/TomlVariationExercise"}], "title": "Exercise Configuration", "description": "TOML configuration schema for exercise definitions. Exercises are organized into logical sections: [demo], [exercise], and optional [audio], [model_processing], and [metadata] sections."}