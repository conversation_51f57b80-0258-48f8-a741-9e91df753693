# Alternating Exercise Example: ArmStreckenAlt (New Format)
# This demonstrates the new organized TOML structure for alternating exercises

exercise_type = "alternating"

# Demo section containing explanation and optional demo movement
[demo]
explanation = """
\\mrk=0\\ \\mrk=1\\ Nimm bitte deinen linken Arm ganz lang ausgestreckt \\mrk=2\\ nach oben bis auf De<PERSON> \\pau=1000\\ und dann wieder zurück.
\\mrk=3\\ Versuche deinen Arm ruhig auf das Kopfkissen abzulegen. Jetzt versuche das gleiche mit deinem rechten Arm \\mrk=4\\ \\pau=1000\\  Klasse, machst Du das! \\mrk=5\\ \\pau=1000\\
"""
demo = "demos/ArmStreckenAlt_Demo.py"

# Core exercise configuration
[exercise]
name = "Arme abwechselnd strecken"
init_pose = "LyingBack"
station_tags = ["HUF", "KC"]
turntype = 0  # NONE
alternating_explanation = "Jetzt kommt die andere Seite dran."

# Left and right movement file paths
left_movements = [
    "moves/ArmStrecken_Init.py",
    "moves/ArmStreckenLeft_Repeat.py",
    "moves/ArmStrecken_Exit.py"
]

right_movements = [
    "moves/ArmStrecken_Init.py",
    "moves/ArmStreckenRight_Repeat.py",
    "moves/ArmStrecken_Exit.py"
]

# Exercise metadata (optional)
[metadata]
intensity = "low"
tags = ["Arm"]
