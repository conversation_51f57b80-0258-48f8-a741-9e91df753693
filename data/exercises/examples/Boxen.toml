# Standard Exercise Example: Boxen (New Format)
# This demonstrates the new organized TOML structure with sections

exercise_type = "standard"

# Demo section containing explanation and optional demo movement
[demo]
explanation = """
\\mrk=0\\ Halte deine Arme \\mrk=1\\ wie ein Boxer vor dein Gesicht.
\\mrk=2\\ Boxe dann \\mrk=3\\ im Wechsel \\mrk=4\\ mit der rechten \\mrk=5\\ und linken Hand \\mrk=6\\ schnell nach vorne.
Bitte versuche \\mrk=7\\ die Übung schneller durchzuführen als ich.
"""
demo = "demos/Boxen_Demo.py"

# Core exercise configuration
[exercise]
name = "Boxen"
init_pose = "Stand"
station_tags = ["HUF", "KJP"]
turntype = 0  # NONE

# Movement file paths (relative to the movement base directory)
movements = [
    "moves/Boxen_Init.py",
    "moves/Boxen_Repeat.py",
    "moves/Boxen_Exit.py"
]

# Audio settings (optional)
[audio]
sounds = []

# Model processing settings (optional)
[model_processing]
model = ["boxen_classification.pkl"]
classifications = ["boxenup", "boxendown"]
hold = true

# Exercise metadata (optional)
[metadata]
intensity = "low"
tags = ["Aktivierung", "Arm"]
