# Variation Exercise Example: Lunge (New Format)
# This demonstrates the new organized TOML structure for variation exercises

exercise_type = "variation"

# Demo section containing explanation and optional demo movement
[demo]
explanation = """
Stelle dich in eine Ausfallschritt-Position. Ein Bein ist nach vorne gestreckt, das andere nach hinten.
Senke deinen Körper ab, indem du das vordere Knie beugst. Achte darauf, dass dein Knie nicht über die Zehen hinausragt.
"""
demo = "demos/Lunge_Demo.py"

# Core exercise configuration
[exercise]
name = "Ausfallschritte"
init_pose = "Stand"
station_tags = ["HUF"]
turntype = 0  # NONE

# Variations - each is a list of movement file paths
variations = [
    [
        "exercises/moves/Lunge_Init.py",
        "exercises/moves/LungeLeft_Repeat.py", 
        "exercises/moves/Lunge_Exit.py"
    ],
    [
        "exercises/moves/Lunge_Init.py",
        "exercises/moves/LungeRight_Repeat.py",
        "exercises/moves/Lunge_Exit.py"
    ],
    [
        "exercises/moves/Lunge_Init.py",
        "exercises/moves/LungeLeft_Repeat.py",
        "exercises/moves/Lunge_Exit.py"
    ],
    [
        "exercises/moves/Lunge_Init.py", 
        "exercises/moves/LungeRight_Repeat.py",
        "exercises/moves/Lunge_Exit.py"
    ]
]

# Explanations for each variation
variation_explanations = [
    "Jetzt wechseln wir die Seite.",
    "Jetzt wechseln wir wieder die Seite.",
    "Noch einmal links.",
    "Und zum Abschluss rechts."
]

# Exercise metadata (optional)
[metadata]
intensity = "medium"
tags = ["Bein", "Kräftigung"]
