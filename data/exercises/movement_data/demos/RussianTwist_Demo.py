def __call__():
    names = list()
    times = list()
    keys = list()

    names.append("HeadPitch")
    times.append([0.96, 1.36, 1.76, 2.16, 2.56])
    keys.append([[-0.0567998, [3, -0.333333, 0], [3, 0.133333, 0]], [-0.0567998, [3, -0.133333, 0], [3, 0.133333, 0]], [-0.0567998, [3, -0.133333, 0], [3, 0.133333, 0]], [-0.0567998, [3, -0.133333, 0], [3, 0.133333, 0]], [-0.0567998, [3, -0.133333, 0], [3, 0, 0]]])

    names.append("HeadYaw")
    times.append([0.96, 1.36, 1.76, 2.16, 2.56])
    keys.append([[-0.046062, [3, -0.333333, 0], [3, 0.133333, 0]], [0.342041, [3, -0.133333, 0], [3, 0.133333, 0]], [-0.046062, [3, -0.133333, 0.114014], [3, 0.133333, -0.114014]], [-0.342041, [3, -0.133333, 0], [3, 0.133333, 0]], [-0.046062, [3, -0.133333, 0], [3, 0, 0]]])

    names.append("LAnklePitch")
    times.append([0.96, 1.36, 1.76, 2.16, 2.56])
    keys.append([[0.860533, [3, -0.333333, 0], [3, 0.133333, 0]], [0.860533, [3, -0.133333, 0], [3, 0.133333, 0]], [0.860533, [3, -0.133333, 0], [3, 0.133333, 0]], [0.866751, [3, -0.133333, 0], [3, 0.133333, 0]], [0.860533, [3, -0.133333, 0], [3, 0, 0]]])

    names.append("LAnkleRoll")
    times.append([0.96, 1.36, 1.76, 2.16, 2.56])
    keys.append([[-0.0106959, [3, -0.333333, 0], [3, 0.133333, 0]], [-0.0106959, [3, -0.133333, 0], [3, 0.133333, 0]], [-0.0106959, [3, -0.133333, 0], [3, 0.133333, 0]], [-0.019984, [3, -0.133333, 0], [3, 0.133333, 0]], [-0.0106959, [3, -0.133333, 0], [3, 0, 0]]])

    names.append("LElbowRoll")
    times.append([0.96, 1.36, 1.76, 2.16, 2.56])
    keys.append([[-1.52629, [3, -0.333333, 0], [3, 0.133333, 0]], [-1.50941, [3, -0.133333, 0], [3, 0.133333, 0]], [-1.52629, [3, -0.133333, 0], [3, 0.133333, 0]], [-1.20577, [3, -0.133333, 0], [3, 0.133333, 0]], [-1.52629, [3, -0.133333, 0], [3, 0, 0]]])

    names.append("LElbowYaw")
    times.append([0.96, 1.36, 1.76, 2.16, 2.56])
    keys.append([[-0.61671, [3, -0.333333, 0], [3, 0.133333, 0]], [-0.754769, [3, -0.133333, 0], [3, 0.133333, 0]], [-0.61671, [3, -0.133333, -0.049869], [3, 0.133333, 0.049869]], [-0.455555, [3, -0.133333, 0], [3, 0.133333, 0]], [-0.61671, [3, -0.133333, 0], [3, 0, 0]]])

    names.append("LHand")
    times.append([0.96, 1.36, 1.76, 2.16, 2.56])
    keys.append([[0.286, [3, -0.333333, 0], [3, 0.133333, 0]], [0.286, [3, -0.133333, 0], [3, 0.133333, 0]], [0.286, [3, -0.133333, 0], [3, 0.133333, 0]], [0.2908, [3, -0.133333, 0], [3, 0.133333, 0]], [0.286, [3, -0.133333, 0], [3, 0, 0]]])

    names.append("LHipPitch")
    times.append([0.96, 1.36, 1.76, 2.16, 2.56])
    keys.append([[-1.43732, [3, -0.333333, 0], [3, 0.133333, 0]], [-1.4864, [3, -0.133333, 0], [3, 0.133333, 0]], [-1.43732, [3, -0.133333, 0], [3, 0.133333, 0]], [-1.50029, [3, -0.133333, 0], [3, 0.133333, 0]], [-1.43732, [3, -0.133333, 0], [3, 0, 0]]])

    names.append("LHipRoll")
    times.append([0.96, 1.36, 1.76, 2.16, 2.56])
    keys.append([[0.286901, [3, -0.333333, 0], [3, 0.133333, 0]], [0.276162, [3, -0.133333, 0], [3, 0.133333, 0]], [0.286901, [3, -0.133333, 0], [3, 0.133333, 0]], [0.240796, [3, -0.133333, 0], [3, 0.133333, 0]], [0.286901, [3, -0.133333, 0], [3, 0, 0]]])

    names.append("LHipYawPitch")
    times.append([0.96, 1.36, 1.76, 2.16, 2.56])
    keys.append([[-0.532256, [3, -0.333333, 0], [3, 0.133333, 0]], [-0.501576, [3, -0.133333, 0], [3, 0.133333, 0]], [-0.532256, [3, -0.133333, 0], [3, 0.133333, 0]], [-0.501576, [3, -0.133333, 0], [3, 0.133333, 0]], [-0.532256, [3, -0.133333, 0], [3, 0, 0]]])

    names.append("LKneePitch")
    times.append([0.96, 1.36, 1.76, 2.16, 2.56])
    keys.append([[1.36062, [3, -0.333333, 0], [3, 0.133333, 0]], [1.36368, [3, -0.133333, 0], [3, 0.133333, 0]], [1.36062, [3, -0.133333, 0], [3, 0.133333, 0]], [1.42206, [3, -0.133333, 0], [3, 0.133333, 0]], [1.36062, [3, -0.133333, 0], [3, 0, 0]]])

    names.append("LShoulderPitch")
    times.append([0.96, 1.36, 1.76, 2.16, 2.56])
    keys.append([[0.605888, [3, -0.333333, 0], [3, 0.133333, 0]], [1.04615, [3, -0.133333, 0], [3, 0.133333, 0]], [0.605888, [3, -0.133333, 0], [3, 0.133333, 0]], [0.687274, [3, -0.133333, 0], [3, 0.133333, 0]], [0.605888, [3, -0.133333, 0], [3, 0, 0]]])

    names.append("LShoulderRoll")
    times.append([0.96, 1.36, 1.76, 2.16, 2.56])
    keys.append([[0.197844, [3, -0.333333, 0], [3, 0.133333, 0]], [0.566003, [3, -0.133333, 0], [3, 0.133333, 0]], [0.197844, [3, -0.133333, 0.134722], [3, 0.133333, -0.134722]], [-0.24233, [3, -0.133333, 0], [3, 0.133333, 0]], [0.197844, [3, -0.133333, 0], [3, 0, 0]]])

    names.append("LWristYaw")
    times.append([0.96, 1.36, 1.76, 2.16, 2.56])
    keys.append([[-0.619779, [3, -0.333333, 0], [3, 0.133333, 0]], [-0.273093, [3, -0.133333, 0], [3, 0.133333, 0]], [-0.619779, [3, -0.133333, 0], [3, 0.133333, 0]], [-0.147222, [3, -0.133333, 0], [3, 0.133333, 0]], [-0.619779, [3, -0.133333, 0], [3, 0, 0]]])

    names.append("RAnklePitch")
    times.append([0.96, 1.36, 1.76, 2.16, 2.56])
    keys.append([[0.865217, [3, -0.333333, 0], [3, 0.133333, 0]], [0.866751, [3, -0.133333, 0], [3, 0.133333, 0]], [0.865217, [3, -0.133333, 0.00103644], [3, 0.133333, -0.00103644]], [0.860533, [3, -0.133333, 0], [3, 0.133333, 0]], [0.865217, [3, -0.133333, 0], [3, 0, 0]]])

    names.append("RAnkleRoll")
    times.append([0.96, 1.36, 1.76, 2.16, 2.56])
    keys.append([[0.00771189, [3, -0.333333, 0], [3, 0.133333, 0]], [0.019984, [3, -0.133333, 0], [3, 0.133333, 0]], [0.00771189, [3, -0.133333, 0], [3, 0.133333, 0]], [0.0106959, [3, -0.133333, 0], [3, 0.133333, 0]], [0.00771189, [3, -0.133333, 0], [3, 0, 0]]])

    names.append("RElbowRoll")
    times.append([0.96, 1.36, 1.76, 2.16, 2.56])
    keys.append([[1.53097, [3, -0.333333, 0], [3, 0.133333, 0]], [1.20577, [3, -0.133333, 0], [3, 0.133333, 0]], [1.53097, [3, -0.133333, 0], [3, 0.133333, 0]], [1.50941, [3, -0.133333, 0], [3, 0.133333, 0]], [1.53097, [3, -0.133333, 0], [3, 0, 0]]])

    names.append("RElbowYaw")
    times.append([0.96, 1.36, 1.76, 2.16, 2.56])
    keys.append([[0.842125, [3, -0.333333, 0], [3, 0.133333, 0]], [0.455555, [3, -0.133333, 0], [3, 0.133333, 0]], [0.842125, [3, -0.133333, 0], [3, 0.133333, 0]], [0.754769, [3, -0.133333, 0], [3, 0.133333, 0]], [0.842125, [3, -0.133333, 0], [3, 0, 0]]])

    names.append("RHand")
    times.append([0.96, 1.36, 1.76, 2.16, 2.56])
    keys.append([[0.2908, [3, -0.333333, 0], [3, 0.133333, 0]], [0.2908, [3, -0.133333, 0], [3, 0.133333, 0]], [0.2908, [3, -0.133333, 0], [3, 0.133333, 0]], [0.286, [3, -0.133333, 0], [3, 0.133333, 0]], [0.2908, [3, -0.133333, 0], [3, 0, 0]]])

    names.append("RHipPitch")
    times.append([0.96, 1.36, 1.76, 2.16, 2.56])
    keys.append([[-1.45427, [3, -0.333333, 0], [3, 0.133333, 0]], [-1.50029, [3, -0.133333, 0], [3, 0.133333, 0]], [-1.45427, [3, -0.133333, 0], [3, 0.133333, 0]], [-1.4864, [3, -0.133333, 0], [3, 0.133333, 0]], [-1.45427, [3, -0.133333, 0], [3, 0, 0]]])

    names.append("RHipRoll")
    times.append([0.96, 1.36, 1.76, 2.16, 2.56])
    keys.append([[-0.254602, [3, -0.333333, 0], [3, 0.133333, 0]], [-0.240796, [3, -0.133333, 0], [3, 0.133333, 0]], [-0.254602, [3, -0.133333, 0.00589427], [3, 0.133333, -0.00589427]], [-0.276162, [3, -0.133333, 0], [3, 0.133333, 0]], [-0.254602, [3, -0.133333, 0], [3, 0, 0]]])

    names.append("RHipYawPitch")
    times.append([0.96, 1.36, 1.76, 2.16, 2.56])
    keys.append([[-0.532256, [3, -0.333333, 0], [3, 0.133333, 0]], [-0.501576, [3, -0.133333, 0], [3, 0.133333, 0]], [-0.532256, [3, -0.133333, 0], [3, 0.133333, 0]], [-0.501576, [3, -0.133333, 0], [3, 0.133333, 0]], [-0.532256, [3, -0.133333, 0], [3, 0, 0]]])

    names.append("RKneePitch")
    times.append([0.96, 1.36, 1.76, 2.16, 2.56])
    keys.append([[1.40058, [3, -0.333333, 0], [3, 0.133333, 0]], [1.42206, [3, -0.133333, 0], [3, 0.133333, 0]], [1.40058, [3, -0.133333, 0.00972933], [3, 0.133333, -0.00972933]], [1.36368, [3, -0.133333, 0], [3, 0.133333, 0]], [1.40058, [3, -0.133333, 0], [3, 0, 0]]])

    names.append("RShoulderPitch")
    times.append([0.96, 1.36, 1.76, 2.16, 2.56])
    keys.append([[0.711819, [3, -0.333333, 0], [3, 0.133333, 0]], [0.687274, [3, -0.133333, 0], [3, 0.133333, 0]], [0.711819, [3, -0.133333, -0.0245446], [3, 0.133333, 0.0245446]], [1.04615, [3, -0.133333, 0], [3, 0.133333, 0]], [0.711819, [3, -0.133333, 0], [3, 0, 0]]])

    names.append("RShoulderRoll")
    times.append([0.96, 1.36, 1.76, 2.16, 2.56])
    keys.append([[-0.099752, [3, -0.333333, 0], [3, 0.133333, 0]], [0.24233, [3, -0.133333, 0], [3, 0.133333, 0]], [-0.099752, [3, -0.133333, 0.134722], [3, 0.133333, -0.134722]], [-0.566003, [3, -0.133333, 0], [3, 0.133333, 0]], [-0.099752, [3, -0.133333, 0], [3, 0, 0]]])

    names.append("RWristYaw")
    times.append([0.96, 1.36, 1.76, 2.16, 2.56])
    keys.append([[0.0858622, [3, -0.333333, 0], [3, 0.133333, 0]], [0.147222, [3, -0.133333, 0], [3, 0.133333, 0]], [0.0858622, [3, -0.133333, 0], [3, 0.133333, 0]], [0.273093, [3, -0.133333, 0], [3, 0.133333, 0]], [0.0858622, [3, -0.133333, 0], [3, 0, 0]]])

    return names, times, keys