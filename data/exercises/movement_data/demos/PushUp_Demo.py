def __call__():
	names = list()
	times = list()
	keys = list()
	
	names.append("HeadPitch")
	times.append([0.68, 1.24, 1.92, 2.6, 3.44, 4.12, 4.52, 4.92, 5.4, 6.2, 7.7, 12.0])
	keys.append([[-0.0368581, [3, -0.226667, 0], [3, 0.186667, 0]], [-0.0368581, [3, -0.186667, 0], [3, 0.226667, 0]], [-0.0368581, [3, -0.226667, 0], [3, 0.226667, 0]], [-0.0368581, [3, -0.226667, 0], [3, 0.28, 0]], [-0.0368581, [3, -0.28, 0], [3, 0.226667, 0]], [0.00302601, [3, -0.226667, -0.0160976], [3, 0.133333, 0.00946916]], [0.0398421, [3, -0.133333, 0], [3, 0.133333, 0]], [0.0398421, [3, -0.133333, 0], [3, 0.16, 0]], [0.0398421, [3, -0.16, 0], [3, 0, 0]],
	            [-0.11049, [3, -0.266667, 0], [3, 0.266667, 0]], [-0.11049, [3, -0.266667, 0], [3, 0.333333, 0]], [-0.11049, [3, -0.333333, 0], [3, 0, 0]]])
	

	names.append("HeadYaw")
	times.append([0.68, 1.24, 1.92, 2.6, 3.44, 4.12, 4.52, 4.92, 5.4, 6.2, 7.7, 12.0])
	keys.append([[-0.00310993, [3, -0.226667, 0], [3, 0.186667, 0]], [-0.00310993, [3, -0.186667, 0], [3, 0.226667, 0]], [-0.00310993, [3, -0.226667, 0], [3, 0.226667, 0]], [-0.00310993, [3, -0.226667, 0], [3, 0.28, 0]], [-0.00310993, [3, -0.28, 0], [3, 0.226667, 0]], [-0.00310993, [3, -0.226667, 0], [3, 0.133333, 0]], [-0.00310993, [3, -0.133333, 0], [3, 0.133333, 0]], [-0.00310993, [3, -0.133333, 0], [3, 0.16, 0]], [-0.00310993, [3, -0.16, 0], [3, 0, 0]],
	             [0.022968, [3, -0.266667, 0], [3, 0.266667, 0]], [0.022968, [3, -0.266667, 0], [3, 0.333333, 0]], [0.022968, [3, -0.333333, 0], [3, 0, 0]]])
	

	names.append("LAnklePitch")
	times.append([0.68, 1.24, 1.92, 2.6, 3.44, 4.12, 4.52, 4.92, 5.4, 6.2, 7.7, 12.0])
	keys.append([[0.254602, [3, -0.226667, 0], [3, 0.186667, 0]], [0.254602, [3, -0.186667, 0], [3, 0.226667, 0]], [0.483168, [3, -0.226667, -0.0646837], [3, 0.226667, 0.0646837]], [0.642704, [3, -0.226667, -0.0317968], [3, 0.28, 0.0392785]], [0.696394, [3, -0.28, 0], [3, 0.226667, 0]], [0.197844, [3, -0.226667, 0.0156466], [3, 0.133333, -0.0092039]], [0.18864, [3, -0.133333, 0.0092039], [3, 0.133333, -0.0092039]], [-0.293036, [3, -0.133333, 0.127601], [3, 0.16, -0.153121]], [-0.653526, [3, -0.16, 0], [3, 0, 0]],
	             [-1.16281, [3, -0.266667, 0], [3, 0.266667, 0]], [-1.17355, [3, -0.266667, 0], [3, 0.333333, 0]], [-1.16281, [3, -0.333333, 0], [3, 0, 0]]])
	

	names.append("LAnkleRoll")
	times.append([0.68, 1.24, 1.92, 2.6, 3.44, 4.12, 4.52, 4.92, 5.4, 6.2, 7.7, 12.0])
	keys.append([[-0.253068, [3, -0.226667, 0], [3, 0.186667, 0]], [-0.263806, [3, -0.186667, 0.00715867], [3, 0.226667, -0.00869267]], [-0.300622, [3, -0.226667, 0], [3, 0.226667, 0]], [-0.199378, [3, -0.226667, -0.0207469], [3, 0.28, 0.0256285]], [-0.161496, [3, -0.28, 0], [3, 0.226667, 0]], [-0.39597, [3, -0.226667, 0.00219991], [3, 0.133333, -0.00129406]], [-0.397264, [3, -0.133333, 0], [3, 0.133333, 0]], [-0.397264, [3, -0.133333, 0], [3, 0.16, 0]], [-0.124212, [3, -0.16, 0], [3, 0, 0]],
	             [-0.0604966, [3, -0.266667, 0], [3, 0.266667, 0]], [-0.0582142, [3, -0.266667, 0], [3, 0.333333, 0]], [-0.0604966, [3, -0.333333, 0], [3, 0, 0]]])
	
	names.append("LElbowRoll")
	times.append([0.68, 1.24, 1.92, 2.6, 3.44, 4.12, 4.52, 4.92, 5.4, 6.2, 7.7, 12.0])
	keys.append([[-0.400332, [3, -0.226667, 0], [3, 0.186667, 0]], [-0.400332, [3, -0.186667, 0], [3, 0.226667, 0]], [-0.400332, [3, -0.226667, 0], [3, 0.226667, 0]], [-0.424876, [3, -0.226667, 0.00526137], [3, 0.28, -0.00649934]], [-0.435614, [3, -0.28, 0.00339095], [3, 0.226667, -0.00274506]], [-0.443284, [3, -0.226667, 0.00193168], [3, 0.133333, -0.00113628]], [-0.444818, [3, -0.133333, 0.00153398], [3, 0.133333, -0.00153398]], [-0.460158, [3, -0.133333, 0], [3, 0.16, 0]], [-0.0935321, [3, -0.16, 0], [3, 0, 0]],
	             [-0.102736, [3, -0.266667, 0], [3, 0.266667, 0]], [-1.45726, [3, -0.266667, 0], [3, 0.333333, 0]], [-0.102736, [3, -0.333333, 0], [3, 0, 0]]])
	
	names.append("LElbowYaw")
	times.append([0.68, 1.24, 1.92, 2.6, 3.44, 4.12, 4.52, 4.92, 5.4, 6.2, 7.7, 12.0])
	keys.append([[-1.21037, [3, -0.226667, 0], [3, 0.186667, 0]], [-1.21037, [3, -0.186667, 0], [3, 0.226667, 0]], [-1.21037, [3, -0.226667, 0], [3, 0.226667, 0]], [-1.21037, [3, -0.226667, 0], [3, 0.28, 0]], [-1.21037, [3, -0.28, 0], [3, 0.226667, 0]], [-1.2165, [3, -0.226667, 0], [3, 0.133333, 0]], [-1.21497, [3, -0.133333, 0], [3, 0.133333, 0]], [-1.21497, [3, -0.133333, 0], [3, 0.16, 0]], [-0.871354, [3, -0.16, 0], [3, 0, 0]],
	             [0.384992, [3, -0.266667, 0], [3, 0.266667, 0]], [0.107338, [3, -0.266667, 0], [3, 0.333333, 0]], [0.384992, [3, -0.333333, 0], [3, 0, 0]]])
	
	names.append("LHand")
	times.append([0.68, 1.24, 1.92, 2.6, 3.44, 4.12, 4.52, 4.92, 5.4, 6.2, 7.7, 12.0])
	keys.append([[0.2908, [3, -0.226667, 0], [3, 0.186667, 0]], [0.2908, [3, -0.186667, 0], [3, 0.226667, 0]], [0.2908, [3, -0.226667, 0], [3, 0.226667, 0]], [0.2908, [3, -0.226667, 0], [3, 0.28, 0]], [0.2908, [3, -0.28, 0], [3, 0.226667, 0]], [0.2908, [3, -0.226667, 0], [3, 0.133333, 0]], [0.2908, [3, -0.133333, 0], [3, 0.133333, 0]], [0.2908, [3, -0.133333, 0], [3, 0.16, 0]], [0.2908, [3, -0.16, 0], [3, 0, 0]],
	             [0.2868, [3, -0.266667, 0], [3, 0.266667, 0]], [0.2868, [3, -0.266667, 0], [3, 0.333333, 0]], [0.2868, [3, -0.333333, 0], [3, 0, 0]]])
	
	names.append("LHipPitch")
	times.append([0.68, 1.24, 1.92, 2.6, 3.44, 4.12, 4.52, 4.92, 5.4, 6.2, 7.7, 12.0])
	keys.append([[-0.05825, [3, -0.226667, 0], [3, 0.186667, 0]], [-0.05825, [3, -0.186667, 0], [3, 0.226667, 0]], [-0.747016, [3, -0.226667, 0.234958], [3, 0.226667, -0.234958]], [-1.468, [3, -0.226667, 0], [3, 0.28, 0]], [-1.40203, [3, -0.28, -0.0251496], [3, 0.226667, 0.0203592]], [-1.33147, [3, -0.226667, 0], [3, 0.133333, 0]], [-1.333, [3, -0.133333, 0], [3, 0.133333, 0]], [-0.951038, [3, -0.133333, -0.0990127], [3, 0.16, 0.118815]], [-0.67952, [3, -0.16, 0], [3, 0, 0]],
	             [0.259288, [3, -0.266667, 0], [3, 0.266667, 0]], [0.251617, [3, -0.266667, 0], [3, 0.333333, 0]], [0.259288, [3, -0.333333, 0], [3, 0, 0]]])
	
	names.append("LHipRoll")
	times.append([0.68, 1.24, 1.92, 2.6, 3.44, 4.12, 4.52, 4.92, 5.4, 6.2, 7.7, 12.0])
	keys.append([[0.265424, [3, -0.226667, 0], [3, 0.186667, 0]], [0.265424, [3, -0.186667, 0], [3, 0.226667, 0]], [0.744032, [3, -0.226667, -0.0352821], [3, 0.226667, 0.0352821]], [0.779314, [3, -0.226667, 0], [3, 0.28, 0]], [0.77778, [3, -0.28, 0.00153398], [3, 0.226667, -0.00124179]], [0.046062, [3, -0.226667, 0.0130389], [3, 0.133333, -0.00766992]], [0.0383921, [3, -0.133333, 0], [3, 0.133333, 0]], [0.070606, [3, -0.133333, -0.00627543], [3, 0.16, 0.00753052]], [0.0798099, [3, -0.16, 0], [3, 0, 0]],
	            [0.01845, [3, -0.266667, 0], [3, 0.266667, 0]], [0.11049, [3, -0.266667, 0], [3, 0.333333, 0]], [0.01845, [3, -0.333333, 0], [3, 0, 0]]])
	
	names.append("LHipYawPitch")
	times.append([0.68, 1.24, 1.92, 2.6, 3.44, 4.12, 4.52, 4.92, 5.4, 6.2, 7.7, 12.0])
	keys.append([[-0.538392, [3, -0.226667, 0], [3, 0.186667, 0]], [-0.538392, [3, -0.186667, 0], [3, 0.226667, 0]], [-0.605888, [3, -0.226667, 0.0634053], [3, 0.226667, -0.0634053]], [-0.918824, [3, -0.226667, 0.00869277], [3, 0.28, -0.0107381]], [-0.929562, [3, -0.28, 0], [3, 0.226667, 0]], [-0.546062, [3, -0.226667, 0], [3, 0.133333, 0]], [-0.555266, [3, -0.133333, 0.00332367], [3, 0.133333, -0.00332367]], [-0.566004, [3, -0.133333, 0], [3, 0.16, 0]], [-0.108872, [3, -0.16, 0], [3, 0, 0]],
	             [-0.217786, [3, -0.266667, 0], [3, 0.266667, 0]], [-0.128814, [3, -0.266667, 0], [3, 0.333333, 0]], [-0.217786, [3, -0.333333, 0], [3, 0, 0]]])
	
	names.append("LKneePitch")
	times.append([0.68, 1.24, 1.92, 2.6, 3.44, 4.12, 4.52, 4.92, 5.4, 6.2, 7.7, 12.0])
	keys.append([[0.00455999, [3, -0.226667, 0], [3, 0.186667, 0]], [0.00455999, [3, -0.186667, 0], [3, 0.226667, 0]], [0.161028, [3, -0.226667, -0.0138059], [3, 0.226667, 0.0138059]], [0.174834, [3, -0.226667, -0.00823515], [3, 0.28, 0.0101728]], [0.216252, [3, -0.28, 0], [3, 0.226667, 0]], [0.176368, [3, -0.226667, 0.00260777], [3, 0.133333, -0.00153398]], [0.174834, [3, -0.133333, 0], [3, 0.133333, 0]], [0.197844, [3, -0.133333, 0], [3, 0.16, 0]], [0.0873961, [3, -0.16, 0], [3, 0, 0]],
	             [0.156426, [3, -0.266667, 0], [3, 0.266667, 0]], [-0.0844118, [3, -0.266667, 0], [3, 0.333333, 0]], [0.156426, [3, -0.333333, 0], [3, 0, 0]]])
	
	names.append("LShoulderPitch")
	times.append([0.68, 1.24, 1.92, 2.6, 3.44, 4.12, 4.52, 4.92, 5.4, 6.2, 7.7, 12.0])
	keys.append([[0.383458, [3, -0.226667, 0], [3, 0.186667, 0]], [0.383458, [3, -0.186667, 0], [3, 0.226667, 0]], [0.383458, [3, -0.226667, 0], [3, 0.226667, 0]], [0.37272, [3, -0.226667, 0.00983645], [3, 0.28, -0.0121509]], [0.317496, [3, -0.28, 0.0155418], [3, 0.226667, -0.0125815]], [0.28835, [3, -0.226667, 0.00965853], [3, 0.133333, -0.00568149]], [0.271476, [3, -0.133333, 0], [3, 0.133333, 0]], [0.282214, [3, -0.133333, 0], [3, 0.16, 0]], [0.214718, [3, -0.16, 0], [3, 0, 0]],
	             [0.277612, [3, -0.266667, 0], [3, 0.266667, 0]], [0.0843279, [3, -0.266667, 0], [3, 0.333333, 0]], [0.277612, [3, -0.333333, 0], [3, 0, 0]]])
	
	names.append("LShoulderRoll")
	times.append([0.68, 1.24, 1.92, 2.6, 3.44, 4.12, 4.52, 4.92, 5.4, 6.2, 7.7, 12.0])
	keys.append([[0.144154, [3, -0.226667, 0], [3, 0.186667, 0]], [0.144154, [3, -0.186667, 0], [3, 0.226667, 0]], [0.151824, [3, -0.226667, 0], [3, 0.226667, 0]], [0.121144, [3, -0.226667, 0], [3, 0.28, 0]], [0.187106, [3, -0.28, 0], [3, 0.226667, 0]], [0.168698, [3, -0.226667, 0.006761], [3, 0.133333, -0.00397706]], [0.154892, [3, -0.133333, 0], [3, 0.133333, 0]], [0.176368, [3, -0.133333, 0], [3, 0.16, 0]], [0.10427, [3, -0.16, 0], [3, 0, 0]],
	             [0.10427, [3, -0.266667, 0], [3, 0.266667, 0]], [1.05075, [3, -0.266667, 0], [3, 0.333333, 0]], [0.10427, [3, -0.333333, 0], [3, 0, 0]]])
	
	names.append("LWristYaw")
	times.append([0.68, 1.24, 1.92, 2.6, 3.44, 4.12, 4.52, 4.92, 5.4, 6.2, 7.7, 12.0])
	keys.append([[0.098134, [3, -0.226667, 0], [3, 0.186667, 0]], [0.098134, [3, -0.186667, 0], [3, 0.226667, 0]], [0.098134, [3, -0.226667, 0], [3, 0.226667, 0]], [0.098134, [3, -0.226667, 0], [3, 0.28, 0]], [0.098134, [3, -0.28, 0], [3, 0.226667, 0]], [0.0966001, [3, -0.226667, 0], [3, 0.133333, 0]], [0.101202, [3, -0.133333, 0], [3, 0.133333, 0]], [0.101202, [3, -0.133333, 0], [3, 0.16, 0]], [0.874338, [3, -0.16, 0], [3, 0, 0]],
	             [-1.76107, [3, -0.266667, 0], [3, 0.266667, 0]], [-1.67977, [3, -0.266667, 0], [3, 0.333333, 0]], [-1.76107, [3, -0.333333, 0], [3, 0, 0]]])
	
	names.append("RAnklePitch")
	times.append([0.68, 1.24, 1.92, 2.6, 3.44, 4.12, 4.52, 4.92, 5.4, 6.2, 7.7, 12.0])
	keys.append([[-0.026036, [3, -0.226667, 0], [3, 0.186667, 0]], [-0.398798, [3, -0.186667, 0.0734341], [3, 0.226667, -0.0891699]], [-0.513848, [3, -0.226667, 0], [3, 0.226667, 0]], [-0.513848, [3, -0.226667, 0], [3, 0.28, 0]], [-0.713268, [3, -0.28, 0], [3, 0.226667, 0]], [-0.630432, [3, -0.226667, -0.0828359], [3, 0.133333, 0.048727]], [-0.277612, [3, -0.133333, -0.021476], [3, 0.133333, 0.021476]], [-0.256136, [3, -0.133333, 0], [3, 0.16, 0]], [-0.56447, [3, -0.16, 0], [3, 0, 0]],
	            [-1.17654, [3, -0.266667, 0], [3, 0.266667, 0]], [-1.17654, [3, -0.266667, 0], [3, 0.333333, 0]], [-1.17654, [3, -0.333333, 0], [3, 0, 0]]])
	
	names.append("RAnkleRoll")
	times.append([0.68, 1.24, 1.92, 2.6, 3.44, 4.12, 4.52, 4.92, 5.4, 6.2, 7.7, 12.0])
	keys.append([[0.0107799, [3, -0.226667, 0], [3, 0.186667, 0]], [-0.0935321, [3, -0.186667, 0], [3, 0.226667, 0]], [0.222472, [3, -0.226667, -0.0107381], [3, 0.226667, 0.0107381]], [0.23321, [3, -0.226667, -0.0107381], [3, 0.28, 0.0132648]], [0.382024, [3, -0.28, 0], [3, 0.226667, 0]], [0.268492, [3, -0.226667, 0], [3, 0.133333, 0]], [0.326784, [3, -0.133333, 0], [3, 0.133333, 0]], [0.326784, [3, -0.133333, 0], [3, 0.16, 0]], [0.0752079, [3, -0.16, 0], [3, 0, 0]],
	             [0.0521979, [3, -0.266667, 0], [3, 0.266667, 0]], [0.0525396, [3, -0.266667, 0], [3, 0.333333, 0]], [0.0521979, [3, -0.333333, 0], [3, 0, 0]]])
	
	names.append("RElbowRoll")
	times.append([0.68, 1.24, 1.92, 2.6, 3.44, 4.12, 4.52, 4.92, 5.4, 6.2, 7.7, 12.0])
	keys.append([[0.418824, [3, -0.226667, 0], [3, 0.186667, 0]], [0.418824, [3, -0.186667, 0], [3, 0.226667, 0]], [0.418824, [3, -0.226667, 0], [3, 0.226667, 0]], [0.418824, [3, -0.226667, 0], [3, 0.28, 0]], [0.418824, [3, -0.28, 0], [3, 0.226667, 0]], [0.421892, [3, -0.226667, -0.00160974], [3, 0.133333, 0.000946906]], [0.426494, [3, -0.133333, 0], [3, 0.133333, 0]], [0.426494, [3, -0.133333, 0], [3, 0.16, 0]], [0.0349066, [3, -0.16, 0], [3, 0, 0]],
	             [0.0429941, [3, -0.266667, 0], [3, 0.266667, 0]], [1.36377, [3, -0.266667, 0], [3, 0.333333, 0]], [0.0429941, [3, -0.333333, 0], [3, 0, 0]]])
	
	names.append("RElbowYaw")
	times.append([0.68, 1.24, 1.92, 2.6, 3.44, 4.12, 4.52, 4.92, 5.4, 6.2, 7.7, 12.0])
	keys.append([[1.28085, [3, -0.226667, 0], [3, 0.186667, 0]], [1.28085, [3, -0.186667, 0], [3, 0.226667, 0]], [1.28085, [3, -0.226667, 0], [3, 0.226667, 0]], [1.26858, [3, -0.226667, 0], [3, 0.28, 0]], [1.26858, [3, -0.28, 0], [3, 0.226667, 0]], [1.25784, [3, -0.226667, 0], [3, 0.133333, 0]], [1.27011, [3, -0.133333, 0], [3, 0.133333, 0]], [1.27011, [3, -0.133333, 0], [3, 0.16, 0]], [0.398798, [3, -0.16, 0], [3, 0, 0]],
	             [0.0966001, [3, -0.266667, 0], [3, 0.266667, 0]], [0.202446, [3, -0.266667, 0], [3, 0.333333, 0]], [0.0966001, [3, -0.333333, 0], [3, 0, 0]]])
	
	names.append("RHand")
	times.append([0.68, 1.24, 1.92, 2.6, 3.44, 4.12, 4.52, 4.92, 5.4, 6.2, 7.7, 12.0])
	keys.append([[0.3144, [3, -0.226667, 0], [3, 0.186667, 0]], [0.3144, [3, -0.186667, 0], [3, 0.226667, 0]], [0.3144, [3, -0.226667, 0], [3, 0.226667, 0]], [0.3144, [3, -0.226667, 0], [3, 0.28, 0]], [0.3144, [3, -0.28, 0], [3, 0.226667, 0]], [0.3144, [3, -0.226667, 0], [3, 0.133333, 0]], [0.3144, [3, -0.133333, 0], [3, 0.133333, 0]], [0.3144, [3, -0.133333, 0], [3, 0.16, 0]], [0.3144, [3, -0.16, 0], [3, 0, 0]],
	             [0.2952, [3, -0.266667, 0], [3, 0.266667, 0]], [0.2952, [3, -0.266667, 0], [3, 0.333333, 0]], [0.2952, [3, -0.333333, 0], [3, 0, 0]]])
	
	names.append("RHipPitch")
	times.append([0.68, 1.24, 1.92, 2.6, 3.44, 4.12, 4.52, 4.92, 5.4, 6.2, 7.7, 12.0])
	keys.append([[-0.208666, [3, -0.226667, 0], [3, 0.186667, 0]], [-0.582962, [3, -0.186667, 0.0416887], [3, 0.226667, -0.050622]], [-0.633584, [3, -0.226667, 0], [3, 0.226667, 0]], [-0.633584, [3, -0.226667, 0], [3, 0.28, 0]], [-1.48189, [3, -0.28, 0], [3, 0.226667, 0]], [-1.0539, [3, -0.226667, -0.187375], [3, 0.133333, 0.110221]], [-0.589098, [3, -0.133333, -0.0153401], [3, 0.133333, 0.0153401]], [-0.573758, [3, -0.133333, -0.00813484], [3, 0.16, 0.0097618]], [-0.535408, [3, -0.16, 0], [3, 0, 0]],
	             [0.300622, [3, -0.266667, 0], [3, 0.266667, 0]], [0.249999, [3, -0.266667, 0], [3, 0.333333, 0]], [0.300622, [3, -0.333333, 0], [3, 0, 0]]])
	
	names.append("RHipRoll")
	times.append([0.68, 1.24, 1.92, 2.6, 3.44, 4.12, 4.52, 4.92, 5.4, 6.2, 7.7, 12.0])
	keys.append([[0.0138481, [3, -0.226667, 0], [3, 0.186667, 0]], [0.024586, [3, -0.186667, -0.00900605], [3, 0.226667, 0.0109359]], [0.073674, [3, -0.226667, 0], [3, 0.226667, 0]], [0.0614018, [3, -0.226667, 0.0096077], [3, 0.28, -0.0118683]], [0.00924587, [3, -0.28, 0], [3, 0.226667, 0]], [0.090548, [3, -0.226667, 0], [3, 0.133333, 0]], [0.0537319, [3, -0.133333, 0], [3, 0.133333, 0]], [0.0537319, [3, -0.133333, 0], [3, 0.16, 0]], [-0.0597839, [3, -0.16, 0], [3, 0, 0]],
	             [0.0261199, [3, -0.266667, 0], [3, 0.266667, 0]], [0.023052, [3, -0.266667, 0], [3, 0.333333, 0]], [0.0261199, [3, -0.333333, 0], [3, 0, 0]]])
	
	names.append("RHipYawPitch")
	times.append([0.68, 1.24, 1.92, 2.6, 3.44, 4.12, 4.52, 4.92, 5.4, 6.2, 7.7, 12.0])
	keys.append([[-0.538392, [3, -0.226667, 0], [3, 0.186667, 0]], [-0.538392, [3, -0.186667, 0], [3, 0.226667, 0]], [-0.605888, [3, -0.226667, 0.0634053], [3, 0.226667, -0.0634053]], [-0.918824, [3, -0.226667, 0.00869277], [3, 0.28, -0.0107381]], [-0.929562, [3, -0.28, 0], [3, 0.226667, 0]], [-0.546062, [3, -0.226667, 0], [3, 0.133333, 0]], [-0.555266, [3, -0.133333, 0.00332367], [3, 0.133333, -0.00332367]], [-0.566004, [3, -0.133333, 0], [3, 0.16, 0]], [-0.108872, [3, -0.16, 0], [3, 0, 0]],
	            [-0.217786, [3, -0.266667, 0], [3, 0.266667, 0]], [-0.128814, [3, -0.266667, 0], [3, 0.333333, 0]], [-0.217786, [3, -0.333333, 0], [3, 0, 0]]])
	
	names.append("RKneePitch")
	times.append([0.68, 1.24, 1.92, 2.6, 3.44, 4.12, 4.52, 4.92, 5.4, 6.2, 7.7, 12.0])
	keys.append([[0.41729, [3, -0.226667, 0], [3, 0.186667, 0]], [1.0493, [3, -0.186667, -0.124699], [3, 0.226667, 0.151421]], [1.24565, [3, -0.226667, 0], [3, 0.226667, 0]], [1.23951, [3, -0.226667, 0], [3, 0.28, 0]], [2.10162, [3, -0.28, 0], [3, 0.226667, 0]], [0.737896, [3, -0.226667, 0.460459], [3, 0.133333, -0.270858]], [-0.0923279, [3, -0.133333, 0], [3, 0.133333, 0]], [-0.0923279, [3, -0.133333, 0], [3, 0.16, 0]], [-0.0904641, [3, -0.16, 0], [3, 0, 0]],
	             [0.128898, [3, -0.266667, 0], [3, 0.266667, 0]], [-0.0735901, [3, -0.266667, 0], [3, 0.333333, 0]], [0.128898, [3, -0.333333, 0], [3, 0, 0]]])
	
	names.append("RShoulderPitch")
	times.append([0.68, 1.24, 1.92, 2.6, 3.44, 4.12, 4.52, 4.92, 5.4, 6.2, 7.7, 12.0])
	keys.append([[0.311444, [3, -0.226667, 0], [3, 0.186667, 0]], [0.311444, [3, -0.186667, 0], [3, 0.226667, 0]], [0.311444, [3, -0.226667, 0], [3, 0.226667, 0]], [0.311444, [3, -0.226667, 0], [3, 0.28, 0]], [0.311444, [3, -0.28, 0], [3, 0.226667, 0]], [0.334454, [3, -0.226667, 0], [3, 0.133333, 0]], [0.30991, [3, -0.133333, 0], [3, 0.133333, 0]], [0.320648, [3, -0.133333, 0], [3, 0.16, 0]], [0.208666, [3, -0.16, 0], [3, 0, 0]],
	             [0.487854, [3, -0.266667, 0], [3, 0.266667, 0]], [0.382009, [3, -0.266667, 0], [3, 0.333333, 0]], [0.487854, [3, -0.333333, 0], [3, 0, 0]]])
	
	names.append("RShoulderRoll")
	times.append([0.68, 1.24, 1.92, 2.6, 3.44, 4.12, 4.52, 4.92, 5.4, 6.2, 7.7, 12.0])
	keys.append([[-0.09515, [3, -0.226667, 0], [3, 0.186667, 0]], [-0.09515, [3, -0.186667, 0], [3, 0.226667, 0]], [-0.09515, [3, -0.226667, 0], [3, 0.226667, 0]], [-0.092082, [3, -0.226667, 0], [3, 0.28, 0]], [-0.092082, [3, -0.28, 0], [3, 0.226667, 0]], [-0.10282, [3, -0.226667, 0.00965853], [3, 0.133333, -0.00568149]], [-0.138102, [3, -0.133333, 0], [3, 0.133333, 0]], [-0.127364, [3, -0.133333, -0.0107381], [3, 0.16, 0.0128858]], [0.0367742, [3, -0.16, 0], [3, 0, 0]],
	            [-0.0782759, [3, -0.266667, 0], [3, 0.266667, 0]], [-0.944986, [3, -0.266667, 0], [3, 0.333333, 0]], [-0.0782759, [3, -0.333333, 0], [3, 0, 0]]])
	
	names.append("RWristYaw")
	times.append([0.68, 1.24, 1.92, 2.6, 3.44, 4.12, 4.52, 4.92, 5.4, 6.2, 7.7, 12.0])
	keys.append([[0.337438, [3, -0.226667, 0], [3, 0.186667, 0]], [0.337438, [3, -0.186667, 0], [3, 0.226667, 0]], [0.337438, [3, -0.226667, 0], [3, 0.226667, 0]], [0.337438, [3, -0.226667, 0], [3, 0.28, 0]], [0.337438, [3, -0.28, 0], [3, 0.226667, 0]], [0.323632, [3, -0.226667, 0], [3, 0.133333, 0]], [0.323632, [3, -0.133333, 0], [3, 0.133333, 0]], [0.323632, [3, -0.133333, 0], [3, 0.16, 0]], [0.323632, [3, -0.16, 0], [3, 0, 0]],
	             [1.54623, [3, -0.266667, 0], [3, 0.266667, 0]], [1.54623, [3, -0.266667, 0], [3, 0.333333, 0]], [1.54623, [3, -0.333333, 0], [3, 0, 0]]])
	
	return names, times, keys