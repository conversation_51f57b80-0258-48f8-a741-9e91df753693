def __call__():
    names = list()
    times = list()
    keys = list()

    names.append("HeadPitch")
    times.append([10.0, 17.29])
    keys.append([[-0.20253, [3, -0.533333, 0], [3, 0.533333, 0]], [-0.2102, [3, -0.533333, 0], [3, 0, 0]]])

    names.append("<PERSON>Yaw")
    times.append([10.0, 17.29])
    keys.append([[4.19617e-05, [3, -0.533333, 0], [3, 0.533333, 0]], [0.01845, [3, -0.533333, 0], [3, 0, 0]]])

    names.append("LAnkle<PERSON>itch")
    times.append([10.0, 17.29])
    keys.append([[-1.20261, [3, -0.533333, 0], [3, 0.533333, 0]], [0.0982179, [3, -0.533333, 0], [3, 0, 0]]])

    names.append("<PERSON><PERSON><PERSON><PERSON><PERSON>")
    times.append([10.0, 17.29])
    keys.append([[0.0183661, [3, -0.533333, 0], [3, 0.533333, 0]], [-0.119694, [3, -0.533333, 0], [3, 0, 0]]])

    names.append("LElbowRoll")
    times.append([10.0, 17.29])
    keys.append([[-0.403483, [3, -0.533333, 0], [3, 0.533333, 0]], [-0.406552, [3, -0.533333, 0], [3, 0, 0]]])

    names.append("LElbowYaw")
    times.append([10.0, 17.29])
    keys.append([[-1.21795, [3, -0.533333, 0], [3, 0.533333, 0]], [-1.21489, [3, -0.533333, 0], [3, 0, 0]]])

    names.append("LHand")
    times.append([10.0, 17.29])
    keys.append([[0.2924, [3, -0.533333, 0], [3, 0.533333, 0]], [0.2924, [3, -0.533333, 0], [3, 0, 0]]])

    names.append("LHipPitch")
    times.append([10.0, 17.29])
    keys.append([[0.28068, [3, -0.533333, 0], [3, 0.533333, 0]], [0.131882, [3, -0.533333, 0], [3, 0, 0]]])

    names.append("LHipRoll")
    times.append([10.0, 17.29])
    keys.append([[0.00149202, [3, -0.533333, 0], [3, 0.533333, 0]], [0.12728, [3, -0.533333, 0], [3, 0, 0]]])

    names.append("LHipYawPitch")
    times.append([10.0, 17.29])
    keys.append([[-0.205514, [3, -0.533333, 0], [3, 0.533333, 0]], [-0.1733, [3, -0.533333, 0], [3, 0, 0]]])

    names.append("LKneePitch")
    times.append([10.0, 17.29])
    keys.append([[1.06924, [3, -0.533333, 0], [3, 0.533333, 0]], [-0.0843279, [3, -0.533333, 0], [3, 0, 0]]])

    names.append("LShoulderPitch")
    times.append([10.0, 17.29])
    keys.append([[1.45888, [3, -0.533333, 0], [3, 0.533333, 0]], [1.44814, [3, -0.533333, 0], [3, 0, 0]]])

    names.append("LShoulderRoll")
    times.append([10.0, 17.29])
    keys.append([[0.2102, [3, -0.533333, 0], [3, 0.533333, 0]], [0.196393, [3, -0.533333, 0], [3, 0, 0]]])

    names.append("LWristYaw")
    times.append([10.0, 17.29])
    keys.append([[-0.138018, [3, -0.533333, 0], [3, 0.533333, 0]], [-0.0689882, [3, -0.533333, 0], [3, 0, 0]]])

    names.append("RAnklePitch")
    times.append([10.0, 17.29])
    keys.append([[-0.509331, [3, -0.533333, 0], [3, 0.533333, 0]], [0.091998, [3, -0.533333, 0], [3, 0, 0]]])

    names.append("RAnkleRoll")
    times.append([10.0, 17.29])
    keys.append([[-0.0107799, [3, -0.533333, 0], [3, 0.533333, 0]], [0.116542, [3, -0.533333, 0], [3, 0, 0]]])

    names.append("RElbowRoll")
    times.append([10.0, 17.29])
    keys.append([[0.4034, [3, -0.533333, 0], [3, 0.533333, 0]], [0.406468, [3, -0.533333, 0], [3, 0, 0]]])

    names.append("RElbowYaw")
    times.append([10.0, 17.29])
    keys.append([[1.19963, [3, -0.533333, 0], [3, 0.533333, 0]], [1.22111, [3, -0.533333, 0], [3, 0, 0]]])

    names.append("RHand")
    times.append([10.0, 17.29])
    keys.append([[0.2936, [3, -0.533333, 0], [3, 0.533333, 0]], [0.294, [3, -0.533333, 0], [3, 0, 0]]])

    names.append("RHipPitch")
    times.append([10.0, 17.29])
    keys.append([[-0.931096, [3, -0.533333, 0], [3, 0.533333, 0]], [0.1335, [3, -0.533333, 0], [3, 0, 0]]])

    names.append("RHipRoll")
    times.append([10.0, 17.29])
    keys.append([[0.0536479, [3, -0.533333, 0], [3, 0.533333, 0]], [-0.124296, [3, -0.533333, 0], [3, 0, 0]]])

    names.append("RHipYawPitch")
    times.append([10.0, 17.29])
    keys.append([[-0.205514, [3, -0.533333, 0], [3, 0.533333, 0]], [-0.1733, [3, -0.533333, 0], [3, 0, 0]]])

    names.append("RKneePitch")
    times.append([10.0, 17.29])
    keys.append([[1.59072, [3, -0.533333, 0], [3, 0.533333, 0]], [-0.0828778, [3, -0.533333, 0], [3, 0, 0]]])

    names.append("RShoulderPitch")
    times.append([10.0, 17.29])
    keys.append([[1.47106, [3, -0.533333, 0], [3, 0.533333, 0]], [1.45266, [3, -0.533333, 0], [3, 0, 0]]])

    names.append("RShoulderRoll")
    times.append([10.0, 17.29])
    keys.append([[-0.223922, [3, -0.533333, 0], [3, 0.533333, 0]], [-0.193243, [3, -0.533333, 0], [3, 0, 0]]])

    names.append("RWristYaw")
    times.append([10.0, 17.29])
    keys.append([[-0.0889301, [3, -0.533333, 0], [3, 0.533333, 0]], [-0.076658, [3, -0.533333, 0], [3, 0, 0]]])

    return names, times, keys