def __call__():
    names = list()
    times = list()
    keys = list()

    names.append("HeadPitch")
    times.append([1.56, 3.56, 4.56, 5.56])
    keys.append([[-0.159578, [3, -0.2, 0], [3, 0.333333, 0]], [-0.107422, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.107422, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.159578, [3, -0.333333, 0], [3, 0, 0]]])

    names.append("HeadYaw")
    times.append([1.56, 3.56, 4.56, 5.56])
    keys.append([[-0.00924586, [3, -0.2, 0], [3, 0.333333, 0]], [-0.019984, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.019984, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.00924586, [3, -0.333333, 0], [3, 0, 0]]])

    names.append("LAnkle<PERSON>itch")
    times.append([1.56, 3.56, 4.56, 5.56])
    keys.append([[0.0843279, [3, -0.2, 0], [3, 0.333333, 0]], [-0.559952, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.559952, [3, -0.333333, 0], [3, 0.333333, 0]], [0.0843279, [3, -0.333333, 0], [3, 0, 0]]])

    names.append("LAnkleRoll")
    times.append([1.56, 3.56, 4.56, 5.56])
    keys.append([[-0.115008, [3, -0.2, 0], [3, 0.333333, 0]], [-0.122678, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.122678, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.115008, [3, -0.333333, 0], [3, 0, 0]]])

    names.append("LElbowRoll")
    times.append([1.56, 3.56, 4.56, 5.56])
    keys.append([[-0.0383082, [3, -0.2, 0], [3, 0.333333, 0]], [-0.0383082, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.0383082, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.0383082, [3, -0.333333, 0], [3, 0, 0]]])

    names.append("LElbowYaw")
    times.append([1.56, 3.56, 4.56, 5.56])
    keys.append([[-0.944986, [3, -0.2, 0], [3, 0.333333, 0]], [-0.944986, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.944986, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.944986, [3, -0.333333, 0], [3, 0, 0]]])

    names.append("LHand")
    times.append([1.56, 3.56, 4.56, 5.56])
    keys.append([[0.294, [3, -0.2, 0], [3, 0.333333, 0]], [0.294, [3, -0.333333, 0], [3, 0.333333, 0]], [0.294, [3, -0.333333, 0], [3, 0.333333, 0]], [0.294, [3, -0.333333, 0], [3, 0, 0]]])

    names.append("LHipPitch")
    times.append([1.56, 3.56, 4.56, 5.56])
    keys.append([[0.130432, [3, -0.2, 0], [3, 0.333333, 0]], [-0.923426, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.923426, [3, -0.333333, 0], [3, 0.333333, 0]], [0.130432, [3, -0.333333, 0], [3, 0, 0]]])

    names.append("LHipRoll")
    times.append([1.56, 3.56, 4.56, 5.56])
    keys.append([[0.124296, [3, -0.2, 0], [3, 0.333333, 0]], [0.142704, [3, -0.333333, 0], [3, 0.333333, 0]], [0.142704, [3, -0.333333, 0], [3, 0.333333, 0]], [0.124296, [3, -0.333333, 0], [3, 0, 0]]])

    names.append("LHipYawPitch")
    times.append([1.56, 3.56, 4.56, 5.56])
    keys.append([[-0.176367, [3, -0.2, 0], [3, 0.333333, 0]], [-0.288349, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.288349, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.176367, [3, -0.333333, 0], [3, 0, 0]]])

    names.append("LKneePitch")
    times.append([1.56, 3.56, 4.56, 5.56])
    keys.append([[-0.090548, [3, -0.2, 0], [3, 0.333333, 0]], [1.54776, [3, -0.333333, 0], [3, 0.333333, 0]], [1.54776, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.090548, [3, -0.333333, 0], [3, 0, 0]]])

    names.append("LShoulderPitch")
    times.append([1.56, 3.56, 4.56, 5.56])
    keys.append([[0.05058, [3, -0.2, 0], [3, 0.333333, 0]], [0.0843279, [3, -0.333333, 0], [3, 0.333333, 0]], [0.0843279, [3, -0.333333, 0], [3, 0.333333, 0]], [0.05058, [3, -0.333333, 0], [3, 0, 0]]])

    names.append("LShoulderRoll")
    times.append([1.56, 3.56, 4.56, 5.56])
    keys.append([[-0.17185, [3, -0.2, 0], [3, 0.333333, 0]], [-0.159578, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.159578, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.17185, [3, -0.333333, 0], [3, 0, 0]]])

    names.append("LWristYaw")
    times.append([1.56, 3.56, 4.56, 5.56])
    keys.append([[0.466294, [3, -0.2, 0], [3, 0.333333, 0]], [0.466294, [3, -0.333333, 0], [3, 0.333333, 0]], [0.466294, [3, -0.333333, 0], [3, 0.333333, 0]], [0.466294, [3, -0.333333, 0], [3, 0, 0]]])

    names.append("RAnklePitch")
    times.append([1.56, 3.56, 4.56, 5.56])
    keys.append([[0.0874801, [3, -0.2, 0], [3, 0.333333, 0]], [-0.576742, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.576742, [3, -0.333333, 0], [3, 0.333333, 0]], [0.0874801, [3, -0.333333, 0], [3, 0, 0]]])

    names.append("RAnkleRoll")
    times.append([1.56, 3.56, 4.56, 5.56])
    keys.append([[0.122762, [3, -0.2, 0], [3, 0.333333, 0]], [0.185656, [3, -0.333333, 0], [3, 0.333333, 0]], [0.185656, [3, -0.333333, 0], [3, 0.333333, 0]], [0.122762, [3, -0.333333, 0], [3, 0, 0]]])

    names.append("RElbowRoll")
    times.append([1.56, 3.56, 4.56, 5.56])
    keys.append([[0.0982179, [3, -0.2, 0], [3, 0.333333, 0]], [0.0982179, [3, -0.333333, 0], [3, 0.333333, 0]], [0.0982179, [3, -0.333333, 0], [3, 0.333333, 0]], [0.0982179, [3, -0.333333, 0], [3, 0, 0]]])

    names.append("RElbowYaw")
    times.append([1.56, 3.56, 4.56, 5.56])
    keys.append([[0.898883, [3, -0.2, 0], [3, 0.333333, 0]], [0.898883, [3, -0.333333, 0], [3, 0.333333, 0]], [0.898883, [3, -0.333333, 0], [3, 0.333333, 0]], [0.898883, [3, -0.333333, 0], [3, 0, 0]]])

    names.append("RHand")
    times.append([1.56, 3.56, 4.56, 5.56])
    keys.append([[0.2852, [3, -0.2, 0], [3, 0.333333, 0]], [0.2852, [3, -0.333333, 0], [3, 0.333333, 0]], [0.2852, [3, -0.333333, 0], [3, 0.333333, 0]], [0.2852, [3, -0.333333, 0], [3, 0, 0]]])

    names.append("RHipPitch")
    times.append([1.56, 3.56, 4.56, 5.56])
    keys.append([[0.125746, [3, -0.2, 0], [3, 0.333333, 0]], [-0.813062, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.813062, [3, -0.333333, 0], [3, 0.333333, 0]], [0.125746, [3, -0.333333, 0], [3, 0, 0]]])

    names.append("RHipRoll")
    times.append([1.56, 3.56, 4.56, 5.56])
    keys.append([[-0.125746, [3, -0.2, 0], [3, 0.333333, 0]], [-0.130348, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.130348, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.125746, [3, -0.333333, 0], [3, 0, 0]]])

    names.append("RHipYawPitch")
    times.append([1.56, 3.56, 4.56, 5.56])
    keys.append([[-0.176367, [3, -0.2, 0], [3, 0.333333, 0]], [-0.288349, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.288349, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.176367, [3, -0.333333, 0], [3, 0, 0]]])

    names.append("RKneePitch")
    times.append([1.56, 3.56, 4.56, 5.56])
    keys.append([[-0.0981341, [3, -0.2, 0], [3, 0.333333, 0]], [1.46041, [3, -0.333333, 0], [3, 0.333333, 0]], [1.46041, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.0981341, [3, -0.333333, 0], [3, 0, 0]]])

    names.append("RShoulderPitch")
    times.append([1.56, 3.56, 4.56, 5.56])
    keys.append([[0.0614019, [3, -0.2, 0], [3, 0.333333, 0]], [0.0844118, [3, -0.333333, 0], [3, 0.333333, 0]], [0.0844118, [3, -0.333333, 0], [3, 0.333333, 0]], [0.0614019, [3, -0.333333, 0], [3, 0, 0]]])

    names.append("RShoulderRoll")
    times.append([1.56, 3.56, 4.56, 5.56])
    keys.append([[0.14262, [3, -0.2, 0], [3, 0.333333, 0]], [0.121144, [3, -0.333333, 0], [3, 0.333333, 0]], [0.121144, [3, -0.333333, 0], [3, 0.333333, 0]], [0.14262, [3, -0.333333, 0], [3, 0, 0]]])

    names.append("RWristYaw")
    times.append([1.56, 3.56, 4.56, 5.56])
    keys.append([[-0.592166, [3, -0.2, 0], [3, 0.333333, 0]], [-0.592166, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.592166, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.592166, [3, -0.333333, 0], [3, 0, 0]]])

    return names, times, keys