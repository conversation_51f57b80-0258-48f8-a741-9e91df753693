# Choregraphe bezier export in Python.

def __call__():
    names = list()
    times = list()
    keys = list()

    names.append("HeadPitch")
    times.append([1.0, 3.4, 5.0, 7.8, 9.0, 11, 13, 15])
    keys.append([[-0.231676, [3, -0.133333, 0], [3, 0.2, 0]], [0.213184, [3, -0.2, -9.27351e-07], [3, 0.186667, 8.65527e-07]], [0.213185, [3, -0.186667, 0], [3, 0.146667, 0]], [-0.305308, [3, -0.146667, 1.3078e-06], [3, 0.0933333, -8.32238e-07]], [-0.305309, [3, -0.0933333, 0], [3, 0.0933333, 0]], [0.213185, [3, -0.0933333, 0], [3, 0.16, 0]], [0.213185, [3, -0.16, 0], [3, 0.12, 0]], [-0.231675, [3, -0.12, 0], [3, 0, 0]]])
    
    names.append("HeadYaw")
    times.append([1.0, 3.4, 5.0, 7.8, 9.0, 11, 13, 15])
    keys.append([[-0.67807, [3, -0.133333, 0], [3, 0.2, 0]], [0.0183661, [3, -0.2, 0], [3, 0.186667, 0]], [0.0183661, [3, -0.186667, 0], [3, 0.146667, 0]], [0.02757, [3, -0.146667, -1.3405e-07], [3, 0.0933333, 8.53044e-08]], [0.0275701, [3, -0.0933333, 0], [3, 0.0933333, 0]], [0.0183661, [3, -0.0933333, 0], [3, 0.16, 0]], [0.0183661, [3, -0.16, 0], [3, 0.12, 0]], [-0.678071, [3, -0.12, 0], [3, 0, 0]]])

    names.append("LAnklePitch")
    times.append([1.0, 3.4, 5.0, 7.8, 9.0, 11, 13, 15])
    keys.append([[0.745482, [3, -0.133333, 0], [3, 0.2, 0]], [0.745482, [3, -0.2, 0], [3, 0.186667, 0]], [0.745483, [3, -0.186667, -5.32632e-07], [3, 0.146667, 4.18497e-07]], [0.811444, [3, -0.146667, 0], [3, 0.0933333, 0]], [0.811444, [3, -0.0933333, 3.99474e-07], [3, 0.0933333, -3.99474e-07]], [0.745483, [3, -0.0933333, 0], [3, 0.16, 0]], [0.745483, [3, -0.16, 0], [3, 0.12, 0]], [0.745483, [3, -0.12, 0], [3, 0, 0]]])

    names.append("LAnkleRoll")
    times.append([1.0, 3.4, 5.0, 7.8, 9.0, 11, 13, 15])
    keys.append([[-0.0152981, [3, -0.133333, 0], [3, 0.2, 0]], [-0.0152981, [3, -0.2, 0], [3, 0.186667, 0]], [-0.0152981, [3, -0.186667, -3.12089e-09], [3, 0.146667, 2.45213e-09]], [0.1335, [3, -0.146667, -3.92341e-08], [3, 0.0933333, 2.49671e-08]], [0.1335, [3, -0.0933333, 0], [3, 0.0933333, 0]], [-0.0152981, [3, -0.0933333, 0], [3, 0.16, 0]], [-0.0152981, [3, -0.16, 0], [3, 0.12, 0]], [-0.0152981, [3, -0.12, 0], [3, 0, 0]]])

    names.append("LElbowRoll")
    times.append([1.0, 3.4, 5.0, 7.8, 9.0, 11, 13, 15])
    keys.append([[-1.06149, [3, -0.133333, 0], [3, 0.2, 0]], [-0.145688, [3, -0.2, -1.78337e-08], [3, 0.186667, 1.66448e-08]], [-0.145688, [3, -0.186667, -1.66448e-08], [3, 0.146667, 1.3078e-08]], [-0.052114, [3, -0.146667, -7.84681e-08], [3, 0.0933333, 4.99343e-08]], [-0.052114, [3, -0.0933333, 0], [3, 0.0933333, 0]], [-0.145688, [3, -0.0933333, 0], [3, 0.16, 0]], [-0.145688, [3, -0.16, 0], [3, 0.12, 0]], [-1.06149, [3, -0.12, 0], [3, 0, 0]]])

    names.append("LElbowYaw")
    times.append([1.0, 3.4, 5.0, 7.8, 9.0, 11, 13, 15])
    keys.append([[-0.174918, [3, -0.133333, 0], [3, 0.2, 0]], [-0.174918, [3, -0.2, 0], [3, 0.186667, 0]], [-0.174919, [3, -0.186667, 0], [3, 0.146667, 0]], [-0.14884, [3, -0.146667, -2.6156e-08], [3, 0.0933333, 1.66448e-08]], [-0.14884, [3, -0.0933333, 0], [3, 0.0933333, 0]], [-0.174919, [3, -0.0933333, 0], [3, 0.16, 0]], [-0.174919, [3, -0.16, 0], [3, 0.12, 0]], [-0.174919, [3, -0.12, 0], [3, 0, 0]]])

    names.append("LHand")
    times.append([1.0, 3.4, 5.0, 7.8, 9.0, 11, 13, 15])
    keys.append([[0.0296, [3, -0.133333, 0], [3, 0.2, 0]], [0.8988, [3, -0.2, 0], [3, 0.186667, 0]], [0.8988, [3, -0.186667, 0], [3, 0.146667, 0]], [0.8924, [3, -0.146667, 0], [3, 0.0933333, 0]], [0.8924, [3, -0.0933333, 0], [3, 0.0933333, 0]], [0.8988, [3, -0.0933333, 0], [3, 0.16, 0]], [0.8988, [3, -0.16, 0], [3, 0.12, 0]], [0.0296, [3, -0.12, 0], [3, 0, 0]]])

    names.append("LHipPitch")
    times.append([1.0, 3.4, 5.0, 7.8, 9.0, 11, 13, 15])
    keys.append([[0.305308, [3, -0.133333, 0], [3, 0.2, 0]], [0.305308, [3, -0.2, 0], [3, 0.186667, 0]], [0.305309, [3, -0.186667, -8.32238e-07], [3, 0.146667, 6.53901e-07]], [0.437232, [3, -0.146667, 0], [3, 0.0933333, 0]], [0.437231, [3, -0.0933333, 8.65527e-07], [3, 0.0933333, -8.65527e-07]], [0.305309, [3, -0.0933333, 0], [3, 0.16, 0]], [0.305309, [3, -0.16, 0], [3, 0.12, 0]], [0.305309, [3, -0.12, 0], [3, 0, 0]]])

    names.append("LHipRoll")
    times.append([1.0, 3.4, 5.0, 7.8, 9.0, 11, 13, 15])
    keys.append([[0.0782759, [3, -0.133333, 0], [3, 0.2, 0]], [-0.029104, [3, -0.2, 7.35639e-08], [3, 0.186667, -6.86596e-08]], [-0.0291041, [3, -0.186667, 0], [3, 0.146667, 0]], [0.2102, [3, -0.146667, -6.27745e-07], [3, 0.0933333, 3.99474e-07]], [0.2102, [3, -0.0933333, 0], [3, 0.0933333, 0]], [-0.0291041, [3, -0.0933333, 0], [3, 0.16, 0]], [-0.0291041, [3, -0.16, 0], [3, 0.12, 0]], [0.0782759, [3, -0.12, 0], [3, 0, 0]]])

    names.append("LHipYawPitch")
    times.append([1.0, 3.4, 5.0, 7.8, 9.0, 11, 13, 15])
    keys.append([[-0.0183661, [3, -0.133333, 0], [3, 0.2, 0]], [-0.029104, [3, -0.2, 7.35639e-08], [3, 0.186667, -6.86596e-08]], [-0.0291041, [3, -0.186667, 6.86596e-08], [3, 0.146667, -5.39468e-08]], [-0.415672, [3, -0.146667, 1.20318e-06], [3, 0.0933333, -7.65659e-07]], [-0.415673, [3, -0.0933333, 0], [3, 0.0933333, 0]], [-0.0291041, [3, -0.0933333, 0], [3, 0.16, 0]], [-0.0291041, [3, -0.16, 0], [3, 0.12, 0]], [-0.0183661, [3, -0.12, 0], [3, 0, 0]]])

    names.append("LKneePitch")
    times.append([1.0, 3.4, 5.0, 7.8, 9.0, 11, 13, 15])
    keys.append([[-0.096684, [3, -0.133333, 0], [3, 0.2, 0]], [-0.096684, [3, -0.2, 0], [3, 0.186667, 0]], [-0.0966839, [3, -0.186667, -7.49014e-08], [3, 0.146667, 5.88511e-08]], [0.289884, [3, -0.146667, 0], [3, 0.0933333, 0]], [0.289883, [3, -0.0933333, 5.99211e-07], [3, 0.0933333, -5.99211e-07]], [-0.0966839, [3, -0.0933333, 0], [3, 0.16, 0]], [-0.0966839, [3, -0.16, 0], [3, 0.12, 0]], [-0.0966839, [3, -0.12, 0], [3, 0, 0]]])

    names.append("LShoulderPitch")
    times.append([1.0, 3.4, 5.0, 7.8, 9.0, 11, 13, 15])
    keys.append([[-1.23031, [3, -0.133333, 0], [3, 0.2, 0]], [-1.34536, [3, -0.2, 0], [3, 0.186667, 0]], [-1.34536, [3, -0.186667, 0], [3, 0.146667, 0]], [-1.70125, [3, -0.146667, 0], [3, 0.0933333, 0]], [-1.70125, [3, -0.0933333, -7.98948e-07], [3, 0.0933333, 7.98948e-07]], [-1.34536, [3, -0.0933333, 0], [3, 0.16, 0]], [-1.34536, [3, -0.16, 0], [3, 0.12, 0]], [-1.23031, [3, -0.12, 0], [3, 0, 0]]])

    names.append("LShoulderRoll")
    times.append([1.0, 3.4, 5.0, 7.8, 9.0, 11, 13, 15])
    keys.append([[0.125746, [3, -0.133333, 0], [3, 0.2, 0]], [0.02757, [3, -0.2, 0], [3, 0.186667, 0]], [0.0275701, [3, -0.186667, -8.53044e-08], [3, 0.146667, 6.70249e-08]], [0.207048, [3, -0.146667, -7.32369e-07], [3, 0.0933333, 4.66053e-07]], [0.207048, [3, -0.0933333, 0], [3, 0.0933333, 0]], [0.0275701, [3, -0.0933333, 0], [3, 0.16, 0]], [0.0275701, [3, -0.16, 0], [3, 0.12, 0]], [0.125746, [3, -0.12, 0], [3, 0, 0]]])

    names.append("LWristYaw")
    times.append([1.0, 3.4, 5.0, 7.8, 9.0, 11, 13, 15])
    keys.append([[-1.57393, [3, -0.133333, 0], [3, 0.2, 0]], [-0.403484, [3, -0.2, -4.28008e-07], [3, 0.186667, 3.99474e-07]], [-0.403483, [3, -0.186667, -3.99474e-07], [3, 0.146667, 3.13873e-07]], [0.377322, [3, -0.146667, -1.20318e-06], [3, 0.0933333, 7.65659e-07]], [0.377323, [3, -0.0933333, 0], [3, 0.0933333, 0]], [-0.403483, [3, -0.0933333, 0], [3, 0.16, 0]], [-0.403483, [3, -0.16, 0], [3, 0.12, 0]], [-1.57393, [3, -0.12, 0], [3, 0, 0]]])

    names.append("RAnklePitch")
    times.append([1.0, 3.4, 5.0, 7.8, 9.0, 11, 13, 15])
    keys.append([[0.921976, [3, -0.133333, 0], [3, 0.2, 0]], [0.921976, [3, -0.2, 0], [3, 0.186667, 0]], [0.921975, [3, -0.186667, 0], [3, 0.146667, 0]], [0.921976, [3, -0.146667, 0], [3, 0.0933333, 0]], [0.921975, [3, -0.0933333, 0], [3, 0.0933333, 0]], [0.921975, [3, -0.0933333, 0], [3, 0.16, 0]], [0.921975, [3, -0.16, 0], [3, 0.12, 0]], [0.921975, [3, -0.12, 0], [3, 0, 0]]])

    names.append("RAnkleRoll")
    times.append([1.0, 3.4, 5.0, 7.8, 9.0, 11, 13, 15])
    keys.append([[-0.00149202, [3, -0.133333, 0], [3, 0.2, 0]], [-0.00149202, [3, -0.2, 0], [3, 0.186667, 0]], [-0.00149202, [3, -0.186667, 0], [3, 0.146667, 0]], [-0.00149202, [3, -0.146667, 0], [3, 0.0933333, 0]], [-0.00149202, [3, -0.0933333, 0], [3, 0.0933333, 0]], [-0.00149202, [3, -0.0933333, 0], [3, 0.16, 0]], [-0.00149202, [3, -0.16, 0], [3, 0.12, 0]], [-0.00149202, [3, -0.12, 0], [3, 0, 0]]])

    names.append("RElbowRoll")
    times.append([1.0, 3.4, 5.0, 7.8, 9.0, 11, 13, 15])
    keys.append([[1.48342, [3, -0.133333, 0], [3, 0.2, 0]], [0.1335, [3, -0.2, 0], [3, 0.186667, 0]], [0.1335, [3, -0.186667, 0], [3, 0.146667, 0]], [0.119694, [3, -0.146667, 1.3078e-08], [3, 0.0933333, -8.32238e-09]], [0.119694, [3, -0.0933333, 0], [3, 0.0933333, 0]], [0.1335, [3, -0.0933333, 0], [3, 0.16, 0]], [0.1335, [3, -0.16, 0], [3, 0.12, 0]], [1.48342, [3, -0.12, 0], [3, 0, 0]]])

    names.append("RElbowYaw")
    times.append([1.0, 3.4, 5.0, 7.8, 9.0, 11, 13, 15])
    keys.append([[0.69486, [3, -0.133333, 0], [3, 0.2, 0]], [0.808376, [3, -0.2, 0], [3, 0.186667, 0]], [0.808375, [3, -0.186667, 5.99211e-07], [3, 0.146667, -4.70809e-07]], [0.765424, [3, -0.146667, 0], [3, 0.0933333, 0]], [0.765425, [3, -0.0933333, -5.99211e-07], [3, 0.0933333, 5.99211e-07]], [0.808375, [3, -0.0933333, 0], [3, 0.16, 0]], [0.808375, [3, -0.16, 0], [3, 0.12, 0]], [0.694859, [3, -0.12, 0], [3, 0, 0]]])

    names.append("RHand")
    times.append([1.0, 3.4, 5.0, 7.8, 9.0, 11, 13, 15])
    keys.append([[0.1928, [3, -0.133333, 0], [3, 0.2, 0]], [0.768, [3, -0.2, 0], [3, 0.186667, 0]], [0.768, [3, -0.186667, 0], [3, 0.146667, 0]], [0.7664, [3, -0.146667, 0], [3, 0.0933333, 0]], [0.7664, [3, -0.0933333, 0], [3, 0.0933333, 0]], [0.768, [3, -0.0933333, 0], [3, 0.16, 0]], [0.768, [3, -0.16, 0], [3, 0.12, 0]], [0.1928, [3, -0.12, 0], [3, 0, 0]]])

    names.append("RHipPitch")
    times.append([1.0, 3.4, 5.0, 7.8, 9.0, 11, 13, 15])
    keys.append([[0.133416, [3, -0.133333, 0], [3, 0.2, 0]], [0.185572, [3, -0.2, 0], [3, 0.186667, 0]], [0.185572, [3, -0.186667, 0], [3, 0.146667, 0]], [0.446352, [3, -0.146667, -3.66185e-07], [3, 0.0933333, 2.33027e-07]], [0.446352, [3, -0.0933333, 0], [3, 0.0933333, 0]], [0.185572, [3, -0.0933333, 0], [3, 0.16, 0]], [0.185572, [3, -0.16, 0], [3, 0.12, 0]], [0.133416, [3, -0.12, 0], [3, 0, 0]]])

    names.append("RHipRoll")
    times.append([1.0, 3.4, 5.0, 7.8, 9.0, 11, 13, 15])
    keys.append([[-0.159494, [3, -0.133333, 0], [3, 0.2, 0]], [-0.0152981, [3, -0.2, -3.34381e-09], [3, 0.186667, 3.12089e-09]], [-0.0152981, [3, -0.186667, 0], [3, 0.146667, 0]], [-0.15796, [3, -0.146667, 5.23121e-08], [3, 0.0933333, -3.32895e-08]], [-0.15796, [3, -0.0933333, 0], [3, 0.0933333, 0]], [-0.0152981, [3, -0.0933333, 0], [3, 0.16, 0]], [-0.0152981, [3, -0.16, 0], [3, 0.12, 0]], [-0.159494, [3, -0.12, 0], [3, 0, 0]]])

    names.append("RHipYawPitch")
    times.append([1.0, 3.4, 5.0, 7.8, 9.0, 11, 13, 15])
    keys.append([[-0.0183661, [3, -0.133333, 0], [3, 0.2, 0]], [-0.029104, [3, -0.2, 7.35639e-08], [3, 0.186667, -6.86596e-08]], [-0.0291041, [3, -0.186667, 6.86596e-08], [3, 0.146667, -5.39468e-08]], [-0.415672, [3, -0.146667, 1.20318e-06], [3, 0.0933333, -7.65659e-07]], [-0.415673, [3, -0.0933333, 0], [3, 0.0933333, 0]], [-0.0291041, [3, -0.0933333, 0], [3, 0.16, 0]], [-0.0291041, [3, -0.16, 0], [3, 0.12, 0]], [-0.0183661, [3, -0.12, 0], [3, 0, 0]]])

    names.append("RKneePitch")
    times.append([1.0, 3.4, 5.0, 7.8, 9.0, 11, 13, 15])
    keys.append([[-0.082794, [3, -0.133333, 0], [3, 0.2, 0]], [-0.0613179, [3, -0.2, 0], [3, 0.186667, 0]], [-0.061318, [3, -0.186667, 0], [3, 0.146667, 0]], [0.303774, [3, -0.146667, -1.09855e-06], [3, 0.0933333, 6.9908e-07]], [0.303775, [3, -0.0933333, 0], [3, 0.0933333, 0]], [-0.061318, [3, -0.0933333, 0], [3, 0.16, 0]], [-0.061318, [3, -0.16, 0], [3, 0.12, 0]], [-0.0827939, [3, -0.12, 0], [3, 0, 0]]])

    names.append("RShoulderPitch")
    times.append([1.0, 3.4, 5.0, 7.8, 9.0, 11, 13, 15])
    keys.append([[-0.711734, [3, -0.133333, 0], [3, 0.2, 0]], [-1.21028, [3, -0.2, 5.70677e-07], [3, 0.186667, -5.32632e-07]], [-1.21028, [3, -0.186667, 5.32632e-07], [3, 0.146667, -4.18497e-07]], [-1.67202, [3, -0.146667, 6.27745e-07], [3, 0.0933333, -3.99474e-07]], [-1.67202, [3, -0.0933333, 0], [3, 0.0933333, 0]], [-1.21028, [3, -0.0933333, 0], [3, 0.16, 0]], [-1.21028, [3, -0.16, 0], [3, 0.12, 0]], [-0.711735, [3, -0.12, 0], [3, 0, 0]]])

    names.append("RShoulderRoll")
    times.append([1.0, 3.4, 5.0, 7.8, 9.0, 11, 13, 15])
    keys.append([[-0.415756, [3, -0.133333, 0], [3, 0.2, 0]], [0.00609398, [3, -0.2, 0], [3, 0.186667, 0]], [0.00609397, [3, -0.186667, 4.68134e-09], [3, 0.146667, -3.67819e-09]], [-0.147306, [3, -0.146667, 0], [3, 0.0933333, 0]], [-0.147306, [3, -0.0933333, 0], [3, 0.0933333, 0]], [0.00609397, [3, -0.0933333, 0], [3, 0.16, 0]], [0.00609397, [3, -0.16, 0], [3, 0.12, 0]], [-0.415757, [3, -0.12, 0], [3, 0, 0]]])

    names.append("RWristYaw")
    times.append([1.0, 3.4, 5.0, 7.8, 9.0, 11, 13, 15])
    keys.append([[1.59072, [3, -0.133333, 0], [3, 0.2, 0]], [-0.15651, [3, -0.2, 7.13347e-08], [3, 0.186667, -6.6579e-08]], [-0.15651, [3, -0.186667, 6.6579e-08], [3, 0.146667, -5.23121e-08]], [-0.86982, [3, -0.146667, 0], [3, 0.0933333, 0]], [-0.86982, [3, -0.0933333, -3.32895e-07], [3, 0.0933333, 3.32895e-07]], [-0.15651, [3, -0.0933333, 0], [3, 0.16, 0]], [-0.15651, [3, -0.16, 0], [3, 0.12, 0]], [1.59072, [3, -0.12, 0], [3, 0, 0]]])

    return names, times, keys