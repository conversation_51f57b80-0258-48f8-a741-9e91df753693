def __call__():
    names = list()
    times = list()
    keys = list()

    names.append("HeadPitch")
    times.append([0.24, 0.96, 1.96, 2.96, 3.96, 4.96, 5.96, 6.96, 7.96, 8.96, 9.96, 10.96, 13.36, 16.76, 17.96, 19.16, 20.36])
    keys.append([[-0.1335, [3, -0.0933333, 0], [3, 0.24, 0]], [-0.145772, [3, -0.24, 0], [3, 0.333333, 0]], [-0.0521979, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.0521979, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.0521979, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.00157595, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.00157595, [3, -0.333333, 0], [3, 0.333333, 0]], [0.033706, [3, -0.333333, -0.00818133], [3, 0.333333, 0.00818133]], [0.0475121, [3, -0.333333, 0], [3, 0.333333, 0]], [0.0475121, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.0537319, [3, -0.333333, 0], [3, 0.333333, 0]], [0.501576, [3, -0.333333, 0], [3, 0.8, 0]], [0.469363, [3, -0.8, 0], [3, 1.13333, 0]], [0.469363, [3, -1.13333, 0], [3, 0.4, 0]], [0.472429, [3, -0.4, 0], [3, 0.4, 0]], [0.472429, [3, -0.4, 0], [3, 0.4, 0]], [0.469363, [3, -0.4, 0], [3, 0, 0]]])

    names.append("HeadYaw")
    times.append([0.24, 0.96, 1.96, 2.96, 3.96, 4.96, 5.96, 6.96, 7.96, 8.96, 9.96, 10.96, 13.36, 16.76, 17.96, 19.16, 20.36])
    keys.append([[-0.0153821, [3, -0.0933333, 0], [3, 0.24, 0]], [-0.0153821, [3, -0.24, 0], [3, 0.333333, 0]], [-0.0153821, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.0153821, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.0153821, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.0153821, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.0153821, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.0153821, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.0153821, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.0153821, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.00464392, [3, -0.333333, -0.0107381], [3, 0.333333, 0.0107381]], [0.076658, [3, -0.333333, 0], [3, 0.8, 0]], [0.076658, [3, -0.8, 0], [3, 1.13333, 0]], [0.076658, [3, -1.13333, 0], [3, 0.4, 0]], [0.056716, [3, -0.4, 0], [3, 0.4, 0]], [0.056716, [3, -0.4, 0], [3, 0.4, 0]], [0.076658, [3, -0.4, 0], [3, 0, 0]]])

    names.append("LAnklePitch")
    times.append([0.24, 0.96, 1.96, 2.96, 3.96, 4.96, 5.96, 6.96, 7.96, 8.96, 9.96, 10.96, 13.36, 16.76, 17.96, 19.16, 20.36])
    keys.append([[0.0444441, [3, -0.0933333, 0], [3, 0.24, 0]], [0.0812599, [3, -0.24, -0.027398], [3, 0.333333, 0.0380527]], [0.240796, [3, -0.333333, -0.0245446], [3, 0.333333, 0.0245446]], [0.265341, [3, -0.333333, -0.0245446], [3, 0.333333, 0.0245446]], [0.501576, [3, -0.333333, -0.0611043], [3, 0.333333, 0.0611043]], [0.631966, [3, -0.333333, 0], [3, 0.333333, 0]], [0.582879, [3, -0.333333, 0.0490874], [3, 0.333333, -0.0490874]], [0.18864, [3, -0.333333, 0.12451], [3, 0.333333, -0.12451]], [-0.16418, [3, -0.333333, 0.161837], [3, 0.333333, -0.161837]], [-0.782382, [3, -0.333333, 0.165672], [3, 0.333333, -0.165672]], [-1.15821, [3, -0.333333, 0.0444849], [3, 0.333333, -0.0444849]], [-1.2027, [3, -0.333333, 0], [3, 0.8, 0]], [-1.18889, [3, -0.8, -0.0138056], [3, 1.13333, 0.0195579]], [0.931096, [3, -1.13333, 0], [3, 0.4, 0]], [0.895815, [3, -0.4, 0], [3, 0.4, 0]], [0.906552, [3, -0.4, -0.0058803], [3, 0.4, 0.0058803]], [0.931096, [3, -0.4, 0], [3, 0, 0]]])

    names.append("LAnkleRoll")
    times.append([0.24, 0.96, 1.96, 2.96, 3.96, 4.96, 5.96, 6.96, 7.96, 8.96, 9.96, 10.96, 13.36, 16.76, 17.96, 19.16, 20.36])
    keys.append([[-0.10427, [3, -0.0933333, 0], [3, 0.24, 0]], [-0.093532, [3, -0.24, 0], [3, 0.333333, 0]], [-0.248467, [3, -0.333333, 0.0214745], [3, 0.333333, -0.0214745]], [-0.269941, [3, -0.333333, 0.00715847], [3, 0.333333, -0.00715847]], [-0.291418, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.208583, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.213185, [3, -0.333333, 0.00460242], [3, 0.333333, -0.00460242]], [-0.397265, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.397265, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.0966001, [3, -0.333333, -0.033748], [3, 0.333333, 0.033748]], [-0.0628521, [3, -0.333333, -0.00971532], [3, 0.333333, 0.00971532]], [-0.0383082, [3, -0.333333, 0], [3, 0.8, 0]], [-0.052114, [3, -0.8, 0], [3, 1.13333, 0]], [0.00157595, [3, -1.13333, -0.00869259], [3, 0.4, 0.00306797]], [0.00464392, [3, -0.4, -0.00230102], [3, 0.4, 0.00230102]], [0.0153821, [3, -0.4, 0], [3, 0.4, 0]], [0.00157595, [3, -0.4, 0], [3, 0, 0]]])

    names.append("LElbowRoll")
    times.append([0.24, 0.96, 1.96, 2.96, 3.96, 4.96, 5.96, 6.96, 7.96, 8.96, 9.96, 10.96, 13.36, 16.76, 17.96, 19.16, 20.36])
    keys.append([[-0.489305, [3, -0.0933333, 0], [3, 0.24, 0]], [-0.569072, [3, -0.24, 0], [3, 0.333333, 0]], [-0.389594, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.389594, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.389594, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.470897, [3, -0.333333, 0.0138056], [3, 0.333333, -0.0138056]], [-0.484702, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.432547, [3, -0.333333, -0.0104824], [3, 0.333333, 0.0104824]], [-0.421808, [3, -0.333333, -0.010739], [3, 0.333333, 0.010739]], [-0.0674542, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.078192, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.059784, [3, -0.333333, -0.00616606], [3, 0.8, 0.0147986]], [-0.0152981, [3, -0.8, 0], [3, 1.13333, 0]], [-0.0367741, [3, -1.13333, 0], [3, 0.4, 0]], [-0.00609397, [3, -0.4, 0], [3, 0.4, 0]], [-0.0183661, [3, -0.4, 0.00511335], [3, 0.4, -0.00511335]], [-0.0367741, [3, -0.4, 0], [3, 0, 0]]])

    names.append("LElbowYaw")
    times.append([0.24, 0.96, 1.96, 2.96, 3.96, 4.96, 5.96, 6.96, 7.96, 8.96, 9.96, 10.96, 13.36, 16.76, 17.96, 19.16, 20.36])
    keys.append([[-1.15208, [3, -0.0933333, 0], [3, 0.24, 0]], [-1.18429, [3, -0.24, 0.00599338], [3, 0.333333, -0.00832413]], [-1.19503, [3, -0.333333, 0], [3, 0.333333, 0]], [-1.19503, [3, -0.333333, 0], [3, 0.333333, 0]], [-1.19503, [3, -0.333333, 0], [3, 0.333333, 0]], [-1.19656, [3, -0.333333, 0.00153238], [3, 0.333333, -0.00153238]], [-1.21497, [3, -0.333333, 0], [3, 0.333333, 0]], [-1.20116, [3, -0.333333, 0], [3, 0.333333, 0]], [-1.20116, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.648924, [3, -0.333333, -0.256178], [3, 0.333333, 0.256178]], [0.335904, [3, -0.333333, 0], [3, 0.333333, 0]], [-1.25486, [3, -0.333333, 0.00830845], [3, 0.8, -0.0199403]], [-1.2748, [3, -0.8, 0.00423163], [3, 1.13333, -0.00599482]], [-1.28553, [3, -1.13333, 0], [3, 0.4, 0]], [-1.26406, [3, -0.4, 0], [3, 0.4, 0]], [-1.26406, [3, -0.4, 0], [3, 0.4, 0]], [-1.28553, [3, -0.4, 0], [3, 0, 0]]])

    names.append("LHand")
    times.append([0.24, 0.96, 1.96, 2.96, 3.96, 4.96, 5.96, 6.96, 7.96, 8.96, 9.96, 10.96, 13.36, 16.76, 17.96, 19.16, 20.36])
    keys.append([[0.2752, [3, -0.0933333, 0], [3, 0.24, 0]], [0.3172, [3, -0.24, 0], [3, 0.333333, 0]], [0.292, [3, -0.333333, 0], [3, 0.333333, 0]], [0.292, [3, -0.333333, 0], [3, 0.333333, 0]], [0.292, [3, -0.333333, 0], [3, 0.333333, 0]], [0.292, [3, -0.333333, 0], [3, 0.333333, 0]], [0.292, [3, -0.333333, 0], [3, 0.333333, 0]], [0.292, [3, -0.333333, 0], [3, 0.333333, 0]], [0.292, [3, -0.333333, 0], [3, 0.333333, 0]], [0.292, [3, -0.333333, 0], [3, 0.333333, 0]], [0.292, [3, -0.333333, 0], [3, 0.333333, 0]], [0.0372, [3, -0.333333, 0], [3, 0.8, 0]], [0.044, [3, -0.8, 0], [3, 1.13333, 0]], [0.044, [3, -1.13333, 0], [3, 0.4, 0]], [0.0552, [3, -0.4, 0], [3, 0.4, 0]], [0.0552, [3, -0.4, 0], [3, 0.4, 0]], [0.044, [3, -0.4, 0], [3, 0, 0]]])

    names.append("LHipPitch")
    times.append([0.24, 0.96, 1.96, 2.96, 3.96, 4.96, 5.96, 6.96, 7.96, 8.96, 9.96, 10.96, 13.36, 16.76, 17.96, 19.16, 20.36])
    keys.append([[0.0951499, [3, -0.0933333, 0], [3, 0.24, 0]], [0.181053, [3, -0.24, 0], [3, 0.333333, 0]], [-0.061318, [3, -0.333333, 0.0485766], [3, 0.333333, -0.0485766]], [-0.110406, [3, -0.333333, 0.0490879], [3, 0.333333, -0.0490879]], [-0.797638, [3, -0.333333, 0.216294], [3, 0.333333, -0.216294]], [-1.40817, [3, -0.333333, 0], [3, 0.333333, 0]], [-1.38056, [3, -0.333333, -0.0102268], [3, 0.333333, 0.0102268]], [-1.34681, [3, -0.333333, -0.0337494], [3, 0.333333, 0.0337494]], [-1.04921, [3, -0.333333, -0.147264], [3, 0.333333, 0.147264]], [-0.463226, [3, -0.333333, -0.215271], [3, 0.333333, 0.215271]], [0.242414, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.41107, [3, -0.333333, 0.0918895], [3, 0.8, -0.220535]], [-0.694859, [3, -0.8, 0.0499342], [3, 1.13333, -0.0707401]], [-0.773094, [3, -1.13333, 0], [3, 0.4, 0]], [0.438765, [3, -0.4, -0.0322153], [3, 0.4, 0.0322153]], [0.470981, [3, -0.4, 0], [3, 0.4, 0]], [-0.773094, [3, -0.4, 0], [3, 0, 0]]])

    names.append("LHipRoll")
    times.append([0.24, 0.96, 1.96, 2.96, 3.96, 4.96, 5.96, 6.96, 7.96, 8.96, 9.96, 10.96, 13.36, 16.76, 17.96, 19.16, 20.36])
    keys.append([[0.099752, [3, -0.0933333, 0], [3, 0.24, 0]], [0.090548, [3, -0.24, 0], [3, 0.333333, 0]], [0.254685, [3, -0.333333, -0.0386058], [3, 0.333333, 0.0386058]], [0.322183, [3, -0.333333, -0.0674971], [3, 0.333333, 0.0674971]], [0.753235, [3, -0.333333, -0.010739], [3, 0.333333, 0.010739]], [0.763974, [3, -0.333333, 0], [3, 0.333333, 0]], [0.618244, [3, -0.333333, 0.12093], [3, 0.333333, -0.12093]], [0.038392, [3, -0.333333, 0], [3, 0.333333, 0]], [0.0598679, [3, -0.333333, -0.00715864], [3, 0.333333, 0.00715864]], [0.0813439, [3, -0.333333, 0], [3, 0.333333, 0]], [0.0245859, [3, -0.333333, 0], [3, 0.333333, 0]], [0.0567998, [3, -0.333333, 0], [3, 0.8, 0]], [0.039926, [3, -0.8, 0], [3, 1.13333, 0]], [0.0890141, [3, -1.13333, 0], [3, 0.4, 0]], [0.0844118, [3, -0.4, 0], [3, 0.4, 0]], [0.717953, [3, -0.4, 0], [3, 0.4, 0]], [0.0890141, [3, -0.4, 0], [3, 0, 0]]])

    names.append("LHipYawPitch")
    times.append([0.24, 0.96, 1.96, 2.96, 3.96, 4.96, 5.96, 6.96, 7.96, 8.96, 9.96, 10.96, 13.36, 16.76, 17.96, 19.16, 20.36])
    keys.append([[-0.177901, [3, -0.0933333, 0], [3, 0.24, 0]], [-0.156426, [3, -0.24, 0], [3, 0.333333, 0]], [-0.54146, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.54146, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.613558, [3, -0.333333, 0.0544569], [3, 0.333333, -0.0544569]], [-0.868202, [3, -0.333333, 0.0322135], [3, 0.333333, -0.0322135]], [-0.900415, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.552198, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.562937, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.107338, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.20398, [3, -0.333333, 0], [3, 0.333333, 0]], [0.0276539, [3, -0.333333, 0], [3, 0.8, 0]], [-0.170232, [3, -0.8, 0], [3, 1.13333, 0]], [0.0153821, [3, -1.13333, -0.058581], [3, 0.4, 0.0206757]], [0.067538, [3, -0.4, -0.0127833], [3, 0.4, 0.0127833]], [0.092082, [3, -0.4, 0], [3, 0.4, 0]], [0.0153821, [3, -0.4, 0], [3, 0, 0]]])

    names.append("LKneePitch")
    times.append([0.24, 0.96, 1.96, 2.96, 3.96, 4.96, 5.96, 6.96, 7.96, 8.96, 9.96, 10.96, 13.36, 16.76, 17.96, 19.16, 20.36])
    keys.append([[-0.0153821, [3, -0.0933333, 0], [3, 0.24, 0]], [-0.093616, [3, -0.24, 0], [3, 0.333333, 0]], [-0.016916, [3, -0.333333, -0.018408], [3, 0.333333, 0.018408]], [0.0168321, [3, -0.333333, -0.0294017], [3, 0.333333, 0.0294017]], [0.159494, [3, -0.333333, -0.0260782], [3, 0.333333, 0.0260782]], [0.185572, [3, -0.333333, -0.00792574], [3, 0.333333, 0.00792574]], [0.207048, [3, -0.333333, 0], [3, 0.333333, 0]], [0.171766, [3, -0.333333, 0], [3, 0.333333, 0]], [0.182504, [3, -0.333333, 0], [3, 0.333333, 0]], [0.0858622, [3, -0.333333, 0], [3, 0.333333, 0]], [0.15029, [3, -0.333333, -0.0644278], [3, 0.333333, 0.0644278]], [0.526121, [3, -0.333333, -0.184682], [3, 0.8, 0.443236]], [2.03404, [3, -0.8, 0], [3, 1.13333, 0]], [1.21795, [3, -1.13333, 0.521182], [3, 0.4, -0.183947]], [-0.0813439, [3, -0.4, 0], [3, 0.4, 0]], [-0.047596, [3, -0.4, -0.0337479], [3, 0.4, 0.0337479]], [1.21795, [3, -0.4, 0], [3, 0, 0]]])

    names.append("LShoulderPitch")
    times.append([0.24, 0.96, 1.96, 2.96, 3.96, 4.96, 5.96, 6.96, 7.96, 8.96, 9.96, 10.96, 13.36, 16.76, 17.96, 19.16, 20.36])
    keys.append([[1.42504, [3, -0.0933333, 0], [3, 0.24, 0]], [1.50788, [3, -0.24, 0], [3, 0.333333, 0]], [0.461692, [3, -0.333333, 0], [3, 0.333333, 0]], [0.461692, [3, -0.333333, 0], [3, 0.333333, 0]], [0.450955, [3, -0.333333, 0.0107373], [3, 0.333333, -0.0107373]], [0.357381, [3, -0.333333, 0.0299132], [3, 0.333333, -0.0299132]], [0.271475, [3, -0.333333, 0], [3, 0.333333, 0]], [0.294486, [3, -0.333333, -0.00792583], [3, 0.333333, 0.00792583]], [0.31903, [3, -0.333333, 0], [3, 0.333333, 0]], [0.294486, [3, -0.333333, 0.00588031], [3, 0.333333, -0.00588031]], [0.283749, [3, -0.333333, 0.0107373], [3, 0.333333, -0.0107373]], [0.194775, [3, -0.333333, 0], [3, 0.8, 0]], [0.325165, [3, -0.8, 0], [3, 1.13333, 0]], [0.289883, [3, -1.13333, 0.012094], [3, 0.4, -0.00426847]], [0.276078, [3, -0.4, 0], [3, 0.4, 0]], [0.276078, [3, -0.4, 0], [3, 0.4, 0]], [0.289883, [3, -0.4, 0], [3, 0, 0]]])

    names.append("LShoulderRoll")
    times.append([0.24, 0.96, 1.96, 2.96, 3.96, 4.96, 5.96, 6.96, 7.96, 8.96, 9.96, 10.96, 13.36, 16.76, 17.96, 19.16, 20.36])
    keys.append([[0.18864, [3, -0.0933333, 0], [3, 0.24, 0]], [0.18864, [3, -0.24, 0], [3, 0.333333, 0]], [0.148756, [3, -0.333333, 0.0086927], [3, 0.333333, -0.0086927]], [0.136484, [3, -0.333333, 0], [3, 0.333333, 0]], [0.136484, [3, -0.333333, 0], [3, 0.333333, 0]], [0.095066, [3, -0.333333, 0], [3, 0.333333, 0]], [0.18864, [3, -0.333333, 0], [3, 0.333333, 0]], [0.15029, [3, -0.333333, 0.00818138], [3, 0.333333, -0.00818138]], [0.139552, [3, -0.333333, 0], [3, 0.333333, 0]], [0.193243, [3, -0.333333, 0], [3, 0.333333, 0]], [0.156426, [3, -0.333333, 0.0368168], [3, 0.333333, -0.0368168]], [-0.124296, [3, -0.333333, 0.00319584], [3, 0.8, -0.00767002]], [-0.131966, [3, -0.8, 0.00402013], [3, 1.13333, -0.00569518]], [-0.153442, [3, -1.13333, 0], [3, 0.4, 0]], [-0.00771189, [3, -0.4, 0], [3, 0.4, 0]], [-0.019984, [3, -0.4, 0.0122721], [3, 0.4, -0.0122721]], [-0.153442, [3, -0.4, 0], [3, 0, 0]]])

    names.append("LWristYaw")
    times.append([0.24, 0.96, 1.96, 2.96, 3.96, 4.96, 5.96, 6.96, 7.96, 8.96, 9.96, 10.96, 13.36, 16.76, 17.96, 19.16, 20.36])
    keys.append([[0.144154, [3, -0.0933333, 0], [3, 0.24, 0]], [0.130348, [3, -0.24, 0], [3, 0.333333, 0]], [0.130348, [3, -0.333333, 0], [3, 0.333333, 0]], [0.11961, [3, -0.333333, 0], [3, 0.333333, 0]], [0.11961, [3, -0.333333, 0], [3, 0.333333, 0]], [0.105804, [3, -0.333333, 0.00562464], [3, 0.333333, -0.00562464]], [0.0858622, [3, -0.333333, 0], [3, 0.333333, 0]], [0.0858622, [3, -0.333333, 0], [3, 0.333333, 0]], [0.0858622, [3, -0.333333, 0], [3, 0.333333, 0]], [0.592082, [3, -0.333333, 0], [3, 0.333333, 0]], [-1.58927, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.638187, [3, -0.333333, 0], [3, 0.8, 0]], [-0.648924, [3, -0.8, 0.002962], [3, 1.13333, -0.00419617]], [-0.659661, [3, -1.13333, 0], [3, 0.4, 0]], [-0.589097, [3, -0.4, -0.0107373], [3, 0.4, 0.0107373]], [-0.57836, [3, -0.4, 0], [3, 0.4, 0]], [-0.659661, [3, -0.4, 0], [3, 0, 0]]])

    names.append("RAnklePitch")
    times.append([0.24, 0.96, 1.96, 2.96, 3.96, 4.96, 5.96, 6.96, 7.96, 8.96, 9.96, 10.96, 13.36, 16.76, 17.96, 19.16, 20.36])
    keys.append([[0.0414601, [3, -0.0933333, 0], [3, 0.24, 0]], [0.0782759, [3, -0.24, 0], [3, 0.333333, 0]], [-0.032172, [3, -0.333333, 0.086671], [3, 0.333333, -0.086671]], [-0.44175, [3, -0.333333, 0.0720978], [3, 0.333333, -0.0720978]], [-0.513848, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.504645, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.704064, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.423342, [3, -0.333333, -0.0741433], [3, 0.333333, 0.0741433]], [-0.259204, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.711735, [3, -0.333333, 0.149309], [3, 0.333333, -0.149309]], [-1.15506, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.954107, [3, -0.333333, 0], [3, 0.8, 0]], [-1.17654, [3, -0.8, 0], [3, 1.13333, 0]], [0.943452, [3, -1.13333, 0], [3, 0.4, 0]], [0.925044, [3, -0.4, 0], [3, 0.4, 0]], [0.925044, [3, -0.4, 0], [3, 0.4, 0]], [0.943452, [3, -0.4, 0], [3, 0, 0]]])

    names.append("RAnkleRoll")
    times.append([0.24, 0.96, 1.96, 2.96, 3.96, 4.96, 5.96, 6.96, 7.96, 8.96, 9.96, 10.96, 13.36, 16.76, 17.96, 19.16, 20.36])
    keys.append([[0.101286, [3, -0.0933333, 0], [3, 0.24, 0]], [0.113558, [3, -0.24, 0], [3, 0.333333, 0]], [0.0123138, [3, -0.333333, 0.0311914], [3, 0.333333, -0.0311914]], [-0.0735901, [3, -0.333333, 0], [3, 0.333333, 0]], [0.230143, [3, -0.333333, 0], [3, 0.333333, 0]], [0.230143, [3, -0.333333, 0], [3, 0.333333, 0]], [0.362067, [3, -0.333333, 0], [3, 0.333333, 0]], [0.297638, [3, -0.333333, 0], [3, 0.333333, 0]], [0.319114, [3, -0.333333, 0], [3, 0.333333, 0]], [0.066004, [3, -0.333333, 0.0107381], [3, 0.333333, -0.0107381]], [0.0552659, [3, -0.333333, 0.0107381], [3, 0.333333, -0.0107381]], [-0.0260359, [3, -0.333333, 0], [3, 0.8, 0]], [-0.0199001, [3, -0.8, -0.00444329], [3, 1.13333, 0.00629466]], [0.00617791, [3, -1.13333, 0], [3, 0.4, 0]], [0.00157595, [3, -0.4, 0], [3, 0.4, 0]], [0.00157595, [3, -0.4, 0], [3, 0.4, 0]], [0.00617791, [3, -0.4, 0], [3, 0, 0]]])

    names.append("RElbowRoll")
    times.append([0.24, 0.96, 1.96, 2.96, 3.96, 4.96, 5.96, 6.96, 7.96, 8.96, 9.96, 10.96, 13.36, 16.76, 17.96, 19.16, 20.36])
    keys.append([[0.48632, [3, -0.0933333, 0], [3, 0.24, 0]], [0.500126, [3, -0.24, 0], [3, 0.333333, 0]], [0.415757, [3, -0.333333, 0.010739], [3, 0.333333, -0.010739]], [0.405018, [3, -0.333333, 0], [3, 0.333333, 0]], [0.405018, [3, -0.333333, 0], [3, 0.333333, 0]], [0.429562, [3, -0.333333, 0], [3, 0.333333, 0]], [0.397349, [3, -0.333333, 0], [3, 0.333333, 0]], [0.40962, [3, -0.333333, 0], [3, 0.333333, 0]], [0.40962, [3, -0.333333, 0], [3, 0.333333, 0]], [0.0261199, [3, -0.333333, 0], [3, 0.333333, 0]], [0.0261199, [3, -0.333333, 0], [3, 0.333333, 0]], [0.130432, [3, -0.333333, 0], [3, 0.8, 0]], [0.0767419, [3, -0.8, 0], [3, 1.13333, 0]], [0.090548, [3, -1.13333, 0], [3, 0.4, 0]], [0.0690719, [3, -0.4, 0], [3, 0.4, 0]], [0.0690719, [3, -0.4, 0], [3, 0.4, 0]], [0.090548, [3, -0.4, 0], [3, 0, 0]]])

    names.append("RElbowYaw")
    times.append([0.24, 0.96, 1.96, 2.96, 3.96, 4.96, 5.96, 6.96, 7.96, 8.96, 9.96, 10.96, 13.36, 16.76, 17.96, 19.16, 20.36])
    keys.append([[1.15659, [3, -0.0933333, 0], [3, 0.24, 0]], [1.28698, [3, -0.24, -0.00883544], [3, 0.333333, 0.0122714]], [1.29926, [3, -0.333333, 0], [3, 0.333333, 0]], [1.29926, [3, -0.333333, 0], [3, 0.333333, 0]], [1.29926, [3, -0.333333, 0], [3, 0.333333, 0]], [1.24403, [3, -0.333333, 0], [3, 0.333333, 0]], [1.2471, [3, -0.333333, -0.00306823], [3, 0.333333, 0.00306823]], [1.27011, [3, -0.333333, -0.00562462], [3, 0.333333, 0.00562462]], [1.28085, [3, -0.333333, 0], [3, 0.333333, 0]], [0.328234, [3, -0.333333, 0.192773], [3, 0.333333, -0.192773]], [0.124212, [3, -0.333333, 0], [3, 0.333333, 0]], [1.29619, [3, -0.333333, -0.00319613], [3, 0.8, 0.0076707]], [1.30386, [3, -0.8, 0], [3, 1.13333, 0]], [1.29312, [3, -1.13333, 0.00718114], [3, 0.4, -0.00253452]], [1.27471, [3, -0.4, 0], [3, 0.4, 0]], [1.27471, [3, -0.4, 0], [3, 0.4, 0]], [1.29312, [3, -0.4, 0], [3, 0, 0]]])

    names.append("RHand")
    times.append([0.24, 0.96, 1.96, 2.96, 3.96, 4.96, 5.96, 6.96, 7.96, 8.96, 9.96, 10.96, 13.36, 16.76, 17.96, 19.16, 20.36])
    keys.append([[0.2752, [3, -0.0933333, 0], [3, 0.24, 0]], [0.3548, [3, -0.24, 0], [3, 0.333333, 0]], [0.328, [3, -0.333333, 0], [3, 0.333333, 0]], [0.328, [3, -0.333333, 0], [3, 0.333333, 0]], [0.328, [3, -0.333333, 0], [3, 0.333333, 0]], [0.328, [3, -0.333333, 0], [3, 0.333333, 0]], [0.328, [3, -0.333333, 0], [3, 0.333333, 0]], [0.328, [3, -0.333333, 0], [3, 0.333333, 0]], [0.328, [3, -0.333333, 0], [3, 0.333333, 0]], [0.328, [3, -0.333333, 0], [3, 0.333333, 0]], [0.3068, [3, -0.333333, 0.0212], [3, 0.333333, -0.0212]], [0.0332, [3, -0.333333, 0], [3, 0.8, 0]], [0.0424, [3, -0.8, 0], [3, 1.13333, 0]], [0.0424, [3, -1.13333, 0], [3, 0.4, 0]], [0.0508, [3, -0.4, 0], [3, 0.4, 0]], [0.0508, [3, -0.4, 0], [3, 0.4, 0]], [0.0424, [3, -0.4, 0], [3, 0, 0]]])

    names.append("RHipPitch")
    times.append([0.24, 0.96, 1.96, 2.96, 3.96, 4.96, 5.96, 6.96, 7.96, 8.96, 9.96, 10.96, 13.36, 16.76, 17.96, 19.16, 20.36])
    keys.append([[0.091998, [3, -0.0933333, 0], [3, 0.24, 0]], [0.156426, [3, -0.24, 0], [3, 0.333333, 0]], [-0.214803, [3, -0.333333, 0.127066], [3, 0.333333, -0.127066]], [-0.605971, [3, -0.333333, 0.0230104], [3, 0.333333, -0.0230104]], [-0.628982, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.610574, [3, -0.333333, 0], [3, 0.333333, 0]], [-1.41899, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.742498, [3, -0.333333, -0.139594], [3, 0.333333, 0.139594]], [-0.581429, [3, -0.333333, -0.0562467], [3, 0.333333, 0.0562467]], [-0.405018, [3, -0.333333, -0.141384], [3, 0.333333, 0.141384]], [0.266875, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.0966839, [3, -0.333333, 0.089333], [3, 0.8, -0.214399]], [-0.644321, [3, -0.8, 0.0010829], [3, 1.13333, -0.00153411]], [-0.645856, [3, -1.13333, 0], [3, 0.4, 0]], [-0.630516, [3, -0.4, 0], [3, 0.4, 0]], [-0.630516, [3, -0.4, 0], [3, 0.4, 0]], [-0.645856, [3, -0.4, 0], [3, 0, 0]]])

    names.append("RHipRoll")
    times.append([0.24, 0.96, 1.96, 2.96, 3.96, 4.96, 5.96, 6.96, 7.96, 8.96, 9.96, 10.96, 13.36, 16.76, 17.96, 19.16, 20.36])
    keys.append([[-0.091998, [3, -0.0933333, 0], [3, 0.24, 0]], [-0.115008, [3, -0.24, 0], [3, 0.333333, 0]], [0.019984, [3, -0.333333, -0.010738], [3, 0.333333, 0.010738]], [0.030722, [3, -0.333333, -0.00715867], [3, 0.333333, 0.00715867]], [0.062936, [3, -0.333333, 0], [3, 0.333333, 0]], [0.0552659, [3, -0.333333, 0.00639169], [3, 0.333333, -0.00639169]], [0.0245859, [3, -0.333333, 0], [3, 0.333333, 0]], [0.092082, [3, -0.333333, 0], [3, 0.333333, 0]], [0.0598679, [3, -0.333333, 0.024544], [3, 0.333333, -0.024544]], [-0.0551819, [3, -0.333333, 0], [3, 0.333333, 0]], [0.016916, [3, -0.333333, -0.0309356], [3, 0.333333, 0.0309356]], [0.130432, [3, -0.333333, -0.00447423], [3, 0.8, 0.0107381]], [0.14117, [3, -0.8, 0], [3, 1.13333, 0]], [0.14117, [3, -1.13333, 0], [3, 0.4, 0]], [0.154976, [3, -0.4, 0], [3, 0.4, 0]], [0.154976, [3, -0.4, 0], [3, 0.4, 0]], [0.14117, [3, -0.4, 0], [3, 0, 0]]])

    names.append("RHipYawPitch")
    times.append([0.24, 0.96, 1.96, 2.96, 3.96, 4.96, 5.96, 6.96, 7.96, 8.96, 9.96, 10.96, 13.36, 16.76, 17.96, 19.16, 20.36])
    keys.append([[-0.177901, [3, -0.0933333, 0], [3, 0.24, 0]], [-0.156426, [3, -0.24, 0], [3, 0.333333, 0]], [-0.54146, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.54146, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.613558, [3, -0.333333, 0.0544569], [3, 0.333333, -0.0544569]], [-0.868202, [3, -0.333333, 0.0322135], [3, 0.333333, -0.0322135]], [-0.900415, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.552198, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.562937, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.107338, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.20398, [3, -0.333333, 0], [3, 0.333333, 0]], [0.0276539, [3, -0.333333, 0], [3, 0.8, 0]], [-0.170232, [3, -0.8, 0], [3, 1.13333, 0]], [0.0153821, [3, -1.13333, -0.058581], [3, 0.4, 0.0206757]], [0.067538, [3, -0.4, -0.0127833], [3, 0.4, 0.0127833]], [0.092082, [3, -0.4, 0], [3, 0.4, 0]], [0.0153821, [3, -0.4, 0], [3, 0, 0]]])

    names.append("RKneePitch")
    times.append([0.24, 0.96, 1.96, 2.96, 3.96, 4.96, 5.96, 6.96, 7.96, 8.96, 9.96, 10.96, 13.36, 16.76, 17.96, 19.16, 20.36])
    keys.append([[-0.0183661, [3, -0.0933333, 0], [3, 0.24, 0]], [-0.095066, [3, -0.24, 0], [3, 0.333333, 0]], [0.429562, [3, -0.333333, -0.204533], [3, 0.333333, 0.204533]], [1.13213, [3, -0.333333, -0.105845], [3, 0.333333, 0.105845]], [1.23798, [3, -0.333333, -0.0217317], [3, 0.333333, 0.0217317]], [1.26252, [3, -0.333333, -0.0245446], [3, 0.333333, 0.0245446]], [1.86693, [3, -0.333333, 0], [3, 0.333333, 0]], [0.119694, [3, -0.333333, 0.21476], [3, 0.333333, -0.21476]], [-0.095066, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.0720561, [3, -0.333333, -0.0230099], [3, 0.333333, 0.0230099]], [0.116626, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.0275701, [3, -0.333333, 0], [3, 0.8, 0]], [2.00037, [3, -0.8, 0], [3, 1.13333, 0]], [1.06617, [3, -1.13333, 0], [3, 0.4, 0]], [1.07691, [3, -0.4, 0], [3, 0.4, 0]], [1.06617, [3, -0.4, 0], [3, 0.4, 0]], [1.06617, [3, -0.4, 0], [3, 0, 0]]])

    names.append("RShoulderPitch")
    times.append([0.24, 0.96, 1.96, 2.96, 3.96, 4.96, 5.96, 6.96, 7.96, 8.96, 9.96, 10.96, 13.36, 16.76, 17.96, 19.16, 20.36])
    keys.append([[1.42513, [3, -0.0933333, 0], [3, 0.24, 0]], [1.48802, [3, -0.24, 0], [3, 0.333333, 0]], [0.395814, [3, -0.333333, 0], [3, 0.333333, 0]], [0.395814, [3, -0.333333, 0], [3, 0.333333, 0]], [0.395814, [3, -0.333333, 0], [3, 0.333333, 0]], [0.352862, [3, -0.333333, 0.0109938], [3, 0.333333, -0.0109938]], [0.329852, [3, -0.333333, 0.00562462], [3, 0.333333, -0.00562462]], [0.319114, [3, -0.333333, 0], [3, 0.333333, 0]], [0.329852, [3, -0.333333, -0.00357938], [3, 0.333333, 0.00357938]], [0.340591, [3, -0.333333, -0.010739], [3, 0.333333, 0.010739]], [0.46331, [3, -0.333333, 0], [3, 0.333333, 0]], [0.288435, [3, -0.333333, 0], [3, 0.8, 0]], [0.345191, [3, -0.8, 0], [3, 1.13333, 0]], [0.320648, [3, -1.13333, 0], [3, 0.4, 0]], [0.334454, [3, -0.4, -0.00434616], [3, 0.4, 0.00434616]], [0.346725, [3, -0.4, 0], [3, 0.4, 0]], [0.320648, [3, -0.4, 0], [3, 0, 0]]])

    names.append("RShoulderRoll")
    times.append([0.24, 0.96, 1.96, 2.96, 3.96, 4.96, 5.96, 6.96, 7.96, 8.96, 9.96, 10.96, 13.36, 16.76, 17.96, 19.16, 20.36])
    keys.append([[-0.181053, [3, -0.0933333, 0], [3, 0.24, 0]], [-0.161112, [3, -0.24, -0.0136989], [3, 0.333333, 0.0190263]], [-0.0828778, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.0828778, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.0828778, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.062936, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.0706061, [3, -0.333333, 0.00767003], [3, 0.333333, -0.00767003]], [-0.165714, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.165714, [3, -0.333333, 0], [3, 0.333333, 0]], [-0.099752, [3, -0.333333, -0.0163627], [3, 0.333333, 0.0163627]], [-0.067538, [3, -0.333333, -0.013039], [3, 0.333333, 0.013039]], [-0.021518, [3, -0.333333, -0.00947471], [3, 0.8, 0.0227393]], [0.0291041, [3, -0.8, 0], [3, 1.13333, 0]], [0.00762796, [3, -1.13333, 0.0214761], [3, 0.4, -0.0075798]], [-0.127364, [3, -0.4, 0], [3, 0.4, 0]], [-0.115092, [3, -0.4, -0.0122719], [3, 0.4, 0.0122719]], [0.00762796, [3, -0.4, 0], [3, 0, 0]]])

    names.append("RWristYaw")
    times.append([0.24, 0.96, 1.96, 2.96, 3.96, 4.96, 5.96, 6.96, 7.96, 8.96, 9.96, 10.96, 13.36, 16.76, 17.96, 19.16, 20.36])
    keys.append([[0.0413762, [3, -0.0933333, 0], [3, 0.24, 0]], [0.113474, [3, -0.24, -0.0387425], [3, 0.333333, 0.053809]], [0.31903, [3, -0.333333, 0], [3, 0.333333, 0]], [0.31903, [3, -0.333333, 0], [3, 0.333333, 0]], [0.31903, [3, -0.333333, 0], [3, 0.333333, 0]], [0.36505, [3, -0.333333, 0], [3, 0.333333, 0]], [0.348176, [3, -0.333333, 0.0104822], [3, 0.333333, -0.0104822]], [0.302157, [3, -0.333333, 0], [3, 0.333333, 0]], [0.302157, [3, -0.333333, 0], [3, 0.333333, 0]], [0.454021, [3, -0.333333, -0.151865], [3, 0.333333, 0.151865]], [1.44959, [3, -0.333333, 0], [3, 0.333333, 0]], [0.328234, [3, -0.333333, 0.00383463], [3, 0.8, -0.00920312]], [0.31903, [3, -0.8, 0], [3, 1.13333, 0]], [0.337438, [3, -1.13333, 0], [3, 0.4, 0]], [0.228525, [3, -0.4, 0], [3, 0.4, 0]], [0.243864, [3, -0.4, -0.0153397], [3, 0.4, 0.0153397]], [0.337438, [3, -0.4, 0], [3, 0, 0]]])

    return names, times, keys