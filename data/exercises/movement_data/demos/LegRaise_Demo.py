# Init, Init, Init, Up, Down, Exit, Exit, Exit, Exit
def __call__():
    names = list()
    times = list()
    keys = list()

    names.append("HeadPitch")
    times.append([0.56, 1.16, 1.76, 2.96, 4.16, 4.76, 5.36, 5.96, 6.56])
    keys.append([[0.1733, [3, -0.2, 0], [3, 0.2, 0]], [0.194775, [3, -0.2, 0], [3, 0.2, 0]], [0.1733, [3, -0.2, 0], [3, 0.4, 0]], [0.239262, [3, -0.4, 0], [3, 0.4, 0]], [0.239262, [3, -0.4, 0], [3, 0.2, 0]], [0.1733, [3, -0.2, 0], [3, 0.2, 0]], [0.1733, [3, -0.2, 0], [3, 0.2, 0]], [0.1733, [3, -0.2, 0], [3, 0.2, 0]], [0.194775, [3, -0.2, 0], [3, 0, 0]]])

    names.append("HeadYaw")
    times.append([0.56, 1.16, 1.76, 2.96, 4.16, 4.76, 5.36, 5.96, 6.56])
    keys.append([[-0.0153821, [3, -0.2, 0], [3, 0.2, 0]], [-0.0138481, [3, -0.2, 0], [3, 0.2, 0]], [-0.0153821, [3, -0.2, 0], [3, 0.4, 0]], [0.0398422, [3, -0.4, 0], [3, 0.4, 0]], [0.0398422, [3, -0.4, 0], [3, 0.2, 0]], [-0.0153821, [3, -0.2, 0], [3, 0.2, 0]], [-0.0153821, [3, -0.2, 0], [3, 0.2, 0]], [-0.0153821, [3, -0.2, 0], [3, 0.2, 0]], [-0.0138481, [3, -0.2, 0], [3, 0, 0]]])

    names.append("LAnklePitch")
    times.append([0.56, 1.16, 1.76, 2.96, 4.16, 4.76, 5.36, 5.96, 6.56])
    keys.append([[0.863599, [3, -0.2, 0], [3, 0.2, 0]], [0.865134, [3, -0.2, 0], [3, 0.2, 0]], [0.863599, [3, -0.2, 0.00153418], [3, 0.4, -0.00306836]], [0.788433, [3, -0.4, 0], [3, 0.4, 0]], [0.788433, [3, -0.4, 0], [3, 0.2, 0]], [0.863599, [3, -0.2, 0], [3, 0.2, 0]], [0.863599, [3, -0.2, 0], [3, 0.2, 0]], [0.863599, [3, -0.2, 0], [3, 0.2, 0]], [0.854396, [3, -0.2, 0], [3, 0, 0]]])

    names.append("LAnkleRoll")
    times.append([0.56, 1.16, 1.76, 2.96, 4.16, 4.76, 5.36, 5.96, 6.56])
    keys.append([[-0.0352399, [3, -0.2, 0], [3, 0.2, 0]], [-0.030638, [3, -0.2, 0], [3, 0.2, 0]], [-0.0352399, [3, -0.2, 0], [3, 0.4, 0]], [-0.0260359, [3, -0.4, 0], [3, 0.4, 0]], [-0.0260359, [3, -0.4, 0], [3, 0.2, 0]], [-0.0352399, [3, -0.2, 0], [3, 0.2, 0]], [-0.0352399, [3, -0.2, 0], [3, 0.2, 0]], [-0.0352399, [3, -0.2, 0], [3, 0.2, 0]], [-0.030638, [3, -0.2, 0], [3, 0, 0]]])

    names.append("LElbowRoll")
    times.append([0.56, 1.16, 1.76, 2.96, 4.16, 4.76, 5.36, 5.96, 6.56])
    keys.append([[-1.4005, [3, -0.2, 0], [3, 0.2, 0]], [-0.720938, [3, -0.2, -0.178455], [3, 0.2, 0.178455]], [-0.329768, [3, -0.2, -0.00536863], [3, 0.4, 0.0107373]], [-0.31903, [3, -0.4, 0], [3, 0.4, 0]], [-0.31903, [3, -0.4, 0], [3, 0.2, 0]], [-0.329768, [3, -0.2, 0.0107373], [3, 0.2, -0.0107373]], [-0.727074, [3, -0.2, 0.178455], [3, 0.2, -0.178455]], [-1.4005, [3, -0.2, 0.113516], [3, 0.2, -0.113516]], [-1.51402, [3, -0.2, 0], [3, 0, 0]]])

    names.append("LElbowYaw")
    times.append([0.56, 1.16, 1.76, 2.96, 4.16, 4.76, 5.36, 5.96, 6.56])
    keys.append([[-0.977199, [3, -0.2, 0], [3, 0.2, 0]], [-1.57853, [3, -0.2, 0.105845], [3, 0.2, -0.105845]], [-1.68437, [3, -0.2, 0], [3, 0.4, 0]], [-1.68437, [3, -0.4, 0], [3, 0.4, 0]], [-1.68437, [3, -0.4, 0], [3, 0.2, 0]], [-1.68437, [3, -0.2, 0], [3, 0.2, 0]], [-1.60154, [3, -0.2, -0.0828351], [3, 0.2, 0.0828351]], [-0.977199, [3, -0.2, -0.0843692], [3, 0.2, 0.0843692]], [-0.89283, [3, -0.2, 0], [3, 0, 0]]])

    names.append("LHand")
    times.append([0.56, 1.16, 1.76, 2.96, 4.16, 4.76, 5.36, 5.96, 6.56])
    keys.append([[0.2892, [3, -0.2, 0], [3, 0.2, 0]], [0.2968, [3, -0.2, 0], [3, 0.2, 0]], [0.2892, [3, -0.2, 0], [3, 0.4, 0]], [0.2892, [3, -0.4, 0], [3, 0.4, 0]], [0.2892, [3, -0.4, 0], [3, 0.2, 0]], [0.2892, [3, -0.2, 0], [3, 0.2, 0]], [0.2892, [3, -0.2, 0], [3, 0.2, 0]], [0.2892, [3, -0.2, 0], [3, 0.2, 0]], [0.2968, [3, -0.2, 0], [3, 0, 0]]])

    names.append("LHipPitch")
    times.append([0.56, 1.16, 1.76, 2.96, 4.16, 4.76, 5.36, 5.96, 6.56])
    keys.append([[0.37127, [3, -0.2, 0], [3, 0.2, 0]], [0.37127, [3, -0.2, 0], [3, 0.2, 0]], [0.37127, [3, -0.2, 0], [3, 0.4, 0]], [-1.46953, [3, -0.4, 0], [3, 0.4, 0]], [0.0276539, [3, -0.4, -0.409067], [3, 0.2, 0.204533]], [0.37127, [3, -0.2, 0], [3, 0.2, 0]], [0.37127, [3, -0.2, 0], [3, 0.2, 0]], [0.37127, [3, -0.2, 0], [3, 0.2, 0]], [0.37127, [3, -0.2, 0], [3, 0, 0]]])

    names.append("LHipRoll")
    times.append([0.56, 1.16, 1.76, 2.96, 4.16, 4.76, 5.36, 5.96, 6.56])
    keys.append([[0.108956, [3, -0.2, 0], [3, 0.2, 0]], [0.105888, [3, -0.2, 0], [3, 0.2, 0]], [0.108956, [3, -0.2, 0], [3, 0.4, 0]], [-0.00455999, [3, -0.4, 0], [3, 0.4, 0]], [-0.00455999, [3, -0.4, 0], [3, 0.2, 0]], [0.108956, [3, -0.2, 0], [3, 0.2, 0]], [0.108956, [3, -0.2, 0], [3, 0.2, 0]], [0.108956, [3, -0.2, 0], [3, 0.2, 0]], [0.105888, [3, -0.2, 0], [3, 0, 0]]])

    names.append("LHipYawPitch")
    times.append([0.56, 1.16, 1.76, 2.96, 4.16, 4.76, 5.36, 5.96, 6.56])
    keys.append([[-0.509247, [3, -0.2, 0], [3, 0.2, 0]], [-0.507713, [3, -0.2, 0], [3, 0.2, 0]], [-0.509247, [3, -0.2, 0], [3, 0.4, 0]], [-0.095066, [3, -0.4, -0.0971535], [3, 0.4, 0.0971535]], [0.073674, [3, -0.4, 0], [3, 0.2, 0]], [-0.509247, [3, -0.2, 0], [3, 0.2, 0]], [-0.509247, [3, -0.2, 0], [3, 0.2, 0]], [-0.509247, [3, -0.2, 0], [3, 0.2, 0]], [-0.51845, [3, -0.2, 0], [3, 0, 0]]])

    names.append("LKneePitch")
    times.append([0.56, 1.16, 1.76, 2.96, 4.16, 4.76, 5.36, 5.96, 6.56])
    keys.append([[-0.090548, [3, -0.2, 0], [3, 0.2, 0]], [-0.0859461, [3, -0.2, 0], [3, 0.2, 0]], [-0.090548, [3, -0.2, 0.00153399], [3, 0.4, -0.00306798]], [-0.099752, [3, -0.4, 0], [3, 0.4, 0]], [-0.0782759, [3, -0.4, 0], [3, 0.2, 0]], [-0.090548, [3, -0.2, 0], [3, 0.2, 0]], [-0.090548, [3, -0.2, 0], [3, 0.2, 0]], [-0.090548, [3, -0.2, 0], [3, 0.2, 0]], [-0.0859461, [3, -0.2, 0], [3, 0, 0]]])

    names.append("LShoulderPitch")
    times.append([0.56, 1.16, 1.76, 2.96, 4.16, 4.76, 5.36, 5.96, 6.56])
    keys.append([[1.38669, [3, -0.2, 0], [3, 0.2, 0]], [1.40664, [3, -0.2, -0.0199421], [3, 0.2, 0.0199421]], [1.86991, [3, -0.2, -0.0084299], [3, 0.4, 0.0168598]], [1.88677, [3, -0.4, 0], [3, 0.4, 0]], [1.88677, [3, -0.4, 0], [3, 0.2, 0]], [1.86991, [3, -0.2, 0.0168598], [3, 0.2, -0.0168598]], [1.32687, [3, -0.2, 0], [3, 0.2, 0]], [1.38669, [3, -0.2, -0.0598263], [3, 0.2, 0.0598263]], [1.69656, [3, -0.2, 0], [3, 0, 0]]])

    names.append("LShoulderRoll")
    times.append([0.56, 1.16, 1.76, 2.96, 4.16, 4.76, 5.36, 5.96, 6.56])
    keys.append([[0.0352399, [3, -0.2, 0], [3, 0.2, 0]], [0.136484, [3, -0.2, 0], [3, 0.2, 0]], [0.0981341, [3, -0.2, 0], [3, 0.4, 0]], [0.0996681, [3, -0.4, 0], [3, 0.4, 0]], [0.0996681, [3, -0.4, 0], [3, 0.2, 0]], [0.0981341, [3, -0.2, 0], [3, 0.2, 0]], [0.130348, [3, -0.2, 0], [3, 0.2, 0]], [0.0352399, [3, -0.2, 0], [3, 0.2, 0]], [0.197844, [3, -0.2, 0], [3, 0, 0]]])

    names.append("LWristYaw")
    times.append([0.56, 1.16, 1.76, 2.96, 4.16, 4.76, 5.36, 5.96, 6.56])
    keys.append([[0.128814, [3, -0.2, 0], [3, 0.2, 0]], [0.262272, [3, -0.2, -0.0245446], [3, 0.2, 0.0245446]], [0.286817, [3, -0.2, 0], [3, 0.4, 0]], [0.286817, [3, -0.4, 0], [3, 0.4, 0]], [0.286817, [3, -0.4, 0], [3, 0.2, 0]], [0.286817, [3, -0.2, 0], [3, 0.2, 0]], [0.286817, [3, -0.2, 0], [3, 0.2, 0]], [0.128814, [3, -0.2, 0], [3, 0.2, 0]], [0.225456, [3, -0.2, 0], [3, 0, 0]]])

    names.append("RAnklePitch")
    times.append([0.56, 1.16, 1.76, 2.96, 4.16, 4.76, 5.36, 5.96, 6.56])
    keys.append([[0.65506, [3, -0.2, 0], [3, 0.2, 0]], [0.552281, [3, -0.2, 0.0199421], [3, 0.2, -0.0199421]], [0.532339, [3, -0.2, 0], [3, 0.4, 0]], [0.763974, [3, -0.4, 0], [3, 0.4, 0]], [0.727158, [3, -0.4, 0.0368159], [3, 0.2, -0.018408]], [0.532339, [3, -0.2, 0], [3, 0.2, 0]], [0.65506, [3, -0.2, 0], [3, 0.2, 0]], [0.65506, [3, -0.2, 0], [3, 0.2, 0]], [0.651992, [3, -0.2, 0], [3, 0, 0]]])

    names.append("RAnkleRoll")
    times.append([0.56, 1.16, 1.76, 2.96, 4.16, 4.76, 5.36, 5.96, 6.56])
    keys.append([[-0.282215, [3, -0.2, 0], [3, 0.2, 0]], [-0.355846, [3, -0.2, 0], [3, 0.2, 0]], [-0.282215, [3, -0.2, -0.0373274], [3, 0.4, 0.0746547]], [-0.0199001, [3, -0.4, 0], [3, 0.4, 0]], [-0.022968, [3, -0.4, 0.00306794], [3, 0.2, -0.00153397]], [-0.282215, [3, -0.2, 0], [3, 0.2, 0]], [-0.282215, [3, -0.2, 0], [3, 0.2, 0]], [-0.282215, [3, -0.2, 0], [3, 0.2, 0]], [-0.28068, [3, -0.2, 0], [3, 0, 0]]])

    names.append("RElbowRoll")
    times.append([0.56, 1.16, 1.76, 2.96, 4.16, 4.76, 5.36, 5.96, 6.56])
    keys.append([[1.48495, [3, -0.2, 0], [3, 0.2, 0]], [0.79312, [3, -0.2, 0.187404], [3, 0.2, -0.187404]], [0.360533, [3, -0.2, 0], [3, 0.4, 0]], [0.360533, [3, -0.4, 0], [3, 0.4, 0]], [0.360533, [3, -0.4, 0], [3, 0.2, 0]], [0.360533, [3, -0.2, 0], [3, 0.2, 0]], [0.803859, [3, -0.2, -0.187404], [3, 0.2, 0.187404]], [1.48495, [3, -0.2, -0.035282], [3, 0.2, 0.035282]], [1.52024, [3, -0.2, 0], [3, 0, 0]]])

    names.append("RElbowYaw")
    times.append([0.56, 1.16, 1.76, 2.96, 4.16, 4.76, 5.36, 5.96, 6.56])
    keys.append([[0.834454, [3, -0.2, 0], [3, 0.2, 0]], [1.19801, [3, -0.2, -0.0892276], [3, 0.2, 0.0892276]], [1.36982, [3, -0.2, 0], [3, 0.4, 0]], [1.36982, [3, -0.4, 0], [3, 0.4, 0]], [1.36982, [3, -0.4, 0], [3, 0.2, 0]], [1.36982, [3, -0.2, 0], [3, 0.2, 0]], [1.21335, [3, -0.2, 0.0892276], [3, 0.2, -0.0892276]], [0.834454, [3, -0.2, 0], [3, 0.2, 0]], [0.843657, [3, -0.2, 0], [3, 0, 0]]])

    names.append("RHand")
    times.append([0.56, 1.16, 1.76, 2.96, 4.16, 4.76, 5.36, 5.96, 6.56])
    keys.append([[0.35, [3, -0.2, 0], [3, 0.2, 0]], [0.3528, [3, -0.2, 0], [3, 0.2, 0]], [0.35, [3, -0.2, 0], [3, 0.4, 0]], [0.35, [3, -0.4, 0], [3, 0.4, 0]], [0.35, [3, -0.4, 0], [3, 0.2, 0]], [0.35, [3, -0.2, 0], [3, 0.2, 0]], [0.35, [3, -0.2, 0], [3, 0.2, 0]], [0.35, [3, -0.2, 0], [3, 0.2, 0]], [0.3528, [3, -0.2, 0], [3, 0, 0]]])

    names.append("RHipPitch")
    times.append([0.56, 1.16, 1.76, 2.96, 4.16, 4.76, 5.36, 5.96, 6.56])
    keys.append([[-0.406552, [3, -0.2, 0], [3, 0.2, 0]], [-0.0782759, [3, -0.2, -0.108147], [3, 0.2, 0.108147]], [0.24233, [3, -0.2, 0], [3, 0.4, 0]], [-1.54325, [3, -0.4, 0], [3, 0.4, 0]], [0.0275701, [3, -0.4, -0.396795], [3, 0.2, 0.198397]], [0.24233, [3, -0.2, 0], [3, 0.2, 0]], [-0.406552, [3, -0.2, 0], [3, 0.2, 0]], [-0.406552, [3, -0.2, 0], [3, 0.2, 0]], [-0.405018, [3, -0.2, 0], [3, 0, 0]]])

    names.append("RHipRoll")
    times.append([0.56, 1.16, 1.76, 2.96, 4.16, 4.76, 5.36, 5.96, 6.56])
    keys.append([[-0.0843279, [3, -0.2, 0], [3, 0.2, 0]], [-0.108872, [3, -0.2, 0], [3, 0.2, 0]], [-0.06592, [3, -0.2, 0], [3, 0.4, 0]], [-0.116542, [3, -0.4, 0], [3, 0.4, 0]], [-0.105804, [3, -0.4, -0.0107381], [3, 0.2, 0.00536907]], [-0.06592, [3, -0.2, 0], [3, 0.2, 0]], [-0.0843279, [3, -0.2, 0], [3, 0.2, 0]], [-0.0843279, [3, -0.2, 0], [3, 0.2, 0]], [-0.076658, [3, -0.2, 0], [3, 0, 0]]])

    names.append("RHipYawPitch")
    times.append([0.56, 1.16, 1.76, 2.96, 4.16, 4.76, 5.36, 5.96, 6.56])
    keys.append([[-0.509247, [3, -0.2, 0], [3, 0.2, 0]], [-0.507713, [3, -0.2, 0], [3, 0.2, 0]], [-0.509247, [3, -0.2, 0], [3, 0.4, 0]], [-0.095066, [3, -0.4, -0.0971535], [3, 0.4, 0.0971535]], [0.073674, [3, -0.4, 0], [3, 0.2, 0]], [-0.509247, [3, -0.2, 0], [3, 0.2, 0]], [-0.509247, [3, -0.2, 0], [3, 0.2, 0]], [-0.509247, [3, -0.2, 0], [3, 0.2, 0]], [-0.51845, [3, -0.2, 0], [3, 0, 0]]])

    names.append("RKneePitch")
    times.append([0.56, 1.16, 1.76, 2.96, 4.16, 4.76, 5.36, 5.96, 6.56])
    keys.append([[1.735, [3, -0.2, 0], [3, 0.2, 0]], [0.859083, [3, -0.2, 0.250809], [3, 0.2, -0.250809]], [0.230143, [3, -0.2, 0.106187], [3, 0.4, -0.212374]], [-0.0966001, [3, -0.4, 0], [3, 0.4, 0]], [-0.0352399, [3, -0.4, -0.0613602], [3, 0.2, 0.0306801]], [0.230143, [3, -0.2, -0.265383], [3, 0.2, 0.265383]], [1.735, [3, -0.2, 0], [3, 0.2, 0]], [1.735, [3, -0.2, 0], [3, 0.2, 0]], [1.74267, [3, -0.2, 0], [3, 0, 0]]])

    names.append("RShoulderPitch")
    times.append([0.56, 1.16, 1.76, 2.96, 4.16, 4.76, 5.36, 5.96, 6.56])
    keys.append([[1.31775, [3, -0.2, 0], [3, 0.2, 0]], [1.26559, [3, -0.2, 0], [3, 0.2, 0]], [1.88532, [3, -0.2, -0.00690271], [3, 0.4, 0.0138054]], [1.89913, [3, -0.4, 0], [3, 0.4, 0]], [1.89913, [3, -0.4, 0], [3, 0.2, 0]], [1.88532, [3, -0.2, 0.0138054], [3, 0.2, -0.0138054]], [1.19349, [3, -0.2, 0], [3, 0.2, 0]], [1.31775, [3, -0.2, -0.0859039], [3, 0.2, 0.0859039]], [1.70892, [3, -0.2, 0], [3, 0, 0]]])

    names.append("RShoulderRoll")
    times.append([0.56, 1.16, 1.76, 2.96, 4.16, 4.76, 5.36, 5.96, 6.56])
    keys.append([[-0.257754, [3, -0.2, 0], [3, 0.2, 0]], [-0.408086, [3, -0.2, 0], [3, 0.2, 0]], [-0.300706, [3, -0.2, -0.00536951], [3, 0.4, 0.010739]], [-0.289967, [3, -0.4, 0], [3, 0.4, 0]], [-0.289967, [3, -0.4, 0], [3, 0.2, 0]], [-0.300706, [3, -0.2, 0.010739], [3, 0.2, -0.010739]], [-0.395814, [3, -0.2, 0], [3, 0.2, 0]], [-0.257754, [3, -0.2, -0.00766897], [3, 0.2, 0.00766897]], [-0.250085, [3, -0.2, 0], [3, 0, 0]]])

    names.append("RWristYaw")
    times.append([0.56, 1.16, 1.76, 2.96, 4.16, 4.76, 5.36, 5.96, 6.56])
    keys.append([[0.197844, [3, -0.2, 0], [3, 0.2, 0]], [-0.090548, [3, -0.2, 0.0628938], [3, 0.2, -0.0628938]], [-0.179519, [3, -0.2, 0], [3, 0.4, 0]], [-0.179519, [3, -0.4, 0], [3, 0.4, 0]], [-0.179519, [3, -0.4, 0], [3, 0.2, 0]], [-0.179519, [3, -0.2, 0], [3, 0.2, 0]], [-0.108956, [3, -0.2, -0.0628938], [3, 0.2, 0.0628938]], [0.197844, [3, -0.2, 0], [3, 0.2, 0]], [0.191709, [3, -0.2, 0], [3, 0, 0]]])

    return names, times, keys