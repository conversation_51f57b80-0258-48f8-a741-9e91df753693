def __call__():
    names = list()
    times = list()
    keys = list()

    names.append("HeadPitch")
    times.append([0.76, 1.52, 2.36, 3.56, 4.76, 5.36, 6.36, 7.56, 8.16, 9.16, 10.36])
    keys.append([[0.154892, [3, -0.266667, 0], [3, 0.253333, 0]], [0.346642, [3, -0.253333, -0.03959], [3, 0.28, 0.0437573]], [0.404934, [3, -0.28, -0.023371], [3, 0.4, 0.0333871]], [0.516916, [3, -0.4, -0.00613659], [3, 0.4, 0.00613659]], [0.523053, [3, -0.4, 0], [3, 0.2, 0]], [0.523053, [3, -0.2, 0], [3, 0.333333, 0]], [0.516916, [3, -0.333333, 0], [3, 0.4, 0]], [0.530721, [3, -0.4, 0], [3, 0.2, 0]], [0.530721, [3, -0.2, 0], [3, 0.333333, 0]], [0.516916, [3, -0.333333, 0.0138056], [3, 0.4, -0.0165667]], [0.404934, [3, -0.4, 0], [3, 0, 0]]])

    names.append("HeadYaw")
    times.append([0.76, 1.52, 2.36, 3.56, 4.76, 5.36, 6.36, 7.56, 8.16, 9.16, 10.36])
    keys.append([[-0.023052, [3, -0.266667, 0], [3, 0.253333, 0]], [-0.0276539, [3, -0.253333, 0], [3, 0.28, 0]], [-0.0276539, [3, -0.28, 0], [3, 0.4, 0]], [-0.0276539, [3, -0.4, 0], [3, 0.4, 0]], [-0.0276539, [3, -0.4, 0], [3, 0.2, 0]], [-0.0276539, [3, -0.2, 0], [3, 0.333333, 0]], [-0.0276539, [3, -0.333333, 0], [3, 0.4, 0]], [-0.019984, [3, -0.4, 0], [3, 0.2, 0]], [-0.019984, [3, -0.2, 0], [3, 0.333333, 0]], [-0.0276539, [3, -0.333333, 0], [3, 0.4, 0]], [-0.0276539, [3, -0.4, 0], [3, 0, 0]]])

    names.append("LAnklePitch")
    times.append([0.76, 1.52, 2.36, 3.56, 4.76, 5.36, 6.36, 7.56, 8.16, 9.16, 10.36])
    keys.append([[0.866668, [3, -0.266667, 0], [3, 0.253333, 0]], [0.549129, [3, -0.253333, 0], [3, 0.28, 0]], [0.5568, [3, -0.28, 0], [3, 0.4, 0]], [0.386526, [3, -0.4, 0], [3, 0.4, 0]], [0.395731, [3, -0.4, 0], [3, 0.2, 0]], [0.395731, [3, -0.2, 0], [3, 0.333333, 0]], [0.386526, [3, -0.333333, 0.00464866], [3, 0.4, -0.00557839]], [0.36505, [3, -0.4, 0], [3, 0.2, 0]], [0.36505, [3, -0.2, 0], [3, 0.333333, 0]], [0.386526, [3, -0.333333, -0.0214763], [3, 0.4, 0.0257715]], [0.5568, [3, -0.4, 0], [3, 0, 0]]])

    names.append("LAnkleRoll")
    times.append([0.76, 1.52, 2.36, 3.56, 4.76, 5.36, 6.36, 7.56, 8.16, 9.16, 10.36])
    keys.append([[-0.0398422, [3, -0.266667, 0], [3, 0.253333, 0]], [0.0123138, [3, -0.253333, 0], [3, 0.28, 0]], [0.00771189, [3, -0.28, 0], [3, 0.4, 0]], [0.101286, [3, -0.4, 0], [3, 0.4, 0]], [0.099752, [3, -0.4, 0], [3, 0.2, 0]], [0.099752, [3, -0.2, 0], [3, 0.333333, 0]], [0.101286, [3, -0.333333, 0], [3, 0.4, 0]], [0.0567998, [3, -0.4, 0], [3, 0.2, 0]], [0.0567998, [3, -0.2, 0], [3, 0.333333, 0]], [0.101286, [3, -0.333333, 0], [3, 0.4, 0]], [0.00771189, [3, -0.4, 0], [3, 0, 0]]])

    names.append("LElbowRoll")
    times.append([0.76, 1.52, 2.36, 3.56, 4.76, 5.36, 6.36, 7.56, 8.16, 9.16, 10.36])
    keys.append([[-1.51248, [3, -0.266667, 0], [3, 0.253333, 0]], [-1.50941, [3, -0.253333, -0.0030665], [3, 0.28, 0.00338929]], [-0.363515, [3, -0.28, -0.158922], [3, 0.4, 0.227031]], [-0.136484, [3, -0.4, 0], [3, 0.4, 0]], [-0.162562, [3, -0.4, 0], [3, 0.2, 0]], [-0.162562, [3, -0.2, 0], [3, 0.333333, 0]], [-0.136484, [3, -0.333333, 0], [3, 0.4, 0]], [-0.167164, [3, -0.4, 0], [3, 0.2, 0]], [-0.167164, [3, -0.2, 0], [3, 0.333333, 0]], [-0.136484, [3, -0.333333, 0], [3, 0.4, 0]], [-0.363515, [3, -0.4, 0], [3, 0, 0]]])

    names.append("LElbowYaw")
    times.append([0.76, 1.52, 2.36, 3.56, 4.76, 5.36, 6.36, 7.56, 8.16, 9.16, 10.36])
    keys.append([[-0.934249, [3, -0.266667, 0], [3, 0.253333, 0]], [-1.69511, [3, -0.253333, 0], [3, 0.28, 0]], [-1.0447, [3, -0.28, 0], [3, 0.4, 0]], [-1.0447, [3, -0.4, 0], [3, 0.4, 0]], [-1.0447, [3, -0.4, 0], [3, 0.2, 0]], [-1.0447, [3, -0.2, 0], [3, 0.333333, 0]], [-1.0447, [3, -0.333333, 0], [3, 0.4, 0]], [-1.05697, [3, -0.4, 0], [3, 0.2, 0]], [-1.05697, [3, -0.2, 0], [3, 0.333333, 0]], [-1.0447, [3, -0.333333, 0], [3, 0.4, 0]], [-1.0447, [3, -0.4, 0], [3, 0, 0]]])

    names.append("LHand")
    times.append([0.76, 1.52, 2.36, 3.56, 4.76, 5.36, 6.36, 7.56, 8.16, 9.16, 10.36])
    keys.append([[0.386, [3, -0.266667, 0], [3, 0.253333, 0]], [0.32, [3, -0.253333, 0.00723808], [3, 0.28, -0.00799999]], [0.312, [3, -0.28, 0], [3, 0.4, 0]], [0.312, [3, -0.4, 0], [3, 0.4, 0]], [0.312, [3, -0.4, 0], [3, 0.2, 0]], [0.312, [3, -0.2, 0], [3, 0.333333, 0]], [0.312, [3, -0.333333, 0], [3, 0.4, 0]], [0.3256, [3, -0.4, 0], [3, 0.2, 0]], [0.3256, [3, -0.2, 0], [3, 0.333333, 0]], [0.312, [3, -0.333333, 0], [3, 0.4, 0]], [0.312, [3, -0.4, 0], [3, 0, 0]]])

    names.append("LHipPitch")
    times.append([0.76, 1.52, 2.36, 3.56, 4.76, 5.36, 6.36, 7.56, 8.16, 9.16, 10.36])
    keys.append([[0.380475, [3, -0.266667, 0], [3, 0.253333, 0]], [-0.547595, [3, -0.253333, 0.00277605], [3, 0.28, -0.00306826]], [-0.550664, [3, -0.28, 0], [3, 0.4, 0]], [0.105888, [3, -0.4, 0], [3, 0.4, 0]], [0.0537319, [3, -0.4, 0], [3, 0.2, 0]], [0.0537319, [3, -0.2, 0], [3, 0.333333, 0]], [0.105888, [3, -0.333333, 0], [3, 0.4, 0]], [0.0859461, [3, -0.4, 0], [3, 0.2, 0]], [0.0859461, [3, -0.2, 0], [3, 0.333333, 0]], [0.105888, [3, -0.333333, 0], [3, 0.4, 0]], [-0.550664, [3, -0.4, 0], [3, 0, 0]]])

    names.append("LHipRoll")
    times.append([0.76, 1.52, 2.36, 3.56, 4.76, 5.36, 6.36, 7.56, 8.16, 9.16, 10.36])
    keys.append([[0.108956, [3, -0.266667, 0], [3, 0.253333, 0]], [-0.0459781, [3, -0.253333, 0.00693939], [3, 0.28, -0.00766985]], [-0.0536479, [3, -0.28, 0], [3, 0.4, 0]], [4.19617e-05, [3, -0.4, 0], [3, 0.4, 0]], [-0.00455999, [3, -0.4, 0], [3, 0.2, 0]], [-0.00455999, [3, -0.2, 0], [3, 0.333333, 0]], [4.19617e-05, [3, -0.333333, -0.00371879], [3, 0.4, 0.00446255]], [0.019984, [3, -0.4, 0], [3, 0.2, 0]], [0.019984, [3, -0.2, 0], [3, 0.333333, 0]], [4.19617e-05, [3, -0.333333, 0.0111564], [3, 0.4, -0.0133876]], [-0.0536479, [3, -0.4, 0], [3, 0, 0]]])

    names.append("LHipYawPitch")
    times.append([0.76, 1.52, 2.36, 3.56, 4.76, 5.36, 6.36, 7.56, 8.16, 9.16, 10.36])
    keys.append([[-0.50311, [3, -0.266667, 0], [3, 0.253333, 0]], [-0.0689882, [3, -0.253333, 0], [3, 0.28, 0]], [-0.0705221, [3, -0.28, 0.00153397], [3, 0.4, -0.00219138]], [-0.124212, [3, -0.4, 0.0163627], [3, 0.4, -0.0163627]], [-0.168698, [3, -0.4, 0], [3, 0.2, 0]], [-0.168698, [3, -0.2, 0], [3, 0.333333, 0]], [-0.124212, [3, -0.333333, 0], [3, 0.4, 0]], [-0.159494, [3, -0.4, 0], [3, 0.2, 0]], [-0.159494, [3, -0.2, 0], [3, 0.333333, 0]], [-0.124212, [3, -0.333333, -0.0134806], [3, 0.4, 0.0161767]], [-0.0705221, [3, -0.4, 0], [3, 0, 0]]])

    names.append("LKneePitch")
    times.append([0.76, 1.52, 2.36, 3.56, 4.76, 5.36, 6.36, 7.56, 8.16, 9.16, 10.36])
    keys.append([[-0.0859461, [3, -0.266667, 0], [3, 0.253333, 0]], [1.73338, [3, -0.253333, 0], [3, 0.28, 0]], [1.72878, [3, -0.28, 0.00460074], [3, 0.4, -0.00657249]], [1.49868, [3, -0.4, 0.230101], [3, 0.4, -0.230101]], [-0.0828778, [3, -0.4, 0], [3, 0.2, 0]], [-0.0828778, [3, -0.2, 0], [3, 0.333333, 0]], [1.49868, [3, -0.333333, -0.0409076], [3, 0.4, 0.0490891]], [1.54776, [3, -0.4, 0], [3, 0.2, 0]], [1.54776, [3, -0.2, 0], [3, 0.333333, 0]], [1.49868, [3, -0.333333, 0], [3, 0.4, 0]], [1.72878, [3, -0.4, 0], [3, 0, 0]]])

    names.append("LShoulderPitch")
    times.append([0.76, 1.52, 2.36, 3.56, 4.76, 5.36, 6.36, 7.56, 8.16, 9.16, 10.36])
    keys.append([[1.8745, [3, -0.266667, 0], [3, 0.253333, 0]], [2.05553, [3, -0.253333, 0], [3, 0.28, 0]], [1.96655, [3, -0.28, 0], [3, 0.4, 0]], [2.07239, [3, -0.4, 0], [3, 0.4, 0]], [2.03097, [3, -0.4, 0], [3, 0.2, 0]], [2.03097, [3, -0.2, 0], [3, 0.333333, 0]], [2.07239, [3, -0.333333, -0.0081343], [3, 0.4, 0.00976116]], [2.08466, [3, -0.4, 0], [3, 0.2, 0]], [2.08466, [3, -0.2, 0], [3, 0.333333, 0]], [2.07239, [3, -0.333333, 0.0122697], [3, 0.4, -0.0147237]], [1.96655, [3, -0.4, 0], [3, 0, 0]]])

    names.append("LShoulderRoll")
    times.append([0.76, 1.52, 2.36, 3.56, 4.76, 5.36, 6.36, 7.56, 8.16, 9.16, 10.36])
    keys.append([[0.31903, [3, -0.266667, 0], [3, 0.253333, 0]], [0.325165, [3, -0.253333, -0.0058292], [3, 0.28, 0.00644279]], [0.355846, [3, -0.28, 0], [3, 0.4, 0]], [0.309826, [3, -0.4, 0], [3, 0.4, 0]], [0.320565, [3, -0.4, 0], [3, 0.2, 0]], [0.320565, [3, -0.2, 0], [3, 0.333333, 0]], [0.309826, [3, -0.333333, 0.00371888], [3, 0.4, -0.00446265]], [0.29602, [3, -0.4, 0], [3, 0.2, 0]], [0.29602, [3, -0.2, 0], [3, 0.333333, 0]], [0.309826, [3, -0.333333, -0.00906461], [3, 0.4, 0.0108775]], [0.355846, [3, -0.4, 0], [3, 0, 0]]])

    names.append("LWristYaw")
    times.append([0.76, 1.52, 2.36, 3.56, 4.76, 5.36, 6.36, 7.56, 8.16, 9.16, 10.36])
    keys.append([[0.20398, [3, -0.266667, 0], [3, 0.253333, 0]], [0.154892, [3, -0.253333, 0], [3, 0.28, 0]], [0.727074, [3, -0.28, 0], [3, 0.4, 0]], [0.656511, [3, -0.4, 0], [3, 0.4, 0]], [0.677985, [3, -0.4, 0], [3, 0.2, 0]], [0.677985, [3, -0.2, 0], [3, 0.333333, 0]], [0.656511, [3, -0.333333, 0], [3, 0.4, 0]], [0.682588, [3, -0.4, 0], [3, 0.2, 0]], [0.682588, [3, -0.2, 0], [3, 0.333333, 0]], [0.656511, [3, -0.333333, 0], [3, 0.4, 0]], [0.727074, [3, -0.4, 0], [3, 0, 0]]])

    names.append("RAnklePitch")
    times.append([0.76, 1.52, 2.36, 3.56, 4.76, 5.36, 6.36, 7.56, 8.16, 9.16, 10.36])
    keys.append([[0.64739, [3, -0.266667, 0], [3, 0.253333, 0]], [0.570689, [3, -0.253333, 0], [3, 0.28, 0]], [0.573758, [3, -0.28, 0], [3, 0.4, 0]], [0.362067, [3, -0.4, 0], [3, 0.4, 0]], [0.363599, [3, -0.4, 0], [3, 0.2, 0]], [0.363599, [3, -0.2, 0], [3, 0.333333, 0]], [0.362067, [3, -0.333333, 0], [3, 0.4, 0]], [0.383541, [3, -0.4, 0], [3, 0.2, 0]], [0.383541, [3, -0.2, 0], [3, 0.333333, 0]], [0.362067, [3, -0.333333, 0], [3, 0.4, 0]], [0.573758, [3, -0.4, 0], [3, 0, 0]]])

    names.append("RAnkleRoll")
    times.append([0.76, 1.52, 2.36, 3.56, 4.76, 5.36, 6.36, 7.56, 8.16, 9.16, 10.36])
    keys.append([[-0.283749, [3, -0.266667, 0], [3, 0.253333, 0]], [-0.0827939, [3, -0.253333, -0.00277576], [3, 0.28, 0.00306795]], [-0.0797259, [3, -0.28, -0.0018949], [3, 0.4, 0.00270701]], [-0.0689882, [3, -0.4, -0.00332366], [3, 0.4, 0.00332366]], [-0.059784, [3, -0.4, 0], [3, 0.2, 0]], [-0.059784, [3, -0.2, 0], [3, 0.333333, 0]], [-0.0689882, [3, -0.333333, 0.00395124], [3, 0.4, -0.00474149]], [-0.0858622, [3, -0.4, 0], [3, 0.2, 0]], [-0.0858622, [3, -0.2, 0], [3, 0.333333, 0]], [-0.0689882, [3, -0.333333, 0], [3, 0.4, 0]], [-0.0797259, [3, -0.4, 0], [3, 0, 0]]])

    names.append("RElbowRoll")
    times.append([0.76, 1.52, 2.36, 3.56, 4.76, 5.36, 6.36, 7.56, 8.16, 9.16, 10.36])
    keys.append([[1.5141, [3, -0.266667, 0], [3, 0.253333, 0]], [1.50796, [3, -0.253333, 0.00613486], [3, 0.28, -0.00678063]], [0.276162, [3, -0.28, 0.0934203], [3, 0.4, -0.133458]], [0.142704, [3, -0.4, 0], [3, 0.4, 0]], [0.15651, [3, -0.4, 0], [3, 0.2, 0]], [0.15651, [3, -0.2, 0], [3, 0.333333, 0]], [0.142704, [3, -0.333333, 0], [3, 0.4, 0]], [0.165714, [3, -0.4, 0], [3, 0.2, 0]], [0.165714, [3, -0.2, 0], [3, 0.333333, 0]], [0.142704, [3, -0.333333, 0], [3, 0.4, 0]], [0.276162, [3, -0.4, 0], [3, 0, 0]]])

    names.append("RElbowYaw")
    times.append([0.76, 1.52, 2.36, 3.56, 4.76, 5.36, 6.36, 7.56, 8.16, 9.16, 10.36])
    keys.append([[0.918823, [3, -0.266667, 0], [3, 0.253333, 0]], [1.67509, [3, -0.253333, 0], [3, 0.28, 0]], [1.15046, [3, -0.28, 0.143384], [3, 0.4, -0.204834]], [0.630432, [3, -0.4, 0], [3, 0.4, 0]], [0.642704, [3, -0.4, 0], [3, 0.2, 0]], [0.642704, [3, -0.2, 0], [3, 0.333333, 0]], [0.630432, [3, -0.333333, 0], [3, 0.4, 0]], [0.642704, [3, -0.4, 0], [3, 0.2, 0]], [0.642704, [3, -0.2, 0], [3, 0.333333, 0]], [0.630432, [3, -0.333333, 0], [3, 0.4, 0]], [1.15046, [3, -0.4, 0], [3, 0, 0]]])

    names.append("RHand")
    times.append([0.76, 1.52, 2.36, 3.56, 4.76, 5.36, 6.36, 7.56, 8.16, 9.16, 10.36])
    keys.append([[0.3596, [3, -0.266667, 0], [3, 0.253333, 0]], [0.3528, [3, -0.253333, 0], [3, 0.28, 0]], [0.3528, [3, -0.28, 0], [3, 0.4, 0]], [0.3528, [3, -0.4, 0], [3, 0.4, 0]], [0.3528, [3, -0.4, 0], [3, 0.2, 0]], [0.3528, [3, -0.2, 0], [3, 0.333333, 0]], [0.3528, [3, -0.333333, 0], [3, 0.4, 0]], [0.3556, [3, -0.4, 0], [3, 0.2, 0]], [0.3556, [3, -0.2, 0], [3, 0.333333, 0]], [0.3528, [3, -0.333333, 0], [3, 0.4, 0]], [0.3528, [3, -0.4, 0], [3, 0, 0]]])

    names.append("RHipPitch")
    times.append([0.76, 1.52, 2.36, 3.56, 4.76, 5.36, 6.36, 7.56, 8.16, 9.16, 10.36])
    keys.append([[-0.400415, [3, -0.266667, 0], [3, 0.253333, 0]], [-0.641253, [3, -0.253333, 0], [3, 0.28, 0]], [-0.633584, [3, -0.28, -0.00766897], [3, 0.4, 0.0109557]], [0.0858622, [3, -0.4, 0], [3, 0.4, 0]], [0.0827939, [3, -0.4, 0], [3, 0.2, 0]], [0.0827939, [3, -0.2, 0], [3, 0.333333, 0]], [0.0858622, [3, -0.333333, 0], [3, 0.4, 0]], [0.076658, [3, -0.4, 0], [3, 0.2, 0]], [0.076658, [3, -0.2, 0], [3, 0.333333, 0]], [0.0858622, [3, -0.333333, 0], [3, 0.4, 0]], [-0.633584, [3, -0.4, 0], [3, 0, 0]]])

    names.append("RHipRoll")
    times.append([0.76, 1.52, 2.36, 3.56, 4.76, 5.36, 6.36, 7.56, 8.16, 9.16, 10.36])
    keys.append([[-0.0827939, [3, -0.266667, 0], [3, 0.253333, 0]], [-0.00149202, [3, -0.253333, -0.00555157], [3, 0.28, 0.00613594]], [0.00464392, [3, -0.28, 0], [3, 0.4, 0]], [-0.0352399, [3, -0.4, 0], [3, 0.4, 0]], [-0.032172, [3, -0.4, 0], [3, 0.2, 0]], [-0.032172, [3, -0.2, 0], [3, 0.333333, 0]], [-0.0352399, [3, -0.333333, 0], [3, 0.4, 0]], [-0.00916195, [3, -0.4, 0], [3, 0.2, 0]], [-0.00916195, [3, -0.2, 0], [3, 0.333333, 0]], [-0.0352399, [3, -0.333333, 0], [3, 0.4, 0]], [0.00464392, [3, -0.4, 0], [3, 0, 0]]])

    names.append("RHipYawPitch")
    times.append([0.76, 1.52, 2.36, 3.56, 4.76, 5.36, 6.36, 7.56, 8.16, 9.16, 10.36])
    keys.append([[-0.50311, [3, -0.266667, 0], [3, 0.253333, 0]], [-0.0689882, [3, -0.253333, 0], [3, 0.28, 0]], [-0.0705221, [3, -0.28, 0.00153397], [3, 0.4, -0.00219138]], [-0.124212, [3, -0.4, 0.0163627], [3, 0.4, -0.0163627]], [-0.168698, [3, -0.4, 0], [3, 0.2, 0]], [-0.168698, [3, -0.2, 0], [3, 0.333333, 0]], [-0.124212, [3, -0.333333, 0], [3, 0.4, 0]], [-0.159494, [3, -0.4, 0], [3, 0.2, 0]], [-0.159494, [3, -0.2, 0], [3, 0.333333, 0]], [-0.124212, [3, -0.333333, -0.0134806], [3, 0.4, 0.0161767]], [-0.0705221, [3, -0.4, 0], [3, 0, 0]]])

    names.append("RKneePitch")
    times.append([0.76, 1.52, 2.36, 3.56, 4.76, 5.36, 6.36, 7.56, 8.16, 9.16, 10.36])
    keys.append([[1.7396, [3, -0.266667, 0], [3, 0.253333, 0]], [1.79636, [3, -0.253333, -0.00554263], [3, 0.28, 0.00612607]], [1.80249, [3, -0.28, 0], [3, 0.4, 0]], [1.54938, [3, -0.4, 0], [3, 0.4, 0]], [1.55398, [3, -0.4, 0], [3, 0.2, 0]], [1.55398, [3, -0.2, 0], [3, 0.333333, 0]], [1.54938, [3, -0.333333, 0.00460061], [3, 0.4, -0.00552073]], [-0.0735901, [3, -0.4, 0], [3, 0.2, 0]], [-0.0735901, [3, -0.2, 0], [3, 0.333333, 0]], [1.54938, [3, -0.333333, -0.210922], [3, 0.4, 0.253106]], [1.80249, [3, -0.4, 0], [3, 0, 0]]])

    names.append("RShoulderPitch")
    times.append([0.76, 1.52, 2.36, 3.56, 4.76, 5.36, 6.36, 7.56, 8.16, 9.16, 10.36])
    keys.append([[1.74727, [3, -0.266667, 0], [3, 0.253333, 0]], [1.93903, [3, -0.253333, 0], [3, 0.28, 0]], [1.86846, [3, -0.28, 0], [3, 0.4, 0]], [2.06787, [3, -0.4, -0.00921534], [3, 0.4, 0.00921534]], [2.07708, [3, -0.4, 0], [3, 0.2, 0]], [2.07708, [3, -0.2, 0], [3, 0.333333, 0]], [2.06787, [3, -0.333333, 0.00921534], [3, 0.4, -0.0110584]], [2.00805, [3, -0.4, 0], [3, 0.2, 0]], [2.00805, [3, -0.2, 0], [3, 0.333333, 0]], [2.06787, [3, -0.333333, 0], [3, 0.4, 0]], [1.86846, [3, -0.4, 0], [3, 0, 0]]])

    names.append("RShoulderRoll")
    times.append([0.76, 1.52, 2.36, 3.56, 4.76, 5.36, 6.36, 7.56, 8.16, 9.16, 10.36])
    keys.append([[-0.325249, [3, -0.266667, 0], [3, 0.253333, 0]], [-0.33292, [3, -0.253333, 0.0076707], [3, 0.28, -0.00847815]], [-0.392746, [3, -0.28, 0], [3, 0.4, 0]], [-0.380475, [3, -0.4, -0.00357938], [3, 0.4, 0.00357938]], [-0.37127, [3, -0.4, 0], [3, 0.2, 0]], [-0.37127, [3, -0.2, 0], [3, 0.333333, 0]], [-0.380475, [3, -0.333333, 0.0023242], [3, 0.4, -0.00278904]], [-0.38661, [3, -0.4, 0], [3, 0.2, 0]], [-0.38661, [3, -0.2, 0], [3, 0.333333, 0]], [-0.380475, [3, -0.333333, 0], [3, 0.4, 0]], [-0.392746, [3, -0.4, 0], [3, 0, 0]]])

    names.append("RWristYaw")
    times.append([0.76, 1.52, 2.36, 3.56, 4.76, 5.36, 6.36, 7.56, 8.16, 9.16, 10.36])
    keys.append([[0.194775, [3, -0.266667, 0], [3, 0.253333, 0]], [0.0429101, [3, -0.253333, 0.151865], [3, 0.28, -0.167851]], [-1.06617, [3, -0.28, 0], [3, 0.4, 0]], [-0.351328, [3, -0.4, 0], [3, 0.4, 0]], [-0.366667, [3, -0.4, 0], [3, 0.2, 0]], [-0.366667, [3, -0.2, 0], [3, 0.333333, 0]], [-0.351328, [3, -0.333333, 0], [3, 0.4, 0]], [-0.372804, [3, -0.4, 0], [3, 0.2, 0]], [-0.372804, [3, -0.2, 0], [3, 0.333333, 0]], [-0.351328, [3, -0.333333, 0], [3, 0.4, 0]], [-1.06617, [3, -0.4, 0], [3, 0, 0]]])

    return names, times, keys