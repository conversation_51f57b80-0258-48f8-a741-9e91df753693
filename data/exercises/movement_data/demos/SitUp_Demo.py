# Choregraphe bezier export in Python.

def __call__():
    names = list()
    times = list()
    keys = list()

    names.append("HeadPitch")
    times.append([3.8, 5.0, 6.5, 9.0, 9.7, 11, 15, 17, 19])
    keys.append([[0.179436, [3, -0.0666667, 0], [3, 0.24, 0]], [0.228524, [3, -0.24, -0.0159717], [3, 0.213333, 0.014197]], [0.269942, [3, -0.213333, 0], [3, 0.12, 0]], [0.269941, [3, -0.12, 0], [3, 0.12, 0]], [0.596684, [3, -0.12, 0], [3, 0.0933333, 0]], [0.269941, [3, -0.0933333, 0], [3, 0.0666667, 0]], [0.269941, [3, -0.0666667, 0], [3, 0.0266667, 0]], [0.228525, [3, -0.0266667, 0.00335207], [3, 0.213333, -0.0268165]], [0.179436, [3, -0.213333, 0], [3, 0, 0]]])

    names.append("HeadYaw")
    times.append([3.8, 5.0, 6.5, 9.0, 9.7, 11, 15, 17, 19])
    keys.append([[0.00149202, [3, -0.0666667, 0], [3, 0.24, 0]], [0.0168321, [3, -0.24, -0.00406059], [3, 0.213333, 0.00360941]], [0.024502, [3, -0.213333, 0], [3, 0.12, 0]], [0.024502, [3, -0.12, 0], [3, 0.12, 0]], [0.026036, [3, -0.12, 0], [3, 0.0933333, 0]], [0.024502, [3, -0.0933333, 0], [3, 0.0666667, 0]], [0.024502, [3, -0.0666667, 0], [3, 0.0266667, 0]], [0.0168321, [3, -0.0266667, 0.000852221], [3, 0.213333, -0.00681776]], [0.00149202, [3, -0.213333, 0], [3, 0, 0]]])

    names.append("LAnklePitch")
    times.append([3.8, 5.0, 6.5, 9.0, 9.7, 11, 15, 17, 19])
    keys.append([[0.862066, [3, -0.0666667, 0], [3, 0.24, 0]], [0.582878, [3, -0.24, 0.0224348], [3, 0.213333, -0.0199421]], [0.562936, [3, -0.213333, 0], [3, 0.12, 0]], [0.562937, [3, -0.12, -6.6579e-07], [3, 0.12, 6.6579e-07]], [0.569072, [3, -0.12, 0], [3, 0.0933333, 0]], [0.562937, [3, -0.0933333, 0], [3, 0.0666667, 0]], [0.562937, [3, -0.0666667, 0], [3, 0.0266667, 0]], [0.582879, [3, -0.0266667, -0.0110788], [3, 0.213333, 0.0886307]], [0.862065, [3, -0.213333, 0], [3, 0, 0]]])

    names.append("LAnkleRoll")
    times.append([3.8, 5.0, 6.5, 9.0, 9.7, 11, 15, 17, 19])
    keys.append([[-0.0444441, [3, -0.0666667, 0], [3, 0.24, 0]], [0.173384, [3, -0.24, 0], [3, 0.213333, 0]], [0.104354, [3, -0.213333, 0], [3, 0.12, 0]], [0.104354, [3, -0.12, -3.32895e-08], [3, 0.12, 3.32895e-08]], [0.108956, [3, -0.12, 0], [3, 0.0933333, 0]], [0.104354, [3, -0.0933333, 0], [3, 0.0666667, 0]], [0.104354, [3, -0.0666667, 0], [3, 0.0266667, 0]], [0.173384, [3, -0.0266667, 0], [3, 0.213333, 0]], [-0.0444441, [3, -0.213333, 0], [3, 0, 0]]])

    names.append("LElbowRoll")
    times.append([3.8, 5.0, 6.5, 9.0, 9.7, 11, 15, 17, 19])
    keys.append([[-1.51555, [3, -0.0666667, 0], [3, 0.24, 0]], [-1.34221, [3, -0.24, -0.0744441], [3, 0.213333, 0.0661725]], [-1.0937, [3, -0.213333, -2.36725e-07], [3, 0.12, 1.33158e-07]], [-1.0937, [3, -0.12, 0], [3, 0.12, 0]], [-1.41584, [3, -0.12, 0], [3, 0.0933333, 0]], [-1.0937, [3, -0.0933333, 0], [3, 0.0666667, 0]], [-1.0937, [3, -0.0666667, 0], [3, 0.0266667, 0]], [-1.34221, [3, -0.0266667, 0.0156241], [3, 0.213333, -0.124992]], [-1.51555, [3, -0.213333, 0], [3, 0, 0]]])

    names.append("LElbowYaw")
    times.append([3.8, 5.0, 6.5, 9.0, 9.7, 11, 15, 17, 19])
    keys.append([[-0.888228, [3, -0.0666667, 0], [3, 0.24, 0]], [-0.845276, [3, -0.24, -0.0429521], [3, 0.213333, 0.0381796]], [0.0674541, [3, -0.213333, -1.47953e-07], [3, 0.12, 8.32238e-08]], [0.0674542, [3, -0.12, 0], [3, 0.12, 0]], [0.055182, [3, -0.12, 0], [3, 0.0933333, 0]], [0.0674542, [3, -0.0933333, 0], [3, 0.0666667, 0]], [0.0674542, [3, -0.0666667, 0], [3, 0.0266667, 0]], [-0.845275, [3, -0.0266667, 0.00536907], [3, 0.213333, -0.0429526]], [-0.888228, [3, -0.213333, 0], [3, 0, 0]]])

    names.append("LHand")
    times.append([3.8, 5.0, 6.5, 9.0, 9.7, 11, 15, 17, 19])
    keys.append([[0.2952, [3, -0.0666667, 0], [3, 0.24, 0]], [0.2952, [3, -0.24, 0], [3, 0.213333, 0]], [0.7044, [3, -0.213333, -1.05964e-07], [3, 0.12, 5.96046e-08]], [0.7044, [3, -0.12, 0], [3, 0.12, 0]], [0.6952, [3, -0.12, 0], [3, 0.0933333, 0]], [0.7044, [3, -0.0933333, 0], [3, 0.0666667, 0]], [0.7044, [3, -0.0666667, 0], [3, 0.0266667, 0]], [0.2952, [3, -0.0266667, 0], [3, 0.213333, 0]], [0.2952, [3, -0.213333, 0], [3, 0, 0]]])

    names.append("LHipPitch")
    times.append([3.8, 5.0, 6.5, 9.0, 9.7, 11, 15, 17, 19])
    keys.append([[0.372804, [3, -0.0666667, 0], [3, 0.24, 0]], [-0.56447, [3, -0.24, 0.131157], [3, 0.213333, -0.116584]], [-0.681054, [3, -0.213333, 0], [3, 0.12, 0]], [-0.681054, [3, -0.12, -4.66053e-07], [3, 0.12, 4.66053e-07]], [-0.677986, [3, -0.12, 0], [3, 0.0933333, 0]], [-0.681054, [3, -0.0933333, 0], [3, 0.0666667, 0]], [-0.681054, [3, -0.0666667, 0], [3, 0.0266667, 0]], [-0.564471, [3, -0.0266667, -0.0390318], [3, 0.213333, 0.312254]], [0.372804, [3, -0.213333, 0], [3, 0, 0]]])

    names.append("LHipRoll")
    times.append([3.8, 5.0, 6.5, 9.0, 9.7, 11, 15, 17, 19])
    keys.append([[0.105888, [3, -0.0666667, 0], [3, 0.24, 0]], [0.0567999, [3, -0.24, 0], [3, 0.213333, 0]], [0.127364, [3, -0.213333, -1.47953e-07], [3, 0.12, 8.32238e-08]], [0.127364, [3, -0.12, 0], [3, 0.12, 0]], [0.124296, [3, -0.12, 0], [3, 0.0933333, 0]], [0.127364, [3, -0.0933333, 0], [3, 0.0666667, 0]], [0.127364, [3, -0.0666667, 0], [3, 0.0266667, 0]], [0.0567998, [3, -0.0266667, 0], [3, 0.213333, 0]], [0.105888, [3, -0.213333, 0], [3, 0, 0]]])

    names.append("LHipYawPitch")
    times.append([3.8, 5.0, 6.5, 9.0, 9.7, 11, 15, 17, 19])
    keys.append([[-0.509246, [3, -0.0666667, 0], [3, 0.24, 0]], [-0.253068, [3, -0.24, -0.0606381], [3, 0.213333, 0.0539005]], [-0.16563, [3, -0.213333, 0], [3, 0.12, 0]], [-0.16563, [3, -0.12, 0], [3, 0.12, 0]], [-0.164096, [3, -0.12, 0], [3, 0.0933333, 0]], [-0.16563, [3, -0.0933333, 0], [3, 0.0666667, 0]], [-0.16563, [3, -0.0666667, 0], [3, 0.0266667, 0]], [-0.253067, [3, -0.0266667, 0.0127265], [3, 0.213333, -0.101812]], [-0.509247, [3, -0.213333, 0], [3, 0, 0]]])

    names.append("LKneePitch")
    times.append([3.8, 5.0, 6.5, 9.0, 9.7, 11, 15, 17, 19])
    keys.append([[-0.092082, [3, -0.0666667, 0], [3, 0.24, 0]], [1.80701, [3, -0.24, -0.0776588], [3, 0.213333, 0.0690301]], [1.87604, [3, -0.213333, 0], [3, 0.12, 0]], [1.87604, [3, -0.12, 3.19579e-06], [3, 0.12, -3.19579e-06]], [1.87297, [3, -0.12, 0], [3, 0.0933333, 0]], [1.87604, [3, -0.0933333, 0], [3, 0.0666667, 0]], [1.87604, [3, -0.0666667, 0], [3, 0.0266667, 0]], [1.80701, [3, -0.0266667, 0.0690278], [3, 0.213333, -0.552222]], [-0.092082, [3, -0.213333, 0], [3, 0, 0]]])

    names.append("LShoulderPitch")
    times.append([3.8, 5.0, 6.5, 9.0, 9.7, 11, 15, 17, 19])
    keys.append([[1.74105, [3, -0.0666667, 0], [3, 0.24, 0]], [0.530722, [3, -0.24, 0.597719], [3, 0.213333, -0.531305]], [-1.64602, [3, -0.213333, 0], [3, 0.12, 0]], [-1.64602, [3, -0.12, -6.6579e-07], [3, 0.12, 6.6579e-07]], [-1.26559, [3, -0.12, 0], [3, 0.0933333, 0]], [-1.64602, [3, -0.0933333, 0], [3, 0.0666667, 0]], [-1.64602, [3, -0.0666667, 0], [3, 0.0266667, 0]], [0.530721, [3, -0.0266667, -0.125447], [3, 0.213333, 1.00358]], [1.74105, [3, -0.213333, 0], [3, 0, 0]]])

    names.append("LShoulderRoll")
    times.append([3.8, 5.0, 6.5, 9.0, 9.7, 11, 15, 17, 19])
    keys.append([[0.231592, [3, -0.0666667, 0], [3, 0.24, 0]], [0.400332, [3, -0.24, -0.0378988], [3, 0.213333, 0.0336879]], [0.446352, [3, -0.213333, -4.14269e-07], [3, 0.12, 2.33027e-07]], [0.446352, [3, -0.12, -2.33027e-07], [3, 0.12, 2.33027e-07]], [0.613558, [3, -0.12, 0], [3, 0.0933333, 0]], [0.446352, [3, -0.0933333, 0], [3, 0.0666667, 0]], [0.446352, [3, -0.0666667, 0], [3, 0.0266667, 0]], [0.400331, [3, -0.0266667, 0.00795411], [3, 0.213333, -0.0636329]], [0.231591, [3, -0.213333, 0], [3, 0, 0]]])

    names.append("LWristYaw")
    times.append([3.8, 5.0, 6.5, 9.0, 9.7, 11, 15, 17, 19])
    keys.append([[0.220854, [3, -0.0666667, 0], [3, 0.24, 0]], [-0.667332, [3, -0.24, 0.305356], [3, 0.213333, -0.271428]], [-1.5095, [3, -0.213333, 9.46902e-07], [3, 0.12, -5.32632e-07]], [-1.5095, [3, -0.12, 0], [3, 0.12, 0]], [-1.50796, [3, -0.12, 0], [3, 0.0933333, 0]], [-1.5095, [3, -0.0933333, 0], [3, 0.0666667, 0]], [-1.5095, [3, -0.0666667, 0], [3, 0.0266667, 0]], [-0.667332, [3, -0.0266667, -0.0640871], [3, 0.213333, 0.512697]], [0.220854, [3, -0.213333, 0], [3, 0, 0]]])

    names.append("RAnklePitch")
    times.append([3.8, 5.0, 6.5, 9.0, 9.7, 11, 15, 17, 19])
    keys.append([[0.642788, [3, -0.0666667, 0], [3, 0.24, 0]], [0.517, [3, -0.24, 0], [3, 0.213333, 0]], [0.595234, [3, -0.213333, -2.36725e-07], [3, 0.12, 1.33158e-07]], [0.595234, [3, -0.12, 0], [3, 0.12, 0]], [0.5937, [3, -0.12, 0], [3, 0.0933333, 0]], [0.595234, [3, -0.0933333, 0], [3, 0.0666667, 0]], [0.595234, [3, -0.0666667, 0], [3, 0.0266667, 0]], [0.517, [3, -0.0266667, 0], [3, 0.213333, 0]], [0.642787, [3, -0.213333, 0], [3, 0, 0]]])

    names.append("RAnkleRoll")
    times.append([3.8, 5.0, 6.5, 9.0, 9.7, 11, 15, 17, 19])
    keys.append([[-0.289884, [3, -0.0666667, 0], [3, 0.24, 0]], [-0.216252, [3, -0.24, -0.0400645], [3, 0.213333, 0.0356129]], [-0.0628521, [3, -0.213333, -8.13744e-08], [3, 0.12, 4.57731e-08]], [-0.0628521, [3, -0.12, 0], [3, 0.12, 0]], [-0.0628521, [3, -0.12, 0], [3, 0.0933333, 0]], [-0.0628521, [3, -0.0933333, 0], [3, 0.0666667, 0]], [-0.0628521, [3, -0.0666667, 0], [3, 0.0266667, 0]], [-0.216252, [3, -0.0266667, 0.00840857], [3, 0.213333, -0.0672686]], [-0.289883, [3, -0.213333, 0], [3, 0, 0]]])

    names.append("RElbowRoll")
    times.append([3.8, 5.0, 6.5, 9.0, 9.7, 11, 15, 17, 19])
    keys.append([[1.51257, [3, -0.0666667, 0], [3, 0.24, 0]], [1.34996, [3, -0.24, 0.0722785], [3, 0.213333, -0.0642476]], [1.10299, [3, -0.213333, 1.42035e-06], [3, 0.12, -7.98948e-07]], [1.10299, [3, -0.12, 0], [3, 0.12, 0]], [1.24872, [3, -0.12, 0], [3, 0.0933333, 0]], [1.10299, [3, -0.0933333, 0], [3, 0.0666667, 0]], [1.10299, [3, -0.0666667, 0], [3, 0.0266667, 0]], [1.34996, [3, -0.0266667, -0.0151696], [3, 0.213333, 0.121357]], [1.51257, [3, -0.213333, 0], [3, 0, 0]]])

    names.append("RElbowYaw")
    times.append([3.8, 5.0, 6.5, 9.0, 9.7, 11, 15, 17, 19])
    keys.append([[0.854396, [3, -0.0666667, 0], [3, 0.24, 0]], [0.608956, [3, -0.24, 0.170545], [3, 0.213333, -0.151595]], [-0.112024, [3, -0.213333, 1.03567e-07], [3, 0.12, -5.82566e-08]], [-0.112024, [3, -0.12, 5.82567e-08], [3, 0.12, -5.82567e-08]], [-0.20253, [3, -0.12, 0], [3, 0.0933333, 0]], [-0.112024, [3, -0.0933333, 0], [3, 0.0666667, 0]], [-0.112024, [3, -0.0666667, 0], [3, 0.0266667, 0]], [0.608956, [3, -0.0266667, -0.0306801], [3, 0.213333, 0.24544]], [0.854396, [3, -0.213333, 0], [3, 0, 0]]])

    names.append("RHand")
    times.append([3.8, 5.0, 6.5, 9.0, 9.7, 11, 15, 17, 19])
    keys.append([[0.3424, [3, -0.0666667, 0], [3, 0.24, 0]], [0.3424, [3, -0.24, 0], [3, 0.213333, 0]], [0.8444, [3, -0.213333, 0], [3, 0.12, 0]], [0.8444, [3, -0.12, 0], [3, 0.12, 0]], [0.8348, [3, -0.12, 0], [3, 0.0933333, 0]], [0.8444, [3, -0.0933333, 0], [3, 0.0666667, 0]], [0.8444, [3, -0.0666667, 0], [3, 0.0266667, 0]], [0.3424, [3, -0.0266667, 0], [3, 0.213333, 0]], [0.3424, [3, -0.213333, 0], [3, 0, 0]]])

    names.append("RHipPitch")
    times.append([3.8, 5.0, 6.5, 9.0, 9.7, 11, 15, 17, 19])
    keys.append([[-0.395814, [3, -0.0666667, 0], [3, 0.24, 0]], [-0.612108, [3, -0.24, 0], [3, 0.213333, 0]], [-0.602904, [3, -0.213333, 0], [3, 0.12, 0]], [-0.602905, [3, -0.12, 6.6579e-07], [3, 0.12, -6.6579e-07]], [-0.607506, [3, -0.12, 0], [3, 0.0933333, 0]], [-0.602905, [3, -0.0933333, 0], [3, 0.0666667, 0]], [-0.602905, [3, -0.0666667, 0], [3, 0.0266667, 0]], [-0.612108, [3, -0.0266667, 0], [3, 0.213333, 0]], [-0.395814, [3, -0.213333, 0], [3, 0, 0]]])

    names.append("RHipRoll")
    times.append([3.8, 5.0, 6.5, 9.0, 9.7, 11, 15, 17, 19])
    keys.append([[-0.076658, [3, -0.0666667, 0], [3, 0.24, 0]], [-0.101202, [3, -0.24, 0], [3, 0.213333, 0]], [0.04146, [3, -0.213333, -1.47953e-07], [3, 0.12, 8.32238e-08]], [0.0414601, [3, -0.12, 0], [3, 0.12, 0]], [0.0337899, [3, -0.12, 0], [3, 0.0933333, 0]], [0.0414601, [3, -0.0933333, 0], [3, 0.0666667, 0]], [0.0414601, [3, -0.0666667, 0], [3, 0.0266667, 0]], [-0.101202, [3, -0.0266667, 0], [3, 0.213333, 0]], [-0.076658, [3, -0.213333, 0], [3, 0, 0]]])

    names.append("RHipYawPitch")
    times.append([3.8, 5.0, 6.5, 9.0, 9.7, 11, 15, 17, 19])
    keys.append([[-0.509246, [3, -0.0666667, 0], [3, 0.24, 0]], [-0.253068, [3, -0.24, -0.0606381], [3, 0.213333, 0.0539005]], [-0.16563, [3, -0.213333, 0], [3, 0.12, 0]], [-0.16563, [3, -0.12, 0], [3, 0.12, 0]], [-0.164096, [3, -0.12, 0], [3, 0.0933333, 0]], [-0.16563, [3, -0.0933333, 0], [3, 0.0666667, 0]], [-0.16563, [3, -0.0666667, 0], [3, 0.0266667, 0]], [-0.253067, [3, -0.0266667, 0.0127265], [3, 0.213333, -0.101812]], [-0.509247, [3, -0.213333, 0], [3, 0, 0]]])

    names.append("RKneePitch")
    times.append([3.8, 5.0, 6.5, 9.0, 9.7, 11, 15, 17, 19])
    keys.append([[1.73346, [3, -0.0666667, 0], [3, 0.24, 0]], [1.8884, [3, -0.24, 0], [3, 0.213333, 0]], [1.77335, [3, -0.213333, 7.33849e-06], [3, 0.12, -4.1279e-06]], [1.77334, [3, -0.12, 0], [3, 0.12, 0]], [1.78102, [3, -0.12, 0], [3, 0.0933333, 0]], [1.77334, [3, -0.0933333, 0], [3, 0.0666667, 0]], [1.77334, [3, -0.0666667, 0], [3, 0.0266667, 0]], [1.88839, [3, -0.0266667, 0], [3, 0.213333, 0]], [1.73346, [3, -0.213333, 0], [3, 0, 0]]])

    names.append("RShoulderPitch")
    times.append([3.8, 5.0, 6.5, 9.0, 9.7, 11, 15, 17, 19])
    keys.append([[1.87612, [3, -0.0666667, 0], [3, 0.24, 0]], [0.405018, [3, -0.24, 0.618292], [3, 0.213333, -0.549593]], [-1.62753, [3, -0.213333, 0], [3, 0.12, 0]], [-1.62753, [3, -0.12, -3.99474e-07], [3, 0.12, 3.99474e-07]], [-1.31306, [3, -0.12, 0], [3, 0.0933333, 0]], [-1.62753, [3, -0.0933333, 0], [3, 0.0666667, 0]], [-1.62753, [3, -0.0666667, 0], [3, 0.0266667, 0]], [0.405018, [3, -0.0266667, -0.129765], [3, 0.213333, 1.03812]], [1.87612, [3, -0.213333, 0], [3, 0, 0]]])

    names.append("RShoulderRoll")
    times.append([3.8, 5.0, 6.5, 9.0, 9.7, 11, 15, 17, 19])
    keys.append([[-0.259288, [3, -0.0666667, 0], [3, 0.24, 0]], [-0.434164, [3, -0.24, 0.0120801], [3, 0.213333, -0.0107379]], [-0.444902, [3, -0.213333, 0], [3, 0.12, 0]], [-0.444902, [3, -0.12, -6.6579e-08], [3, 0.12, 6.6579e-08]], [-0.411154, [3, -0.12, 0], [3, 0.0933333, 0]], [-0.444902, [3, -0.0933333, 0], [3, 0.0666667, 0]], [-0.444902, [3, -0.0666667, 0], [3, 0.0266667, 0]], [-0.434165, [3, -0.0266667, -0.00687459], [3, 0.213333, 0.0549967]], [-0.259288, [3, -0.213333, 0], [3, 0, 0]]])

    names.append("RWristYaw")
    times.append([3.8, 5.0, 6.5, 9.0, 9.7, 11, 15, 17, 19])
    keys.append([[0.208582, [3, -0.0666667, 0], [3, 0.24, 0]], [0.793036, [3, -0.24, -0.257983], [3, 0.213333, 0.229318]], [1.67048, [3, -0.213333, -4.73451e-07], [3, 0.12, 2.66316e-07]], [1.67048, [3, -0.12, -2.66316e-07], [3, 0.12, 2.66316e-07]], [1.72111, [3, -0.12, 0], [3, 0.0933333, 0]], [1.67048, [3, -0.0933333, 0], [3, 0.0666667, 0]], [1.67048, [3, -0.0666667, 0], [3, 0.0266667, 0]], [0.793036, [3, -0.0266667, 0.0541445], [3, 0.213333, -0.433156]], [0.208583, [3, -0.213333, 0], [3, 0, 0]]])

    return names, times, keys