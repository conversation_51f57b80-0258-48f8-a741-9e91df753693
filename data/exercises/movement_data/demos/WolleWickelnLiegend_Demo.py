def __call__():
    names = list()
    times = list()
    keys = list()

    names.append("HeadPitch")
    times.append([1.56, 1.72, 1.88, 2.04, 2.2, 2.36, 2.52, 2.68, 2.84, 3, 3.16])
    keys.append([[0.1733, [3, -0.533333, 0], [3, 0.0533333, 0]], [0.1733, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.1733, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.1733, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.1733, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.1733, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.1733, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.1733, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.1733, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.1733, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.1733, [3, -0.0533333, 0], [3, 0, 0]]])

    names.append("HeadYaw")
    times.append([1.56, 1.72, 1.88, 2.04, 2.2, 2.36, 2.52, 2.68, 2.84, 3, 3.16])
    keys.append([[-0.00310993, [3, -0.533333, 0], [3, 0.0533333, 0]], [-0.00310993, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.00310993, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.00310993, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.00310993, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.00310993, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.00310993, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.00310993, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.00310993, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.00310993, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.00310993, [3, -0.0533333, 0], [3, 0, 0]]])

    names.append("LAnklePitch")
    times.append([1.56, 1.72, 1.88, 2.04, 2.2, 2.36, 2.52, 2.68, 2.84, 3, 3.16])
    keys.append([[0.852862, [3, -0.533333, 0], [3, 0.0533333, 0]], [0.852862, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.852862, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.852862, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.852862, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.852862, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.852862, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.852862, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.852862, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.852862, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.852862, [3, -0.0533333, 0], [3, 0, 0]]])

    names.append("LAnkleRoll")
    times.append([1.56, 1.72, 1.88, 2.04, 2.2, 2.36, 2.52, 2.68, 2.84, 3, 3.16])
    keys.append([[-0.032172, [3, -0.533333, 0], [3, 0.0533333, 0]], [-0.032172, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.032172, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.032172, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.032172, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.032172, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.032172, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.032172, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.032172, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.032172, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.032172, [3, -0.0533333, 0], [3, 0, 0]]])

    names.append("LElbowRoll")
    times.append([1.56, 1.72, 1.88, 2.04, 2.2, 2.36, 2.52, 2.68, 2.84, 3, 3.16])
    keys.append([[-1.53396, [3, -0.533333, 0], [3, 0.0533333, 0]], [-1.51095, [3, -0.0533333, -0.0230101], [3, 0.0533333, 0.0230101]], [-1.25937, [3, -0.0533333, -0.0687743], [3, 0.0533333, 0.0687743]], [-1.0983, [3, -0.0533333, -0.036049], [3, 0.0533333, 0.036049]], [-1.04308, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-1.11211, [3, -0.0533333, 0.036049], [3, 0.0533333, -0.036049]], [-1.25937, [3, -0.0533333, 0.0442304], [3, 0.0533333, -0.0442304]], [-1.37749, [3, -0.0533333, 0.0419293], [3, 0.0533333, -0.0419293]], [-1.51095, [3, -0.0533333, 0.012272], [3, 0.0533333, -0.012272]], [-1.52322, [3, -0.0533333, 0.00409068], [3, 0.0533333, -0.00409068]], [-1.53549, [3, -0.0533333, 0], [3, 0, 0]]])

    names.append("LElbowYaw")
    times.append([1.56, 1.72, 1.88, 2.04, 2.2, 2.36, 2.52, 2.68, 2.84, 3, 3.16])
    keys.append([[-0.388144, [3, -0.533333, 0], [3, 0.0533333, 0]], [-0.297638, [3, -0.0533333, -0.0224987], [3, 0.0533333, 0.0224987]], [-0.253152, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.26389, [3, -0.0533333, 0.0107381], [3, 0.0533333, -0.0107381]], [-0.45564, [3, -0.0533333, 0.0585477], [3, 0.0533333, -0.0585477]], [-0.615176, [3, -0.0533333, 0.04602], [3, 0.0533333, -0.04602]], [-0.73176, [3, -0.0533333, 0.0122719], [3, 0.0533333, -0.0122719]], [-0.744032, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.744032, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.628982, [3, -0.0533333, -0.0424407], [3, 0.0533333, 0.0424407]], [-0.489388, [3, -0.0533333, 0], [3, 0, 0]]])

    names.append("LHand")
    times.append([1.56, 1.72, 1.88, 2.04, 2.2, 2.36, 2.52, 2.68, 2.84, 3, 3.16])
    keys.append([[0.304, [3, -0.533333, 0], [3, 0.0533333, 0]], [0.304, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.304, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.304, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.304, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.304, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.304, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.304, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.304, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.304, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.304, [3, -0.0533333, 0], [3, 0, 0]]])

    names.append("LHipPitch")
    times.append([1.56, 1.72, 1.88, 2.04, 2.2, 2.36, 2.52, 2.68, 2.84, 3, 3.16])
    keys.append([[0.375872, [3, -0.533333, 0], [3, 0.0533333, 0]], [0.375872, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.375872, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.375872, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.375872, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.375872, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.375872, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.375872, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.375872, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.375872, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.375872, [3, -0.0533333, 0], [3, 0, 0]]])

    names.append("LHipRoll")
    times.append([1.56, 1.72, 1.88, 2.04, 2.2, 2.36, 2.52, 2.68, 2.84, 3, 3.16])
    keys.append([[0.115092, [3, -0.533333, 0], [3, 0.0533333, 0]], [0.115092, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.115092, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.115092, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.115092, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.115092, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.115092, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.115092, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.115092, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.115092, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.115092, [3, -0.0533333, 0], [3, 0, 0]]])

    names.append("LHipYawPitch")
    times.append([1.56, 1.72, 1.88, 2.04, 2.2, 2.36, 2.52, 2.68, 2.84, 3, 3.16])
    keys.append([[-0.51845, [3, -0.533333, 0], [3, 0.0533333, 0]], [-0.51845, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.51845, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.51845, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.51845, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.51845, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.51845, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.51845, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.51845, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.51845, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.51845, [3, -0.0533333, 0], [3, 0, 0]]])

    names.append("LKneePitch")
    times.append([1.56, 1.72, 1.88, 2.04, 2.2, 2.36, 2.52, 2.68, 2.84, 3, 3.16])
    keys.append([[-0.093616, [3, -0.533333, 0], [3, 0.0533333, 0]], [-0.093616, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.093616, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.093616, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.093616, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.093616, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.093616, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.093616, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.093616, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.093616, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.093616, [3, -0.0533333, 0], [3, 0, 0]]])

    names.append("LShoulderPitch")
    times.append([1.56, 1.72, 1.88, 2.04, 2.2, 2.36, 2.52, 2.68, 2.84, 3, 3.16])
    keys.append([[0.858998, [3, -0.533333, 0], [3, 0.0533333, 0]], [0.843658, [3, -0.0533333, 0.00434633], [3, 0.0533333, -0.00434633]], [0.83292, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.83292, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.734744, [3, -0.0533333, 0.02301], [3, 0.0533333, -0.02301]], [0.69486, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.69486, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.69486, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.684122, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.684122, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.780764, [3, -0.0533333, 0], [3, 0, 0]]])

    names.append("LShoulderRoll")
    times.append([1.56, 1.72, 1.88, 2.04, 2.2, 2.36, 2.52, 2.68, 2.84, 3, 3.16])
    keys.append([[0.282214, [3, -0.533333, 0], [3, 0.0533333, 0]], [0.19631, [3, -0.0533333, 0.023777], [3, 0.0533333, -0.023777]], [0.139552, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.176368, [3, -0.0533333, -0.0140616], [3, 0.0533333, 0.0140616]], [0.223922, [3, -0.0533333, -0.0125277], [3, 0.0533333, 0.0125277]], [0.251534, [3, -0.0533333, -0.00639168], [3, 0.0533333, 0.00639168]], [0.262272, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.245398, [3, -0.0533333, 0.0120163], [3, 0.0533333, -0.0120163]], [0.190174, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.190174, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.190174, [3, -0.0533333, 0], [3, 0, 0]]])

    names.append("LWristYaw")
    times.append([1.56, 1.72, 1.88, 2.04, 2.2, 2.36, 2.52, 2.68, 2.84, 3, 3.16])
    keys.append([[-0.888228, [3, -0.533333, 0], [3, 0.0533333, 0]], [-0.871354, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.871354, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.921976, [3, -0.0533333, 0.016874], [3, 0.0533333, -0.016874]], [-0.972598, [3, -0.0533333, 0.0104823], [3, 0.0533333, -0.0104823]], [-0.98487, [3, -0.0533333, 0.00383496], [3, 0.0533333, -0.00383496]], [-0.995608, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.995608, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.920442, [3, -0.0533333, -0.0189193], [3, 0.0533333, 0.0189193]], [-0.882092, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.882092, [3, -0.0533333, 0], [3, 0, 0]]])

    names.append("RAnklePitch")
    times.append([1.56, 1.72, 1.88, 2.04, 2.2, 2.36, 2.52, 2.68, 2.84, 3, 3.16])
    keys.append([[0.63205, [3, -0.533333, 0], [3, 0.0533333, 0]], [0.63205, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.63205, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.63205, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.63205, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.63205, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.63205, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.63205, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.63205, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.63205, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.63205, [3, -0.0533333, 0], [3, 0, 0]]])

    names.append("RAnkleRoll")
    times.append([1.56, 1.72, 1.88, 2.04, 2.2, 2.36, 2.52, 2.68, 2.84, 3, 3.16])
    keys.append([[-0.27301, [3, -0.533333, 0], [3, 0.0533333, 0]], [-0.27301, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.27301, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.27301, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.27301, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.27301, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.27301, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.27301, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.27301, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.27301, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.27301, [3, -0.0533333, 0], [3, 0, 0]]])

    names.append("RElbowRoll")
    times.append([1.56, 1.72, 1.88, 2.04, 2.2, 2.36, 2.52, 2.68, 2.84, 3, 3.16])
    keys.append([[0.952656, [3, -0.533333, 0], [3, 0.0533333, 0]], [1.33462, [3, -0.0533333, -0.0879494], [3, 0.0533333, 0.0879494]], [1.48035, [3, -0.0533333, -0.0347707], [3, 0.0533333, 0.0347707]], [1.54325, [3, -0.0533333, 0], [3, 0.0533333, 0]], [1.54325, [3, -0.0533333, 0], [3, 0.0533333, 0]], [1.54325, [3, -0.0533333, 0], [3, 0.0533333, 0]], [1.442, [3, -0.0533333, 0.0641723], [3, 0.0533333, -0.0641723]], [1.15821, [3, -0.0533333, 0.0784896], [3, 0.0533333, -0.0784896]], [0.971064, [3, -0.0533333, 0.0416737], [3, 0.0533333, -0.0416737]], [0.90817, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.90817, [3, -0.0533333, 0], [3, 0, 0]]])

    names.append("RElbowYaw")
    times.append([1.56, 1.72, 1.88, 2.04, 2.2, 2.36, 2.52, 2.68, 2.84, 3, 3.16])
    keys.append([[0.366584, [3, -0.533333, 0], [3, 0.0533333, 0]], [0.53379, [3, -0.0533333, -0.027612], [3, 0.0533333, 0.027612]], [0.561402, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.547596, [3, -0.0533333, 0.0138061], [3, 0.0533333, -0.0138061]], [0.421808, [3, -0.0533333, 0.026845], [3, 0.0533333, -0.026845]], [0.386526, [3, -0.0533333, 0.0212203], [3, 0.0533333, -0.0212203]], [0.294486, [3, -0.0533333, 0.0138061], [3, 0.0533333, -0.0138061]], [0.28068, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.28068, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.315962, [3, -0.0533333, -0.0094597], [3, 0.0533333, 0.0094597]], [0.337438, [3, -0.0533333, 0], [3, 0, 0]]])

    names.append("RHand")
    times.append([1.56, 1.72, 1.88, 2.04, 2.2, 2.36, 2.52, 2.68, 2.84, 3, 3.16])
    keys.append([[0.3104, [3, -0.533333, 0], [3, 0.0533333, 0]], [0.3104, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.3104, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.3104, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.3104, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.3104, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.3104, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.3104, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.3104, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.3104, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.3104, [3, -0.0533333, 0], [3, 0, 0]]])

    names.append("RHipPitch")
    times.append([1.56, 1.72, 1.88, 2.04, 2.2, 2.36, 2.52, 2.68, 2.84, 3, 3.16])
    keys.append([[0.421808, [3, -0.533333, 0], [3, 0.0533333, 0]], [0.421808, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.421808, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.421808, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.421808, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.421808, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.421808, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.421808, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.421808, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.421808, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.421808, [3, -0.0533333, 0], [3, 0, 0]]])

    names.append("RHipRoll")
    times.append([1.56, 1.72, 1.88, 2.04, 2.2, 2.36, 2.52, 2.68, 2.84, 3, 3.16])
    keys.append([[-0.0705221, [3, -0.533333, 0], [3, 0.0533333, 0]], [-0.0705221, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.0705221, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.0705221, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.0705221, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.0705221, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.0705221, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.0705221, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.0705221, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.0705221, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.0705221, [3, -0.0533333, 0], [3, 0, 0]]])

    names.append("RHipYawPitch")
    times.append([1.56, 1.72, 1.88, 2.04, 2.2, 2.36, 2.52, 2.68, 2.84, 3, 3.16])
    keys.append([[-0.51845, [3, -0.533333, 0], [3, 0.0533333, 0]], [-0.51845, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.51845, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.51845, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.51845, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.51845, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.51845, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.51845, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.51845, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.51845, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.51845, [3, -0.0533333, 0], [3, 0, 0]]])

    names.append("RKneePitch")
    times.append([1.56, 1.72, 1.88, 2.04, 2.2, 2.36, 2.52, 2.68, 2.84, 3, 3.16])
    keys.append([[-0.0966001, [3, -0.533333, 0], [3, 0.0533333, 0]], [-0.0966001, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.0966001, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.0966001, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.0966001, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.0966001, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.0966001, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.0966001, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.0966001, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.0966001, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.0966001, [3, -0.0533333, 0], [3, 0, 0]]])

    names.append("RShoulderPitch")
    times.append([1.56, 1.72, 1.88, 2.04, 2.2, 2.36, 2.52, 2.68, 2.84, 3, 3.16])
    keys.append([[0.467912, [3, -0.533333, 0], [3, 0.0533333, 0]], [0.613642, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.613642, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.667332, [3, -0.0533333, -0.0224987], [3, 0.0533333, 0.0224987]], [0.748634, [3, -0.0533333, -0.025311], [3, 0.0533333, 0.025311]], [0.819198, [3, -0.0533333, -0.0107381], [3, 0.0533333, 0.0107381]], [0.829936, [3, -0.0533333, -0.00357934], [3, 0.0533333, 0.00357934]], [0.840674, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.840674, [3, -0.0533333, 0], [3, 0.0533333, 0]], [0.717954, [3, -0.0533333, 0.043719], [3, 0.0533333, -0.043719]], [0.57836, [3, -0.0533333, 0], [3, 0, 0]]])

    names.append("RShoulderRoll")
    times.append([1.56, 1.72, 1.88, 2.04, 2.2, 2.36, 2.52, 2.68, 2.84, 3, 3.16])
    keys.append([[-0.174918, [3, -0.533333, 0], [3, 0.0533333, 0]], [-0.331386, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.30224, [3, -0.0533333, -0.019175], [3, 0.0533333, 0.019175]], [-0.216336, [3, -0.0533333, -0.0196863], [3, 0.0533333, 0.0196863]], [-0.184122, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.214802, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.214802, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.130432, [3, -0.0533333, -0.0173854], [3, 0.0533333, 0.0173854]], [-0.11049, [3, -0.0533333, 0], [3, 0.0533333, 0]], [-0.121228, [3, -0.0533333, 0.00894837], [3, 0.0533333, -0.00894837]], [-0.16418, [3, -0.0533333, 0], [3, 0, 0]]])

    names.append("RWristYaw")
    times.append([1.56, 1.72, 1.88, 2.04, 2.2, 2.36, 2.52, 2.68, 2.84, 3, 3.16])
    keys.append([[1.14586, [3, -0.533333, 0], [3, 0.0533333, 0]], [1.333, [3, -0.0533333, 0], [3, 0.0533333, 0]], [1.30386, [3, -0.0533333, 0.029146], [3, 0.0533333, -0.029146]], [1.14586, [3, -0.0533333, 0.0334923], [3, 0.0533333, -0.0334923]], [1.1029, [3, -0.0533333, 0], [3, 0.0533333, 0]], [1.1029, [3, -0.0533333, 0], [3, 0.0533333, 0]], [1.1029, [3, -0.0533333, 0], [3, 0.0533333, 0]], [1.19648, [3, -0.0533333, -0.0189193], [3, 0.0533333, 0.0189193]], [1.21642, [3, -0.0533333, 0], [3, 0.0533333, 0]], [1.21642, [3, -0.0533333, 0], [3, 0.0533333, 0]], [1.21642, [3, -0.0533333, 0], [3, 0, 0]]])

    return names, times, keys