def __call__():
  names = list()
  times = list()
  keys = list()

  names.append("HeadPitch")
  times.append([0, 1.16, 2.76, 4.76, 6.36, 7.56, 8.76, 9.56])
  keys.append([[-0.153442, [3, -0.0133333, 0], [3, 0.386667, 0]], [0.0919981, [3, -0.386667, 0], [3, 0.533333, 0]], [-0.144238, [3, -0.533333, 0], [3, 0.666667, 0]], [0.262272, [3, -0.666667, -0.0968693], [3, 0.533333, 0.0774954]], [0.378856, [3, -0.533333, 0], [3, 0.4, 0]], [0.378856, [3, -0.4, 0], [3, 0.4, 0]], [0.378856, [3, -0.4, 0], [3, 0.266667, 0]], [0.378856, [3, -0.266667, 0], [3, 0, 0]]])

  names.append("HeadYaw")
  times.append([0, 1.16, 2.76, 4.76, 6.36, 7.56, 8.76, 9.56])
  keys.append([[-0.00924587, [3, -0.0133333, 0], [3, 0.386667, 0]], [-0.00924587, [3, -0.386667, 0], [3, 0.533333, 0]], [-0.069072, [3, -0.533333, 0.0143173], [3, 0.666667, -0.0178967]], [-0.105888, [3, -0.666667, 0], [3, 0.533333, 0]], [0.174834, [3, -0.533333, 0], [3, 0.4, 0]], [0.174834, [3, -0.4, 0], [3, 0.4, 0]], [0.182504, [3, -0.4, 0], [3, 0.266667, 0]], [0.182504, [3, -0.266667, 0], [3, 0, 0]]])

  names.append("LAnklePitch")
  times.append([0, 1.16, 2.76, 4.76, 6.36, 7.56, 8.76, 9.56])
  keys.append([[0.0873961, [3, -0.0133333, 0], [3, 0.386667, 0]], [-1.19963, [3, -0.386667, 0], [3, 0.533333, 0]], [0.492372, [3, -0.533333, 0], [3, 0.666667, 0]], [-0.191792, [3, -0.666667, 0], [3, 0.533333, 0]], [0.478566, [3, -0.533333, 0], [3, 0.4, 0]], [-0.852946, [3, -0.4, 0], [3, 0.4, 0]], [-0.0123138, [3, -0.4, 0], [3, 0.266667, 0]], [-0.0123138, [3, -0.266667, 0], [3, 0, 0]]])

  names.append("LAnkleRoll")
  times.append([0, 1.16, 2.76, 4.76, 6.36, 7.56, 8.76, 9.56])
  keys.append([[-0.110406, [3, -0.0133333, 0], [3, 0.386667, 0]], [0.0828779, [3, -0.386667, 0], [3, 0.533333, 0]], [-0.082794, [3, -0.533333, 0.0361342], [3, 0.666667, -0.0451678]], [-0.161028, [3, -0.666667, 0.0394863], [3, 0.533333, -0.0315891]], [-0.29602, [3, -0.533333, 0], [3, 0.4, 0]], [-0.164096, [3, -0.4, -0.0495993], [3, 0.4, 0.0495993]], [0.00157595, [3, -0.4, -0.0161068], [3, 0.266667, 0.0107379]], [0.0123138, [3, -0.266667, 0], [3, 0, 0]]])

  names.append("LElbowRoll")
  times.append([0, 1.16, 2.76, 4.76, 6.36, 7.56, 8.76, 9.56])
  keys.append([[-0.406468, [3, -0.0133333, 0], [3, 0.386667, 0]], [-1.03848, [3, -0.386667, 0], [3, 0.533333, 0]], [-0.865134, [3, -0.533333, 0], [3, 0.666667, 0]], [-1.4864, [3, -0.666667, 0], [3, 0.533333, 0]], [-1.43885, [3, -0.533333, 0], [3, 0.4, 0]], [-1.49561, [3, -0.4, 0.019175], [3, 0.4, -0.019175]], [-1.5539, [3, -0.4, 0], [3, 0.266667, 0]], [-1.5539, [3, -0.266667, 0], [3, 0, 0]]])

  names.append("LElbowYaw")
  times.append([0, 1.16, 2.76, 4.76, 6.36, 7.56, 8.76, 9.56])
  keys.append([[-1.19043, [3, -0.0133333, 0], [3, 0.386667, 0]], [-0.84681, [3, -0.386667, 0], [3, 0.533333, 0]], [-0.91584, [3, -0.533333, 0.0690303], [3, 0.666667, -0.0862878]], [-1.48189, [3, -0.666667, 0.140901], [3, 0.533333, -0.112721]], [-1.6767, [3, -0.533333, 0], [3, 0.4, 0]], [-1.60767, [3, -0.4, -0.0294016], [3, 0.4, 0.0294016]], [-1.50029, [3, -0.4, -0.0161076], [3, 0.266667, 0.0107384]], [-1.48956, [3, -0.266667, 0], [3, 0, 0]]])

  names.append("LHand")
  times.append([0, 1.16, 2.76, 4.76, 6.36, 7.56, 8.76, 9.56])
  keys.append([[0.3012, [3, -0.0133333, 0], [3, 0.386667, 0]], [0.0420001, [3, -0.386667, 0], [3, 0.533333, 0]], [0.0532, [3, -0.533333, 0], [3, 0.666667, 0]], [0.0532, [3, -0.666667, 0], [3, 0.533333, 0]], [0.0427999, [3, -0.533333, 0], [3, 0.4, 0]], [0.0427999, [3, -0.4, 0], [3, 0.4, 0]], [0.0427999, [3, -0.4, 0], [3, 0.266667, 0]], [0.0427999, [3, -0.266667, 0], [3, 0, 0]]])

  names.append("LHipPitch")
  times.append([0, 1.16, 2.76, 4.76, 6.36, 7.56, 8.76, 9.56])
  keys.append([[0.128898, [3, -0.0133333, 0], [3, 0.386667, 0]], [-0.693326, [3, -0.386667, 0.194922], [3, 0.533333, -0.268858]], [-1.26244, [3, -0.533333, 0], [3, 0.666667, 0]], [-1.26091, [3, -0.666667, 0], [3, 0.533333, 0]], [-1.54163, [3, -0.533333, 0], [3, 0.4, 0]], [-1.50481, [3, -0.4, -0.0368161], [3, 0.4, 0.0368161]], [-0.217786, [3, -0.4, 0], [3, 0.266667, 0]], [-0.228524, [3, -0.266667, 0], [3, 0, 0]]])

  names.append("LHipRoll")
  times.append([0, 1.16, 2.76, 4.76, 6.36, 7.56, 8.76, 9.56])
  keys.append([[0.11816, [3, -0.0133333, 0], [3, 0.386667, 0]], [-0.075124, [3, -0.386667, 0], [3, 0.533333, 0]], [0.167248, [3, -0.533333, 0], [3, 0.666667, 0]], [-0.325166, [3, -0.666667, 0], [3, 0.533333, 0]], [-0.325166, [3, -0.533333, 0], [3, 0.4, 0]], [-0.325166, [3, -0.4, 0], [3, 0.4, 0]], [0.00924587, [3, -0.4, 0], [3, 0.266667, 0]], [0.00924587, [3, -0.266667, 0], [3, 0, 0]]])

  names.append("LHipYawPitch")
  times.append([0, 1.16, 2.76, 4.76, 6.36, 7.56, 8.76, 9.56])
  keys.append([[-0.168698, [3, -0.0133333, 0], [3, 0.386667, 0]], [-0.233126, [3, -0.386667, 0.0644281], [3, 0.533333, -0.0888663]], [-1.04921, [3, -0.533333, 0.0208624], [3, 0.666667, -0.0260779]], [-1.07529, [3, -0.666667, 0], [3, 0.533333, 0]], [-0.898882, [3, -0.533333, 0], [3, 0.4, 0]], [-1.13052, [3, -0.4, 0], [3, 0.4, 0]], [0.0798099, [3, -0.4, 0], [3, 0.266667, 0]], [0.067538, [3, -0.266667, 0], [3, 0, 0]]])

  names.append("LKneePitch")
  times.append([0, 1.16, 2.76, 4.76, 6.36, 7.56, 8.76, 9.56])
  keys.append([[-0.092082, [3, -0.0133333, 0], [3, 0.386667, 0]], [2.13068, [3, -0.386667, 0], [3, 0.533333, 0]], [1.14892, [3, -0.533333, 0], [3, 0.666667, 0]], [1.30386, [3, -0.666667, 0], [3, 0.533333, 0]], [0.346642, [3, -0.533333, 0], [3, 0.4, 0]], [2.0954, [3, -0.4, 0], [3, 0.4, 0]], [0.079726, [3, -0.4, 0.0253107], [3, 0.266667, -0.0168738]], [0.0628521, [3, -0.266667, 0], [3, 0, 0]]])

  names.append("LShoulderPitch")
  times.append([0, 1.16, 2.76, 4.76, 6.36, 7.56, 8.76, 9.56])
  keys.append([[1.45419, [3, -0.0133333, 0], [3, 0.386667, 0]], [1.4097, [3, -0.386667, 0.044486], [3, 0.533333, -0.06136]], [1.00319, [3, -0.533333, 0.0897674], [3, 0.666667, -0.112209]], [0.803774, [3, -0.666667, 0.181239], [3, 0.533333, -0.144991]], [0.024502, [3, -0.533333, 0.0143172], [3, 0.4, -0.0107379]], [0.0137641, [3, -0.4, 0], [3, 0.4, 0]], [0.0168321, [3, -0.4, 0], [3, 0.266667, 0]], [0.0168321, [3, -0.266667, 0], [3, 0, 0]]])

  names.append("LShoulderRoll")
  times.append([0, 1.16, 2.76, 4.76, 6.36, 7.56, 8.76, 9.56])
  keys.append([[0.20398, [3, -0.0133333, 0], [3, 0.386667, 0]], [0.193242, [3, -0.386667, 0], [3, 0.533333, 0]], [0.661112, [3, -0.533333, 0], [3, 0.666667, 0]], [0.0459781, [3, -0.666667, 0.0709474], [3, 0.533333, -0.0567579]], [-0.0107799, [3, -0.533333, 0.0151939], [3, 0.4, -0.0113954]], [-0.0337899, [3, -0.4, 0.0148287], [3, 0.4, -0.0148287]], [-0.0997519, [3, -0.4, 0], [3, 0.266667, 0]], [-0.0997519, [3, -0.266667, 0], [3, 0, 0]]])

  names.append("LWristYaw")
  times.append([0, 1.16, 2.76, 4.76, 6.36, 7.56, 8.76, 9.56])
  keys.append([[0.115008, [3, -0.0133333, 0], [3, 0.386667, 0]], [0.115008, [3, -0.386667, 0], [3, 0.533333, 0]], [-1.126, [3, -0.533333, 0], [3, 0.666667, 0]], [-1.02322, [3, -0.666667, 0], [3, 0.533333, 0]], [-1.45274, [3, -0.533333, 0], [3, 0.4, 0]], [-1.39752, [3, -0.4, -0.0209647], [3, 0.4, 0.0209647]], [-1.32695, [3, -0.4, -0.0190215], [3, 0.266667, 0.012681]], [-1.30241, [3, -0.266667, 0], [3, 0, 0]]])

  names.append("RAnklePitch")
  times.append([0, 1.16, 2.76, 4.76, 6.36, 7.56, 8.76, 9.56])
  keys.append([[0.096684, [3, -0.0133333, 0], [3, 0.386667, 0]], [-1.1704, [3, -0.386667, 0.014458], [3, 0.533333, -0.019942]], [-1.19034, [3, -0.533333, 0], [3, 0.666667, 0]], [-1.10751, [3, -0.666667, 0], [3, 0.533333, 0]], [-1.18267, [3, -0.533333, 0], [3, 0.4, 0]], [-1.05995, [3, -0.4, -0.12272], [3, 0.4, 0.12272]], [0.136568, [3, -0.4, 0], [3, 0.266667, 0]], [0.136568, [3, -0.266667, 0], [3, 0, 0]]])

  names.append("RAnkleRoll")
  times.append([0, 1.16, 2.76, 4.76, 6.36, 7.56, 8.76, 9.56])
  keys.append([[0.112024, [3, -0.0133333, 0], [3, 0.386667, 0]], [-0.07359, [3, -0.386667, 0], [3, 0.533333, 0]], [-0.0444441, [3, -0.533333, -0.0095449], [3, 0.666667, 0.0119311]], [-0.00916195, [3, -0.666667, -0.0198852], [3, 0.533333, 0.0159082]], [0.0629361, [3, -0.533333, -0.0216221], [3, 0.4, 0.0162166]], [0.104354, [3, -0.4, -0.013806], [3, 0.4, 0.013806]], [0.145772, [3, -0.4, -0.0104312], [3, 0.266667, 0.00695413]], [0.15651, [3, -0.266667, 0], [3, 0, 0]]])

  names.append("RElbowRoll")
  times.append([0, 1.16, 2.76, 4.76, 6.36, 7.56, 8.76, 9.56])
  keys.append([[0.406552, [3, -0.0133333, 0], [3, 0.386667, 0]], [1.04009, [3, -0.386667, 0], [3, 0.533333, 0]], [0.285366, [3, -0.533333, 0], [3, 0.666667, 0]], [0.721022, [3, -0.666667, -0.218169], [3, 0.533333, 0.174535]], [1.46348, [3, -0.533333, -0.0715867], [3, 0.4, 0.05369]], [1.51717, [3, -0.4, -0.0135503], [3, 0.4, 0.0135503]], [1.54478, [3, -0.4, 0], [3, 0.266667, 0]], [1.54478, [3, -0.266667, 0], [3, 0, 0]]])

  names.append("RElbowYaw")
  times.append([0, 1.16, 2.76, 4.76, 6.36, 7.56, 8.76, 9.56])
  keys.append([[1.18727, [3, -0.0133333, 0], [3, 0.386667, 0]], [0.835988, [3, -0.386667, 0.0696303], [3, 0.533333, -0.0960418]], [0.690258, [3, -0.533333, 0], [3, 0.666667, 0]], [1.84229, [3, -0.666667, 0], [3, 0.533333, 0]], [1.73798, [3, -0.533333, 0.0268815], [3, 0.4, -0.0201611]], [1.70116, [3, -0.4, 0.0217317], [3, 0.4, -0.0217317]], [1.60759, [3, -0.4, 0.0263848], [3, 0.266667, -0.0175899]], [1.56924, [3, -0.266667, 0], [3, 0, 0]]])

  names.append("RHand")
  times.append([0, 1.16, 2.76, 4.76, 6.36, 7.56, 8.76, 9.56])
  keys.append([[0.2984, [3, -0.0133333, 0], [3, 0.386667, 0]], [0.04, [3, -0.386667, 0], [3, 0.533333, 0]], [0.0508, [3, -0.533333, 0], [3, 0.666667, 0]], [0.0404, [3, -0.666667, 0], [3, 0.533333, 0]], [0.0404, [3, -0.533333, 0], [3, 0.4, 0]], [0.0724, [3, -0.4, -0.0138667], [3, 0.4, 0.0138667]], [0.1236, [3, -0.4, -0.016], [3, 0.266667, 0.0106667]], [0.1524, [3, -0.266667, 0], [3, 0, 0]]])

  names.append("RHipPitch")
  times.append([0, 1.16, 2.76, 4.76, 6.36, 7.56, 8.76, 9.56])
  keys.append([[0.12728, [3, -0.0133333, 0], [3, 0.386667, 0]], [-0.696478, [3, -0.386667, 0], [3, 0.533333, 0]], [-0.437232, [3, -0.533333, 0], [3, 0.666667, 0]], [-1.33155, [3, -0.666667, 0.203113], [3, 0.533333, -0.16249]], [-1.53404, [3, -0.533333, 0], [3, 0.4, 0]], [-1.5233, [3, -0.4, -0.0107379], [3, 0.4, 0.0107379]], [-0.205598, [3, -0.4, 0], [3, 0.266667, 0]], [-0.21787, [3, -0.266667, 0], [3, 0, 0]]])

  names.append("RHipRoll")
  times.append([0, 1.16, 2.76, 4.76, 6.36, 7.56, 8.76, 9.56])
  keys.append([[-0.122678, [3, -0.0133333, 0], [3, 0.386667, 0]], [0.066004, [3, -0.386667, -0.0358897], [3, 0.533333, 0.049503]], [0.1335, [3, -0.533333, -0.0286347], [3, 0.666667, 0.0357933]], [0.259288, [3, -0.666667, -0.0363615], [3, 0.533333, 0.0290892]], [0.329852, [3, -0.533333, 0], [3, 0.4, 0]], [0.329852, [3, -0.4, 0], [3, 0.4, 0]], [-0.0674541, [3, -0.4, 0], [3, 0.266667, 0]], [-0.0674541, [3, -0.266667, 0], [3, 0, 0]]])

  names.append("RHipYawPitch")
  times.append([0, 1.16, 2.76, 4.76, 6.36, 7.56, 8.76, 9.56])
  keys.append([[-0.168698, [3, -0.0133333, 0], [3, 0.386667, 0]], [-0.233126, [3, -0.386667, 0.0644281], [3, 0.533333, -0.0888663]], [-1.04921, [3, -0.533333, 0.0208624], [3, 0.666667, -0.0260779]], [-1.07529, [3, -0.666667, 0], [3, 0.533333, 0]], [-0.898882, [3, -0.533333, 0], [3, 0.4, 0]], [-1.13052, [3, -0.4, 0], [3, 0.4, 0]], [0.0798099, [3, -0.4, 0], [3, 0.266667, 0]], [0.067538, [3, -0.266667, 0], [3, 0, 0]]])

  names.append("RKneePitch")
  times.append([0, 1.16, 2.76, 4.76, 6.36, 7.56, 8.76, 9.56])
  keys.append([[-0.0904641, [3, -0.0133333, 0], [3, 0.386667, 0]], [2.12003, [3, -0.386667, 0], [3, 0.533333, 0]], [2.1185, [3, -0.533333, 0.00153398], [3, 0.666667, -0.00191748]], [2.10776, [3, -0.666667, 0.0107379], [3, 0.533333, -0.00859029]], [1.83624, [3, -0.533333, 0], [3, 0.4, 0]], [2.10622, [3, -0.4, 0], [3, 0.4, 0]], [0.0353239, [3, -0.4, 0.0161068], [3, 0.266667, -0.0107379]], [0.024586, [3, -0.266667, 0], [3, 0, 0]]])

  names.append("RShoulderPitch")
  times.append([0, 1.16, 2.76, 4.76, 6.36, 7.56, 8.76, 9.56])
  keys.append([[1.45581, [3, -0.0133333, 0], [3, 0.386667, 0]], [1.40519, [3, -0.386667, 0.050622], [3, 0.533333, -0.0698235]], [0.975666, [3, -0.533333, 0.186807], [3, 0.666667, -0.233509]], [0.144238, [3, -0.666667, 0.238622], [3, 0.533333, -0.190898]], [-0.312894, [3, -0.533333, 0], [3, 0.4, 0]], [-0.130348, [3, -0.4, -0.0534343], [3, 0.4, 0.0534343]], [0.00771189, [3, -0.4, 0], [3, 0.266667, 0]], [0.00771189, [3, -0.266667, 0], [3, 0, 0]]])

  names.append("RShoulderRoll")
  times.append([0, 1.16, 2.76, 4.76, 6.36, 7.56, 8.76, 9.56])
  keys.append([[-0.211734, [3, -0.0133333, 0], [3, 0.386667, 0]], [-0.200996, [3, -0.386667, -0.00945597], [3, 0.533333, 0.0130427]], [-0.144238, [3, -0.533333, -0.0247713], [3, 0.666667, 0.0309641]], [-0.0337899, [3, -0.666667, 0], [3, 0.533333, 0]], [-0.07214, [3, -0.533333, 0], [3, 0.4, 0]], [0.00455999, [3, -0.4, -0.0388614], [3, 0.4, 0.0388614]], [0.161028, [3, -0.4, -0.0426452], [3, 0.266667, 0.0284301]], [0.217786, [3, -0.266667, 0], [3, 0, 0]]])

  names.append("RWristYaw")
  times.append([0, 1.16, 2.76, 4.76, 6.36, 7.56, 8.76, 9.56])
  keys.append([[0.115008, [3, -0.0133333, 0], [3, 0.386667, 0]], [-0.0782759, [3, -0.386667, 0], [3, 0.533333, 0]], [1.37902, [3, -0.533333, 0], [3, 0.666667, 0]], [-0.191792, [3, -0.666667, 0], [3, 0.533333, 0]], [-0.0997519, [3, -0.533333, -0.0920401], [3, 0.4, 0.06903]], [0.34204, [3, -0.4, -0.196352], [3, 0.4, 0.196352]], [1.07836, [3, -0.4, -0.20034], [3, 0.266667, 0.13356]], [1.34374, [3, -0.266667, 0], [3, 0, 0]]])

  return names, times, keys