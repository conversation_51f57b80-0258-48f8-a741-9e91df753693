<?xml version="1.0" encoding="UTF-8" ?>
<ChoregrapheProject xmlns="http://www.ald.softbankrobotics.com/schema/choregraphe/project.xsd" xar_version="3">
  <Box name="root" id="-1" localization="8" tooltip="Root box of Choregraphe&apos;s behavior. Highest level possible." x="0" y="0">
    <bitmap>media/images/box/root.png</bitmap>
    <script language="4">
      <content>
        <![CDATA[]]>
      </content>
    </script>
    <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
    <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
    <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
    <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
    <Timeline enable="0">
      <BehaviorLayer name="behavior_layer1">
        <BehaviorKeyframe name="keyframe1" index="1">
          <Diagram>
            <Box name="VierFuss_Init" id="2" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="1173" y="141">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="420">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="7" value="-7.64898" />
                    <Key frame="25" value="-8.35212" />
                    <Key frame="50" value="-2.99072" />
                    <Key frame="75" value="-2.99072" />
                    <Key frame="100" value="-2.99072" />
                    <Key frame="125" value="-0.0902951" />
                    <Key frame="150" value="-0.0902951" />
                    <Key frame="175" value="1.93121" />
                    <Key frame="200" value="2.72224" />
                    <Key frame="225" value="2.72224" />
                    <Key frame="250" value="-3.07861" />
                    <Key frame="275" value="28.7382" />
                    <Key frame="335" value="26.8925" />
                    <Key frame="420" value="26.8925" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="7" value="-0.881327" />
                    <Key frame="25" value="-0.881327" />
                    <Key frame="50" value="-0.881327" />
                    <Key frame="75" value="-0.881327" />
                    <Key frame="100" value="-0.881327" />
                    <Key frame="125" value="-0.881327" />
                    <Key frame="150" value="-0.881327" />
                    <Key frame="175" value="-0.881327" />
                    <Key frame="200" value="-0.881327" />
                    <Key frame="225" value="-0.881327" />
                    <Key frame="250" value="-0.266077" />
                    <Key frame="275" value="4.39218" />
                    <Key frame="335" value="4.39218" />
                    <Key frame="420" value="4.39218" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="7" value="2.54646" />
                    <Key frame="25" value="4.65585" />
                    <Key frame="50" value="13.7966" />
                    <Key frame="75" value="15.2029" />
                    <Key frame="100" value="28.7382" />
                    <Key frame="125" value="36.209" />
                    <Key frame="150" value="33.3965" />
                    <Key frame="175" value="10.8083" />
                    <Key frame="200" value="-9.40682" />
                    <Key frame="225" value="-44.8272" />
                    <Key frame="250" value="-66.3607" />
                    <Key frame="275" value="-68.9095" />
                    <Key frame="335" value="-68.1185" />
                    <Key frame="420" value="53.3479" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="7" value="-5.97423" />
                    <Key frame="25" value="-5.35899" />
                    <Key frame="50" value="-14.2361" />
                    <Key frame="75" value="-15.4665" />
                    <Key frame="100" value="-16.697" />
                    <Key frame="125" value="-11.9509" />
                    <Key frame="150" value="-12.2146" />
                    <Key frame="175" value="-22.7616" />
                    <Key frame="200" value="-22.7616" />
                    <Key frame="225" value="-5.53478" />
                    <Key frame="250" value="-3.60116" />
                    <Key frame="275" value="-2.1949" />
                    <Key frame="335" value="-2.98591" />
                    <Key frame="420" value="0.0902951" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="7" value="-28.0351" />
                    <Key frame="25" value="-32.6054" />
                    <Key frame="50" value="-22.3221" />
                    <Key frame="75" value="-22.3221" />
                    <Key frame="100" value="-22.3221" />
                    <Key frame="125" value="-26.9804" />
                    <Key frame="150" value="-27.7714" />
                    <Key frame="175" value="-24.7831" />
                    <Key frame="200" value="-24.1678" />
                    <Key frame="225" value="-3.86484" />
                    <Key frame="250" value="-4.48007" />
                    <Key frame="275" value="-3.42537" />
                    <Key frame="335" value="-0.876518" />
                    <Key frame="420" value="-2.107" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="7" value="-66.0091" />
                    <Key frame="25" value="-67.8548" />
                    <Key frame="50" value="-68.4701" />
                    <Key frame="75" value="-68.4701" />
                    <Key frame="100" value="-68.4701" />
                    <Key frame="125" value="-68.5579" />
                    <Key frame="150" value="-69.6127" />
                    <Key frame="175" value="-68.8216" />
                    <Key frame="200" value="-68.8216" />
                    <Key frame="225" value="-37.1806" />
                    <Key frame="250" value="19.2459" />
                    <Key frame="275" value="-71.8979" />
                    <Key frame="335" value="-73.0404" />
                    <Key frame="420" value="-73.6557" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="7" value="0.2752" />
                    <Key frame="25" value="0.3172" />
                    <Key frame="50" value="0.292" />
                    <Key frame="75" value="0.292" />
                    <Key frame="100" value="0.292" />
                    <Key frame="125" value="0.292" />
                    <Key frame="150" value="0.292" />
                    <Key frame="175" value="0.292" />
                    <Key frame="200" value="0.292" />
                    <Key frame="225" value="0.292" />
                    <Key frame="250" value="0.292" />
                    <Key frame="275" value="0.0372" />
                    <Key frame="335" value="0.044" />
                    <Key frame="420" value="0.044" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="7" value="5.45169" />
                    <Key frame="25" value="10.3736" />
                    <Key frame="50" value="-3.51326" />
                    <Key frame="75" value="-6.32579" />
                    <Key frame="100" value="-45.7013" />
                    <Key frame="125" value="-80.6822" />
                    <Key frame="150" value="-79.1002" />
                    <Key frame="175" value="-77.1665" />
                    <Key frame="200" value="-60.1155" />
                    <Key frame="225" value="-26.5409" />
                    <Key frame="250" value="13.8893" />
                    <Key frame="275" value="-23.5526" />
                    <Key frame="335" value="-39.8125" />
                    <Key frame="420" value="-44.295" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="7" value="5.71537" />
                    <Key frame="25" value="5.18802" />
                    <Key frame="50" value="14.5924" />
                    <Key frame="75" value="18.4597" />
                    <Key frame="100" value="43.1572" />
                    <Key frame="125" value="43.7725" />
                    <Key frame="150" value="35.4228" />
                    <Key frame="175" value="2.1997" />
                    <Key frame="200" value="3.43018" />
                    <Key frame="225" value="4.66066" />
                    <Key frame="250" value="1.40867" />
                    <Key frame="275" value="3.25439" />
                    <Key frame="335" value="2.28759" />
                    <Key frame="420" value="5.10013" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="7" value="-10.193" />
                    <Key frame="25" value="-8.96255" />
                    <Key frame="50" value="-31.0234" />
                    <Key frame="75" value="-31.0234" />
                    <Key frame="100" value="-35.1543" />
                    <Key frame="125" value="-49.7443" />
                    <Key frame="150" value="-51.59" />
                    <Key frame="175" value="-31.6386" />
                    <Key frame="200" value="-32.2539" />
                    <Key frame="225" value="-6.15001" />
                    <Key frame="250" value="-11.6872" />
                    <Key frame="275" value="1.58445" />
                    <Key frame="335" value="-9.75358" />
                    <Key frame="420" value="0.881327" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="7" value="-0.881327" />
                    <Key frame="25" value="-5.3638" />
                    <Key frame="50" value="-0.969218" />
                    <Key frame="75" value="0.964409" />
                    <Key frame="100" value="9.13833" />
                    <Key frame="125" value="10.6325" />
                    <Key frame="150" value="11.863" />
                    <Key frame="175" value="9.84147" />
                    <Key frame="200" value="10.4567" />
                    <Key frame="225" value="4.91954" />
                    <Key frame="250" value="8.61098" />
                    <Key frame="275" value="30.1445" />
                    <Key frame="335" value="116.542" />
                    <Key frame="420" value="69.7836" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="7" value="81.649" />
                    <Key frame="25" value="86.3952" />
                    <Key frame="50" value="26.453" />
                    <Key frame="75" value="26.453" />
                    <Key frame="100" value="25.8378" />
                    <Key frame="125" value="20.4764" />
                    <Key frame="150" value="15.5544" />
                    <Key frame="175" value="16.8728" />
                    <Key frame="200" value="18.2791" />
                    <Key frame="225" value="16.8728" />
                    <Key frame="250" value="16.2576" />
                    <Key frame="275" value="11.1598" />
                    <Key frame="335" value="18.6306" />
                    <Key frame="420" value="16.6091" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="7" value="10.8083" />
                    <Key frame="25" value="10.8083" />
                    <Key frame="50" value="8.52309" />
                    <Key frame="75" value="7.81997" />
                    <Key frame="100" value="7.81997" />
                    <Key frame="125" value="5.44688" />
                    <Key frame="150" value="10.8083" />
                    <Key frame="175" value="8.61098" />
                    <Key frame="200" value="7.99575" />
                    <Key frame="225" value="11.072" />
                    <Key frame="250" value="8.96255" />
                    <Key frame="275" value="-7.12163" />
                    <Key frame="335" value="-7.56109" />
                    <Key frame="420" value="-8.79157" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="7" value="8.25942" />
                    <Key frame="25" value="7.46839" />
                    <Key frame="50" value="7.46839" />
                    <Key frame="75" value="6.85315" />
                    <Key frame="100" value="6.85315" />
                    <Key frame="125" value="6.06212" />
                    <Key frame="150" value="4.91954" />
                    <Key frame="175" value="4.91954" />
                    <Key frame="200" value="4.91954" />
                    <Key frame="225" value="33.9238" />
                    <Key frame="250" value="-91.0582" />
                    <Key frame="275" value="-36.5654" />
                    <Key frame="335" value="-37.1806" />
                    <Key frame="420" value="-37.7958" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="7" value="2.37549" />
                    <Key frame="25" value="4.48488" />
                    <Key frame="50" value="-1.84332" />
                    <Key frame="75" value="-25.3104" />
                    <Key frame="100" value="-29.4413" />
                    <Key frame="125" value="-28.914" />
                    <Key frame="150" value="-40.3399" />
                    <Key frame="175" value="-24.2557" />
                    <Key frame="200" value="-14.8513" />
                    <Key frame="225" value="-40.7794" />
                    <Key frame="250" value="-66.1801" />
                    <Key frame="275" value="-54.6663" />
                    <Key frame="335" value="-67.4106" />
                    <Key frame="420" value="54.0558" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="7" value="5.80326" />
                    <Key frame="25" value="6.5064" />
                    <Key frame="50" value="0.705531" />
                    <Key frame="75" value="-4.2164" />
                    <Key frame="100" value="13.1862" />
                    <Key frame="125" value="13.1862" />
                    <Key frame="150" value="20.7449" />
                    <Key frame="175" value="17.0534" />
                    <Key frame="200" value="18.2839" />
                    <Key frame="225" value="3.78175" />
                    <Key frame="250" value="3.1665" />
                    <Key frame="275" value="-1.49175" />
                    <Key frame="335" value="-1.14019" />
                    <Key frame="420" value="0.353968" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="7" value="27.8641" />
                    <Key frame="25" value="28.6551" />
                    <Key frame="50" value="23.8211" />
                    <Key frame="75" value="23.2058" />
                    <Key frame="100" value="23.2058" />
                    <Key frame="125" value="24.6121" />
                    <Key frame="150" value="22.7664" />
                    <Key frame="175" value="23.4695" />
                    <Key frame="200" value="23.4695" />
                    <Key frame="225" value="1.49656" />
                    <Key frame="250" value="1.49656" />
                    <Key frame="275" value="7.4732" />
                    <Key frame="335" value="4.39699" />
                    <Key frame="420" value="5.18802" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="7" value="66.268" />
                    <Key frame="25" value="73.7388" />
                    <Key frame="50" value="74.4419" />
                    <Key frame="75" value="74.4419" />
                    <Key frame="100" value="74.4419" />
                    <Key frame="125" value="71.2778" />
                    <Key frame="150" value="71.4536" />
                    <Key frame="175" value="72.7719" />
                    <Key frame="200" value="73.3872" />
                    <Key frame="225" value="18.8064" />
                    <Key frame="250" value="7.11683" />
                    <Key frame="275" value="74.2661" />
                    <Key frame="335" value="74.7056" />
                    <Key frame="420" value="74.0903" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="7" value="0.2752" />
                    <Key frame="25" value="0.3548" />
                    <Key frame="50" value="0.328" />
                    <Key frame="75" value="0.328" />
                    <Key frame="100" value="0.328" />
                    <Key frame="125" value="0.328" />
                    <Key frame="150" value="0.328" />
                    <Key frame="175" value="0.328" />
                    <Key frame="200" value="0.328" />
                    <Key frame="225" value="0.328" />
                    <Key frame="250" value="0.3068" />
                    <Key frame="275" value="0.0332" />
                    <Key frame="335" value="0.0424" />
                    <Key frame="420" value="0.0424" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="7" value="5.2711" />
                    <Key frame="25" value="8.96255" />
                    <Key frame="50" value="-12.3073" />
                    <Key frame="75" value="-34.7196" />
                    <Key frame="100" value="-36.038" />
                    <Key frame="125" value="-34.9833" />
                    <Key frame="150" value="-81.3023" />
                    <Key frame="175" value="-42.542" />
                    <Key frame="200" value="-33.3134" />
                    <Key frame="225" value="-23.2058" />
                    <Key frame="250" value="15.2908" />
                    <Key frame="275" value="-5.53958" />
                    <Key frame="335" value="-36.9169" />
                    <Key frame="420" value="-37.0048" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="7" value="-5.2711" />
                    <Key frame="25" value="-6.58948" />
                    <Key frame="50" value="1.145" />
                    <Key frame="75" value="1.76024" />
                    <Key frame="100" value="3.60597" />
                    <Key frame="125" value="3.1665" />
                    <Key frame="150" value="1.40867" />
                    <Key frame="175" value="5.27591" />
                    <Key frame="200" value="3.43018" />
                    <Key frame="225" value="-3.16169" />
                    <Key frame="250" value="0.969218" />
                    <Key frame="275" value="7.4732" />
                    <Key frame="335" value="8.08845" />
                    <Key frame="420" value="8.08845" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="7" value="-10.193" />
                    <Key frame="25" value="-8.96255" />
                    <Key frame="50" value="-31.0234" />
                    <Key frame="75" value="-31.0234" />
                    <Key frame="100" value="-35.1543" />
                    <Key frame="125" value="-49.7443" />
                    <Key frame="150" value="-51.59" />
                    <Key frame="175" value="-31.6386" />
                    <Key frame="200" value="-32.2539" />
                    <Key frame="225" value="-6.15001" />
                    <Key frame="250" value="-11.6872" />
                    <Key frame="275" value="1.58445" />
                    <Key frame="335" value="-9.75358" />
                    <Key frame="420" value="0.881327" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="7" value="-1.0523" />
                    <Key frame="25" value="-5.44688" />
                    <Key frame="50" value="24.6121" />
                    <Key frame="75" value="64.8665" />
                    <Key frame="100" value="70.931" />
                    <Key frame="125" value="72.3373" />
                    <Key frame="150" value="106.967" />
                    <Key frame="175" value="6.85796" />
                    <Key frame="200" value="-5.44688" />
                    <Key frame="225" value="-4.12851" />
                    <Key frame="250" value="6.68218" />
                    <Key frame="275" value="-1.57965" />
                    <Key frame="335" value="114.613" />
                    <Key frame="420" value="61.0872" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="7" value="81.6538" />
                    <Key frame="25" value="85.2574" />
                    <Key frame="50" value="22.6785" />
                    <Key frame="75" value="22.6785" />
                    <Key frame="100" value="22.6785" />
                    <Key frame="125" value="20.2175" />
                    <Key frame="150" value="18.8991" />
                    <Key frame="175" value="18.2839" />
                    <Key frame="200" value="18.8991" />
                    <Key frame="225" value="19.5144" />
                    <Key frame="250" value="26.5457" />
                    <Key frame="275" value="16.5261" />
                    <Key frame="335" value="19.778" />
                    <Key frame="420" value="18.3718" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="7" value="-10.3736" />
                    <Key frame="25" value="-9.23104" />
                    <Key frame="50" value="-4.74855" />
                    <Key frame="75" value="-4.74855" />
                    <Key frame="100" value="-4.74855" />
                    <Key frame="125" value="-3.60597" />
                    <Key frame="150" value="-4.04543" />
                    <Key frame="175" value="-9.49471" />
                    <Key frame="200" value="-9.49471" />
                    <Key frame="225" value="-5.71537" />
                    <Key frame="250" value="-3.86964" />
                    <Key frame="275" value="-1.23289" />
                    <Key frame="335" value="1.66754" />
                    <Key frame="420" value="0.43705" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="7" value="2.37068" />
                    <Key frame="25" value="6.50159" />
                    <Key frame="50" value="18.2791" />
                    <Key frame="75" value="18.2791" />
                    <Key frame="100" value="18.2791" />
                    <Key frame="125" value="20.9158" />
                    <Key frame="150" value="19.949" />
                    <Key frame="175" value="17.3123" />
                    <Key frame="200" value="17.3123" />
                    <Key frame="225" value="26.0135" />
                    <Key frame="250" value="83.0553" />
                    <Key frame="275" value="18.8064" />
                    <Key frame="335" value="18.2791" />
                    <Key frame="420" value="19.3338" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="VierFussKickLinks_Repeat" id="1" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="385" y="373">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="60">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="nao">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="30" value="26.7167" />
                    <Key frame="60" value="28.2108" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="30" value="2.81013" />
                    <Key frame="60" value="2.81013" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="30" value="52.7326" />
                    <Key frame="60" value="52.7326" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="30" value="0.266077" />
                    <Key frame="60" value="0.266077" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="30" value="-0.0854867" />
                    <Key frame="60" value="-1.22808" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="30" value="-72.3373" />
                    <Key frame="60" value="-72.3373" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="30" value="0.046" />
                    <Key frame="60" value="0.046" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="30" value="27.3367" />
                    <Key frame="60" value="-11.2477" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="30" value="4.83644" />
                    <Key frame="60" value="4.83644" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="30" value="3.07861" />
                    <Key frame="60" value="3.95753" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="30" value="69.52" />
                    <Key frame="60" value="69.52" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="30" value="18.9822" />
                    <Key frame="60" value="17.7517" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="30" value="-2.11181" />
                    <Key frame="60" value="-0.00240423" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="30" value="-35.6864" />
                    <Key frame="60" value="-35.0712" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="30" value="53.3527" />
                    <Key frame="60" value="53.3527" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="30" value="0.00240423" />
                    <Key frame="60" value="0.00240423" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="30" value="2.28759" />
                    <Key frame="60" value="2.90283" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="30" value="73.7388" />
                    <Key frame="60" value="73.7388" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="30" value="0.0448" />
                    <Key frame="60" value="0.0448" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="30" value="-36.5654" />
                    <Key frame="60" value="-36.5654" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="30" value="8.08845" />
                    <Key frame="60" value="8.08845" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="30" value="3.07861" />
                    <Key frame="60" value="3.95753" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="30" value="60.6477" />
                    <Key frame="60" value="60.6477" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="30" value="22.5027" />
                    <Key frame="60" value="21.5359" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="30" value="-5.97904" />
                    <Key frame="60" value="-6.41851" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="30" value="18.2791" />
                    <Key frame="60" value="18.2791" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="VierFussKickRechts_Repeat" id="3" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="551" y="383">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="60">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="nao">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="30" value="32.957" />
                    <Key frame="60" value="32.957" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="30" value="2.81013" />
                    <Key frame="60" value="2.81013" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="30" value="52.7326" />
                    <Key frame="60" value="52.7326" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="30" value="0.266077" />
                    <Key frame="60" value="0.266077" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="30" value="-1.14019" />
                    <Key frame="60" value="-1.14019" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="30" value="-72.6889" />
                    <Key frame="60" value="-72.6889" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="30" value="0.046" />
                    <Key frame="60" value="0.046" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="30" value="-36.3848" />
                    <Key frame="60" value="-35.7695" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="30" value="-8.25942" />
                    <Key frame="60" value="-8.25942" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="30" value="2.81494" />
                    <Key frame="60" value="3.51807" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="30" value="61.346" />
                    <Key frame="60" value="60.6429" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="30" value="16.6091" />
                    <Key frame="60" value="17.2244" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="30" value="-2.81494" />
                    <Key frame="60" value="-2.81494" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="30" value="-31.7313" />
                    <Key frame="60" value="-32.9618" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="30" value="52.0343" />
                    <Key frame="60" value="52.0343" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="30" value="0.00240423" />
                    <Key frame="60" value="0.00240423" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="30" value="4.04543" />
                    <Key frame="60" value="4.04543" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="30" value="73.1235" />
                    <Key frame="60" value="73.1235" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="30" value="0.0448" />
                    <Key frame="60" value="0.0448" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="30" value="26.9804" />
                    <Key frame="60" value="-10.901" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="30" value="-5.2711" />
                    <Key frame="60" value="-5.2711" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="30" value="2.81494" />
                    <Key frame="60" value="3.51807" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="30" value="69.1732" />
                    <Key frame="60" value="69.1732" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="30" value="18.0202" />
                    <Key frame="60" value="17.3171" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="30" value="-6.24271" />
                    <Key frame="60" value="-7.03374" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="30" value="15.5544" />
                    <Key frame="60" value="15.5544" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="VierFussStreckenLinks_Repeat" id="4" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="1467" y="158">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="60">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="30" value="28.2108" />
                    <Key frame="60" value="33.6601" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="30" value="2.81013" />
                    <Key frame="60" value="2.81013" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="30" value="52.7326" />
                    <Key frame="60" value="52.0295" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="30" value="0.266077" />
                    <Key frame="60" value="0.266077" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="30" value="-1.22808" />
                    <Key frame="60" value="-2.98591" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="30" value="-72.3373" />
                    <Key frame="60" value="-72.6889" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="30" value="0.046" />
                    <Key frame="60" value="0.046" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="30" value="-11.2477" />
                    <Key frame="60" value="27.3367" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="30" value="4.83644" />
                    <Key frame="60" value="4.83644" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="30" value="3.95753" />
                    <Key frame="60" value="5.10013" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="30" value="69.52" />
                    <Key frame="60" value="-3.60597" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="30" value="17.7517" />
                    <Key frame="60" value="14.6755" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="30" value="-0.00240423" />
                    <Key frame="60" value="-0.969218" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="30" value="-35.0712" />
                    <Key frame="60" value="-33.0497" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="30" value="53.3527" />
                    <Key frame="60" value="53.4406" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="30" value="0.00240423" />
                    <Key frame="60" value="0.00240423" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="30" value="2.90283" />
                    <Key frame="60" value="4.04543" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="30" value="73.7388" />
                    <Key frame="60" value="73.1235" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="30" value="0.0448" />
                    <Key frame="60" value="0.0448" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="30" value="-36.5654" />
                    <Key frame="60" value="-36.7411" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="30" value="8.08845" />
                    <Key frame="60" value="7.91266" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="30" value="3.95753" />
                    <Key frame="60" value="5.10013" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="30" value="60.6477" />
                    <Key frame="60" value="60.384" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="30" value="21.5359" />
                    <Key frame="60" value="20.3933" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="30" value="-6.41851" />
                    <Key frame="60" value="-7.73688" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="30" value="18.2791" />
                    <Key frame="60" value="17.5759" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="VierFussStreckenRechts_Repeat" id="5" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="1314" y="150">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="60">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="30" value="32.957" />
                    <Key frame="60" value="29.5292" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="30" value="2.81013" />
                    <Key frame="60" value="3.16169" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="30" value="52.7326" />
                    <Key frame="60" value="52.7326" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="30" value="0.266077" />
                    <Key frame="60" value="0.353968" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="30" value="-1.14019" />
                    <Key frame="60" value="-1.14019" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="30" value="-72.6889" />
                    <Key frame="60" value="-72.601" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="30" value="0.046" />
                    <Key frame="60" value="0.052" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="30" value="-35.7695" />
                    <Key frame="60" value="-35.8574" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="30" value="-8.25942" />
                    <Key frame="60" value="-8.34731" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="30" value="3.51807" />
                    <Key frame="60" value="4.39699" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="30" value="60.6429" />
                    <Key frame="60" value="60.9944" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="30" value="17.2244" />
                    <Key frame="60" value="16.3455" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="30" value="-2.81494" />
                    <Key frame="60" value="-1.67234" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="30" value="-32.9618" />
                    <Key frame="60" value="-32.0829" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="30" value="52.0343" />
                    <Key frame="60" value="52.1222" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="30" value="0.00240423" />
                    <Key frame="60" value="-0.261268" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="30" value="4.04543" />
                    <Key frame="60" value="5.62747" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="30" value="73.1235" />
                    <Key frame="60" value="73.1235" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="30" value="0.0448" />
                    <Key frame="60" value="0.0444" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="30" value="-10.901" />
                    <Key frame="60" value="26.6288" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="30" value="-5.2711" />
                    <Key frame="60" value="-5.09532" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="30" value="3.51807" />
                    <Key frame="60" value="4.39699" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="30" value="69.1732" />
                    <Key frame="60" value="-3.95273" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="30" value="17.3171" />
                    <Key frame="60" value="16.3503" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="30" value="-7.03374" />
                    <Key frame="60" value="-8.26423" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="30" value="15.5544" />
                    <Key frame="60" value="14.1482" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="VierFussBeugenRechts_Repeat" id="6" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="1464" y="269">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="60">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="nao">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="30" value="27.3319" />
                    <Key frame="60" value="32.957" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="30" value="2.89802" />
                    <Key frame="60" value="2.81013" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="30" value="53.0842" />
                    <Key frame="60" value="52.7326" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="30" value="0.0902951" />
                    <Key frame="60" value="0.266077" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="30" value="-1.49175" />
                    <Key frame="60" value="-1.14019" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="30" value="-72.1615" />
                    <Key frame="60" value="-72.6889" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="30" value="0.0468" />
                    <Key frame="60" value="0.046" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="30" value="-44.295" />
                    <Key frame="60" value="-35.7695" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="30" value="5.01224" />
                    <Key frame="60" value="-8.25942" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="30" value="1.49656" />
                    <Key frame="60" value="3.51807" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="30" value="68.8168" />
                    <Key frame="60" value="60.6429" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="30" value="16.697" />
                    <Key frame="60" value="17.2244" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="30" value="-0.61764" />
                    <Key frame="60" value="-2.81494" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="30" value="-32.786" />
                    <Key frame="60" value="-32.9618" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="30" value="52.298" />
                    <Key frame="60" value="52.0343" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="30" value="0.00240423" />
                    <Key frame="60" value="0.00240423" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="30" value="3.69386" />
                    <Key frame="60" value="4.04543" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="30" value="73.1235" />
                    <Key frame="60" value="73.1235" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="30" value="0.0448" />
                    <Key frame="60" value="0.0448" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="30" value="28.0351" />
                    <Key frame="60" value="-10.901" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="30" value="-44.9103" />
                    <Key frame="60" value="-5.2711" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="30" value="1.49656" />
                    <Key frame="60" value="3.51807" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="30" value="74.4467" />
                    <Key frame="60" value="69.1732" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="30" value="16.6139" />
                    <Key frame="60" value="17.3171" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="30" value="-9.40682" />
                    <Key frame="60" value="-7.03374" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="30" value="14.7634" />
                    <Key frame="60" value="15.5544" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="VierFussBeugenLinks_Repeat" id="8" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="1317" y="259">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="60">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="nao">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="30" value="27.3319" />
                    <Key frame="60" value="27.3319" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="30" value="3.51326" />
                    <Key frame="60" value="3.51326" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="30" value="52.469" />
                    <Key frame="60" value="52.469" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="30" value="0.0902951" />
                    <Key frame="60" value="0.0902951" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="30" value="-0.0854867" />
                    <Key frame="60" value="-2.81013" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="30" value="-72.7768" />
                    <Key frame="60" value="-72.7768" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="30" value="0.0468" />
                    <Key frame="60" value="0.0468" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="30" value="27.1609" />
                    <Key frame="60" value="-10.9841" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="30" value="44.124" />
                    <Key frame="60" value="5.97904" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="30" value="1.23289" />
                    <Key frame="60" value="3.1665" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="30" value="73.6509" />
                    <Key frame="60" value="69.2563" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="30" value="16.2576" />
                    <Key frame="60" value="15.4665" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="30" value="-1.84813" />
                    <Key frame="60" value="-2.55127" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="30" value="-32.8739" />
                    <Key frame="60" value="-32.8739" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="30" value="52.8253" />
                    <Key frame="60" value="52.8253" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="30" value="0.00240423" />
                    <Key frame="60" value="0.00240423" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="30" value="6.3306" />
                    <Key frame="60" value="4.04543" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="30" value="73.1235" />
                    <Key frame="60" value="73.1235" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="30" value="0.0448" />
                    <Key frame="60" value="0.0448" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="30" value="-44.0362" />
                    <Key frame="60" value="-36.3017" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="30" value="-5.18321" />
                    <Key frame="60" value="8.00056" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="30" value="1.23289" />
                    <Key frame="60" value="3.1665" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="30" value="68.9095" />
                    <Key frame="60" value="61.5266" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="30" value="16.6139" />
                    <Key frame="60" value="19.1628" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="30" value="-6.68218" />
                    <Key frame="60" value="-6.77007" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="30" value="14.6755" />
                    <Key frame="60" value="15.906" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="VierFussAußenRechts_Repeat" id="9" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="1321" y="372">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="60">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="30" value="29.5292" />
                    <Key frame="60" value="27.3319" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="30" value="3.16169" />
                    <Key frame="60" value="3.51326" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="30" value="52.7326" />
                    <Key frame="60" value="52.469" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="30" value="0.353968" />
                    <Key frame="60" value="0.0902951" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="30" value="-1.14019" />
                    <Key frame="60" value="-1.93121" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="30" value="-72.601" />
                    <Key frame="60" value="-72.7768" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="30" value="0.052" />
                    <Key frame="60" value="0.0468" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="30" value="-35.8574" />
                    <Key frame="60" value="-36.0332" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="30" value="-8.34731" />
                    <Key frame="60" value="-8.17153" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="30" value="4.39699" />
                    <Key frame="60" value="4.57277" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="30" value="60.9944" />
                    <Key frame="60" value="60.6429" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="30" value="16.3455" />
                    <Key frame="60" value="15.4665" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="30" value="-1.67234" />
                    <Key frame="60" value="-2.90283" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="30" value="-32.0829" />
                    <Key frame="60" value="-33.7528" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="30" value="52.1222" />
                    <Key frame="60" value="52.4738" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="30" value="-0.261268" />
                    <Key frame="60" value="-1.31597" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="30" value="5.62747" />
                    <Key frame="60" value="5.97904" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="30" value="73.1235" />
                    <Key frame="60" value="73.1235" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="30" value="0.0444" />
                    <Key frame="60" value="0.0448" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="30" value="26.6288" />
                    <Key frame="60" value="31.287" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="30" value="-5.09532" />
                    <Key frame="60" value="-41.2188" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="30" value="4.39699" />
                    <Key frame="60" value="4.57277" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="30" value="-3.95273" />
                    <Key frame="60" value="-2.45857" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="30" value="16.3503" />
                    <Key frame="60" value="15.8229" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="30" value="-8.26423" />
                    <Key frame="60" value="-8.17634" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="30" value="14.1482" />
                    <Key frame="60" value="15.2908" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="VierFussAußenLinks_Repeat" id="10" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="1461" y="378">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="60">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="30" value="27.0682" />
                    <Key frame="60" value="27.0682" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="30" value="3.24959" />
                    <Key frame="60" value="3.24959" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="30" value="51.3264" />
                    <Key frame="60" value="51.9416" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="30" value="0.266077" />
                    <Key frame="60" value="0.881327" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="30" value="-0.349159" />
                    <Key frame="60" value="-1.0523" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="30" value="-72.4252" />
                    <Key frame="60" value="-72.4252" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="30" value="0.0552" />
                    <Key frame="60" value="0.0552" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="30" value="25.1394" />
                    <Key frame="60" value="26.9852" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="30" value="4.83644" />
                    <Key frame="60" value="41.1357" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="30" value="3.86964" />
                    <Key frame="60" value="5.27591" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="30" value="-4.66066" />
                    <Key frame="60" value="-2.72705" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="30" value="15.8181" />
                    <Key frame="60" value="15.8181" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="30" value="-0.441859" />
                    <Key frame="60" value="-1.145" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="30" value="-33.7528" />
                    <Key frame="60" value="-33.1376" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="30" value="53.0011" />
                    <Key frame="60" value="53.0011" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="30" value="0.0902951" />
                    <Key frame="60" value="0.0902951" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="30" value="3.95753" />
                    <Key frame="60" value="3.95753" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="30" value="73.0356" />
                    <Key frame="60" value="73.0356" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="30" value="0.0508" />
                    <Key frame="60" value="0.0508" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="30" value="-36.1259" />
                    <Key frame="60" value="-36.1259" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="30" value="8.87946" />
                    <Key frame="60" value="8.87946" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="30" value="3.86964" />
                    <Key frame="60" value="5.27591" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="30" value="61.7024" />
                    <Key frame="60" value="61.0872" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="30" value="19.1628" />
                    <Key frame="60" value="19.8659" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="30" value="-7.29742" />
                    <Key frame="60" value="-6.59429" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="30" value="13.0935" />
                    <Key frame="60" value="13.9724" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="Stand" id="12" localization="8" tooltip="The robot goes from its current postition to the asked posture." x="146" y="45">
              <bitmap>media/images/box/box-diagram.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[class MyClass(GeneratedClass):
    def __init__(self):
        GeneratedClass.__init__(self, False)

    def onLoad(self):
        self.nTries = 0
        self.postureService = self.session().service("ALRobotPosture")
        pass

    def onUnload(self):
        self.postureService.stopMove()

    def onInput_onStart(self):
        if(self.nTries != self.getParameter("Maximum of tries")):
            self.nTries = self.getParameter("Maximum of tries")
            self.postureService.setMaxTryNumber(self.nTries)

        result = self.postureService.goToPosture(self.getParameter("Name"), self.getParameter("Speed (%)")/100.)
        if(result):
            self.success()
        else:
            self.failure()
        pass

    def onInput_onStop(self):
        self.onUnload() #~ it is recommanded to call onUnload of this box in a onStop method, as the code written in onUnload is used to stop the box as well
        pass]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="success" type="1" type_size="1" nature="1" inner="0" tooltip="Stimulated if the posture has been reached." id="4" />
              <Output name="failure" type="1" type_size="1" nature="1" inner="0" tooltip="Stimulated if the posture could not be reached." id="5" />
              <Parameter name="Name" inherits_from_parent="0" content_type="3" value="Stand" default_value="Stand" custom_choice="1" tooltip="Name of the posture to go to." id="6">
                <Choice value="Crouch" />
                <Choice value="LyingBack" />
                <Choice value="LyingBelly" />
                <Choice value="Sit" />
                <Choice value="SitRelax" />
                <Choice value="StandInit" />
                <Choice value="Stand" />
                <Choice value="StandZero" />
              </Parameter>
              <Parameter name="Speed (%)" inherits_from_parent="0" content_type="1" value="80" default_value="80" min="0" max="100" tooltip="Speed to go to the posture." id="7" />
              <Parameter name="Maximum of tries" inherits_from_parent="0" content_type="1" value="3" default_value="3" min="1" max="10" tooltip="The maximum number of fails of go to posture before stimulating the failure output." id="8" />
              <Resource name="All motors" type="Lock" timeout="0" />
              <Resource name="Stiffness" type="Lock" timeout="0" />
            </Box>
            <Box name="KrabbelFußball Repeat" id="13" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="424" y="205">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="180">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="nao">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="50" value="29.6171" />
                    <Key frame="65" value="29.9687" />
                    <Key frame="90" value="29.6171" />
                    <Key frame="140" value="29.6171" />
                    <Key frame="155" value="30.4081" />
                    <Key frame="180" value="29.6171" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="50" value="-1.58445" />
                    <Key frame="65" value="-1.58445" />
                    <Key frame="90" value="-1.58445" />
                    <Key frame="140" value="-1.58445" />
                    <Key frame="155" value="-1.145" />
                    <Key frame="180" value="-1.58445" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="50" value="22.1463" />
                    <Key frame="65" value="22.6737" />
                    <Key frame="90" value="22.1463" />
                    <Key frame="140" value="22.1463" />
                    <Key frame="155" value="20.9158" />
                    <Key frame="180" value="22.1463" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="50" value="5.80326" />
                    <Key frame="65" value="5.71537" />
                    <Key frame="90" value="5.80326" />
                    <Key frame="140" value="5.80326" />
                    <Key frame="155" value="3.25439" />
                    <Key frame="180" value="5.80326" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="50" value="-7.81997" />
                    <Key frame="65" value="-9.31412" />
                    <Key frame="90" value="-7.81997" />
                    <Key frame="140" value="-7.81997" />
                    <Key frame="155" value="-9.5778" />
                    <Key frame="180" value="-7.81997" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="50" value="-59.8567" />
                    <Key frame="65" value="-59.8567" />
                    <Key frame="90" value="-59.8567" />
                    <Key frame="140" value="-59.8567" />
                    <Key frame="155" value="-60.5598" />
                    <Key frame="180" value="-59.8567" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="50" value="0.312" />
                    <Key frame="65" value="0.312" />
                    <Key frame="90" value="0.312" />
                    <Key frame="140" value="0.312" />
                    <Key frame="155" value="0.3256" />
                    <Key frame="180" value="0.312" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="50" value="6.06693" />
                    <Key frame="65" value="3.07861" />
                    <Key frame="90" value="6.06693" />
                    <Key frame="140" value="6.06693" />
                    <Key frame="155" value="4.92435" />
                    <Key frame="180" value="6.06693" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="50" value="0.00240423" />
                    <Key frame="65" value="-0.261268" />
                    <Key frame="90" value="0.00240423" />
                    <Key frame="140" value="0.00240423" />
                    <Key frame="155" value="1.145" />
                    <Key frame="180" value="0.00240423" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="50" value="-7.11683" />
                    <Key frame="65" value="-9.66569" />
                    <Key frame="90" value="-7.11683" />
                    <Key frame="140" value="-7.11683" />
                    <Key frame="155" value="-9.13833" />
                    <Key frame="180" value="-7.11683" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="50" value="85.8678" />
                    <Key frame="65" value="-4.74855" />
                    <Key frame="90" value="85.8678" />
                    <Key frame="140" value="85.8678" />
                    <Key frame="155" value="88.6804" />
                    <Key frame="180" value="85.8678" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="50" value="118.739" />
                    <Key frame="65" value="116.366" />
                    <Key frame="90" value="118.739" />
                    <Key frame="140" value="118.739" />
                    <Key frame="155" value="119.442" />
                    <Key frame="180" value="118.739" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="50" value="17.7517" />
                    <Key frame="65" value="18.367" />
                    <Key frame="90" value="17.7517" />
                    <Key frame="140" value="17.7517" />
                    <Key frame="155" value="16.9607" />
                    <Key frame="180" value="17.7517" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="50" value="37.6153" />
                    <Key frame="65" value="38.8457" />
                    <Key frame="90" value="37.6153" />
                    <Key frame="140" value="37.6153" />
                    <Key frame="155" value="39.1094" />
                    <Key frame="180" value="37.6153" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="50" value="20.7449" />
                    <Key frame="65" value="20.8327" />
                    <Key frame="90" value="20.7449" />
                    <Key frame="140" value="20.7449" />
                    <Key frame="155" value="21.9753" />
                    <Key frame="180" value="20.7449" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="50" value="-3.95273" />
                    <Key frame="65" value="-3.42537" />
                    <Key frame="90" value="-3.95273" />
                    <Key frame="140" value="-3.95273" />
                    <Key frame="155" value="-4.91954" />
                    <Key frame="180" value="-3.95273" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="50" value="8.17634" />
                    <Key frame="65" value="8.96736" />
                    <Key frame="90" value="8.17634" />
                    <Key frame="140" value="8.17634" />
                    <Key frame="155" value="9.49471" />
                    <Key frame="180" value="8.17634" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="50" value="36.1211" />
                    <Key frame="65" value="36.8242" />
                    <Key frame="90" value="36.1211" />
                    <Key frame="140" value="36.1211" />
                    <Key frame="155" value="36.8242" />
                    <Key frame="180" value="36.1211" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="50" value="0.3528" />
                    <Key frame="65" value="0.3528" />
                    <Key frame="90" value="0.3528" />
                    <Key frame="140" value="0.3528" />
                    <Key frame="155" value="0.3556" />
                    <Key frame="180" value="0.3528" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="50" value="4.91954" />
                    <Key frame="65" value="4.74374" />
                    <Key frame="90" value="4.91954" />
                    <Key frame="140" value="4.91954" />
                    <Key frame="155" value="4.39218" />
                    <Key frame="180" value="4.91954" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="50" value="-2.0191" />
                    <Key frame="65" value="-1.84332" />
                    <Key frame="90" value="-2.0191" />
                    <Key frame="140" value="-2.0191" />
                    <Key frame="155" value="-0.524941" />
                    <Key frame="180" value="-2.0191" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="50" value="-7.11683" />
                    <Key frame="65" value="-9.66569" />
                    <Key frame="90" value="-7.11683" />
                    <Key frame="140" value="-7.11683" />
                    <Key frame="155" value="-9.13833" />
                    <Key frame="180" value="-7.11683" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="50" value="88.7731" />
                    <Key frame="65" value="89.0367" />
                    <Key frame="90" value="88.7731" />
                    <Key frame="140" value="88.7731" />
                    <Key frame="155" value="-4.2164" />
                    <Key frame="180" value="88.7731" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="50" value="118.48" />
                    <Key frame="65" value="119.008" />
                    <Key frame="90" value="118.48" />
                    <Key frame="140" value="118.48" />
                    <Key frame="155" value="115.053" />
                    <Key frame="180" value="118.48" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="50" value="-21.7996" />
                    <Key frame="65" value="-21.2722" />
                    <Key frame="90" value="-21.7996" />
                    <Key frame="140" value="-21.7996" />
                    <Key frame="155" value="-22.1511" />
                    <Key frame="180" value="-21.7996" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="50" value="-20.1296" />
                    <Key frame="65" value="-21.0085" />
                    <Key frame="90" value="-20.1296" />
                    <Key frame="140" value="-20.1296" />
                    <Key frame="155" value="-21.3601" />
                    <Key frame="180" value="-20.1296" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="Krabbelfußball Init" id="14" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="288" y="202">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="60">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="20" value="8.87466" />
                    <Key frame="39" value="19.8611" />
                    <Key frame="60" value="23.201" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="20" value="-1.32078" />
                    <Key frame="39" value="-1.58445" />
                    <Key frame="60" value="-1.58445" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="20" value="49.6564" />
                    <Key frame="39" value="31.4628" />
                    <Key frame="60" value="31.9023" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="20" value="-2.28279" />
                    <Key frame="39" value="0.705531" />
                    <Key frame="60" value="0.441859" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="20" value="-86.6588" />
                    <Key frame="39" value="-86.4831" />
                    <Key frame="60" value="-20.8279" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="20" value="-53.5285" />
                    <Key frame="39" value="-97.1228" />
                    <Key frame="60" value="-59.8567" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="20" value="0.386" />
                    <Key frame="39" value="0.32" />
                    <Key frame="60" value="0.312" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="20" value="21.7996" />
                    <Key frame="39" value="-31.3749" />
                    <Key frame="60" value="-31.5507" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="20" value="6.24271" />
                    <Key frame="39" value="-2.63435" />
                    <Key frame="60" value="-3.0738" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="20" value="-28.8261" />
                    <Key frame="39" value="-3.95273" />
                    <Key frame="60" value="-4.04062" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="20" value="-4.92435" />
                    <Key frame="39" value="99.3152" />
                    <Key frame="60" value="99.0516" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="20" value="107.401" />
                    <Key frame="39" value="117.773" />
                    <Key frame="60" value="112.675" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="20" value="18.2791" />
                    <Key frame="39" value="18.6306" />
                    <Key frame="60" value="20.3885" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="20" value="11.6872" />
                    <Key frame="39" value="8.87466" />
                    <Key frame="60" value="41.6583" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="20" value="37.0927" />
                    <Key frame="39" value="32.6981" />
                    <Key frame="60" value="32.8739" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="20" value="-16.2576" />
                    <Key frame="39" value="-4.74374" />
                    <Key frame="60" value="-4.56796" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="20" value="86.7515" />
                    <Key frame="39" value="86.4" />
                    <Key frame="60" value="15.8229" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="20" value="52.6447" />
                    <Key frame="39" value="95.9754" />
                    <Key frame="60" value="65.9164" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="20" value="0.3596" />
                    <Key frame="39" value="0.3528" />
                    <Key frame="60" value="0.3528" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="20" value="-22.9421" />
                    <Key frame="39" value="-36.7411" />
                    <Key frame="60" value="-36.3017" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="20" value="-4.74374" />
                    <Key frame="39" value="-0.0854867" />
                    <Key frame="60" value="0.266077" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="20" value="-28.8261" />
                    <Key frame="39" value="-3.95273" />
                    <Key frame="60" value="-4.04062" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="20" value="99.6716" />
                    <Key frame="39" value="102.924" />
                    <Key frame="60" value="103.275" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="20" value="100.111" />
                    <Key frame="39" value="111.098" />
                    <Key frame="60" value="107.055" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="20" value="-18.6354" />
                    <Key frame="39" value="-19.0749" />
                    <Key frame="60" value="-22.5027" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="20" value="11.1598" />
                    <Key frame="39" value="2.45857" />
                    <Key frame="60" value="-61.0872" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="Ischios Init" id="17" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="1256" y="629">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="80">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="nao">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="5" value="-0.178186" />
                    <Key frame="30" value="-0.178186" />
                    <Key frame="55" value="-0.178186" />
                    <Key frame="80" value="-0.178186" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="5" value="-0.793436" />
                    <Key frame="30" value="-0.793436" />
                    <Key frame="55" value="-0.793436" />
                    <Key frame="80" value="-0.793436" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="5" value="52.5568" />
                    <Key frame="30" value="52.6447" />
                    <Key frame="55" value="52.6447" />
                    <Key frame="80" value="52.6447" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="5" value="0.0902951" />
                    <Key frame="30" value="0.0902951" />
                    <Key frame="55" value="0.0902951" />
                    <Key frame="80" value="0.0902951" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="5" value="-9.5778" />
                    <Key frame="30" value="-83.5826" />
                    <Key frame="55" value="-54.1389" />
                    <Key frame="80" value="-54.1389" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="5" value="-117.953" />
                    <Key frame="30" value="-107.846" />
                    <Key frame="55" value="-55.2863" />
                    <Key frame="80" value="-55.2863" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="5" value="0.1288" />
                    <Key frame="30" value="0.2264" />
                    <Key frame="55" value="0.2264" />
                    <Key frame="80" value="0.2264" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="5" value="-63.7191" />
                    <Key frame="30" value="-63.9828" />
                    <Key frame="55" value="-63.3675" />
                    <Key frame="80" value="-63.3675" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="5" value="8.00056" />
                    <Key frame="30" value="7.91266" />
                    <Key frame="55" value="7.91266" />
                    <Key frame="80" value="7.91266" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="5" value="-29.6171" />
                    <Key frame="30" value="-29.7929" />
                    <Key frame="55" value="-29.7929" />
                    <Key frame="80" value="-29.7929" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="5" value="-2.55127" />
                    <Key frame="30" value="-2.55127" />
                    <Key frame="55" value="-2.55127" />
                    <Key frame="80" value="-2.55127" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="5" value="121.025" />
                    <Key frame="30" value="54.2268" />
                    <Key frame="55" value="64.9496" />
                    <Key frame="80" value="64.9496" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="5" value="6.4137" />
                    <Key frame="30" value="26.9804" />
                    <Key frame="55" value="18.6306" />
                    <Key frame="80" value="18.6306" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="5" value="-54.3195" />
                    <Key frame="30" value="-35.9501" />
                    <Key frame="55" value="-38.6748" />
                    <Key frame="80" value="-38.6748" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="5" value="52.5617" />
                    <Key frame="30" value="52.5617" />
                    <Key frame="55" value="52.5617" />
                    <Key frame="80" value="52.5617" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="5" value="0.0902951" />
                    <Key frame="30" value="0.0902951" />
                    <Key frame="55" value="0.0902951" />
                    <Key frame="80" value="0.0902951" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="5" value="2.90283" />
                    <Key frame="30" value="21.5359" />
                    <Key frame="55" value="85.521" />
                    <Key frame="80" value="54.2316" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="5" value="117.86" />
                    <Key frame="30" value="99.6668" />
                    <Key frame="55" value="115.312" />
                    <Key frame="80" value="60.4671" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="5" value="0.1348" />
                    <Key frame="30" value="0.1348" />
                    <Key frame="55" value="0.1348" />
                    <Key frame="80" value="0.1348" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="5" value="-61.0872" />
                    <Key frame="30" value="-60.9114" />
                    <Key frame="55" value="-61.5266" />
                    <Key frame="80" value="-61.5266" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="5" value="-12.5661" />
                    <Key frame="30" value="-12.4782" />
                    <Key frame="55" value="-12.4782" />
                    <Key frame="80" value="-12.4782" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="5" value="-29.6171" />
                    <Key frame="30" value="-29.7929" />
                    <Key frame="55" value="-29.7929" />
                    <Key frame="80" value="-29.7929" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="5" value="-5.53478" />
                    <Key frame="30" value="-5.35899" />
                    <Key frame="55" value="-5.35899" />
                    <Key frame="80" value="-5.35899" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="5" value="119.623" />
                    <Key frame="30" value="107.67" />
                    <Key frame="55" value="79.7202" />
                    <Key frame="80" value="67.4154" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="5" value="-9.93417" />
                    <Key frame="30" value="-14.5924" />
                    <Key frame="55" value="-26.1941" />
                    <Key frame="80" value="-16.3503" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="5" value="57.7425" />
                    <Key frame="30" value="47.2834" />
                    <Key frame="55" value="42.3614" />
                    <Key frame="80" value="34.1875" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="Ischios Repeat" id="18" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="1399" y="632">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="30">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="nao">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="30" value="-0.00240423" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="30" value="-0.881327" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="30" value="52.8205" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="30" value="-0.173378" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="30" value="-8.69887" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="30" value="-76.9956" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="30" value="0.1268" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="30" value="-80.3306" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="30" value="6.15482" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="30" value="-33.9238" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="30" value="-1.67234" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="30" value="15.115" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="30" value="14.3239" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="30" value="36.9121" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="30" value="53.7921" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="30" value="0.00240423" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="30" value="4.66066" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="30" value="52.9963" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="30" value="0.1344" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="30" value="-84.2906" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="30" value="-8.25942" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="30" value="-33.9238" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="30" value="2.63916" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="30" value="14.5045" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="30" value="-11.9557" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="30" value="19.158" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="Ischios Exit" id="19" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="1539" y="633">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="110">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="15" value="-0.00240423" />
                    <Key frame="40" value="-0.178186" />
                    <Key frame="65" value="-0.178186" />
                    <Key frame="90" value="-0.178186" />
                    <Key frame="110" value="-0.178186" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="15" value="-0.881327" />
                    <Key frame="40" value="-0.793436" />
                    <Key frame="65" value="-0.793436" />
                    <Key frame="90" value="-0.793436" />
                    <Key frame="110" value="-0.793436" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="15" value="52.8205" />
                    <Key frame="40" value="52.6447" />
                    <Key frame="65" value="52.6447" />
                    <Key frame="90" value="52.6447" />
                    <Key frame="110" value="52.5568" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="15" value="-0.173378" />
                    <Key frame="40" value="0.0902951" />
                    <Key frame="65" value="0.0902951" />
                    <Key frame="90" value="0.0902951" />
                    <Key frame="110" value="0.0902951" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="15" value="-10.9841" />
                    <Key frame="40" value="-54.1389" />
                    <Key frame="65" value="-54.1389" />
                    <Key frame="90" value="-83.5826" />
                    <Key frame="110" value="-9.5778" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="15" value="-79.2807" />
                    <Key frame="40" value="-55.2863" />
                    <Key frame="65" value="-55.2863" />
                    <Key frame="90" value="-107.846" />
                    <Key frame="110" value="-117.953" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="15" value="0.1268" />
                    <Key frame="40" value="0.2264" />
                    <Key frame="65" value="0.2264" />
                    <Key frame="90" value="0.2264" />
                    <Key frame="110" value="0.1288" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="15" value="-80.3306" />
                    <Key frame="40" value="-63.3675" />
                    <Key frame="65" value="-63.3675" />
                    <Key frame="90" value="-63.9828" />
                    <Key frame="110" value="-63.7191" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="15" value="6.15482" />
                    <Key frame="40" value="7.91266" />
                    <Key frame="65" value="7.91266" />
                    <Key frame="90" value="7.91266" />
                    <Key frame="110" value="8.00056" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="15" value="-33.9238" />
                    <Key frame="40" value="-29.7929" />
                    <Key frame="65" value="-29.7929" />
                    <Key frame="90" value="-29.7929" />
                    <Key frame="110" value="-29.6171" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="15" value="-1.67234" />
                    <Key frame="40" value="-2.55127" />
                    <Key frame="65" value="-2.55127" />
                    <Key frame="90" value="-2.55127" />
                    <Key frame="110" value="-2.55127" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="15" value="-5.27591" />
                    <Key frame="40" value="64.9496" />
                    <Key frame="65" value="64.9496" />
                    <Key frame="90" value="54.2268" />
                    <Key frame="110" value="121.025" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="15" value="14.4997" />
                    <Key frame="40" value="18.6306" />
                    <Key frame="65" value="18.6306" />
                    <Key frame="90" value="26.9804" />
                    <Key frame="110" value="6.4137" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="15" value="31.5507" />
                    <Key frame="40" value="-38.6748" />
                    <Key frame="65" value="-38.6748" />
                    <Key frame="90" value="-35.9501" />
                    <Key frame="110" value="-54.3195" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="15" value="53.7921" />
                    <Key frame="40" value="52.5617" />
                    <Key frame="65" value="52.5617" />
                    <Key frame="90" value="52.5617" />
                    <Key frame="110" value="52.5617" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="15" value="0.00240423" />
                    <Key frame="40" value="0.0902951" />
                    <Key frame="65" value="0.0902951" />
                    <Key frame="90" value="0.0902951" />
                    <Key frame="110" value="0.0902951" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="15" value="4.57277" />
                    <Key frame="40" value="54.2316" />
                    <Key frame="65" value="85.521" />
                    <Key frame="90" value="21.5359" />
                    <Key frame="110" value="2.90283" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="15" value="51.59" />
                    <Key frame="40" value="60.4671" />
                    <Key frame="65" value="115.312" />
                    <Key frame="90" value="99.6668" />
                    <Key frame="110" value="117.86" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="15" value="0.1344" />
                    <Key frame="40" value="0.1348" />
                    <Key frame="65" value="0.1348" />
                    <Key frame="90" value="0.1348" />
                    <Key frame="110" value="0.1348" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="15" value="-84.2906" />
                    <Key frame="40" value="-61.5266" />
                    <Key frame="65" value="-61.5266" />
                    <Key frame="90" value="-60.9114" />
                    <Key frame="110" value="-61.0872" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="15" value="-8.25942" />
                    <Key frame="40" value="-12.4782" />
                    <Key frame="65" value="-12.4782" />
                    <Key frame="90" value="-12.4782" />
                    <Key frame="110" value="-12.5661" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="15" value="-33.9238" />
                    <Key frame="40" value="-29.7929" />
                    <Key frame="65" value="-29.7929" />
                    <Key frame="90" value="-29.7929" />
                    <Key frame="110" value="-29.6171" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="15" value="2.63916" />
                    <Key frame="40" value="-5.35899" />
                    <Key frame="65" value="-5.35899" />
                    <Key frame="90" value="-5.35899" />
                    <Key frame="110" value="-5.53478" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="15" value="-12.8298" />
                    <Key frame="40" value="67.4154" />
                    <Key frame="65" value="79.7202" />
                    <Key frame="90" value="107.67" />
                    <Key frame="110" value="119.623" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="15" value="-13.0983" />
                    <Key frame="40" value="-16.3503" />
                    <Key frame="65" value="-26.1941" />
                    <Key frame="90" value="-14.5924" />
                    <Key frame="110" value="-9.93417" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="15" value="18.4549" />
                    <Key frame="40" value="34.1875" />
                    <Key frame="65" value="42.3614" />
                    <Key frame="90" value="47.2834" />
                    <Key frame="110" value="57.7425" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="LyingBack" id="21" localization="8" tooltip="The robot goes from its current postition to the asked posture." x="140" y="304">
              <bitmap>media/images/box/box-diagram.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[class MyClass(GeneratedClass):
    def __init__(self):
        GeneratedClass.__init__(self, False)

    def onLoad(self):
        self.nTries = 0
        self.postureService = self.session().service("ALRobotPosture")
        pass

    def onUnload(self):
        self.postureService.stopMove()

    def onInput_onStart(self):
        if(self.nTries != self.getParameter("Maximum of tries")):
            self.nTries = self.getParameter("Maximum of tries")
            self.postureService.setMaxTryNumber(self.nTries)

        result = self.postureService.goToPosture(self.getParameter("Name"), self.getParameter("Speed (%)")/100.)
        if(result):
            self.success()
        else:
            self.failure()
        pass

    def onInput_onStop(self):
        self.onUnload() #~ it is recommanded to call onUnload of this box in a onStop method, as the code written in onUnload is used to stop the box as well
        pass]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="success" type="1" type_size="1" nature="1" inner="0" tooltip="Stimulated if the posture has been reached." id="4" />
              <Output name="failure" type="1" type_size="1" nature="1" inner="0" tooltip="Stimulated if the posture could not be reached." id="5" />
              <Parameter name="Name" inherits_from_parent="0" content_type="3" value="LyingBack" default_value="Stand" custom_choice="1" tooltip="Name of the posture to go to." id="6">
                <Choice value="Crouch" />
                <Choice value="LyingBack" />
                <Choice value="LyingBelly" />
                <Choice value="Sit" />
                <Choice value="SitRelax" />
                <Choice value="StandInit" />
                <Choice value="Stand" />
                <Choice value="StandZero" />
              </Parameter>
              <Parameter name="Speed (%)" inherits_from_parent="0" content_type="1" value="80" default_value="80" min="0" max="100" tooltip="Speed to go to the posture." id="7" />
              <Parameter name="Maximum of tries" inherits_from_parent="0" content_type="1" value="3" default_value="3" min="1" max="10" tooltip="The maximum number of fails of go to posture before stimulating the failure output." id="8" />
              <Resource name="All motors" type="Lock" timeout="0" />
              <Resource name="Stiffness" type="Lock" timeout="0" />
            </Box>
            <Box name="VierFuss_Exit" id="26" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="715" y="205">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="226">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="13" value="25.4197" />
                    <Key frame="26" value="24.1716" />
                    <Key frame="38" value="20.2127" />
                    <Key frame="51" value="18.3347" />
                    <Key frame="63" value="18.3347" />
                    <Key frame="76" value="18.5287" />
                    <Key frame="88" value="18.4965" />
                    <Key frame="101" value="18.492" />
                    <Key frame="113" value="13.5864" />
                    <Key frame="126" value="-3.70509" />
                    <Key frame="138" value="-10.149" />
                    <Key frame="151" value="-11.5555" />
                    <Key frame="163" value="0.829408" />
                    <Key frame="176" value="4.86833" />
                    <Key frame="188" value="-3.70515" />
                    <Key frame="201" value="-9.68408" />
                    <Key frame="213" value="-9.74028" />
                    <Key frame="226" value="-9.74028" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="13" value="4.39218" />
                    <Key frame="26" value="5.73234" />
                    <Key frame="38" value="9.9834" />
                    <Key frame="51" value="13.4249" />
                    <Key frame="63" value="13.3911" />
                    <Key frame="76" value="-2.46487" />
                    <Key frame="88" value="-22.6683" />
                    <Key frame="101" value="-22.6217" />
                    <Key frame="113" value="-11.0046" />
                    <Key frame="126" value="1.65501" />
                    <Key frame="138" value="-5.29046" />
                    <Key frame="151" value="-20.2112" />
                    <Key frame="163" value="-9.62462" />
                    <Key frame="176" value="-1.91726" />
                    <Key frame="188" value="-0.272296" />
                    <Key frame="201" value="-0.000157214" />
                    <Key frame="213" value="0" />
                    <Key frame="226" value="0" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="13" value="52.86" />
                    <Key frame="26" value="38.2671" />
                    <Key frame="38" value="-8.02233" />
                    <Key frame="51" value="-46.2919" />
                    <Key frame="63" value="-48.057" />
                    <Key frame="76" value="-19.7533" />
                    <Key frame="88" value="25.1967" />
                    <Key frame="101" value="51.2317" />
                    <Key frame="113" value="52.6455" />
                    <Key frame="126" value="49.2897" />
                    <Key frame="138" value="26.0784" />
                    <Key frame="151" value="-19.1791" />
                    <Key frame="163" value="-49.2511" />
                    <Key frame="176" value="-45.7932" />
                    <Key frame="188" value="-15.9786" />
                    <Key frame="201" value="4.81331" />
                    <Key frame="213" value="5.00876" />
                    <Key frame="226" value="5.00876" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="13" value="0.0902951" />
                    <Key frame="26" value="-1.37326" />
                    <Key frame="38" value="-6.01574" />
                    <Key frame="51" value="-9.85389" />
                    <Key frame="63" value="-10.0838" />
                    <Key frame="76" value="-9.91528" />
                    <Key frame="88" value="-8.96258" />
                    <Key frame="101" value="-1.13602" />
                    <Key frame="113" value="-0.15379" />
                    <Key frame="126" value="-1.96413" />
                    <Key frame="138" value="-4.30219" />
                    <Key frame="151" value="-6.6069" />
                    <Key frame="163" value="-3.2043" />
                    <Key frame="176" value="-2.40304" />
                    <Key frame="188" value="-4.71824" />
                    <Key frame="201" value="-6.3328" />
                    <Key frame="213" value="-6.34798" />
                    <Key frame="226" value="-6.34798" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="13" value="-2.10706" />
                    <Key frame="26" value="-11.5737" />
                    <Key frame="38" value="-51.1283" />
                    <Key frame="51" value="-84.3642" />
                    <Key frame="63" value="-88.4878" />
                    <Key frame="76" value="-88.3356" />
                    <Key frame="88" value="-87.3208" />
                    <Key frame="101" value="-64.7539" />
                    <Key frame="113" value="-41.7686" />
                    <Key frame="126" value="-41.5553" />
                    <Key frame="138" value="-47.4152" />
                    <Key frame="151" value="-62.7658" />
                    <Key frame="163" value="-36.4513" />
                    <Key frame="176" value="-16.0618" />
                    <Key frame="188" value="-20.0801" />
                    <Key frame="201" value="-23.5847" />
                    <Key frame="213" value="-23.9009" />
                    <Key frame="226" value="-23.9077" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="13" value="-73.6559" />
                    <Key frame="26" value="-76.2585" />
                    <Key frame="38" value="-87.1709" />
                    <Key frame="51" value="-96.3341" />
                    <Key frame="63" value="-97.4241" />
                    <Key frame="76" value="-91.729" />
                    <Key frame="88" value="-80.975" />
                    <Key frame="101" value="-73.9477" />
                    <Key frame="113" value="-67.0288" />
                    <Key frame="126" value="-57.7528" />
                    <Key frame="138" value="-53.9921" />
                    <Key frame="151" value="-53.2014" />
                    <Key frame="163" value="-60.9471" />
                    <Key frame="176" value="-67.408" />
                    <Key frame="188" value="-68.5947" />
                    <Key frame="201" value="-68.8391" />
                    <Key frame="213" value="-68.8831" />
                    <Key frame="226" value="-68.8853" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="13" value="0.044" />
                    <Key frame="26" value="0.0411806" />
                    <Key frame="38" value="0.0318082" />
                    <Key frame="51" value="0.0248434" />
                    <Key frame="63" value="0.0245244" />
                    <Key frame="76" value="0.0311797" />
                    <Key frame="88" value="0.0422348" />
                    <Key frame="101" value="0.0483674" />
                    <Key frame="113" value="0.0488" />
                    <Key frame="126" value="0.0488" />
                    <Key frame="138" value="0.0492529" />
                    <Key frame="151" value="0.0546995" />
                    <Key frame="163" value="0.20312" />
                    <Key frame="176" value="0.284076" />
                    <Key frame="188" value="0.297738" />
                    <Key frame="201" value="0.299999" />
                    <Key frame="213" value="0.3" />
                    <Key frame="226" value="0.3" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="13" value="-44.295" />
                    <Key frame="26" value="-50.5786" />
                    <Key frame="38" value="-71.4666" />
                    <Key frame="51" value="-86.989" />
                    <Key frame="63" value="-87.7517" />
                    <Key frame="76" value="-76.492" />
                    <Key frame="88" value="-61.734" />
                    <Key frame="101" value="-64.072" />
                    <Key frame="113" value="-67.353" />
                    <Key frame="126" value="-67.6584" />
                    <Key frame="138" value="-69.9909" />
                    <Key frame="151" value="-72.7268" />
                    <Key frame="163" value="-48.3187" />
                    <Key frame="176" value="-21.3491" />
                    <Key frame="188" value="-2.15229" />
                    <Key frame="201" value="7.22344" />
                    <Key frame="213" value="7.30057" />
                    <Key frame="226" value="7.30057" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="13" value="5.10013" />
                    <Key frame="26" value="1.5348" />
                    <Key frame="38" value="-10.3171" />
                    <Key frame="51" value="-19.1246" />
                    <Key frame="63" value="-19.6742" />
                    <Key frame="76" value="-19.0695" />
                    <Key frame="88" value="-18.0165" />
                    <Key frame="101" value="-17.4323" />
                    <Key frame="113" value="-9.45804" />
                    <Key frame="126" value="9.92218" />
                    <Key frame="138" value="10.311" />
                    <Key frame="151" value="7.69568" />
                    <Key frame="163" value="2.88503" />
                    <Key frame="176" value="2.31251" />
                    <Key frame="188" value="4.96045" />
                    <Key frame="201" value="6.80705" />
                    <Key frame="213" value="6.82441" />
                    <Key frame="226" value="6.82441" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="13" value="0.881327" />
                    <Key frame="26" value="-8.66414" />
                    <Key frame="38" value="-40.3954" />
                    <Key frame="51" value="-63.9758" />
                    <Key frame="63" value="-65.4318" />
                    <Key frame="76" value="-63.1799" />
                    <Key frame="88" value="-60.2283" />
                    <Key frame="101" value="-63.6113" />
                    <Key frame="113" value="-65.603" />
                    <Key frame="126" value="-65.6199" />
                    <Key frame="138" value="-57.879" />
                    <Key frame="151" value="-36.4825" />
                    <Key frame="163" value="-19.1962" />
                    <Key frame="176" value="-12.1212" />
                    <Key frame="188" value="-10.0789" />
                    <Key frame="201" value="-9.74103" />
                    <Key frame="213" value="-9.74084" />
                    <Key frame="226" value="-9.74084" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="13" value="69.7836" />
                    <Key frame="26" value="77.1545" />
                    <Key frame="38" value="101.657" />
                    <Key frame="51" value="119.865" />
                    <Key frame="63" value="120.292" />
                    <Key frame="76" value="83.1934" />
                    <Key frame="88" value="31.8514" />
                    <Key frame="101" value="20.4638" />
                    <Key frame="113" value="22.2933" />
                    <Key frame="126" value="36.0252" />
                    <Key frame="138" value="72.4488" />
                    <Key frame="151" value="113.664" />
                    <Key frame="163" value="106.195" />
                    <Key frame="176" value="71.3013" />
                    <Key frame="188" value="22.7559" />
                    <Key frame="201" value="-5.04526" />
                    <Key frame="213" value="-5.29" />
                    <Key frame="226" value="-5.29" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="13" value="16.5705" />
                    <Key frame="26" value="15.4136" />
                    <Key frame="38" value="10.3253" />
                    <Key frame="51" value="6.06493" />
                    <Key frame="63" value="5.73295" />
                    <Key frame="76" value="27.1186" />
                    <Key frame="88" value="58.6791" />
                    <Key frame="101" value="54.0124" />
                    <Key frame="113" value="44.4013" />
                    <Key frame="126" value="44.2565" />
                    <Key frame="138" value="50.2162" />
                    <Key frame="151" value="69.6468" />
                    <Key frame="163" value="83.5005" />
                    <Key frame="176" value="88.0038" />
                    <Key frame="188" value="85.4837" />
                    <Key frame="201" value="83.0502" />
                    <Key frame="213" value="82.6683" />
                    <Key frame="226" value="82.6545" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="13" value="-8.69678" />
                    <Key frame="26" value="-7.76246" />
                    <Key frame="38" value="-3.51933" />
                    <Key frame="51" value="0.0551908" />
                    <Key frame="63" value="0.500119" />
                    <Key frame="76" value="1.10338" />
                    <Key frame="88" value="5.79658" />
                    <Key frame="101" value="31.6839" />
                    <Key frame="113" value="48.4425" />
                    <Key frame="126" value="39.2655" />
                    <Key frame="138" value="40.0633" />
                    <Key frame="151" value="45.876" />
                    <Key frame="163" value="27.5708" />
                    <Key frame="176" value="15.2963" />
                    <Key frame="188" value="14.3178" />
                    <Key frame="201" value="13.1355" />
                    <Key frame="213" value="12.8713" />
                    <Key frame="226" value="12.8608" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="13" value="-37.7956" />
                    <Key frame="26" value="-36.6885" />
                    <Key frame="38" value="-32.0606" />
                    <Key frame="51" value="-28.1722" />
                    <Key frame="63" value="-27.8297" />
                    <Key frame="76" value="-43.6615" />
                    <Key frame="88" value="-72.0744" />
                    <Key frame="101" value="-85.0052" />
                    <Key frame="113" value="-85.6065" />
                    <Key frame="126" value="-85.5606" />
                    <Key frame="138" value="-73.4556" />
                    <Key frame="151" value="-39.7521" />
                    <Key frame="163" value="-33.2749" />
                    <Key frame="176" value="-27.0403" />
                    <Key frame="188" value="-8.67958" />
                    <Key frame="201" value="4.99447" />
                    <Key frame="213" value="5.72915" />
                    <Key frame="226" value="5.72958" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="13" value="53.4" />
                    <Key frame="26" value="36.6652" />
                    <Key frame="38" value="-17.1927" />
                    <Key frame="51" value="-57.216" />
                    <Key frame="63" value="-59.8281" />
                    <Key frame="76" value="-63.0301" />
                    <Key frame="88" value="-67.3337" />
                    <Key frame="101" value="-45.0064" />
                    <Key frame="113" value="-41.9046" />
                    <Key frame="126" value="-66.4598" />
                    <Key frame="138" value="-67.9659" />
                    <Key frame="151" value="-67.97" />
                    <Key frame="163" value="-60.5038" />
                    <Key frame="176" value="-39.0812" />
                    <Key frame="188" value="-11.0363" />
                    <Key frame="201" value="4.86926" />
                    <Key frame="213" value="5.00876" />
                    <Key frame="226" value="5.00876" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="13" value="2.66804e-07" />
                    <Key frame="26" value="0.86007" />
                    <Key frame="38" value="3.71913" />
                    <Key frame="51" value="5.84378" />
                    <Key frame="63" value="5.89587" />
                    <Key frame="76" value="1.74387" />
                    <Key frame="88" value="-3.83664" />
                    <Key frame="101" value="-2.01212" />
                    <Key frame="113" value="-1.65952" />
                    <Key frame="126" value="-4.14643" />
                    <Key frame="138" value="-2.04122" />
                    <Key frame="151" value="1.89203" />
                    <Key frame="163" value="0.840036" />
                    <Key frame="176" value="1.09872" />
                    <Key frame="188" value="4.17926" />
                    <Key frame="201" value="6.32755" />
                    <Key frame="213" value="6.34774" />
                    <Key frame="226" value="6.34774" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="13" value="5.18759" />
                    <Key frame="26" value="15.5159" />
                    <Key frame="38" value="52.3667" />
                    <Key frame="51" value="84.3532" />
                    <Key frame="63" value="88.0568" />
                    <Key frame="76" value="58.629" />
                    <Key frame="88" value="10.7958" />
                    <Key frame="101" value="2.91384" />
                    <Key frame="113" value="7.65176" />
                    <Key frame="126" value="22.495" />
                    <Key frame="138" value="18.9527" />
                    <Key frame="151" value="5.3679" />
                    <Key frame="163" value="12.7485" />
                    <Key frame="176" value="20.5524" />
                    <Key frame="188" value="22.7861" />
                    <Key frame="201" value="23.7302" />
                    <Key frame="213" value="23.9" />
                    <Key frame="226" value="23.9077" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="13" value="74.0906" />
                    <Key frame="26" value="76.9834" />
                    <Key frame="38" value="87.3063" />
                    <Key frame="51" value="96.2666" />
                    <Key frame="63" value="97.4283" />
                    <Key frame="76" value="102.572" />
                    <Key frame="88" value="110.929" />
                    <Key frame="101" value="112.27" />
                    <Key frame="113" value="98.5298" />
                    <Key frame="126" value="46.1055" />
                    <Key frame="138" value="38.5722" />
                    <Key frame="151" value="38.3042" />
                    <Key frame="163" value="50.4083" />
                    <Key frame="176" value="64.0147" />
                    <Key frame="188" value="67.7774" />
                    <Key frame="201" value="68.8073" />
                    <Key frame="213" value="68.8828" />
                    <Key frame="226" value="68.8853" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="13" value="0.0424" />
                    <Key frame="26" value="0.0420149" />
                    <Key frame="38" value="0.0409071" />
                    <Key frame="51" value="0.0400543" />
                    <Key frame="63" value="0.0397273" />
                    <Key frame="76" value="0.0252311" />
                    <Key frame="88" value="0.00782127" />
                    <Key frame="101" value="0.0305697" />
                    <Key frame="113" value="0.0432" />
                    <Key frame="126" value="0.0432" />
                    <Key frame="138" value="0.043328" />
                    <Key frame="151" value="0.0446047" />
                    <Key frame="163" value="0.243707" />
                    <Key frame="176" value="0.316814" />
                    <Key frame="188" value="0.306946" />
                    <Key frame="201" value="0.300016" />
                    <Key frame="213" value="0.3" />
                    <Key frame="226" value="0.3" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="13" value="-37.0048" />
                    <Key frame="26" value="-45.044" />
                    <Key frame="38" value="-68.1673" />
                    <Key frame="51" value="-85.9698" />
                    <Key frame="63" value="-86.953" />
                    <Key frame="76" value="-78.9711" />
                    <Key frame="88" value="-69.385" />
                    <Key frame="101" value="-81.3393" />
                    <Key frame="113" value="-70.7388" />
                    <Key frame="126" value="-20.4068" />
                    <Key frame="138" value="-26.3179" />
                    <Key frame="151" value="-40.0186" />
                    <Key frame="163" value="-38.0009" />
                    <Key frame="176" value="-23.4588" />
                    <Key frame="188" value="-4.33948" />
                    <Key frame="201" value="7.27488" />
                    <Key frame="213" value="7.30058" />
                    <Key frame="226" value="7.30058" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="13" value="8.08845" />
                    <Key frame="26" value="9.90785" />
                    <Key frame="38" value="15.141" />
                    <Key frame="51" value="19.17" />
                    <Key frame="63" value="19.387" />
                    <Key frame="76" value="17.2885" />
                    <Key frame="88" value="14.7682" />
                    <Key frame="101" value="17.1168" />
                    <Key frame="113" value="17.8531" />
                    <Key frame="126" value="14.0106" />
                    <Key frame="138" value="5.40602" />
                    <Key frame="151" value="-3.33044" />
                    <Key frame="163" value="-1.55134" />
                    <Key frame="176" value="-1.57144" />
                    <Key frame="188" value="-4.6087" />
                    <Key frame="201" value="-6.81885" />
                    <Key frame="213" value="-6.82407" />
                    <Key frame="226" value="-6.82407" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="13" value="0.881327" />
                    <Key frame="26" value="-9.76709" />
                    <Key frame="38" value="-40.3954" />
                    <Key frame="51" value="-63.9758" />
                    <Key frame="63" value="-65.4318" />
                    <Key frame="76" value="-63.0335" />
                    <Key frame="88" value="-60.1532" />
                    <Key frame="101" value="-63.6113" />
                    <Key frame="113" value="-65.603" />
                    <Key frame="126" value="-65.6199" />
                    <Key frame="138" value="-57.0623" />
                    <Key frame="151" value="-36.4825" />
                    <Key frame="163" value="-19.1962" />
                    <Key frame="176" value="-11.9693" />
                    <Key frame="188" value="-10.0384" />
                    <Key frame="201" value="-9.74086" />
                    <Key frame="213" value="-9.74084" />
                    <Key frame="226" value="-9.74084" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="13" value="61.0872" />
                    <Key frame="26" value="70.7077" />
                    <Key frame="38" value="98.3795" />
                    <Key frame="51" value="119.684" />
                    <Key frame="63" value="121.04" />
                    <Key frame="76" value="121.04" />
                    <Key frame="88" value="121.04" />
                    <Key frame="101" value="121.04" />
                    <Key frame="113" value="121.016" />
                    <Key frame="126" value="120.944" />
                    <Key frame="138" value="120.976" />
                    <Key frame="151" value="121.037" />
                    <Key frame="163" value="106.452" />
                    <Key frame="176" value="68.4957" />
                    <Key frame="188" value="20.3641" />
                    <Key frame="201" value="-5.22946" />
                    <Key frame="213" value="-5.29" />
                    <Key frame="226" value="-5.29" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="13" value="18.3723" />
                    <Key frame="26" value="16.3957" />
                    <Key frame="38" value="9.34501" />
                    <Key frame="51" value="3.07836" />
                    <Key frame="63" value="2.48335" />
                    <Key frame="76" value="3.74222" />
                    <Key frame="88" value="6.30597" />
                    <Key frame="101" value="8.217" />
                    <Key frame="113" value="20.2581" />
                    <Key frame="126" value="48.1535" />
                    <Key frame="138" value="52.9027" />
                    <Key frame="151" value="54.4313" />
                    <Key frame="163" value="77.681" />
                    <Key frame="176" value="91.8373" />
                    <Key frame="188" value="87.0539" />
                    <Key frame="201" value="83.1043" />
                    <Key frame="213" value="82.6682" />
                    <Key frame="226" value="82.6545" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="13" value="0.437528" />
                    <Key frame="26" value="0.320655" />
                    <Key frame="38" value="-0.115237" />
                    <Key frame="51" value="-0.463776" />
                    <Key frame="63" value="-0.511423" />
                    <Key frame="76" value="-1.33715" />
                    <Key frame="88" value="-3.46376" />
                    <Key frame="101" value="-8.26691" />
                    <Key frame="113" value="-8.78924" />
                    <Key frame="126" value="-1.60794" />
                    <Key frame="138" value="-5.78239" />
                    <Key frame="151" value="-18.5058" />
                    <Key frame="163" value="-18.6921" />
                    <Key frame="176" value="-16.7334" />
                    <Key frame="188" value="-14.9106" />
                    <Key frame="201" value="-13.1507" />
                    <Key frame="213" value="-12.8712" />
                    <Key frame="226" value="-12.8608" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="13" value="19.3334" />
                    <Key frame="26" value="15.4144" />
                    <Key frame="38" value="0.73671" />
                    <Key frame="51" value="-10.9996" />
                    <Key frame="63" value="-12.2179" />
                    <Key frame="76" value="-11.9229" />
                    <Key frame="88" value="-9.99535" />
                    <Key frame="101" value="44.4538" />
                    <Key frame="113" value="83.1646" />
                    <Key frame="126" value="83.1501" />
                    <Key frame="138" value="83.1432" />
                    <Key frame="151" value="83.1432" />
                    <Key frame="163" value="64.6426" />
                    <Key frame="176" value="38.1583" />
                    <Key frame="188" value="17.466" />
                    <Key frame="201" value="6.15643" />
                    <Key frame="213" value="5.72982" />
                    <Key frame="226" value="5.72958" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="Krabbelfußball Exit" id="7" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="564" y="216">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="60">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="20" value="23.201" />
                    <Key frame="39" value="19.8611" />
                    <Key frame="60" value="8.87466" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="20" value="-1.58445" />
                    <Key frame="39" value="-1.58445" />
                    <Key frame="60" value="-1.32078" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="20" value="31.9023" />
                    <Key frame="39" value="31.4628" />
                    <Key frame="60" value="49.6564" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="20" value="0.441859" />
                    <Key frame="39" value="0.705531" />
                    <Key frame="60" value="-2.28279" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="20" value="-20.8279" />
                    <Key frame="39" value="-86.4831" />
                    <Key frame="60" value="-86.6588" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="20" value="-59.8567" />
                    <Key frame="39" value="-97.1228" />
                    <Key frame="60" value="-53.5285" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="20" value="0.312" />
                    <Key frame="39" value="0.32" />
                    <Key frame="60" value="0.386" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="20" value="-31.5507" />
                    <Key frame="39" value="-31.3749" />
                    <Key frame="60" value="21.7996" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="20" value="-3.0738" />
                    <Key frame="39" value="-2.63435" />
                    <Key frame="60" value="6.24271" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="20" value="-4.04062" />
                    <Key frame="39" value="-3.95273" />
                    <Key frame="60" value="-28.8261" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="20" value="99.0516" />
                    <Key frame="39" value="99.3152" />
                    <Key frame="60" value="-4.92435" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="20" value="112.675" />
                    <Key frame="39" value="117.773" />
                    <Key frame="60" value="107.401" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="20" value="20.3885" />
                    <Key frame="39" value="18.6306" />
                    <Key frame="60" value="18.2791" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="20" value="41.6583" />
                    <Key frame="39" value="8.87466" />
                    <Key frame="60" value="11.6872" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="20" value="32.8739" />
                    <Key frame="39" value="32.6981" />
                    <Key frame="60" value="37.0927" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="20" value="-4.56796" />
                    <Key frame="39" value="-4.74374" />
                    <Key frame="60" value="-16.2576" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="20" value="15.8229" />
                    <Key frame="39" value="86.4" />
                    <Key frame="60" value="86.7515" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="20" value="65.9164" />
                    <Key frame="39" value="95.9754" />
                    <Key frame="60" value="52.6447" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="20" value="0.3528" />
                    <Key frame="39" value="0.3528" />
                    <Key frame="60" value="0.3596" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="20" value="-36.3017" />
                    <Key frame="39" value="-36.7411" />
                    <Key frame="60" value="-22.9421" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="20" value="0.266077" />
                    <Key frame="39" value="-0.0854867" />
                    <Key frame="60" value="-4.74374" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="20" value="-4.04062" />
                    <Key frame="39" value="-3.95273" />
                    <Key frame="60" value="-28.8261" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="20" value="103.275" />
                    <Key frame="39" value="102.924" />
                    <Key frame="60" value="99.6716" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="20" value="107.055" />
                    <Key frame="39" value="111.098" />
                    <Key frame="60" value="100.111" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="20" value="-22.5027" />
                    <Key frame="39" value="-19.0749" />
                    <Key frame="60" value="-18.6354" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="20" value="-61.0872" />
                    <Key frame="39" value="2.45857" />
                    <Key frame="60" value="11.1598" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Link inputowner="4" indexofinput="2" outputowner="4" indexofoutput="4" />
            <Link inputowner="3" indexofinput="2" outputowner="3" indexofoutput="4" />
            <Link inputowner="6" indexofinput="2" outputowner="6" indexofoutput="4" />
            <Link inputowner="8" indexofinput="2" outputowner="8" indexofoutput="4" />
            <Link inputowner="9" indexofinput="2" outputowner="9" indexofoutput="4" />
            <Link inputowner="10" indexofinput="2" outputowner="10" indexofoutput="4" />
            <Link inputowner="18" indexofinput="2" outputowner="17" indexofoutput="4" />
            <Link inputowner="19" indexofinput="2" outputowner="18" indexofoutput="4" />
            <Link inputowner="13" indexofinput="2" outputowner="14" indexofoutput="4" />
            <Link inputowner="5" indexofinput="2" outputowner="5" indexofoutput="4" />
            <Link inputowner="14" indexofinput="2" outputowner="0" indexofoutput="2" />
            <Link inputowner="13" indexofinput="2" outputowner="13" indexofoutput="4" />
          </Diagram>
        </BehaviorKeyframe>
      </BehaviorLayer>
    </Timeline>
  </Box>
</ChoregrapheProject>
