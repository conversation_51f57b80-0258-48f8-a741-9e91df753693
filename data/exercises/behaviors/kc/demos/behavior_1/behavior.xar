<?xml version="1.0" encoding="UTF-8" ?>
<ChoregrapheProject xmlns="http://www.ald.softbankrobotics.com/schema/choregraphe/project.xsd" xar_version="3">
  <Box name="root" id="-1" localization="8" tooltip="Root box of Choregraphe&apos;s behavior. Highest level possible." x="0" y="0">
    <bitmap>media/images/box/root.png</bitmap>
    <script language="4">
      <content>
        <![CDATA[]]>
      </content>
    </script>
    <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
    <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
    <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
    <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
    <Timeline enable="0">
      <BehaviorLayer name="behavior_layer1">
        <BehaviorKeyframe name="keyframe1" index="1">
          <Diagram>
            <Box name="AepfelPflueckenLiegend_Demo" id="1" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="1673" y="65">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="85">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="25" value="9.75358" />
                    <Key frame="40" value="9.75358" />
                    <Key frame="55" value="9.75358" />
                    <Key frame="70" value="9.75358" />
                    <Key frame="85" value="9.75358" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="25" value="-0.705531" />
                    <Key frame="40" value="-0.705531" />
                    <Key frame="55" value="-0.705531" />
                    <Key frame="70" value="-0.705531" />
                    <Key frame="85" value="-0.705531" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="25" value="49.0412" />
                    <Key frame="40" value="49.0412" />
                    <Key frame="55" value="49.0412" />
                    <Key frame="70" value="49.0412" />
                    <Key frame="85" value="49.0412" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="25" value="-2.1949" />
                    <Key frame="40" value="-2.1949" />
                    <Key frame="55" value="-2.1949" />
                    <Key frame="70" value="-2.1949" />
                    <Key frame="85" value="-2.1949" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="25" value="-86.8346" />
                    <Key frame="40" value="-86.8346" />
                    <Key frame="55" value="-26.0135" />
                    <Key frame="70" value="-87.9772" />
                    <Key frame="85" value="-87.9772" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="25" value="-57.4836" />
                    <Key frame="40" value="-57.4836" />
                    <Key frame="55" value="-61.3508" />
                    <Key frame="70" value="-59.4172" />
                    <Key frame="85" value="-54.9347" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="25" value="0.294" />
                    <Key frame="40" value="0.294" />
                    <Key frame="55" value="0.5988" />
                    <Key frame="70" value="0.3068" />
                    <Key frame="85" value="0.35" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="25" value="21.5359" />
                    <Key frame="40" value="21.5359" />
                    <Key frame="55" value="21.5359" />
                    <Key frame="70" value="21.5359" />
                    <Key frame="85" value="21.5359" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="25" value="6.41851" />
                    <Key frame="40" value="6.41851" />
                    <Key frame="55" value="6.41851" />
                    <Key frame="70" value="6.41851" />
                    <Key frame="85" value="6.41851" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="25" value="-29.1777" />
                    <Key frame="40" value="-29.1777" />
                    <Key frame="55" value="-29.1777" />
                    <Key frame="70" value="-29.1777" />
                    <Key frame="85" value="-29.1777" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="25" value="-5.3638" />
                    <Key frame="40" value="-5.3638" />
                    <Key frame="55" value="-5.3638" />
                    <Key frame="70" value="-5.3638" />
                    <Key frame="85" value="-5.3638" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="25" value="72.0688" />
                    <Key frame="40" value="72.0688" />
                    <Key frame="55" value="21.7068" />
                    <Key frame="70" value="69.52" />
                    <Key frame="85" value="70.9262" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="25" value="7.99575" />
                    <Key frame="40" value="7.99575" />
                    <Key frame="55" value="-7.82477" />
                    <Key frame="70" value="6.76526" />
                    <Key frame="85" value="15.0271" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="25" value="-11.5162" />
                    <Key frame="40" value="-11.5162" />
                    <Key frame="55" value="-12.1315" />
                    <Key frame="70" value="-12.9225" />
                    <Key frame="85" value="-28.3914" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="25" value="32.786" />
                    <Key frame="40" value="32.786" />
                    <Key frame="55" value="32.786" />
                    <Key frame="70" value="32.786" />
                    <Key frame="85" value="32.786" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="25" value="-16.2576" />
                    <Key frame="40" value="-16.2576" />
                    <Key frame="55" value="-16.2576" />
                    <Key frame="70" value="-16.2576" />
                    <Key frame="85" value="-16.2576" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="25" value="87.4547" />
                    <Key frame="40" value="87.4547" />
                    <Key frame="55" value="87.4547" />
                    <Key frame="70" value="87.4547" />
                    <Key frame="85" value="25.6668" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="25" value="52.0295" />
                    <Key frame="40" value="52.0295" />
                    <Key frame="55" value="52.6447" />
                    <Key frame="70" value="52.6447" />
                    <Key frame="85" value="61.6097" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="25" value="0.3532" />
                    <Key frame="40" value="0.3532" />
                    <Key frame="55" value="0.3532" />
                    <Key frame="70" value="0.3532" />
                    <Key frame="85" value="0.6076" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="25" value="23.9041" />
                    <Key frame="40" value="23.9041" />
                    <Key frame="55" value="23.9041" />
                    <Key frame="70" value="23.9041" />
                    <Key frame="85" value="23.9041" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="25" value="-4.65585" />
                    <Key frame="40" value="-4.65585" />
                    <Key frame="55" value="-4.65585" />
                    <Key frame="70" value="-4.65585" />
                    <Key frame="85" value="-4.65585" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="25" value="-29.1777" />
                    <Key frame="40" value="-29.1777" />
                    <Key frame="55" value="-29.1777" />
                    <Key frame="70" value="-29.1777" />
                    <Key frame="85" value="-29.1777" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="25" value="-6.2379" />
                    <Key frame="40" value="-5.62267" />
                    <Key frame="55" value="-5.62267" />
                    <Key frame="70" value="-5.62267" />
                    <Key frame="85" value="-5.62267" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="25" value="72.6889" />
                    <Key frame="40" value="72.6889" />
                    <Key frame="55" value="72.6889" />
                    <Key frame="70" value="72.6889" />
                    <Key frame="85" value="21.7117" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="25" value="-14.7682" />
                    <Key frame="40" value="-14.7682" />
                    <Key frame="55" value="-14.7682" />
                    <Key frame="70" value="-14.7682" />
                    <Key frame="85" value="8.08364" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="25" value="29.6171" />
                    <Key frame="40" value="29.6171" />
                    <Key frame="55" value="29.6171" />
                    <Key frame="70" value="29.6171" />
                    <Key frame="85" value="13.8845" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="GotoLyingBack" id="2" localization="8" tooltip="The robot goes from its current postition to the asked posture." x="85" y="281">
              <bitmap>media/images/box/box-diagram.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[class MyClass(GeneratedClass):
    def __init__(self):
        GeneratedClass.__init__(self, False)

    def onLoad(self):
        self.nTries = 0
        self.postureService = self.session().service("ALRobotPosture")
        pass

    def onUnload(self):
        self.postureService.stopMove()

    def onInput_onStart(self):
        if(self.nTries != self.getParameter("Maximum of tries")):
            self.nTries = self.getParameter("Maximum of tries")
            self.postureService.setMaxTryNumber(self.nTries)

        result = self.postureService.goToPosture(self.getParameter("Name"), self.getParameter("Speed (%)")/100.)
        if(result):
            self.success()
        else:
            self.failure()
        pass

    def onInput_onStop(self):
        self.onUnload() #~ it is recommanded to call onUnload of this box in a onStop method, as the code written in onUnload is used to stop the box as well
        pass]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="success" type="1" type_size="1" nature="1" inner="0" tooltip="Stimulated if the posture has been reached." id="4" />
              <Output name="failure" type="1" type_size="1" nature="1" inner="0" tooltip="Stimulated if the posture could not be reached." id="5" />
              <Parameter name="Name" inherits_from_parent="0" content_type="3" value="LyingBack" default_value="Stand" custom_choice="1" tooltip="Name of the posture to go to." id="6">
                <Choice value="Crouch" />
                <Choice value="LyingBack" />
                <Choice value="LyingBelly" />
                <Choice value="Sit" />
                <Choice value="SitRelax" />
                <Choice value="StandInit" />
                <Choice value="Stand" />
                <Choice value="StandZero" />
              </Parameter>
              <Parameter name="Speed (%)" inherits_from_parent="0" content_type="1" value="80" default_value="80" min="0" max="100" tooltip="Speed to go to the posture." id="7" />
              <Parameter name="Maximum of tries" inherits_from_parent="0" content_type="1" value="3" default_value="3" min="1" max="10" tooltip="The maximum number of fails of go to posture before stimulating the failure output." id="8" />
              <Resource name="All motors" type="Lock" timeout="0" />
              <Resource name="Stiffness" type="Lock" timeout="0" />
            </Box>
            <Box name="ApplyLyingBack" id="3" localization="8" tooltip="Set directly all the joints of the robot in the asked posture." x="141" y="63">
              <bitmap>media/images/box/movement/setRobotPosture.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[class MyClass(GeneratedClass):
    def __init__(self):
        GeneratedClass.__init__(self, False)

    def onLoad(self):
        self.postureService = self.session().service("ALRobotPosture")
        pass

    def onUnload(self):
        self.postureService.stopMove()

    def onInput_onStart(self):
        result = self.postureService.applyPosture(self.getParameter("Name"), self.getParameter("Speed (%)")/100.)
        if(result):
            self.success()
        else:
            self.failure()
        pass

    def onInput_onStop(self):
        self.onUnload() #~ it is recommanded to call onUnload of this box in a onStop method, as the code written in onUnload is used to stop the box as well
        pass]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="success" type="1" type_size="1" nature="1" inner="0" tooltip="Stimulated if the posture has been reached." id="4" />
              <Output name="failure" type="1" type_size="1" nature="1" inner="0" tooltip="Stimulated if the posture could not be reached." id="5" />
              <Parameter name="Name" inherits_from_parent="0" content_type="3" value="LyingBack" default_value="Sit" custom_choice="1" tooltip="Name of the posture to go to." id="6">
                <Choice value="Crouch" />
                <Choice value="LyingBack" />
                <Choice value="LyingBelly" />
                <Choice value="Sit" />
                <Choice value="SitRelax" />
                <Choice value="StandInit" />
                <Choice value="Stand" />
                <Choice value="StandZero" />
              </Parameter>
              <Parameter name="Speed (%)" inherits_from_parent="0" content_type="1" value="80" default_value="100" min="0" max="100" tooltip="Speed to go to the posture." id="7" />
              <Resource name="All motors" type="Lock" timeout="0" />
            </Box>
            <Box name="GotoCrouch" id="4" localization="8" tooltip="The robot goes from its current postition to the asked posture." x="99" y="173">
              <bitmap>media/images/box/box-diagram.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[class MyClass(GeneratedClass):
    def __init__(self):
        GeneratedClass.__init__(self, False)

    def onLoad(self):
        self.nTries = 0
        self.postureService = self.session().service("ALRobotPosture")
        pass

    def onUnload(self):
        self.postureService.stopMove()

    def onInput_onStart(self):
        if(self.nTries != self.getParameter("Maximum of tries")):
            self.nTries = self.getParameter("Maximum of tries")
            self.postureService.setMaxTryNumber(self.nTries)

        result = self.postureService.goToPosture(self.getParameter("Name"), self.getParameter("Speed (%)")/100.)
        if(result):
            self.success()
        else:
            self.failure()
        pass

    def onInput_onStop(self):
        self.onUnload() #~ it is recommanded to call onUnload of this box in a onStop method, as the code written in onUnload is used to stop the box as well
        pass]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="success" type="1" type_size="1" nature="1" inner="0" tooltip="Stimulated if the posture has been reached." id="4" />
              <Output name="failure" type="1" type_size="1" nature="1" inner="0" tooltip="Stimulated if the posture could not be reached." id="5" />
              <Parameter name="Name" inherits_from_parent="0" content_type="3" value="Crouch" default_value="Stand" custom_choice="1" tooltip="Name of the posture to go to." id="6">
                <Choice value="Crouch" />
                <Choice value="LyingBack" />
                <Choice value="LyingBelly" />
                <Choice value="Sit" />
                <Choice value="SitRelax" />
                <Choice value="StandInit" />
                <Choice value="Stand" />
                <Choice value="StandZero" />
              </Parameter>
              <Parameter name="Speed (%)" inherits_from_parent="0" content_type="1" value="80" default_value="80" min="0" max="100" tooltip="Speed to go to the posture." id="7" />
              <Parameter name="Maximum of tries" inherits_from_parent="0" content_type="1" value="3" default_value="3" min="1" max="10" tooltip="The maximum number of fails of go to posture before stimulating the failure output." id="8" />
              <Resource name="All motors" type="Lock" timeout="0" />
              <Resource name="Stiffness" type="Lock" timeout="0" />
            </Box>
            <Box name="ArmStrecken_Demo" id="5" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="88" y="407">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="300">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="10" value="9.75358" />
                    <Key frame="60" value="11.863" />
                    <Key frame="180" value="13.9724" />
                    <Key frame="300" value="11.863" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="10" value="-0.529749" />
                    <Key frame="60" value="-1.32078" />
                    <Key frame="180" value="-0.705531" />
                    <Key frame="300" value="-1.32078" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="49.217" />
                    <Key frame="60" value="38.1426" />
                    <Key frame="180" value="37.8789" />
                    <Key frame="300" value="38.1426" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-2.37068" />
                    <Key frame="60" value="14.3288" />
                    <Key frame="180" value="14.5924" />
                    <Key frame="300" value="14.3288" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="-86.7467" />
                    <Key frame="60" value="-85.7799" />
                    <Key frame="180" value="-53.6115" />
                    <Key frame="300" value="-85.7799" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="-50.8038" />
                    <Key frame="60" value="-50.1007" />
                    <Key frame="180" value="2.107" />
                    <Key frame="300" value="-50.1007" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="10" value="0.2956" />
                    <Key frame="60" value="0.2868" />
                    <Key frame="180" value="0.2964" />
                    <Key frame="300" value="0.2868" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="10" value="21.2722" />
                    <Key frame="60" value="-22.2342" />
                    <Key frame="180" value="-21.619" />
                    <Key frame="300" value="-22.2342" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="10" value="6.3306" />
                    <Key frame="60" value="3.07861" />
                    <Key frame="180" value="3.34228" />
                    <Key frame="300" value="3.07861" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.705" />
                    <Key frame="60" value="-23.1131" />
                    <Key frame="180" value="-23.3768" />
                    <Key frame="300" value="-23.1131" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="10" value="-5.01224" />
                    <Key frame="60" value="95.5359" />
                    <Key frame="180" value="94.8328" />
                    <Key frame="300" value="95.5359" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="100.37" />
                    <Key frame="60" value="101.6" />
                    <Key frame="180" value="-93.6071" />
                    <Key frame="300" value="101.6" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="13.0056" />
                    <Key frame="60" value="12.0388" />
                    <Key frame="180" value="14.0603" />
                    <Key frame="300" value="12.0388" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="10" value="13.0935" />
                    <Key frame="60" value="13.9724" />
                    <Key frame="180" value="-105.56" />
                    <Key frame="300" value="13.9724" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="36.9169" />
                    <Key frame="60" value="36.5654" />
                    <Key frame="180" value="36.829" />
                    <Key frame="300" value="36.5654" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-16.3455" />
                    <Key frame="60" value="-16.4334" />
                    <Key frame="180" value="-15.9939" />
                    <Key frame="300" value="-16.4334" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="87.191" />
                    <Key frame="60" value="87.0152" />
                    <Key frame="180" value="61.4387" />
                    <Key frame="300" value="87.0152" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="49.8322" />
                    <Key frame="60" value="48.7775" />
                    <Key frame="180" value="-3.25439" />
                    <Key frame="300" value="48.7775" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="10" value="0.3528" />
                    <Key frame="60" value="0.3488" />
                    <Key frame="180" value="0.3304" />
                    <Key frame="300" value="0.3488" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="10" value="-22.8542" />
                    <Key frame="60" value="-23.2937" />
                    <Key frame="180" value="-22.9421" />
                    <Key frame="300" value="-23.2937" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="10" value="-4.74374" />
                    <Key frame="60" value="-5.35899" />
                    <Key frame="180" value="-5.44688" />
                    <Key frame="300" value="-5.35899" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.705" />
                    <Key frame="60" value="-23.1131" />
                    <Key frame="180" value="-23.3768" />
                    <Key frame="300" value="-23.1131" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="10" value="99.5837" />
                    <Key frame="60" value="99.0564" />
                    <Key frame="180" value="98.0896" />
                    <Key frame="300" value="99.0564" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="101.781" />
                    <Key frame="60" value="103.978" />
                    <Key frame="180" value="-92.8992" />
                    <Key frame="300" value="103.978" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="-14.8561" />
                    <Key frame="60" value="-14.7682" />
                    <Key frame="180" value="-23.1179" />
                    <Key frame="300" value="-14.7682" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="10" value="12.4782" />
                    <Key frame="60" value="12.1267" />
                    <Key frame="180" value="103.798" />
                    <Key frame="300" value="12.1267" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="ArmStreckenAlt_Demo" id="6" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="92" y="520">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="540">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="10" value="9.75358" />
                    <Key frame="60" value="11.863" />
                    <Key frame="180" value="10.193" />
                    <Key frame="300" value="9.5778" />
                    <Key frame="420" value="10.193" />
                    <Key frame="540" value="9.5778" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="10" value="-0.529749" />
                    <Key frame="60" value="-1.32078" />
                    <Key frame="180" value="-1.49656" />
                    <Key frame="300" value="-1.49656" />
                    <Key frame="420" value="1.49656" />
                    <Key frame="540" value="-1.49656" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="49.217" />
                    <Key frame="60" value="38.1426" />
                    <Key frame="180" value="38.3184" />
                    <Key frame="300" value="38.3184" />
                    <Key frame="420" value="36.1259" />
                    <Key frame="540" value="38.3184" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-2.37068" />
                    <Key frame="60" value="14.3288" />
                    <Key frame="180" value="14.153" />
                    <Key frame="300" value="14.153" />
                    <Key frame="420" value="16.4334" />
                    <Key frame="540" value="14.153" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="-86.7467" />
                    <Key frame="60" value="-85.7799" />
                    <Key frame="180" value="-51.6779" />
                    <Key frame="300" value="-85.3405" />
                    <Key frame="420" value="-87.4547" />
                    <Key frame="540" value="-85.3405" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="-50.8038" />
                    <Key frame="60" value="-50.1007" />
                    <Key frame="180" value="-7.73688" />
                    <Key frame="300" value="-51.8585" />
                    <Key frame="420" value="-52.0295" />
                    <Key frame="540" value="-51.8585" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="10" value="0.2956" />
                    <Key frame="60" value="0.2868" />
                    <Key frame="180" value="0.2968" />
                    <Key frame="300" value="0.2968" />
                    <Key frame="420" value="0.3468" />
                    <Key frame="540" value="0.2968" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="10" value="21.2722" />
                    <Key frame="60" value="-22.2342" />
                    <Key frame="180" value="-22.2342" />
                    <Key frame="300" value="-22.2342" />
                    <Key frame="420" value="-23.3816" />
                    <Key frame="540" value="-22.2342" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="10" value="6.3306" />
                    <Key frame="60" value="3.07861" />
                    <Key frame="180" value="3.07861" />
                    <Key frame="300" value="3.07861" />
                    <Key frame="420" value="5.53478" />
                    <Key frame="540" value="3.07861" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.705" />
                    <Key frame="60" value="-23.1131" />
                    <Key frame="180" value="-23.2889" />
                    <Key frame="300" value="-23.2889" />
                    <Key frame="420" value="-23.2889" />
                    <Key frame="540" value="-23.2889" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="10" value="-5.01224" />
                    <Key frame="60" value="95.5359" />
                    <Key frame="180" value="96.0633" />
                    <Key frame="300" value="96.0633" />
                    <Key frame="420" value="98.7048" />
                    <Key frame="540" value="96.0633" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="100.37" />
                    <Key frame="60" value="101.6" />
                    <Key frame="180" value="-88.3336" />
                    <Key frame="300" value="98.9637" />
                    <Key frame="420" value="100.638" />
                    <Key frame="540" value="98.9637" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="13.0056" />
                    <Key frame="60" value="12.0388" />
                    <Key frame="180" value="10.1051" />
                    <Key frame="300" value="15.2908" />
                    <Key frame="420" value="16.8776" />
                    <Key frame="540" value="15.2908" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="10" value="13.0935" />
                    <Key frame="60" value="13.9724" />
                    <Key frame="180" value="-106.351" />
                    <Key frame="300" value="12.3903" />
                    <Key frame="420" value="-10.9841" />
                    <Key frame="540" value="12.3903" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="36.9169" />
                    <Key frame="60" value="36.5654" />
                    <Key frame="180" value="36.1259" />
                    <Key frame="300" value="36.9169" />
                    <Key frame="420" value="38.3184" />
                    <Key frame="540" value="36.9169" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-16.3455" />
                    <Key frame="60" value="-16.4334" />
                    <Key frame="180" value="-16.4334" />
                    <Key frame="300" value="-16.4334" />
                    <Key frame="420" value="-14.153" />
                    <Key frame="540" value="-16.4334" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="87.191" />
                    <Key frame="60" value="87.0152" />
                    <Key frame="180" value="87.4547" />
                    <Key frame="300" value="87.4547" />
                    <Key frame="420" value="51.6779" />
                    <Key frame="540" value="87.4547" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="49.8322" />
                    <Key frame="60" value="48.7775" />
                    <Key frame="180" value="52.0295" />
                    <Key frame="300" value="52.0295" />
                    <Key frame="420" value="7.73688" />
                    <Key frame="540" value="52.0295" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="10" value="0.3528" />
                    <Key frame="60" value="0.3488" />
                    <Key frame="180" value="0.3468" />
                    <Key frame="300" value="0.3468" />
                    <Key frame="420" value="0.2968" />
                    <Key frame="540" value="0.3468" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="10" value="-22.8542" />
                    <Key frame="60" value="-23.2937" />
                    <Key frame="180" value="-23.3816" />
                    <Key frame="300" value="-23.3816" />
                    <Key frame="420" value="-22.2342" />
                    <Key frame="540" value="-23.3816" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="10" value="-4.74374" />
                    <Key frame="60" value="-5.35899" />
                    <Key frame="180" value="-5.53478" />
                    <Key frame="300" value="-4.91954" />
                    <Key frame="420" value="-3.07861" />
                    <Key frame="540" value="-4.91954" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.705" />
                    <Key frame="60" value="-23.1131" />
                    <Key frame="180" value="-23.2889" />
                    <Key frame="300" value="-23.2889" />
                    <Key frame="420" value="-23.2889" />
                    <Key frame="540" value="-23.2889" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="10" value="99.5837" />
                    <Key frame="60" value="99.0564" />
                    <Key frame="180" value="98.7048" />
                    <Key frame="300" value="99.3201" />
                    <Key frame="420" value="96.0633" />
                    <Key frame="540" value="99.3201" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="101.781" />
                    <Key frame="60" value="103.978" />
                    <Key frame="180" value="100.638" />
                    <Key frame="300" value="100.638" />
                    <Key frame="420" value="-88.3336" />
                    <Key frame="540" value="100.638" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="-14.8561" />
                    <Key frame="60" value="-14.7682" />
                    <Key frame="180" value="-16.8776" />
                    <Key frame="300" value="-16.8776" />
                    <Key frame="420" value="-10.1051" />
                    <Key frame="540" value="-16.8776" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="10" value="12.4782" />
                    <Key frame="60" value="12.1267" />
                    <Key frame="180" value="10.9841" />
                    <Key frame="300" value="10.9841" />
                    <Key frame="420" value="106.351" />
                    <Key frame="540" value="10.9841" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="ArmStreckenRechts_Demo" id="7" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="97" y="637">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="300">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="10" value="9.75358" />
                    <Key frame="60" value="11.863" />
                    <Key frame="180" value="10.193" />
                    <Key frame="300" value="9.5778" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="10" value="-0.529749" />
                    <Key frame="60" value="-1.32078" />
                    <Key frame="180" value="1.49656" />
                    <Key frame="300" value="-1.49656" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="49.217" />
                    <Key frame="60" value="38.1426" />
                    <Key frame="180" value="36.1259" />
                    <Key frame="300" value="38.3184" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-2.37068" />
                    <Key frame="60" value="14.3288" />
                    <Key frame="180" value="16.4334" />
                    <Key frame="300" value="14.153" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="-86.7467" />
                    <Key frame="60" value="-85.7799" />
                    <Key frame="180" value="-87.4547" />
                    <Key frame="300" value="-85.3405" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="-50.8038" />
                    <Key frame="60" value="-50.1007" />
                    <Key frame="180" value="-52.0295" />
                    <Key frame="300" value="-51.8585" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="10" value="0.2956" />
                    <Key frame="60" value="0.2868" />
                    <Key frame="180" value="0.3468" />
                    <Key frame="300" value="0.2968" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="10" value="21.2722" />
                    <Key frame="60" value="-22.2342" />
                    <Key frame="180" value="-23.3816" />
                    <Key frame="300" value="-22.2342" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="10" value="6.3306" />
                    <Key frame="60" value="3.07861" />
                    <Key frame="180" value="5.53478" />
                    <Key frame="300" value="3.07861" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.705" />
                    <Key frame="60" value="-23.1131" />
                    <Key frame="180" value="-23.2889" />
                    <Key frame="300" value="-23.2889" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="10" value="-5.01224" />
                    <Key frame="60" value="95.5359" />
                    <Key frame="180" value="98.7048" />
                    <Key frame="300" value="96.0633" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="100.37" />
                    <Key frame="60" value="101.6" />
                    <Key frame="180" value="100.638" />
                    <Key frame="300" value="98.9637" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="13.0056" />
                    <Key frame="60" value="12.0388" />
                    <Key frame="180" value="16.8776" />
                    <Key frame="300" value="15.2908" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="10" value="13.0935" />
                    <Key frame="60" value="13.9724" />
                    <Key frame="180" value="-10.9841" />
                    <Key frame="300" value="12.3903" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="36.9169" />
                    <Key frame="60" value="36.5654" />
                    <Key frame="180" value="38.3184" />
                    <Key frame="300" value="36.9169" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-16.3455" />
                    <Key frame="60" value="-16.4334" />
                    <Key frame="180" value="-14.153" />
                    <Key frame="300" value="-16.4334" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="87.191" />
                    <Key frame="60" value="87.0152" />
                    <Key frame="180" value="51.6779" />
                    <Key frame="300" value="87.4547" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="49.8322" />
                    <Key frame="60" value="48.7775" />
                    <Key frame="180" value="7.73688" />
                    <Key frame="300" value="52.0295" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="10" value="0.3528" />
                    <Key frame="60" value="0.3488" />
                    <Key frame="180" value="0.2968" />
                    <Key frame="300" value="0.3468" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="10" value="-22.8542" />
                    <Key frame="60" value="-23.2937" />
                    <Key frame="180" value="-22.2342" />
                    <Key frame="300" value="-23.3816" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="10" value="-4.74374" />
                    <Key frame="60" value="-5.35899" />
                    <Key frame="180" value="-3.07861" />
                    <Key frame="300" value="-4.91954" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.705" />
                    <Key frame="60" value="-23.1131" />
                    <Key frame="180" value="-23.2889" />
                    <Key frame="300" value="-23.2889" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="10" value="99.5837" />
                    <Key frame="60" value="99.0564" />
                    <Key frame="180" value="96.0633" />
                    <Key frame="300" value="99.3201" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="101.781" />
                    <Key frame="60" value="103.978" />
                    <Key frame="180" value="-88.3336" />
                    <Key frame="300" value="100.638" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="-14.8561" />
                    <Key frame="60" value="-14.7682" />
                    <Key frame="180" value="-10.1051" />
                    <Key frame="300" value="-16.8776" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="10" value="12.4782" />
                    <Key frame="60" value="12.1267" />
                    <Key frame="180" value="106.351" />
                    <Key frame="300" value="10.9841" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="ArmStreckenLinks_Demo" id="9" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="101" y="749">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="300">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="10" value="9.75358" />
                    <Key frame="60" value="11.863" />
                    <Key frame="180" value="10.193" />
                    <Key frame="300" value="9.5778" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="10" value="-0.529749" />
                    <Key frame="60" value="-1.32078" />
                    <Key frame="180" value="-1.49656" />
                    <Key frame="300" value="-1.49656" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="49.217" />
                    <Key frame="60" value="38.1426" />
                    <Key frame="180" value="38.3184" />
                    <Key frame="300" value="38.3184" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-2.37068" />
                    <Key frame="60" value="14.3288" />
                    <Key frame="180" value="14.153" />
                    <Key frame="300" value="14.153" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="-86.7467" />
                    <Key frame="60" value="-85.7799" />
                    <Key frame="180" value="-51.6779" />
                    <Key frame="300" value="-85.3405" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="-50.8038" />
                    <Key frame="60" value="-50.1007" />
                    <Key frame="180" value="-7.73688" />
                    <Key frame="300" value="-51.8585" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="10" value="0.2956" />
                    <Key frame="60" value="0.2868" />
                    <Key frame="180" value="0.2968" />
                    <Key frame="300" value="0.2968" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="10" value="21.2722" />
                    <Key frame="60" value="-22.2342" />
                    <Key frame="180" value="-22.2342" />
                    <Key frame="300" value="-22.2342" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="10" value="6.3306" />
                    <Key frame="60" value="3.07861" />
                    <Key frame="180" value="3.07861" />
                    <Key frame="300" value="3.07861" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.705" />
                    <Key frame="60" value="-23.1131" />
                    <Key frame="180" value="-23.2889" />
                    <Key frame="300" value="-23.2889" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="10" value="-5.01224" />
                    <Key frame="60" value="95.5359" />
                    <Key frame="180" value="96.0633" />
                    <Key frame="300" value="96.0633" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="100.37" />
                    <Key frame="60" value="101.6" />
                    <Key frame="180" value="-88.3336" />
                    <Key frame="300" value="98.9637" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="13.0056" />
                    <Key frame="60" value="12.0388" />
                    <Key frame="180" value="10.1051" />
                    <Key frame="300" value="15.2908" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="10" value="13.0935" />
                    <Key frame="60" value="13.9724" />
                    <Key frame="180" value="-106.351" />
                    <Key frame="300" value="12.3903" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="36.9169" />
                    <Key frame="60" value="36.5654" />
                    <Key frame="180" value="36.1259" />
                    <Key frame="300" value="36.9169" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-16.3455" />
                    <Key frame="60" value="-16.4334" />
                    <Key frame="180" value="-16.4334" />
                    <Key frame="300" value="-16.4334" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="87.191" />
                    <Key frame="60" value="87.0152" />
                    <Key frame="180" value="87.4547" />
                    <Key frame="300" value="87.4547" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="49.8322" />
                    <Key frame="60" value="48.7775" />
                    <Key frame="180" value="52.0295" />
                    <Key frame="300" value="52.0295" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="10" value="0.3528" />
                    <Key frame="60" value="0.3488" />
                    <Key frame="180" value="0.3468" />
                    <Key frame="300" value="0.3468" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="10" value="-22.8542" />
                    <Key frame="60" value="-23.2937" />
                    <Key frame="180" value="-23.3816" />
                    <Key frame="300" value="-23.3816" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="10" value="-4.74374" />
                    <Key frame="60" value="-5.35899" />
                    <Key frame="180" value="-5.53478" />
                    <Key frame="300" value="-4.91954" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.705" />
                    <Key frame="60" value="-23.1131" />
                    <Key frame="180" value="-23.2889" />
                    <Key frame="300" value="-23.2889" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="10" value="99.5837" />
                    <Key frame="60" value="99.0564" />
                    <Key frame="180" value="98.7048" />
                    <Key frame="300" value="99.3201" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="101.781" />
                    <Key frame="60" value="103.978" />
                    <Key frame="180" value="100.638" />
                    <Key frame="300" value="100.638" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="-14.8561" />
                    <Key frame="60" value="-14.7682" />
                    <Key frame="180" value="-16.8776" />
                    <Key frame="300" value="-16.8776" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="10" value="12.4782" />
                    <Key frame="60" value="12.1267" />
                    <Key frame="180" value="10.9841" />
                    <Key frame="300" value="10.9841" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="Bauchatmung_Demo" id="8" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="343" y="985">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="85">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="10" value="9.48991" />
                    <Key frame="30" value="9.48991" />
                    <Key frame="45" value="9.48991" />
                    <Key frame="55" value="9.48991" />
                    <Key frame="75" value="9.48991" />
                    <Key frame="85" value="9.48991" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="10" value="-0.441859" />
                    <Key frame="30" value="-0.441859" />
                    <Key frame="45" value="-0.441859" />
                    <Key frame="55" value="-0.441859" />
                    <Key frame="75" value="-0.441859" />
                    <Key frame="85" value="-0.441859" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="49.5685" />
                    <Key frame="30" value="49.5685" />
                    <Key frame="45" value="49.5685" />
                    <Key frame="55" value="49.5685" />
                    <Key frame="75" value="49.5685" />
                    <Key frame="85" value="49.5685" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-2.28279" />
                    <Key frame="30" value="-2.28279" />
                    <Key frame="45" value="-2.28279" />
                    <Key frame="55" value="-2.28279" />
                    <Key frame="75" value="-2.28279" />
                    <Key frame="85" value="-2.28279" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="-84.7252" />
                    <Key frame="30" value="-84.11" />
                    <Key frame="45" value="-83.4947" />
                    <Key frame="55" value="-83.4947" />
                    <Key frame="75" value="-86.3952" />
                    <Key frame="85" value="-86.3952" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="-49.6612" />
                    <Key frame="30" value="-26.8973" />
                    <Key frame="45" value="-26.1941" />
                    <Key frame="55" value="-26.1941" />
                    <Key frame="75" value="-17.3171" />
                    <Key frame="85" value="-17.3171" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="10" value="0.2936" />
                    <Key frame="30" value="0.6968" />
                    <Key frame="45" value="0.6968" />
                    <Key frame="55" value="0.6968" />
                    <Key frame="75" value="0.6968" />
                    <Key frame="85" value="0.7072" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="10" value="21.6238" />
                    <Key frame="30" value="21.6238" />
                    <Key frame="45" value="21.6238" />
                    <Key frame="55" value="21.6238" />
                    <Key frame="75" value="21.6238" />
                    <Key frame="85" value="21.6238" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="10" value="6.41851" />
                    <Key frame="30" value="6.41851" />
                    <Key frame="45" value="6.41851" />
                    <Key frame="55" value="6.41851" />
                    <Key frame="75" value="6.41851" />
                    <Key frame="85" value="6.41851" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.2655" />
                    <Key frame="30" value="-29.2655" />
                    <Key frame="45" value="-29.2655" />
                    <Key frame="55" value="-29.2655" />
                    <Key frame="75" value="-29.2655" />
                    <Key frame="85" value="-29.2655" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="10" value="-5.45169" />
                    <Key frame="30" value="-5.45169" />
                    <Key frame="45" value="-5.45169" />
                    <Key frame="55" value="-5.45169" />
                    <Key frame="75" value="-5.45169" />
                    <Key frame="85" value="-5.45169" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="99.1395" />
                    <Key frame="30" value="84.2858" />
                    <Key frame="45" value="81.2096" />
                    <Key frame="55" value="81.2096" />
                    <Key frame="75" value="77.3423" />
                    <Key frame="85" value="77.3423" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="15.115" />
                    <Key frame="30" value="26.8925" />
                    <Key frame="45" value="27.5077" />
                    <Key frame="55" value="27.5077" />
                    <Key frame="75" value="38.1426" />
                    <Key frame="85" value="38.1426" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="10" value="13.3571" />
                    <Key frame="30" value="-38.0595" />
                    <Key frame="45" value="-39.5537" />
                    <Key frame="55" value="-39.5537" />
                    <Key frame="75" value="-41.2236" />
                    <Key frame="85" value="-41.2236" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="37.0927" />
                    <Key frame="30" value="37.708" />
                    <Key frame="45" value="37.708" />
                    <Key frame="55" value="37.708" />
                    <Key frame="75" value="37.708" />
                    <Key frame="85" value="37.708" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-16.0818" />
                    <Key frame="30" value="-16.0818" />
                    <Key frame="45" value="-16.0818" />
                    <Key frame="55" value="-16.0818" />
                    <Key frame="75" value="-16.0818" />
                    <Key frame="85" value="-16.0818" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="87.4547" />
                    <Key frame="30" value="84.4664" />
                    <Key frame="45" value="83.5874" />
                    <Key frame="55" value="83.5874" />
                    <Key frame="75" value="83.5874" />
                    <Key frame="85" value="83.5874" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="49.5685" />
                    <Key frame="30" value="23.7284" />
                    <Key frame="45" value="23.0252" />
                    <Key frame="55" value="23.0252" />
                    <Key frame="75" value="12.8298" />
                    <Key frame="85" value="12.8298" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="10" value="0.3532" />
                    <Key frame="30" value="0.6444" />
                    <Key frame="45" value="0.656" />
                    <Key frame="55" value="0.656" />
                    <Key frame="75" value="0.656" />
                    <Key frame="85" value="0.656" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="10" value="-22.6785" />
                    <Key frame="30" value="11.3356" />
                    <Key frame="45" value="11.3356" />
                    <Key frame="55" value="11.3356" />
                    <Key frame="75" value="11.3356" />
                    <Key frame="85" value="11.3356" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="10" value="-4.83163" />
                    <Key frame="30" value="-5.44688" />
                    <Key frame="45" value="-5.44688" />
                    <Key frame="55" value="-5.44688" />
                    <Key frame="75" value="-5.44688" />
                    <Key frame="85" value="-5.44688" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.2655" />
                    <Key frame="30" value="-29.2655" />
                    <Key frame="45" value="-29.2655" />
                    <Key frame="55" value="-29.2655" />
                    <Key frame="75" value="-29.2655" />
                    <Key frame="85" value="-29.2655" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="10" value="99.7595" />
                    <Key frame="30" value="15.735" />
                    <Key frame="45" value="15.735" />
                    <Key frame="55" value="15.735" />
                    <Key frame="75" value="15.735" />
                    <Key frame="85" value="15.735" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="98.6169" />
                    <Key frame="30" value="81.478" />
                    <Key frame="45" value="79.0171" />
                    <Key frame="55" value="79.0171" />
                    <Key frame="75" value="72.8646" />
                    <Key frame="85" value="72.8646" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="-16.4382" />
                    <Key frame="30" value="-28.3914" />
                    <Key frame="45" value="-29.0946" />
                    <Key frame="55" value="-29.0946" />
                    <Key frame="75" value="-36.1259" />
                    <Key frame="85" value="-36.1259" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="10" value="11.1598" />
                    <Key frame="30" value="34.4511" />
                    <Key frame="45" value="35.9453" />
                    <Key frame="55" value="35.9453" />
                    <Key frame="75" value="36.6484" />
                    <Key frame="85" value="36.6484" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="BeinAußen_Demo" id="10" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="80" y="877">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="130">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="10" value="9.48991" />
                    <Key frame="20" value="9.48991" />
                    <Key frame="30" value="9.48991" />
                    <Key frame="40" value="10.8083" />
                    <Key frame="85" value="10.8083" />
                    <Key frame="130" value="10.8083" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="10" value="-0.529749" />
                    <Key frame="20" value="-0.529749" />
                    <Key frame="30" value="-0.529749" />
                    <Key frame="40" value="-0.529749" />
                    <Key frame="85" value="-0.529749" />
                    <Key frame="130" value="-0.529749" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="49.5685" />
                    <Key frame="20" value="49.5685" />
                    <Key frame="30" value="49.5685" />
                    <Key frame="40" value="5.53478" />
                    <Key frame="85" value="4.91954" />
                    <Key frame="130" value="4.91954" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-2.1949" />
                    <Key frame="20" value="-2.1949" />
                    <Key frame="30" value="-2.1949" />
                    <Key frame="40" value="4.83644" />
                    <Key frame="85" value="5.45169" />
                    <Key frame="130" value="5.45169" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="-85.0768" />
                    <Key frame="20" value="-85.7799" />
                    <Key frame="30" value="-19.8611" />
                    <Key frame="40" value="-19.8611" />
                    <Key frame="85" value="-19.2459" />
                    <Key frame="130" value="-19.2459" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="-52.6496" />
                    <Key frame="20" value="-86.3121" />
                    <Key frame="30" value="-92.2887" />
                    <Key frame="40" value="-92.2887" />
                    <Key frame="85" value="-92.2887" />
                    <Key frame="130" value="-92.2887" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="10" value="0.2856" />
                    <Key frame="20" value="0.2856" />
                    <Key frame="30" value="0.2856" />
                    <Key frame="40" value="0.2856" />
                    <Key frame="85" value="0.2856" />
                    <Key frame="130" value="0.2856" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="10" value="21.448" />
                    <Key frame="20" value="21.448" />
                    <Key frame="30" value="21.448" />
                    <Key frame="40" value="20.8327" />
                    <Key frame="85" value="12.3073" />
                    <Key frame="130" value="19.5144" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="10" value="6.41851" />
                    <Key frame="20" value="6.41851" />
                    <Key frame="30" value="6.41851" />
                    <Key frame="40" value="6.41851" />
                    <Key frame="85" value="29.9735" />
                    <Key frame="130" value="7.03374" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.0898" />
                    <Key frame="20" value="-29.0898" />
                    <Key frame="30" value="-29.0898" />
                    <Key frame="40" value="-20.3885" />
                    <Key frame="85" value="-19.7732" />
                    <Key frame="130" value="-19.7732" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="10" value="-5.53958" />
                    <Key frame="20" value="-5.53958" />
                    <Key frame="30" value="-5.53958" />
                    <Key frame="40" value="-5.53958" />
                    <Key frame="85" value="-4.92435" />
                    <Key frame="130" value="-4.92435" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="102.04" />
                    <Key frame="20" value="103.095" />
                    <Key frame="30" value="107.401" />
                    <Key frame="40" value="108.72" />
                    <Key frame="85" value="108.72" />
                    <Key frame="130" value="108.72" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="16.3455" />
                    <Key frame="20" value="18.1912" />
                    <Key frame="30" value="14.1482" />
                    <Key frame="40" value="13.5329" />
                    <Key frame="85" value="13.5329" />
                    <Key frame="130" value="14.1482" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="10" value="11.9509" />
                    <Key frame="20" value="11.9509" />
                    <Key frame="30" value="12.654" />
                    <Key frame="40" value="12.654" />
                    <Key frame="85" value="12.654" />
                    <Key frame="130" value="12.654" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="36.7411" />
                    <Key frame="20" value="36.7411" />
                    <Key frame="30" value="31.1161" />
                    <Key frame="40" value="1.40867" />
                    <Key frame="85" value="4.92435" />
                    <Key frame="130" value="2.02391" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-16.4334" />
                    <Key frame="20" value="-16.4334" />
                    <Key frame="30" value="-16.4334" />
                    <Key frame="40" value="-6.58948" />
                    <Key frame="85" value="-5.88634" />
                    <Key frame="130" value="-5.88634" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="87.2789" />
                    <Key frame="20" value="82.7085" />
                    <Key frame="30" value="23.3816" />
                    <Key frame="40" value="23.3816" />
                    <Key frame="85" value="22.7664" />
                    <Key frame="130" value="22.7664" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="53.26" />
                    <Key frame="20" value="83.4947" />
                    <Key frame="30" value="90.1745" />
                    <Key frame="40" value="90.1745" />
                    <Key frame="85" value="90.1745" />
                    <Key frame="130" value="90.1745" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="10" value="0.3368" />
                    <Key frame="20" value="0.3368" />
                    <Key frame="30" value="0.3368" />
                    <Key frame="40" value="0.3368" />
                    <Key frame="85" value="0.3368" />
                    <Key frame="130" value="0.3368" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="10" value="-22.8542" />
                    <Key frame="20" value="-7.64898" />
                    <Key frame="30" value="21.9705" />
                    <Key frame="40" value="20.5643" />
                    <Key frame="85" value="12.3903" />
                    <Key frame="130" value="19.6853" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="10" value="-4.48007" />
                    <Key frame="20" value="-4.48007" />
                    <Key frame="30" value="-7.02893" />
                    <Key frame="40" value="-7.64417" />
                    <Key frame="85" value="-29.7929" />
                    <Key frame="130" value="-8.96255" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.0898" />
                    <Key frame="20" value="-29.0898" />
                    <Key frame="30" value="-29.0898" />
                    <Key frame="40" value="-20.3885" />
                    <Key frame="85" value="-19.7732" />
                    <Key frame="130" value="-19.7732" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="10" value="100.199" />
                    <Key frame="20" value="54.7589" />
                    <Key frame="30" value="-5.09532" />
                    <Key frame="40" value="-5.09532" />
                    <Key frame="85" value="-5.09532" />
                    <Key frame="130" value="-5.09532" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="102.66" />
                    <Key frame="20" value="103.363" />
                    <Key frame="30" value="107.67" />
                    <Key frame="40" value="108.285" />
                    <Key frame="85" value="108.285" />
                    <Key frame="130" value="108.285" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="-18.2839" />
                    <Key frame="20" value="-22.9421" />
                    <Key frame="30" value="-16.7018" />
                    <Key frame="40" value="-16.7018" />
                    <Key frame="85" value="-16.7018" />
                    <Key frame="130" value="-16.7018" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="10" value="12.5661" />
                    <Key frame="20" value="8.17153" />
                    <Key frame="30" value="5.88634" />
                    <Key frame="40" value="5.88634" />
                    <Key frame="85" value="5.88634" />
                    <Key frame="130" value="5.88634" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="BeinAußenLinks_Demo" id="11" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="203" y="872">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="130">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="10" value="10.2809" />
                    <Key frame="20" value="10.2809" />
                    <Key frame="30" value="10.2809" />
                    <Key frame="40" value="10.2809" />
                    <Key frame="85" value="10.2809" />
                    <Key frame="130" value="10.2809" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="10" value="-1.145" />
                    <Key frame="20" value="-1.145" />
                    <Key frame="30" value="-1.145" />
                    <Key frame="40" value="-1.145" />
                    <Key frame="85" value="-1.145" />
                    <Key frame="130" value="-1.145" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="49.4806" />
                    <Key frame="20" value="49.4806" />
                    <Key frame="30" value="49.4806" />
                    <Key frame="40" value="5.53478" />
                    <Key frame="85" value="4.91954" />
                    <Key frame="130" value="4.91954" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-1.84332" />
                    <Key frame="20" value="-1.84332" />
                    <Key frame="30" value="-1.84332" />
                    <Key frame="40" value="4.92435" />
                    <Key frame="85" value="5.53958" />
                    <Key frame="130" value="5.53958" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="-84.901" />
                    <Key frame="20" value="-86.1315" />
                    <Key frame="30" value="-19.6853" />
                    <Key frame="40" value="-19.6853" />
                    <Key frame="85" value="-19.0701" />
                    <Key frame="130" value="-19.0701" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="-53.7042" />
                    <Key frame="20" value="-85.1695" />
                    <Key frame="30" value="-92.3766" />
                    <Key frame="40" value="-92.3766" />
                    <Key frame="85" value="-92.3766" />
                    <Key frame="130" value="-92.3766" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="10" value="0.2856" />
                    <Key frame="20" value="0.2856" />
                    <Key frame="30" value="0.2856" />
                    <Key frame="40" value="0.2856" />
                    <Key frame="85" value="0.2856" />
                    <Key frame="130" value="0.2856" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="10" value="20.8327" />
                    <Key frame="20" value="20.8327" />
                    <Key frame="30" value="20.8327" />
                    <Key frame="40" value="20.8327" />
                    <Key frame="85" value="12.3073" />
                    <Key frame="130" value="19.6902" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="10" value="6.41851" />
                    <Key frame="20" value="6.41851" />
                    <Key frame="30" value="6.41851" />
                    <Key frame="40" value="6.41851" />
                    <Key frame="85" value="30.0614" />
                    <Key frame="130" value="7.12163" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.0898" />
                    <Key frame="20" value="-29.0898" />
                    <Key frame="30" value="-29.0898" />
                    <Key frame="40" value="-20.6522" />
                    <Key frame="85" value="-20.0369" />
                    <Key frame="130" value="-20.0369" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="10" value="-5.62747" />
                    <Key frame="20" value="-5.62747" />
                    <Key frame="30" value="-5.62747" />
                    <Key frame="40" value="-5.62747" />
                    <Key frame="85" value="-4.92435" />
                    <Key frame="130" value="-4.92435" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="102.567" />
                    <Key frame="20" value="103.182" />
                    <Key frame="30" value="107.665" />
                    <Key frame="40" value="108.368" />
                    <Key frame="85" value="108.983" />
                    <Key frame="130" value="108.983" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="16.6091" />
                    <Key frame="20" value="18.2791" />
                    <Key frame="30" value="14.0603" />
                    <Key frame="40" value="13.445" />
                    <Key frame="85" value="13.445" />
                    <Key frame="130" value="14.0603" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="10" value="12.654" />
                    <Key frame="20" value="12.654" />
                    <Key frame="30" value="12.654" />
                    <Key frame="40" value="12.654" />
                    <Key frame="85" value="12.654" />
                    <Key frame="130" value="12.654" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="37.1806" />
                    <Key frame="20" value="37.1806" />
                    <Key frame="30" value="30.7645" />
                    <Key frame="40" value="1.76024" />
                    <Key frame="85" value="1.76024" />
                    <Key frame="130" value="1.76024" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-16.4334" />
                    <Key frame="20" value="-16.4334" />
                    <Key frame="30" value="-16.4334" />
                    <Key frame="40" value="-6.32579" />
                    <Key frame="85" value="-6.32579" />
                    <Key frame="130" value="-6.32579" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="86.5758" />
                    <Key frame="20" value="84.2906" />
                    <Key frame="30" value="23.2058" />
                    <Key frame="40" value="23.2058" />
                    <Key frame="85" value="22.5906" />
                    <Key frame="130" value="22.5906" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="53.1721" />
                    <Key frame="20" value="81.7369" />
                    <Key frame="30" value="90.5261" />
                    <Key frame="40" value="90.5261" />
                    <Key frame="85" value="90.5261" />
                    <Key frame="130" value="90.5261" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="10" value="0.3368" />
                    <Key frame="20" value="0.3368" />
                    <Key frame="30" value="0.3368" />
                    <Key frame="40" value="0.3368" />
                    <Key frame="85" value="0.3368" />
                    <Key frame="130" value="0.3368" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="10" value="-22.239" />
                    <Key frame="20" value="-8.08845" />
                    <Key frame="30" value="22.3221" />
                    <Key frame="40" value="20.9158" />
                    <Key frame="85" value="20.9158" />
                    <Key frame="130" value="20.3006" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="10" value="-4.2164" />
                    <Key frame="20" value="-5.53478" />
                    <Key frame="30" value="-6.76526" />
                    <Key frame="40" value="-7.99575" />
                    <Key frame="85" value="-7.99575" />
                    <Key frame="130" value="-7.99575" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.0898" />
                    <Key frame="20" value="-29.0898" />
                    <Key frame="30" value="-29.0898" />
                    <Key frame="40" value="-20.6522" />
                    <Key frame="85" value="-20.0369" />
                    <Key frame="130" value="-20.0369" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="10" value="99.9353" />
                    <Key frame="20" value="55.3742" />
                    <Key frame="30" value="-5.62267" />
                    <Key frame="40" value="-5.62267" />
                    <Key frame="85" value="-5.62267" />
                    <Key frame="130" value="-5.62267" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="102.484" />
                    <Key frame="20" value="103.275" />
                    <Key frame="30" value="107.846" />
                    <Key frame="40" value="108.461" />
                    <Key frame="85" value="108.461" />
                    <Key frame="130" value="108.461" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="-18.5476" />
                    <Key frame="20" value="-23.4695" />
                    <Key frame="30" value="-16.0866" />
                    <Key frame="40" value="-16.0866" />
                    <Key frame="85" value="-16.0866" />
                    <Key frame="130" value="-16.0866" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="10" value="11.7751" />
                    <Key frame="20" value="8.69887" />
                    <Key frame="30" value="6.4137" />
                    <Key frame="40" value="5.53478" />
                    <Key frame="85" value="5.53478" />
                    <Key frame="130" value="5.53478" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="BeinAußenRechts_Demo" id="12" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="97" y="986">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="130">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="10" value="10.0173" />
                    <Key frame="20" value="10.0173" />
                    <Key frame="30" value="10.0173" />
                    <Key frame="40" value="10.6325" />
                    <Key frame="85" value="10.6325" />
                    <Key frame="130" value="10.6325" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="10" value="-1.145" />
                    <Key frame="20" value="-1.145" />
                    <Key frame="30" value="-1.145" />
                    <Key frame="40" value="-1.145" />
                    <Key frame="85" value="-1.145" />
                    <Key frame="130" value="-1.145" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="48.7775" />
                    <Key frame="20" value="49.3927" />
                    <Key frame="30" value="49.3927" />
                    <Key frame="40" value="5.09532" />
                    <Key frame="85" value="1.93121" />
                    <Key frame="130" value="4.74374" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-2.1949" />
                    <Key frame="20" value="-2.1949" />
                    <Key frame="30" value="-2.1949" />
                    <Key frame="40" value="4.92435" />
                    <Key frame="85" value="5.53958" />
                    <Key frame="130" value="5.53958" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="-85.0768" />
                    <Key frame="20" value="-85.7799" />
                    <Key frame="30" value="-19.7732" />
                    <Key frame="40" value="-19.7732" />
                    <Key frame="85" value="-19.158" />
                    <Key frame="130" value="-19.158" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="-53.4406" />
                    <Key frame="20" value="-84.9058" />
                    <Key frame="30" value="-92.0251" />
                    <Key frame="40" value="-92.0251" />
                    <Key frame="85" value="-92.0251" />
                    <Key frame="130" value="-92.0251" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="10" value="0.2856" />
                    <Key frame="20" value="0.2856" />
                    <Key frame="30" value="0.2856" />
                    <Key frame="40" value="0.2856" />
                    <Key frame="85" value="0.2856" />
                    <Key frame="130" value="0.2856" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="10" value="20.9206" />
                    <Key frame="20" value="20.9206" />
                    <Key frame="30" value="20.9206" />
                    <Key frame="40" value="20.9206" />
                    <Key frame="85" value="20.9206" />
                    <Key frame="130" value="20.3054" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="10" value="6.5064" />
                    <Key frame="20" value="6.5064" />
                    <Key frame="30" value="6.5064" />
                    <Key frame="40" value="6.5064" />
                    <Key frame="85" value="7.82477" />
                    <Key frame="130" value="7.82477" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.8808" />
                    <Key frame="20" value="-29.8808" />
                    <Key frame="30" value="-29.2655" />
                    <Key frame="40" value="-20.3885" />
                    <Key frame="85" value="-19.7732" />
                    <Key frame="130" value="-19.7732" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="10" value="-4.92435" />
                    <Key frame="20" value="-4.92435" />
                    <Key frame="30" value="-4.92435" />
                    <Key frame="40" value="-4.92435" />
                    <Key frame="85" value="-4.92435" />
                    <Key frame="130" value="-4.92435" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="102.479" />
                    <Key frame="20" value="103.095" />
                    <Key frame="30" value="107.753" />
                    <Key frame="40" value="108.456" />
                    <Key frame="85" value="108.456" />
                    <Key frame="130" value="108.456" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="16.697" />
                    <Key frame="20" value="18.4549" />
                    <Key frame="30" value="13.9724" />
                    <Key frame="40" value="13.9724" />
                    <Key frame="85" value="13.3571" />
                    <Key frame="130" value="13.3571" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="10" value="12.654" />
                    <Key frame="20" value="12.654" />
                    <Key frame="30" value="12.654" />
                    <Key frame="40" value="12.654" />
                    <Key frame="85" value="12.654" />
                    <Key frame="130" value="12.654" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="37.4443" />
                    <Key frame="20" value="37.4443" />
                    <Key frame="30" value="30.8524" />
                    <Key frame="40" value="1.40867" />
                    <Key frame="85" value="4.74855" />
                    <Key frame="130" value="2.02391" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-16.4334" />
                    <Key frame="20" value="-16.4334" />
                    <Key frame="30" value="-16.4334" />
                    <Key frame="40" value="-6.06212" />
                    <Key frame="85" value="-5.44688" />
                    <Key frame="130" value="-6.06212" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="86.6637" />
                    <Key frame="20" value="84.4664" />
                    <Key frame="30" value="23.1179" />
                    <Key frame="40" value="23.1179" />
                    <Key frame="85" value="22.5027" />
                    <Key frame="130" value="22.5027" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="52.6447" />
                    <Key frame="20" value="82.1764" />
                    <Key frame="30" value="90.0866" />
                    <Key frame="40" value="90.0866" />
                    <Key frame="85" value="90.0866" />
                    <Key frame="130" value="90.0866" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="10" value="0.3368" />
                    <Key frame="20" value="0.3368" />
                    <Key frame="30" value="0.3368" />
                    <Key frame="40" value="0.3368" />
                    <Key frame="85" value="0.3368" />
                    <Key frame="130" value="0.3368" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="10" value="-22.239" />
                    <Key frame="20" value="-8.00056" />
                    <Key frame="30" value="21.9705" />
                    <Key frame="40" value="20.6522" />
                    <Key frame="85" value="12.3903" />
                    <Key frame="130" value="19.6853" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="10" value="-5.18321" />
                    <Key frame="20" value="-5.18321" />
                    <Key frame="30" value="-6.58948" />
                    <Key frame="40" value="-7.90786" />
                    <Key frame="85" value="-29.8808" />
                    <Key frame="130" value="-8.69887" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.8808" />
                    <Key frame="20" value="-29.8808" />
                    <Key frame="30" value="-29.2655" />
                    <Key frame="40" value="-20.3885" />
                    <Key frame="85" value="-19.7732" />
                    <Key frame="130" value="-19.7732" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="10" value="100.551" />
                    <Key frame="20" value="55.3742" />
                    <Key frame="30" value="-5.35899" />
                    <Key frame="40" value="-5.35899" />
                    <Key frame="85" value="-5.35899" />
                    <Key frame="130" value="-5.35899" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="100.99" />
                    <Key frame="20" value="103.012" />
                    <Key frame="30" value="107.846" />
                    <Key frame="40" value="108.549" />
                    <Key frame="85" value="108.549" />
                    <Key frame="130" value="108.549" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="-17.4929" />
                    <Key frame="20" value="-22.8542" />
                    <Key frame="30" value="-16.7018" />
                    <Key frame="40" value="-16.7018" />
                    <Key frame="85" value="-16.7018" />
                    <Key frame="130" value="-16.7018" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="10" value="12.5661" />
                    <Key frame="20" value="9.05044" />
                    <Key frame="30" value="6.2379" />
                    <Key frame="40" value="5.62267" />
                    <Key frame="85" value="5.62267" />
                    <Key frame="130" value="5.62267" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="BeinAußenAlt_Demo" id="13" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="225" y="984">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="220">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="10" value="10.0173" />
                    <Key frame="20" value="10.0173" />
                    <Key frame="30" value="10.0173" />
                    <Key frame="40" value="10.6325" />
                    <Key frame="85" value="10.6325" />
                    <Key frame="130" value="10.6325" />
                    <Key frame="175" value="10.2809" />
                    <Key frame="220" value="10.2809" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="10" value="-1.145" />
                    <Key frame="20" value="-1.145" />
                    <Key frame="30" value="-1.145" />
                    <Key frame="40" value="-1.145" />
                    <Key frame="85" value="-1.145" />
                    <Key frame="130" value="-1.145" />
                    <Key frame="175" value="-1.145" />
                    <Key frame="220" value="-1.145" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="48.7775" />
                    <Key frame="20" value="49.3927" />
                    <Key frame="30" value="49.3927" />
                    <Key frame="40" value="5.09532" />
                    <Key frame="85" value="1.93121" />
                    <Key frame="130" value="4.74374" />
                    <Key frame="175" value="4.91954" />
                    <Key frame="220" value="4.91954" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-2.1949" />
                    <Key frame="20" value="-2.1949" />
                    <Key frame="30" value="-2.1949" />
                    <Key frame="40" value="4.92435" />
                    <Key frame="85" value="5.53958" />
                    <Key frame="130" value="5.53958" />
                    <Key frame="175" value="5.53958" />
                    <Key frame="220" value="5.53958" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="-85.0768" />
                    <Key frame="20" value="-85.7799" />
                    <Key frame="30" value="-19.7732" />
                    <Key frame="40" value="-19.7732" />
                    <Key frame="85" value="-19.158" />
                    <Key frame="130" value="-19.158" />
                    <Key frame="175" value="-19.0701" />
                    <Key frame="220" value="-19.0701" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="-53.4406" />
                    <Key frame="20" value="-84.9058" />
                    <Key frame="30" value="-92.0251" />
                    <Key frame="40" value="-92.0251" />
                    <Key frame="85" value="-92.0251" />
                    <Key frame="130" value="-92.0251" />
                    <Key frame="175" value="-92.3766" />
                    <Key frame="220" value="-92.3766" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="10" value="0.2856" />
                    <Key frame="20" value="0.2856" />
                    <Key frame="30" value="0.2856" />
                    <Key frame="40" value="0.2856" />
                    <Key frame="85" value="0.2856" />
                    <Key frame="130" value="0.2856" />
                    <Key frame="175" value="0.2856" />
                    <Key frame="220" value="0.2856" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="10" value="20.9206" />
                    <Key frame="20" value="20.9206" />
                    <Key frame="30" value="20.9206" />
                    <Key frame="40" value="20.9206" />
                    <Key frame="85" value="20.9206" />
                    <Key frame="130" value="20.3054" />
                    <Key frame="175" value="12.3073" />
                    <Key frame="220" value="19.6902" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="10" value="6.5064" />
                    <Key frame="20" value="6.5064" />
                    <Key frame="30" value="6.5064" />
                    <Key frame="40" value="6.5064" />
                    <Key frame="85" value="7.82477" />
                    <Key frame="130" value="7.82477" />
                    <Key frame="175" value="30.0614" />
                    <Key frame="220" value="7.12163" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.8808" />
                    <Key frame="20" value="-29.8808" />
                    <Key frame="30" value="-29.2655" />
                    <Key frame="40" value="-20.3885" />
                    <Key frame="85" value="-19.7732" />
                    <Key frame="130" value="-19.7732" />
                    <Key frame="175" value="-20.0369" />
                    <Key frame="220" value="-20.0369" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="10" value="-4.92435" />
                    <Key frame="20" value="-4.92435" />
                    <Key frame="30" value="-4.92435" />
                    <Key frame="40" value="-4.92435" />
                    <Key frame="85" value="-4.92435" />
                    <Key frame="130" value="-4.92435" />
                    <Key frame="175" value="-4.92435" />
                    <Key frame="220" value="-4.92435" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="102.479" />
                    <Key frame="20" value="103.095" />
                    <Key frame="30" value="107.753" />
                    <Key frame="40" value="108.456" />
                    <Key frame="85" value="108.456" />
                    <Key frame="130" value="108.456" />
                    <Key frame="175" value="108.983" />
                    <Key frame="220" value="108.983" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="16.697" />
                    <Key frame="20" value="18.4549" />
                    <Key frame="30" value="13.9724" />
                    <Key frame="40" value="13.9724" />
                    <Key frame="85" value="13.3571" />
                    <Key frame="130" value="13.3571" />
                    <Key frame="175" value="13.445" />
                    <Key frame="220" value="14.0603" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="10" value="12.654" />
                    <Key frame="20" value="12.654" />
                    <Key frame="30" value="12.654" />
                    <Key frame="40" value="12.654" />
                    <Key frame="85" value="12.654" />
                    <Key frame="130" value="12.654" />
                    <Key frame="175" value="12.654" />
                    <Key frame="220" value="12.654" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="37.4443" />
                    <Key frame="20" value="37.4443" />
                    <Key frame="30" value="30.8524" />
                    <Key frame="40" value="1.40867" />
                    <Key frame="85" value="4.74855" />
                    <Key frame="130" value="2.02391" />
                    <Key frame="175" value="1.76024" />
                    <Key frame="220" value="1.76024" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-16.4334" />
                    <Key frame="20" value="-16.4334" />
                    <Key frame="30" value="-16.4334" />
                    <Key frame="40" value="-6.06212" />
                    <Key frame="85" value="-5.44688" />
                    <Key frame="130" value="-6.06212" />
                    <Key frame="175" value="-6.32579" />
                    <Key frame="220" value="-6.32579" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="86.6637" />
                    <Key frame="20" value="84.4664" />
                    <Key frame="30" value="23.1179" />
                    <Key frame="40" value="23.1179" />
                    <Key frame="85" value="22.5027" />
                    <Key frame="130" value="22.5027" />
                    <Key frame="175" value="22.5906" />
                    <Key frame="220" value="22.5906" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="52.6447" />
                    <Key frame="20" value="82.1764" />
                    <Key frame="30" value="90.0866" />
                    <Key frame="40" value="90.0866" />
                    <Key frame="85" value="90.0866" />
                    <Key frame="130" value="90.0866" />
                    <Key frame="175" value="90.5261" />
                    <Key frame="220" value="90.5261" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="10" value="0.3368" />
                    <Key frame="20" value="0.3368" />
                    <Key frame="30" value="0.3368" />
                    <Key frame="40" value="0.3368" />
                    <Key frame="85" value="0.3368" />
                    <Key frame="130" value="0.3368" />
                    <Key frame="175" value="0.3368" />
                    <Key frame="220" value="0.3368" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="10" value="-22.239" />
                    <Key frame="20" value="-8.00056" />
                    <Key frame="30" value="21.9705" />
                    <Key frame="40" value="20.6522" />
                    <Key frame="85" value="12.3903" />
                    <Key frame="130" value="19.6853" />
                    <Key frame="175" value="20.9158" />
                    <Key frame="220" value="20.3006" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="10" value="-5.18321" />
                    <Key frame="20" value="-5.18321" />
                    <Key frame="30" value="-6.58948" />
                    <Key frame="40" value="-7.90786" />
                    <Key frame="85" value="-29.8808" />
                    <Key frame="130" value="-8.69887" />
                    <Key frame="175" value="-7.99575" />
                    <Key frame="220" value="-7.99575" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.8808" />
                    <Key frame="20" value="-29.8808" />
                    <Key frame="30" value="-29.2655" />
                    <Key frame="40" value="-20.3885" />
                    <Key frame="85" value="-19.7732" />
                    <Key frame="130" value="-19.7732" />
                    <Key frame="175" value="-20.0369" />
                    <Key frame="220" value="-20.0369" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="10" value="100.551" />
                    <Key frame="20" value="55.3742" />
                    <Key frame="30" value="-5.35899" />
                    <Key frame="40" value="-5.35899" />
                    <Key frame="85" value="-5.35899" />
                    <Key frame="130" value="-5.35899" />
                    <Key frame="175" value="-5.62267" />
                    <Key frame="220" value="-5.62267" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="100.99" />
                    <Key frame="20" value="103.012" />
                    <Key frame="30" value="107.846" />
                    <Key frame="40" value="108.549" />
                    <Key frame="85" value="108.549" />
                    <Key frame="130" value="108.549" />
                    <Key frame="175" value="108.461" />
                    <Key frame="220" value="108.461" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="-17.4929" />
                    <Key frame="20" value="-22.8542" />
                    <Key frame="30" value="-16.7018" />
                    <Key frame="40" value="-16.7018" />
                    <Key frame="85" value="-16.7018" />
                    <Key frame="130" value="-16.7018" />
                    <Key frame="175" value="-16.0866" />
                    <Key frame="220" value="-16.0866" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="10" value="12.5661" />
                    <Key frame="20" value="9.05044" />
                    <Key frame="30" value="6.2379" />
                    <Key frame="40" value="5.62267" />
                    <Key frame="85" value="5.62267" />
                    <Key frame="130" value="5.62267" />
                    <Key frame="175" value="5.53478" />
                    <Key frame="220" value="5.53478" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="BeinBeugenRechts_Demo" id="14" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="221" y="485">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="175">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="10" value="10.0173" />
                    <Key frame="30" value="10.0173" />
                    <Key frame="50" value="10.0173" />
                    <Key frame="55" value="10.6325" />
                    <Key frame="85" value="10.6325" />
                    <Key frame="115" value="10.6325" />
                    <Key frame="145" value="10.6325" />
                    <Key frame="175" value="10.6325" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="10" value="0.173378" />
                    <Key frame="30" value="0.173378" />
                    <Key frame="50" value="0.173378" />
                    <Key frame="55" value="-0.441859" />
                    <Key frame="85" value="0.173378" />
                    <Key frame="115" value="0.173378" />
                    <Key frame="145" value="0.788627" />
                    <Key frame="175" value="0.788627" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="49.3927" />
                    <Key frame="30" value="49.3927" />
                    <Key frame="50" value="30.8476" />
                    <Key frame="55" value="31.1992" />
                    <Key frame="85" value="30.5839" />
                    <Key frame="115" value="30.5839" />
                    <Key frame="145" value="30.5839" />
                    <Key frame="175" value="30.5839" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-2.37068" />
                    <Key frame="30" value="-2.37068" />
                    <Key frame="50" value="15.8229" />
                    <Key frame="55" value="15.6471" />
                    <Key frame="85" value="16.2624" />
                    <Key frame="115" value="16.2624" />
                    <Key frame="145" value="16.2624" />
                    <Key frame="175" value="16.2624" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="-86.6588" />
                    <Key frame="30" value="-86.0436" />
                    <Key frame="50" value="-22.9373" />
                    <Key frame="55" value="-21.0916" />
                    <Key frame="85" value="-20.4764" />
                    <Key frame="115" value="-20.4764" />
                    <Key frame="145" value="-19.8611" />
                    <Key frame="175" value="-21.2674" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="-53.089" />
                    <Key frame="30" value="-84.8179" />
                    <Key frame="50" value="-90.2672" />
                    <Key frame="55" value="-91.7614" />
                    <Key frame="85" value="-91.7614" />
                    <Key frame="115" value="-91.7614" />
                    <Key frame="145" value="-91.7614" />
                    <Key frame="175" value="-91.7614" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="10" value="0.2848" />
                    <Key frame="30" value="0.2848" />
                    <Key frame="50" value="0.3356" />
                    <Key frame="55" value="0.33" />
                    <Key frame="85" value="0.33" />
                    <Key frame="115" value="0.33" />
                    <Key frame="145" value="0.33" />
                    <Key frame="175" value="0.33" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="10" value="20.9206" />
                    <Key frame="30" value="20.9206" />
                    <Key frame="50" value="22.1511" />
                    <Key frame="55" value="21.7996" />
                    <Key frame="85" value="21.7996" />
                    <Key frame="115" value="21.7996" />
                    <Key frame="145" value="21.7996" />
                    <Key frame="175" value="21.7996" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="10" value="6.77007" />
                    <Key frame="30" value="6.77007" />
                    <Key frame="50" value="6.77007" />
                    <Key frame="55" value="7.03374" />
                    <Key frame="85" value="7.03374" />
                    <Key frame="115" value="7.03374" />
                    <Key frame="145" value="7.03374" />
                    <Key frame="175" value="7.64898" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.2655" />
                    <Key frame="30" value="-29.2655" />
                    <Key frame="50" value="-29.2655" />
                    <Key frame="55" value="-29.5292" />
                    <Key frame="85" value="-24.0799" />
                    <Key frame="115" value="-24.0799" />
                    <Key frame="145" value="-24.0799" />
                    <Key frame="175" value="-29.0019" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="10" value="-5.62747" />
                    <Key frame="30" value="-5.62747" />
                    <Key frame="50" value="-5.62747" />
                    <Key frame="55" value="-5.71537" />
                    <Key frame="85" value="-5.71537" />
                    <Key frame="115" value="-5.71537" />
                    <Key frame="145" value="-5.71537" />
                    <Key frame="175" value="-5.71537" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="102.304" />
                    <Key frame="30" value="103.095" />
                    <Key frame="50" value="107.929" />
                    <Key frame="55" value="108.544" />
                    <Key frame="85" value="108.544" />
                    <Key frame="115" value="109.159" />
                    <Key frame="145" value="109.159" />
                    <Key frame="175" value="109.159" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="15.9939" />
                    <Key frame="30" value="18.4549" />
                    <Key frame="50" value="14.8513" />
                    <Key frame="55" value="15.4665" />
                    <Key frame="85" value="15.4665" />
                    <Key frame="115" value="16.0818" />
                    <Key frame="145" value="16.0818" />
                    <Key frame="175" value="16.0818" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="10" value="12.4782" />
                    <Key frame="30" value="12.4782" />
                    <Key frame="50" value="-2.99072" />
                    <Key frame="55" value="-2.72705" />
                    <Key frame="85" value="-2.72705" />
                    <Key frame="115" value="-2.72705" />
                    <Key frame="145" value="-2.72705" />
                    <Key frame="175" value="-2.72705" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="37.4443" />
                    <Key frame="30" value="37.4443" />
                    <Key frame="50" value="49.5733" />
                    <Key frame="55" value="49.5733" />
                    <Key frame="85" value="36.5654" />
                    <Key frame="115" value="36.7411" />
                    <Key frame="145" value="36.7411" />
                    <Key frame="175" value="49.046" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-16.3455" />
                    <Key frame="30" value="-16.3455" />
                    <Key frame="50" value="1.84813" />
                    <Key frame="55" value="2.28759" />
                    <Key frame="85" value="-16.4334" />
                    <Key frame="115" value="-7.11683" />
                    <Key frame="145" value="-16.0818" />
                    <Key frame="175" value="2.28759" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="87.3668" />
                    <Key frame="30" value="84.6421" />
                    <Key frame="50" value="19.778" />
                    <Key frame="55" value="19.778" />
                    <Key frame="85" value="19.1628" />
                    <Key frame="115" value="18.5476" />
                    <Key frame="145" value="18.5476" />
                    <Key frame="175" value="18.5476" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="53.0842" />
                    <Key frame="30" value="81.7369" />
                    <Key frame="50" value="92.0202" />
                    <Key frame="55" value="92.0202" />
                    <Key frame="85" value="92.0202" />
                    <Key frame="115" value="92.0202" />
                    <Key frame="145" value="92.0202" />
                    <Key frame="175" value="92.0202" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="10" value="0.3352" />
                    <Key frame="30" value="0.3456" />
                    <Key frame="50" value="0.3044" />
                    <Key frame="55" value="0.3092" />
                    <Key frame="85" value="0.3092" />
                    <Key frame="115" value="0.3092" />
                    <Key frame="145" value="0.3092" />
                    <Key frame="175" value="0.3092" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="10" value="-22.5906" />
                    <Key frame="30" value="-7.56109" />
                    <Key frame="50" value="21.2674" />
                    <Key frame="55" value="21.4432" />
                    <Key frame="85" value="-6.5064" />
                    <Key frame="115" value="-38.2353" />
                    <Key frame="145" value="-6.68218" />
                    <Key frame="175" value="21.3553" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="10" value="-4.74374" />
                    <Key frame="30" value="-4.74374" />
                    <Key frame="50" value="-5.97423" />
                    <Key frame="55" value="-6.67737" />
                    <Key frame="85" value="1.05711" />
                    <Key frame="115" value="1.05711" />
                    <Key frame="145" value="1.05711" />
                    <Key frame="175" value="-6.15001" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.2655" />
                    <Key frame="30" value="-29.2655" />
                    <Key frame="50" value="-29.2655" />
                    <Key frame="55" value="-29.5292" />
                    <Key frame="85" value="-24.0799" />
                    <Key frame="115" value="-24.0799" />
                    <Key frame="145" value="-24.0799" />
                    <Key frame="175" value="-29.0019" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="10" value="100.199" />
                    <Key frame="30" value="55.55" />
                    <Key frame="50" value="-5.35899" />
                    <Key frame="55" value="-5.35899" />
                    <Key frame="85" value="56.8683" />
                    <Key frame="115" value="120.063" />
                    <Key frame="145" value="57.132" />
                    <Key frame="175" value="-5.79845" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="102.572" />
                    <Key frame="30" value="103.451" />
                    <Key frame="50" value="107.933" />
                    <Key frame="55" value="109.164" />
                    <Key frame="85" value="109.164" />
                    <Key frame="115" value="109.164" />
                    <Key frame="145" value="109.164" />
                    <Key frame="175" value="109.164" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="-17.4929" />
                    <Key frame="30" value="-23.2937" />
                    <Key frame="50" value="-15.0319" />
                    <Key frame="55" value="-15.0319" />
                    <Key frame="85" value="-14.4166" />
                    <Key frame="115" value="-14.4166" />
                    <Key frame="145" value="-14.4166" />
                    <Key frame="175" value="-14.4166" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="10" value="11.4235" />
                    <Key frame="30" value="8.69887" />
                    <Key frame="50" value="-11.692" />
                    <Key frame="55" value="-11.4283" />
                    <Key frame="85" value="-11.4283" />
                    <Key frame="115" value="-11.4283" />
                    <Key frame="145" value="-11.4283" />
                    <Key frame="175" value="-12.0436" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="BeinBeugenLinks_Demo" id="15" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="238" y="603">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="175">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="10" value="10.0173" />
                    <Key frame="30" value="10.0173" />
                    <Key frame="50" value="10.0173" />
                    <Key frame="55" value="10.0173" />
                    <Key frame="85" value="10.0173" />
                    <Key frame="115" value="10.6325" />
                    <Key frame="145" value="10.6325" />
                    <Key frame="175" value="10.6325" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="10" value="0.173378" />
                    <Key frame="30" value="0.173378" />
                    <Key frame="50" value="0.173378" />
                    <Key frame="55" value="0.173378" />
                    <Key frame="85" value="-0.441859" />
                    <Key frame="115" value="-0.441859" />
                    <Key frame="145" value="-1.05711" />
                    <Key frame="175" value="-1.05711" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="49.3927" />
                    <Key frame="30" value="49.3927" />
                    <Key frame="50" value="30.8476" />
                    <Key frame="55" value="49.4806" />
                    <Key frame="85" value="35.9453" />
                    <Key frame="115" value="36.6484" />
                    <Key frame="145" value="36.209" />
                    <Key frame="175" value="49.0412" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-2.37068" />
                    <Key frame="30" value="-2.37068" />
                    <Key frame="50" value="15.8229" />
                    <Key frame="55" value="-1.66754" />
                    <Key frame="85" value="16.3503" />
                    <Key frame="115" value="6.85796" />
                    <Key frame="145" value="15.9108" />
                    <Key frame="175" value="-2.107" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="-86.6588" />
                    <Key frame="30" value="-86.0436" />
                    <Key frame="50" value="-22.9373" />
                    <Key frame="55" value="-19.5096" />
                    <Key frame="85" value="-18.8064" />
                    <Key frame="115" value="-18.1912" />
                    <Key frame="145" value="-18.1912" />
                    <Key frame="175" value="-18.1912" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="-53.089" />
                    <Key frame="30" value="-84.8179" />
                    <Key frame="50" value="-90.2672" />
                    <Key frame="55" value="-91.7614" />
                    <Key frame="85" value="-91.7614" />
                    <Key frame="115" value="-91.7614" />
                    <Key frame="145" value="-91.7614" />
                    <Key frame="175" value="-91.7614" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="10" value="0.2848" />
                    <Key frame="30" value="0.2848" />
                    <Key frame="50" value="0.3356" />
                    <Key frame="55" value="0.3124" />
                    <Key frame="85" value="0.3124" />
                    <Key frame="115" value="0.3124" />
                    <Key frame="145" value="0.3124" />
                    <Key frame="175" value="0.3124" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="10" value="20.9206" />
                    <Key frame="30" value="20.9206" />
                    <Key frame="50" value="22.1511" />
                    <Key frame="55" value="21.448" />
                    <Key frame="85" value="-6.32579" />
                    <Key frame="115" value="-38.3184" />
                    <Key frame="145" value="-6.85315" />
                    <Key frame="175" value="21.1843" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="10" value="6.77007" />
                    <Key frame="30" value="6.77007" />
                    <Key frame="50" value="6.77007" />
                    <Key frame="55" value="6.77007" />
                    <Key frame="85" value="-0.876518" />
                    <Key frame="115" value="-0.700723" />
                    <Key frame="145" value="-0.700723" />
                    <Key frame="175" value="5.80326" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.2655" />
                    <Key frame="30" value="-29.2655" />
                    <Key frame="50" value="-29.2655" />
                    <Key frame="55" value="-29.2655" />
                    <Key frame="85" value="-24.2557" />
                    <Key frame="115" value="-23.6405" />
                    <Key frame="145" value="-23.6405" />
                    <Key frame="175" value="-28.7382" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="10" value="-5.62747" />
                    <Key frame="30" value="-5.62747" />
                    <Key frame="50" value="-5.62747" />
                    <Key frame="55" value="-5.62747" />
                    <Key frame="85" value="56.512" />
                    <Key frame="115" value="119.618" />
                    <Key frame="145" value="56.8635" />
                    <Key frame="175" value="-5.71537" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="102.304" />
                    <Key frame="30" value="103.095" />
                    <Key frame="50" value="107.929" />
                    <Key frame="55" value="107.929" />
                    <Key frame="85" value="107.929" />
                    <Key frame="115" value="107.929" />
                    <Key frame="145" value="108.544" />
                    <Key frame="175" value="108.544" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="15.9939" />
                    <Key frame="30" value="18.4549" />
                    <Key frame="50" value="14.8513" />
                    <Key frame="55" value="14.8513" />
                    <Key frame="85" value="14.8513" />
                    <Key frame="115" value="14.8513" />
                    <Key frame="145" value="14.8513" />
                    <Key frame="175" value="14.8513" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="10" value="12.4782" />
                    <Key frame="30" value="12.4782" />
                    <Key frame="50" value="-2.99072" />
                    <Key frame="55" value="10.6325" />
                    <Key frame="85" value="11.4235" />
                    <Key frame="115" value="11.4235" />
                    <Key frame="145" value="11.4235" />
                    <Key frame="175" value="12.0388" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="37.4443" />
                    <Key frame="30" value="37.4443" />
                    <Key frame="50" value="49.5733" />
                    <Key frame="55" value="30.9403" />
                    <Key frame="85" value="30.9403" />
                    <Key frame="115" value="30.9403" />
                    <Key frame="145" value="30.9403" />
                    <Key frame="175" value="30.9403" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-16.3455" />
                    <Key frame="30" value="-16.3455" />
                    <Key frame="50" value="1.84813" />
                    <Key frame="55" value="-15.906" />
                    <Key frame="85" value="-15.906" />
                    <Key frame="115" value="-15.906" />
                    <Key frame="145" value="-16.5212" />
                    <Key frame="175" value="-16.5212" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="87.3668" />
                    <Key frame="30" value="84.6421" />
                    <Key frame="50" value="19.778" />
                    <Key frame="55" value="21.0085" />
                    <Key frame="85" value="21.0085" />
                    <Key frame="115" value="20.3933" />
                    <Key frame="145" value="20.3933" />
                    <Key frame="175" value="21.6238" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="53.0842" />
                    <Key frame="30" value="81.7369" />
                    <Key frame="50" value="92.0202" />
                    <Key frame="55" value="92.0202" />
                    <Key frame="85" value="92.0202" />
                    <Key frame="115" value="92.0202" />
                    <Key frame="145" value="92.0202" />
                    <Key frame="175" value="92.0202" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="10" value="0.3352" />
                    <Key frame="30" value="0.3456" />
                    <Key frame="50" value="0.3044" />
                    <Key frame="55" value="0.3332" />
                    <Key frame="85" value="0.3332" />
                    <Key frame="115" value="0.3332" />
                    <Key frame="145" value="0.3332" />
                    <Key frame="175" value="0.3332" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="10" value="-22.5906" />
                    <Key frame="30" value="-7.56109" />
                    <Key frame="50" value="21.2674" />
                    <Key frame="55" value="21.9705" />
                    <Key frame="85" value="21.3553" />
                    <Key frame="115" value="21.3553" />
                    <Key frame="145" value="21.3553" />
                    <Key frame="175" value="22.1463" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="10" value="-4.74374" />
                    <Key frame="30" value="-4.74374" />
                    <Key frame="50" value="-5.97423" />
                    <Key frame="55" value="-6.76526" />
                    <Key frame="85" value="-6.76526" />
                    <Key frame="115" value="-6.76526" />
                    <Key frame="145" value="-6.76526" />
                    <Key frame="175" value="-7.55628" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.2655" />
                    <Key frame="30" value="-29.2655" />
                    <Key frame="50" value="-29.2655" />
                    <Key frame="55" value="-29.2655" />
                    <Key frame="85" value="-24.2557" />
                    <Key frame="115" value="-23.6405" />
                    <Key frame="145" value="-23.6405" />
                    <Key frame="175" value="-28.7382" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="10" value="100.199" />
                    <Key frame="30" value="55.55" />
                    <Key frame="50" value="-5.35899" />
                    <Key frame="55" value="-5.35899" />
                    <Key frame="85" value="-5.35899" />
                    <Key frame="115" value="-5.35899" />
                    <Key frame="145" value="-5.35899" />
                    <Key frame="175" value="-5.35899" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="102.572" />
                    <Key frame="30" value="103.451" />
                    <Key frame="50" value="107.933" />
                    <Key frame="55" value="107.933" />
                    <Key frame="85" value="108.549" />
                    <Key frame="115" value="109.164" />
                    <Key frame="145" value="109.164" />
                    <Key frame="175" value="109.164" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="-17.4929" />
                    <Key frame="30" value="-23.2937" />
                    <Key frame="50" value="-15.0319" />
                    <Key frame="55" value="-15.0319" />
                    <Key frame="85" value="-15.0319" />
                    <Key frame="115" value="-15.0319" />
                    <Key frame="145" value="-15.0319" />
                    <Key frame="175" value="-15.6471" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="10" value="11.4235" />
                    <Key frame="30" value="8.69887" />
                    <Key frame="50" value="-11.692" />
                    <Key frame="55" value="3.16169" />
                    <Key frame="85" value="3.16169" />
                    <Key frame="115" value="3.16169" />
                    <Key frame="145" value="3.16169" />
                    <Key frame="175" value="3.16169" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="BeinBeugenAlt_Demo" id="16" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="241" y="719">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="300">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="10" value="10.0173" />
                    <Key frame="30" value="10.0173" />
                    <Key frame="50" value="10.0173" />
                    <Key frame="55" value="10.6325" />
                    <Key frame="85" value="10.6325" />
                    <Key frame="115" value="10.6325" />
                    <Key frame="145" value="10.6325" />
                    <Key frame="175" value="10.6325" />
                    <Key frame="180" value="10.0173" />
                    <Key frame="210" value="10.0173" />
                    <Key frame="240" value="10.6325" />
                    <Key frame="270" value="10.6325" />
                    <Key frame="300" value="10.6325" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="10" value="0.173378" />
                    <Key frame="30" value="0.173378" />
                    <Key frame="50" value="0.173378" />
                    <Key frame="55" value="-0.441859" />
                    <Key frame="85" value="0.173378" />
                    <Key frame="115" value="0.173378" />
                    <Key frame="145" value="0.788627" />
                    <Key frame="175" value="0.788627" />
                    <Key frame="180" value="0.173378" />
                    <Key frame="210" value="-0.441859" />
                    <Key frame="240" value="-0.441859" />
                    <Key frame="270" value="-1.05711" />
                    <Key frame="300" value="-1.05711" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="49.3927" />
                    <Key frame="30" value="49.3927" />
                    <Key frame="50" value="30.8476" />
                    <Key frame="55" value="31.1992" />
                    <Key frame="85" value="30.5839" />
                    <Key frame="115" value="30.5839" />
                    <Key frame="145" value="30.5839" />
                    <Key frame="175" value="30.5839" />
                    <Key frame="180" value="49.4806" />
                    <Key frame="210" value="35.9453" />
                    <Key frame="240" value="36.6484" />
                    <Key frame="270" value="36.209" />
                    <Key frame="300" value="49.0412" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-2.37068" />
                    <Key frame="30" value="-2.37068" />
                    <Key frame="50" value="15.8229" />
                    <Key frame="55" value="15.6471" />
                    <Key frame="85" value="16.2624" />
                    <Key frame="115" value="16.2624" />
                    <Key frame="145" value="16.2624" />
                    <Key frame="175" value="16.2624" />
                    <Key frame="180" value="-1.66754" />
                    <Key frame="210" value="16.3503" />
                    <Key frame="240" value="6.85796" />
                    <Key frame="270" value="15.9108" />
                    <Key frame="300" value="-2.107" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="-86.6588" />
                    <Key frame="30" value="-86.0436" />
                    <Key frame="50" value="-22.9373" />
                    <Key frame="55" value="-21.0916" />
                    <Key frame="85" value="-20.4764" />
                    <Key frame="115" value="-20.4764" />
                    <Key frame="145" value="-19.8611" />
                    <Key frame="175" value="-21.2674" />
                    <Key frame="180" value="-19.5096" />
                    <Key frame="210" value="-18.8064" />
                    <Key frame="240" value="-18.1912" />
                    <Key frame="270" value="-18.1912" />
                    <Key frame="300" value="-18.1912" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="-53.089" />
                    <Key frame="30" value="-84.8179" />
                    <Key frame="50" value="-90.2672" />
                    <Key frame="55" value="-91.7614" />
                    <Key frame="85" value="-91.7614" />
                    <Key frame="115" value="-91.7614" />
                    <Key frame="145" value="-91.7614" />
                    <Key frame="175" value="-91.7614" />
                    <Key frame="180" value="-91.7614" />
                    <Key frame="210" value="-91.7614" />
                    <Key frame="240" value="-91.7614" />
                    <Key frame="270" value="-91.7614" />
                    <Key frame="300" value="-91.7614" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="10" value="0.2848" />
                    <Key frame="30" value="0.2848" />
                    <Key frame="50" value="0.3356" />
                    <Key frame="55" value="0.33" />
                    <Key frame="85" value="0.33" />
                    <Key frame="115" value="0.33" />
                    <Key frame="145" value="0.33" />
                    <Key frame="175" value="0.33" />
                    <Key frame="180" value="0.3124" />
                    <Key frame="210" value="0.3124" />
                    <Key frame="240" value="0.3124" />
                    <Key frame="270" value="0.3124" />
                    <Key frame="300" value="0.3124" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="10" value="20.9206" />
                    <Key frame="30" value="20.9206" />
                    <Key frame="50" value="22.1511" />
                    <Key frame="55" value="21.7996" />
                    <Key frame="85" value="21.7996" />
                    <Key frame="115" value="21.7996" />
                    <Key frame="145" value="21.7996" />
                    <Key frame="175" value="21.7996" />
                    <Key frame="180" value="21.448" />
                    <Key frame="210" value="-6.32579" />
                    <Key frame="240" value="-38.3184" />
                    <Key frame="270" value="-6.85315" />
                    <Key frame="300" value="21.1843" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="10" value="6.77007" />
                    <Key frame="30" value="6.77007" />
                    <Key frame="50" value="6.77007" />
                    <Key frame="55" value="7.03374" />
                    <Key frame="85" value="7.03374" />
                    <Key frame="115" value="7.03374" />
                    <Key frame="145" value="7.03374" />
                    <Key frame="175" value="7.64898" />
                    <Key frame="180" value="6.77007" />
                    <Key frame="210" value="-0.876518" />
                    <Key frame="240" value="-0.700723" />
                    <Key frame="270" value="-0.700723" />
                    <Key frame="300" value="5.80326" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.2655" />
                    <Key frame="30" value="-29.2655" />
                    <Key frame="50" value="-29.2655" />
                    <Key frame="55" value="-29.5292" />
                    <Key frame="85" value="-24.0799" />
                    <Key frame="115" value="-24.0799" />
                    <Key frame="145" value="-24.0799" />
                    <Key frame="175" value="-29.0019" />
                    <Key frame="180" value="-29.2655" />
                    <Key frame="210" value="-24.2557" />
                    <Key frame="240" value="-23.6405" />
                    <Key frame="270" value="-23.6405" />
                    <Key frame="300" value="-28.7382" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="10" value="-5.62747" />
                    <Key frame="30" value="-5.62747" />
                    <Key frame="50" value="-5.62747" />
                    <Key frame="55" value="-5.71537" />
                    <Key frame="85" value="-5.71537" />
                    <Key frame="115" value="-5.71537" />
                    <Key frame="145" value="-5.71537" />
                    <Key frame="175" value="-5.71537" />
                    <Key frame="180" value="-5.62747" />
                    <Key frame="210" value="56.512" />
                    <Key frame="240" value="119.618" />
                    <Key frame="270" value="56.8635" />
                    <Key frame="300" value="-5.71537" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="102.304" />
                    <Key frame="30" value="103.095" />
                    <Key frame="50" value="107.929" />
                    <Key frame="55" value="108.544" />
                    <Key frame="85" value="108.544" />
                    <Key frame="115" value="109.159" />
                    <Key frame="145" value="109.159" />
                    <Key frame="175" value="109.159" />
                    <Key frame="180" value="107.929" />
                    <Key frame="210" value="107.929" />
                    <Key frame="240" value="107.929" />
                    <Key frame="270" value="108.544" />
                    <Key frame="300" value="108.544" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="15.9939" />
                    <Key frame="30" value="18.4549" />
                    <Key frame="50" value="14.8513" />
                    <Key frame="55" value="15.4665" />
                    <Key frame="85" value="15.4665" />
                    <Key frame="115" value="16.0818" />
                    <Key frame="145" value="16.0818" />
                    <Key frame="175" value="16.0818" />
                    <Key frame="180" value="14.8513" />
                    <Key frame="210" value="14.8513" />
                    <Key frame="240" value="14.8513" />
                    <Key frame="270" value="14.8513" />
                    <Key frame="300" value="14.8513" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="10" value="12.4782" />
                    <Key frame="30" value="12.4782" />
                    <Key frame="50" value="-2.99072" />
                    <Key frame="55" value="-2.72705" />
                    <Key frame="85" value="-2.72705" />
                    <Key frame="115" value="-2.72705" />
                    <Key frame="145" value="-2.72705" />
                    <Key frame="175" value="-2.72705" />
                    <Key frame="180" value="10.6325" />
                    <Key frame="210" value="11.4235" />
                    <Key frame="240" value="11.4235" />
                    <Key frame="270" value="11.4235" />
                    <Key frame="300" value="12.0388" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="37.4443" />
                    <Key frame="30" value="37.4443" />
                    <Key frame="50" value="49.5733" />
                    <Key frame="55" value="49.5733" />
                    <Key frame="85" value="36.5654" />
                    <Key frame="115" value="36.7411" />
                    <Key frame="145" value="36.7411" />
                    <Key frame="175" value="49.046" />
                    <Key frame="180" value="30.9403" />
                    <Key frame="210" value="30.9403" />
                    <Key frame="240" value="30.9403" />
                    <Key frame="270" value="30.9403" />
                    <Key frame="300" value="30.9403" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-16.3455" />
                    <Key frame="30" value="-16.3455" />
                    <Key frame="50" value="1.84813" />
                    <Key frame="55" value="2.28759" />
                    <Key frame="85" value="-16.4334" />
                    <Key frame="115" value="-7.11683" />
                    <Key frame="145" value="-16.0818" />
                    <Key frame="175" value="2.28759" />
                    <Key frame="180" value="-15.906" />
                    <Key frame="210" value="-15.906" />
                    <Key frame="240" value="-15.906" />
                    <Key frame="270" value="-16.5212" />
                    <Key frame="300" value="-16.5212" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="87.3668" />
                    <Key frame="30" value="84.6421" />
                    <Key frame="50" value="19.778" />
                    <Key frame="55" value="19.778" />
                    <Key frame="85" value="19.1628" />
                    <Key frame="115" value="18.5476" />
                    <Key frame="145" value="18.5476" />
                    <Key frame="175" value="18.5476" />
                    <Key frame="180" value="21.0085" />
                    <Key frame="210" value="21.0085" />
                    <Key frame="240" value="20.3933" />
                    <Key frame="270" value="20.3933" />
                    <Key frame="300" value="21.6238" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="53.0842" />
                    <Key frame="30" value="81.7369" />
                    <Key frame="50" value="92.0202" />
                    <Key frame="55" value="92.0202" />
                    <Key frame="85" value="92.0202" />
                    <Key frame="115" value="92.0202" />
                    <Key frame="145" value="92.0202" />
                    <Key frame="175" value="92.0202" />
                    <Key frame="180" value="92.0202" />
                    <Key frame="210" value="92.0202" />
                    <Key frame="240" value="92.0202" />
                    <Key frame="270" value="92.0202" />
                    <Key frame="300" value="92.0202" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="10" value="0.3352" />
                    <Key frame="30" value="0.3456" />
                    <Key frame="50" value="0.3044" />
                    <Key frame="55" value="0.3092" />
                    <Key frame="85" value="0.3092" />
                    <Key frame="115" value="0.3092" />
                    <Key frame="145" value="0.3092" />
                    <Key frame="175" value="0.3092" />
                    <Key frame="180" value="0.3332" />
                    <Key frame="210" value="0.3332" />
                    <Key frame="240" value="0.3332" />
                    <Key frame="270" value="0.3332" />
                    <Key frame="300" value="0.3332" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="10" value="-22.5906" />
                    <Key frame="30" value="-7.56109" />
                    <Key frame="50" value="21.2674" />
                    <Key frame="55" value="21.4432" />
                    <Key frame="85" value="-6.5064" />
                    <Key frame="115" value="-38.2353" />
                    <Key frame="145" value="-6.68218" />
                    <Key frame="175" value="21.3553" />
                    <Key frame="180" value="21.9705" />
                    <Key frame="210" value="21.3553" />
                    <Key frame="240" value="21.3553" />
                    <Key frame="270" value="21.3553" />
                    <Key frame="300" value="22.1463" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="10" value="-4.74374" />
                    <Key frame="30" value="-4.74374" />
                    <Key frame="50" value="-5.97423" />
                    <Key frame="55" value="-6.67737" />
                    <Key frame="85" value="1.05711" />
                    <Key frame="115" value="1.05711" />
                    <Key frame="145" value="1.05711" />
                    <Key frame="175" value="-6.15001" />
                    <Key frame="180" value="-6.76526" />
                    <Key frame="210" value="-6.76526" />
                    <Key frame="240" value="-6.76526" />
                    <Key frame="270" value="-6.76526" />
                    <Key frame="300" value="-7.55628" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.2655" />
                    <Key frame="30" value="-29.2655" />
                    <Key frame="50" value="-29.2655" />
                    <Key frame="55" value="-29.5292" />
                    <Key frame="85" value="-24.0799" />
                    <Key frame="115" value="-24.0799" />
                    <Key frame="145" value="-24.0799" />
                    <Key frame="175" value="-29.0019" />
                    <Key frame="180" value="-29.2655" />
                    <Key frame="210" value="-24.2557" />
                    <Key frame="240" value="-23.6405" />
                    <Key frame="270" value="-23.6405" />
                    <Key frame="300" value="-28.7382" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="10" value="100.199" />
                    <Key frame="30" value="55.55" />
                    <Key frame="50" value="-5.35899" />
                    <Key frame="55" value="-5.35899" />
                    <Key frame="85" value="56.8683" />
                    <Key frame="115" value="120.063" />
                    <Key frame="145" value="57.132" />
                    <Key frame="175" value="-5.79845" />
                    <Key frame="180" value="-5.35899" />
                    <Key frame="210" value="-5.35899" />
                    <Key frame="240" value="-5.35899" />
                    <Key frame="270" value="-5.35899" />
                    <Key frame="300" value="-5.35899" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="102.572" />
                    <Key frame="30" value="103.451" />
                    <Key frame="50" value="107.933" />
                    <Key frame="55" value="109.164" />
                    <Key frame="85" value="109.164" />
                    <Key frame="115" value="109.164" />
                    <Key frame="145" value="109.164" />
                    <Key frame="175" value="109.164" />
                    <Key frame="180" value="107.933" />
                    <Key frame="210" value="108.549" />
                    <Key frame="240" value="109.164" />
                    <Key frame="270" value="109.164" />
                    <Key frame="300" value="109.164" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="-17.4929" />
                    <Key frame="30" value="-23.2937" />
                    <Key frame="50" value="-15.0319" />
                    <Key frame="55" value="-15.0319" />
                    <Key frame="85" value="-14.4166" />
                    <Key frame="115" value="-14.4166" />
                    <Key frame="145" value="-14.4166" />
                    <Key frame="175" value="-14.4166" />
                    <Key frame="180" value="-15.0319" />
                    <Key frame="210" value="-15.0319" />
                    <Key frame="240" value="-15.0319" />
                    <Key frame="270" value="-15.0319" />
                    <Key frame="300" value="-15.6471" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="10" value="11.4235" />
                    <Key frame="30" value="8.69887" />
                    <Key frame="50" value="-11.692" />
                    <Key frame="55" value="-11.4283" />
                    <Key frame="85" value="-11.4283" />
                    <Key frame="115" value="-11.4283" />
                    <Key frame="145" value="-11.4283" />
                    <Key frame="175" value="-12.0436" />
                    <Key frame="180" value="3.16169" />
                    <Key frame="210" value="3.16169" />
                    <Key frame="240" value="3.16169" />
                    <Key frame="270" value="3.16169" />
                    <Key frame="300" value="3.16169" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="BoxenLiegend_Demo" id="59" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="217" y="371">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="68">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="nao">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="10" value="9.5778" />
                    <Key frame="30" value="10.193" />
                    <Key frame="37" value="10.193" />
                    <Key frame="49" value="10.193" />
                    <Key frame="56" value="10.193" />
                    <Key frame="68" value="10.193" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="10" value="-0.793436" />
                    <Key frame="30" value="-0.793436" />
                    <Key frame="37" value="-0.793436" />
                    <Key frame="49" value="-0.793436" />
                    <Key frame="56" value="-0.793436" />
                    <Key frame="68" value="-0.793436" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="49.5685" />
                    <Key frame="30" value="49.5685" />
                    <Key frame="37" value="49.5685" />
                    <Key frame="49" value="49.5685" />
                    <Key frame="56" value="49.5685" />
                    <Key frame="68" value="49.5685" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-2.1949" />
                    <Key frame="30" value="-2.1949" />
                    <Key frame="37" value="-2.1949" />
                    <Key frame="49" value="-2.1949" />
                    <Key frame="56" value="-2.1949" />
                    <Key frame="68" value="-2.1949" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="-86.7467" />
                    <Key frame="30" value="-86.4831" />
                    <Key frame="37" value="-72.5962" />
                    <Key frame="49" value="-85.8678" />
                    <Key frame="56" value="-10.9841" />
                    <Key frame="68" value="-85.4284" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="-50.8038" />
                    <Key frame="30" value="-64.0755" />
                    <Key frame="37" value="-31.0282" />
                    <Key frame="49" value="-63.8118" />
                    <Key frame="56" value="-70.14" />
                    <Key frame="68" value="-68.2943" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="10" value="0.2948" />
                    <Key frame="30" value="0.2948" />
                    <Key frame="37" value="0.2948" />
                    <Key frame="49" value="0.2948" />
                    <Key frame="56" value="0.2948" />
                    <Key frame="68" value="0.2948" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="10" value="21.5359" />
                    <Key frame="30" value="21.5359" />
                    <Key frame="37" value="21.5359" />
                    <Key frame="49" value="21.5359" />
                    <Key frame="56" value="21.5359" />
                    <Key frame="68" value="21.5359" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="10" value="6.41851" />
                    <Key frame="30" value="6.41851" />
                    <Key frame="37" value="6.41851" />
                    <Key frame="49" value="6.41851" />
                    <Key frame="56" value="6.41851" />
                    <Key frame="68" value="6.41851" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.1777" />
                    <Key frame="30" value="-28.5624" />
                    <Key frame="37" value="-28.5624" />
                    <Key frame="49" value="-28.5624" />
                    <Key frame="56" value="-28.5624" />
                    <Key frame="68" value="-28.5624" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="10" value="-5.3638" />
                    <Key frame="30" value="-5.3638" />
                    <Key frame="37" value="-5.3638" />
                    <Key frame="49" value="-5.3638" />
                    <Key frame="56" value="-5.3638" />
                    <Key frame="68" value="-5.3638" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="98.5242" />
                    <Key frame="30" value="52.6447" />
                    <Key frame="37" value="23.6405" />
                    <Key frame="49" value="51.3264" />
                    <Key frame="56" value="14.9392" />
                    <Key frame="68" value="49.1291" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="13.0056" />
                    <Key frame="30" value="-4.13332" />
                    <Key frame="37" value="4.56796" />
                    <Key frame="49" value="-3.69386" />
                    <Key frame="56" value="-7.82477" />
                    <Key frame="68" value="-5.45169" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="10" value="13.3571" />
                    <Key frame="30" value="13.3571" />
                    <Key frame="37" value="-24.9637" />
                    <Key frame="49" value="12.0388" />
                    <Key frame="56" value="-15.9108" />
                    <Key frame="68" value="12.0388" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="37.0927" />
                    <Key frame="30" value="36.4775" />
                    <Key frame="37" value="36.4775" />
                    <Key frame="49" value="36.4775" />
                    <Key frame="56" value="36.4775" />
                    <Key frame="68" value="36.4775" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-16.2576" />
                    <Key frame="30" value="-15.6423" />
                    <Key frame="37" value="-15.6423" />
                    <Key frame="49" value="-15.6423" />
                    <Key frame="56" value="-15.6423" />
                    <Key frame="68" value="-15.6423" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="87.4547" />
                    <Key frame="30" value="86.4879" />
                    <Key frame="37" value="8.87946" />
                    <Key frame="49" value="86.0484" />
                    <Key frame="56" value="79.896" />
                    <Key frame="68" value="86.3121" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="51.8537" />
                    <Key frame="30" value="61.1702" />
                    <Key frame="37" value="37.4395" />
                    <Key frame="49" value="61.6097" />
                    <Key frame="56" value="25.1346" />
                    <Key frame="68" value="62.8402" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="10" value="0.3536" />
                    <Key frame="30" value="0.3536" />
                    <Key frame="37" value="0.3536" />
                    <Key frame="49" value="0.3536" />
                    <Key frame="56" value="0.3536" />
                    <Key frame="68" value="0.3324" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="10" value="-22.7664" />
                    <Key frame="30" value="21.3553" />
                    <Key frame="37" value="21.3553" />
                    <Key frame="49" value="21.3553" />
                    <Key frame="56" value="21.3553" />
                    <Key frame="68" value="21.3553" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="10" value="-4.74374" />
                    <Key frame="30" value="-7.64417" />
                    <Key frame="37" value="-8.25942" />
                    <Key frame="49" value="-8.25942" />
                    <Key frame="56" value="-8.25942" />
                    <Key frame="68" value="-8.25942" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.1777" />
                    <Key frame="30" value="-28.5624" />
                    <Key frame="37" value="-28.5624" />
                    <Key frame="49" value="-28.5624" />
                    <Key frame="56" value="-28.5624" />
                    <Key frame="68" value="-28.5624" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="10" value="99.8474" />
                    <Key frame="30" value="-5.62267" />
                    <Key frame="37" value="-5.62267" />
                    <Key frame="49" value="-5.62267" />
                    <Key frame="56" value="-5.62267" />
                    <Key frame="68" value="-5.62267" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="98.2654" />
                    <Key frame="30" value="54.671" />
                    <Key frame="37" value="4.74855" />
                    <Key frame="49" value="52.4738" />
                    <Key frame="56" value="26.1063" />
                    <Key frame="68" value="49.3976" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="-14.7682" />
                    <Key frame="30" value="-0.705531" />
                    <Key frame="37" value="5.88634" />
                    <Key frame="49" value="-0.705531" />
                    <Key frame="56" value="-9.31893" />
                    <Key frame="68" value="-0.178186" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="10" value="8.78677" />
                    <Key frame="30" value="-9.05525" />
                    <Key frame="37" value="14.3239" />
                    <Key frame="49" value="-9.05525" />
                    <Key frame="56" value="5.88634" />
                    <Key frame="68" value="-11.3404" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="EllenbogenLiegendAlt_Demo" id="45" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="400" y="638">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="90">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="nao">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="10" value="9.05044" />
                    <Key frame="20" value="9.05044" />
                    <Key frame="30" value="9.05044" />
                    <Key frame="60" value="9.48991" />
                    <Key frame="90" value="9.48991" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="10" value="-0.793436" />
                    <Key frame="20" value="-0.793436" />
                    <Key frame="30" value="-0.793436" />
                    <Key frame="60" value="-0.61764" />
                    <Key frame="90" value="-0.61764" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="49.3927" />
                    <Key frame="20" value="49.3927" />
                    <Key frame="30" value="49.3927" />
                    <Key frame="60" value="49.4806" />
                    <Key frame="90" value="49.4806" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-2.1949" />
                    <Key frame="20" value="-2.1949" />
                    <Key frame="30" value="-2.1949" />
                    <Key frame="60" value="-2.1949" />
                    <Key frame="90" value="-2.1949" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="-86.7467" />
                    <Key frame="20" value="-86.7467" />
                    <Key frame="30" value="-19.8611" />
                    <Key frame="60" value="-84.9889" />
                    <Key frame="90" value="-13.2692" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="-52.8253" />
                    <Key frame="20" value="-84.9058" />
                    <Key frame="30" value="-92.3766" />
                    <Key frame="60" value="-88.0699" />
                    <Key frame="90" value="-86.8394" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="10" value="0.2872" />
                    <Key frame="20" value="0.2872" />
                    <Key frame="30" value="0.2872" />
                    <Key frame="60" value="0.2948" />
                    <Key frame="90" value="0.7816" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="10" value="21.1843" />
                    <Key frame="20" value="21.1843" />
                    <Key frame="30" value="21.1843" />
                    <Key frame="60" value="21.448" />
                    <Key frame="90" value="21.448" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="10" value="6.41851" />
                    <Key frame="20" value="6.41851" />
                    <Key frame="30" value="6.41851" />
                    <Key frame="60" value="6.3306" />
                    <Key frame="90" value="6.3306" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.1777" />
                    <Key frame="20" value="-29.1777" />
                    <Key frame="30" value="-29.1777" />
                    <Key frame="60" value="-29.1777" />
                    <Key frame="90" value="-29.1777" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="10" value="-5.62747" />
                    <Key frame="20" value="-5.62747" />
                    <Key frame="30" value="-5.62747" />
                    <Key frame="60" value="-5.27591" />
                    <Key frame="90" value="-5.27591" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="102.04" />
                    <Key frame="20" value="103.007" />
                    <Key frame="30" value="107.665" />
                    <Key frame="60" value="102.04" />
                    <Key frame="90" value="97.118" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="15.7302" />
                    <Key frame="20" value="18.5427" />
                    <Key frame="30" value="13.9724" />
                    <Key frame="60" value="11.072" />
                    <Key frame="90" value="13.7087" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="10" value="13.2692" />
                    <Key frame="20" value="13.2692" />
                    <Key frame="30" value="13.2692" />
                    <Key frame="60" value="-98.6169" />
                    <Key frame="90" value="78.6607" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="37.1806" />
                    <Key frame="20" value="37.1806" />
                    <Key frame="30" value="30.9403" />
                    <Key frame="60" value="31.204" />
                    <Key frame="90" value="31.204" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-16.697" />
                    <Key frame="20" value="-16.0818" />
                    <Key frame="30" value="-16.697" />
                    <Key frame="60" value="-16.697" />
                    <Key frame="90" value="-16.697" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="87.6305" />
                    <Key frame="20" value="84.73" />
                    <Key frame="30" value="23.1179" />
                    <Key frame="60" value="84.9058" />
                    <Key frame="90" value="19.778" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="52.6447" />
                    <Key frame="20" value="82.0885" />
                    <Key frame="30" value="90.5261" />
                    <Key frame="60" value="87.9772" />
                    <Key frame="90" value="84.8131" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="10" value="0.346" />
                    <Key frame="20" value="0.346" />
                    <Key frame="30" value="0.346" />
                    <Key frame="60" value="0.3" />
                    <Key frame="90" value="0.69" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="10" value="-21.7996" />
                    <Key frame="20" value="-7.91266" />
                    <Key frame="30" value="21.9705" />
                    <Key frame="60" value="22.3221" />
                    <Key frame="90" value="22.3221" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="10" value="-4.56796" />
                    <Key frame="20" value="-5.18321" />
                    <Key frame="30" value="-6.85315" />
                    <Key frame="60" value="-6.2379" />
                    <Key frame="90" value="-6.2379" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.1777" />
                    <Key frame="20" value="-29.1777" />
                    <Key frame="30" value="-29.1777" />
                    <Key frame="60" value="-29.1777" />
                    <Key frame="90" value="-29.1777" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="10" value="99.2322" />
                    <Key frame="20" value="55.4621" />
                    <Key frame="30" value="-5.44688" />
                    <Key frame="60" value="-5.53478" />
                    <Key frame="90" value="-5.53478" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="102.045" />
                    <Key frame="20" value="103.539" />
                    <Key frame="30" value="107.406" />
                    <Key frame="60" value="100.902" />
                    <Key frame="90" value="99.8474" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="-17.405" />
                    <Key frame="20" value="-23.4695" />
                    <Key frame="30" value="-16.6139" />
                    <Key frame="60" value="-11.4283" />
                    <Key frame="90" value="-13.9772" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="10" value="11.863" />
                    <Key frame="20" value="8.96255" />
                    <Key frame="30" value="5.97423" />
                    <Key frame="60" value="98.3484" />
                    <Key frame="90" value="-70.5795" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="EllenbogenLiegendRechts_Demo" id="17" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="399" y="750">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="90">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="nao">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="10" value="9.05044" />
                    <Key frame="20" value="9.05044" />
                    <Key frame="30" value="9.05044" />
                    <Key frame="60" value="9.66569" />
                    <Key frame="90" value="9.66569" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="10" value="-0.793436" />
                    <Key frame="20" value="-0.793436" />
                    <Key frame="30" value="-0.793436" />
                    <Key frame="60" value="-0.793436" />
                    <Key frame="90" value="-0.793436" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="49.3927" />
                    <Key frame="20" value="49.3927" />
                    <Key frame="30" value="49.3927" />
                    <Key frame="60" value="49.3927" />
                    <Key frame="90" value="49.3927" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-2.1949" />
                    <Key frame="20" value="-2.1949" />
                    <Key frame="30" value="-2.1949" />
                    <Key frame="60" value="-2.1949" />
                    <Key frame="90" value="-2.1949" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="-86.7467" />
                    <Key frame="20" value="-86.7467" />
                    <Key frame="30" value="-19.8611" />
                    <Key frame="60" value="-18.7185" />
                    <Key frame="90" value="-18.7185" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="-52.8253" />
                    <Key frame="20" value="-84.9058" />
                    <Key frame="30" value="-92.3766" />
                    <Key frame="60" value="-91.9372" />
                    <Key frame="90" value="-91.9372" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="10" value="0.2872" />
                    <Key frame="20" value="0.2872" />
                    <Key frame="30" value="0.2872" />
                    <Key frame="60" value="0.3052" />
                    <Key frame="90" value="0.3052" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="10" value="21.1843" />
                    <Key frame="20" value="21.1843" />
                    <Key frame="30" value="21.1843" />
                    <Key frame="60" value="21.1843" />
                    <Key frame="90" value="21.1843" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="10" value="6.41851" />
                    <Key frame="20" value="6.41851" />
                    <Key frame="30" value="6.41851" />
                    <Key frame="60" value="6.41851" />
                    <Key frame="90" value="6.41851" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.1777" />
                    <Key frame="20" value="-29.1777" />
                    <Key frame="30" value="-29.1777" />
                    <Key frame="60" value="-29.1777" />
                    <Key frame="90" value="-29.1777" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="10" value="-5.62747" />
                    <Key frame="20" value="-5.62747" />
                    <Key frame="30" value="-5.62747" />
                    <Key frame="60" value="-5.62747" />
                    <Key frame="90" value="-5.62747" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="102.04" />
                    <Key frame="20" value="103.007" />
                    <Key frame="30" value="107.665" />
                    <Key frame="60" value="108.72" />
                    <Key frame="90" value="108.72" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="15.7302" />
                    <Key frame="20" value="18.5427" />
                    <Key frame="30" value="13.9724" />
                    <Key frame="60" value="15.2029" />
                    <Key frame="90" value="15.8181" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="10" value="13.2692" />
                    <Key frame="20" value="13.2692" />
                    <Key frame="30" value="13.2692" />
                    <Key frame="60" value="10.6325" />
                    <Key frame="90" value="10.6325" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="37.1806" />
                    <Key frame="20" value="37.1806" />
                    <Key frame="30" value="30.9403" />
                    <Key frame="60" value="30.9403" />
                    <Key frame="90" value="30.9403" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-16.697" />
                    <Key frame="20" value="-16.0818" />
                    <Key frame="30" value="-16.697" />
                    <Key frame="60" value="-16.697" />
                    <Key frame="90" value="-16.697" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="87.6305" />
                    <Key frame="20" value="84.73" />
                    <Key frame="30" value="23.1179" />
                    <Key frame="60" value="83.6753" />
                    <Key frame="90" value="19.778" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="52.6447" />
                    <Key frame="20" value="82.0885" />
                    <Key frame="30" value="90.5261" />
                    <Key frame="60" value="88.944" />
                    <Key frame="90" value="85.692" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="10" value="0.346" />
                    <Key frame="20" value="0.346" />
                    <Key frame="30" value="0.346" />
                    <Key frame="60" value="0.3092" />
                    <Key frame="90" value="0.0355999" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="10" value="-21.7996" />
                    <Key frame="20" value="-7.91266" />
                    <Key frame="30" value="21.9705" />
                    <Key frame="60" value="21.9705" />
                    <Key frame="90" value="21.9705" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="10" value="-4.56796" />
                    <Key frame="20" value="-5.18321" />
                    <Key frame="30" value="-6.85315" />
                    <Key frame="60" value="-6.85315" />
                    <Key frame="90" value="-6.85315" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.1777" />
                    <Key frame="20" value="-29.1777" />
                    <Key frame="30" value="-29.1777" />
                    <Key frame="60" value="-29.1777" />
                    <Key frame="90" value="-29.1777" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="10" value="99.2322" />
                    <Key frame="20" value="55.4621" />
                    <Key frame="30" value="-5.44688" />
                    <Key frame="60" value="-5.44688" />
                    <Key frame="90" value="-5.44688" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="102.045" />
                    <Key frame="20" value="103.539" />
                    <Key frame="30" value="107.406" />
                    <Key frame="60" value="104.418" />
                    <Key frame="90" value="106.879" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="-17.405" />
                    <Key frame="20" value="-23.4695" />
                    <Key frame="30" value="-16.6139" />
                    <Key frame="60" value="-12.8346" />
                    <Key frame="90" value="-14.4166" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="10" value="11.863" />
                    <Key frame="20" value="8.96255" />
                    <Key frame="30" value="5.97423" />
                    <Key frame="60" value="95.6238" />
                    <Key frame="90" value="-67.8548" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="EllenbogenLiegendLinks_Demo" id="18" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="388" y="865">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="90">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="nao">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="10" value="9.05044" />
                    <Key frame="20" value="9.05044" />
                    <Key frame="30" value="9.05044" />
                    <Key frame="60" value="9.66569" />
                    <Key frame="90" value="9.66569" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="10" value="-0.793436" />
                    <Key frame="20" value="-0.793436" />
                    <Key frame="30" value="-0.793436" />
                    <Key frame="60" value="-0.793436" />
                    <Key frame="90" value="-0.793436" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="49.3927" />
                    <Key frame="20" value="49.3927" />
                    <Key frame="30" value="49.3927" />
                    <Key frame="60" value="49.3927" />
                    <Key frame="90" value="49.3927" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-2.1949" />
                    <Key frame="20" value="-2.1949" />
                    <Key frame="30" value="-2.1949" />
                    <Key frame="60" value="-2.1949" />
                    <Key frame="90" value="-2.1949" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="-86.7467" />
                    <Key frame="20" value="-86.7467" />
                    <Key frame="30" value="-19.8611" />
                    <Key frame="60" value="-84.11" />
                    <Key frame="90" value="-19.8611" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="-52.8253" />
                    <Key frame="20" value="-84.9058" />
                    <Key frame="30" value="-92.3766" />
                    <Key frame="60" value="-88.9488" />
                    <Key frame="90" value="-85.6968" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="10" value="0.2872" />
                    <Key frame="20" value="0.2872" />
                    <Key frame="30" value="0.2872" />
                    <Key frame="60" value="0.3052" />
                    <Key frame="90" value="0.0348001" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="10" value="21.1843" />
                    <Key frame="20" value="21.1843" />
                    <Key frame="30" value="21.1843" />
                    <Key frame="60" value="21.1843" />
                    <Key frame="90" value="21.1843" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="10" value="6.41851" />
                    <Key frame="20" value="6.41851" />
                    <Key frame="30" value="6.41851" />
                    <Key frame="60" value="6.41851" />
                    <Key frame="90" value="6.41851" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.1777" />
                    <Key frame="20" value="-29.1777" />
                    <Key frame="30" value="-29.1777" />
                    <Key frame="60" value="-29.1777" />
                    <Key frame="90" value="-29.1777" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="10" value="-5.62747" />
                    <Key frame="20" value="-5.62747" />
                    <Key frame="30" value="-5.62747" />
                    <Key frame="60" value="-5.62747" />
                    <Key frame="90" value="-5.62747" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="102.04" />
                    <Key frame="20" value="103.007" />
                    <Key frame="30" value="107.665" />
                    <Key frame="60" value="105.204" />
                    <Key frame="90" value="107.313" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="15.7302" />
                    <Key frame="20" value="18.5427" />
                    <Key frame="30" value="13.9724" />
                    <Key frame="60" value="11.863" />
                    <Key frame="90" value="14.6755" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="10" value="13.2692" />
                    <Key frame="20" value="13.2692" />
                    <Key frame="30" value="13.2692" />
                    <Key frame="60" value="-96.4196" />
                    <Key frame="90" value="67.4984" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="37.1806" />
                    <Key frame="20" value="37.1806" />
                    <Key frame="30" value="30.9403" />
                    <Key frame="60" value="30.9403" />
                    <Key frame="90" value="30.9403" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-16.697" />
                    <Key frame="20" value="-16.0818" />
                    <Key frame="30" value="-16.697" />
                    <Key frame="60" value="-16.697" />
                    <Key frame="90" value="-16.697" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="87.6305" />
                    <Key frame="20" value="84.73" />
                    <Key frame="30" value="23.1179" />
                    <Key frame="60" value="18.8991" />
                    <Key frame="90" value="18.8991" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="52.6447" />
                    <Key frame="20" value="82.0885" />
                    <Key frame="30" value="90.5261" />
                    <Key frame="60" value="90.9655" />
                    <Key frame="90" value="90.9655" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="10" value="0.346" />
                    <Key frame="20" value="0.346" />
                    <Key frame="30" value="0.346" />
                    <Key frame="60" value="0.288" />
                    <Key frame="90" value="0.2988" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="10" value="-21.7996" />
                    <Key frame="20" value="-7.91266" />
                    <Key frame="30" value="21.9705" />
                    <Key frame="60" value="21.9705" />
                    <Key frame="90" value="21.9705" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="10" value="-4.56796" />
                    <Key frame="20" value="-5.18321" />
                    <Key frame="30" value="-6.85315" />
                    <Key frame="60" value="-6.85315" />
                    <Key frame="90" value="-6.85315" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.1777" />
                    <Key frame="20" value="-29.1777" />
                    <Key frame="30" value="-29.1777" />
                    <Key frame="60" value="-29.1777" />
                    <Key frame="90" value="-29.1777" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="10" value="99.2322" />
                    <Key frame="20" value="55.4621" />
                    <Key frame="30" value="-5.44688" />
                    <Key frame="60" value="-5.44688" />
                    <Key frame="90" value="-5.44688" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="102.045" />
                    <Key frame="20" value="103.539" />
                    <Key frame="30" value="107.406" />
                    <Key frame="60" value="108.373" />
                    <Key frame="90" value="108.373" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="-17.405" />
                    <Key frame="20" value="-23.4695" />
                    <Key frame="30" value="-16.6139" />
                    <Key frame="60" value="-16.4382" />
                    <Key frame="90" value="-16.4382" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="10" value="11.863" />
                    <Key frame="20" value="8.96255" />
                    <Key frame="30" value="5.97423" />
                    <Key frame="60" value="-10.5494" />
                    <Key frame="90" value="-11.1647" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="FüßeAnziehen_Demo" id="19" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="525" y="862">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="80">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="nao">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="10" value="9.66569" />
                    <Key frame="20" value="9.66569" />
                    <Key frame="30" value="9.66569" />
                    <Key frame="40" value="10.2809" />
                    <Key frame="60" value="10.9841" />
                    <Key frame="80" value="10.9841" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="10" value="-0.793436" />
                    <Key frame="20" value="-0.793436" />
                    <Key frame="30" value="-0.793436" />
                    <Key frame="40" value="-0.793436" />
                    <Key frame="60" value="-0.793436" />
                    <Key frame="80" value="-0.793436" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="49.3927" />
                    <Key frame="20" value="49.3927" />
                    <Key frame="30" value="49.3927" />
                    <Key frame="40" value="5.00743" />
                    <Key frame="60" value="-48.255" />
                    <Key frame="80" value="4.65585" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-2.1949" />
                    <Key frame="20" value="-2.1949" />
                    <Key frame="30" value="-2.1949" />
                    <Key frame="40" value="4.83644" />
                    <Key frame="60" value="9.84628" />
                    <Key frame="80" value="5.71537" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="-86.2194" />
                    <Key frame="20" value="-86.2194" />
                    <Key frame="30" value="-19.8611" />
                    <Key frame="40" value="-19.8611" />
                    <Key frame="60" value="-19.2459" />
                    <Key frame="80" value="-19.2459" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="-53.4406" />
                    <Key frame="20" value="-84.9937" />
                    <Key frame="30" value="-92.4645" />
                    <Key frame="40" value="-92.4645" />
                    <Key frame="60" value="-92.4645" />
                    <Key frame="80" value="-92.4645" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="10" value="0.2856" />
                    <Key frame="20" value="0.2856" />
                    <Key frame="30" value="0.2856" />
                    <Key frame="40" value="0.2856" />
                    <Key frame="60" value="0.2856" />
                    <Key frame="80" value="0.2856" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="10" value="21.1843" />
                    <Key frame="20" value="21.1843" />
                    <Key frame="30" value="21.1843" />
                    <Key frame="40" value="20.5691" />
                    <Key frame="60" value="22.5906" />
                    <Key frame="80" value="20.3054" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="10" value="6.41851" />
                    <Key frame="20" value="6.41851" />
                    <Key frame="30" value="6.41851" />
                    <Key frame="40" value="6.41851" />
                    <Key frame="60" value="5.3638" />
                    <Key frame="80" value="6.06693" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.1777" />
                    <Key frame="20" value="-29.1777" />
                    <Key frame="30" value="-29.1777" />
                    <Key frame="40" value="-20.3006" />
                    <Key frame="60" value="-16.697" />
                    <Key frame="80" value="-19.3338" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="10" value="-5.62747" />
                    <Key frame="20" value="-5.62747" />
                    <Key frame="30" value="-5.62747" />
                    <Key frame="40" value="-5.62747" />
                    <Key frame="60" value="-4.83644" />
                    <Key frame="80" value="-5.53958" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="101.864" />
                    <Key frame="20" value="102.743" />
                    <Key frame="30" value="107.577" />
                    <Key frame="40" value="108.895" />
                    <Key frame="60" value="108.895" />
                    <Key frame="80" value="108.895" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="16.8728" />
                    <Key frame="20" value="18.4549" />
                    <Key frame="30" value="13.9724" />
                    <Key frame="40" value="13.9724" />
                    <Key frame="60" value="14.5876" />
                    <Key frame="80" value="14.5876" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="10" value="12.8298" />
                    <Key frame="20" value="12.8298" />
                    <Key frame="30" value="12.8298" />
                    <Key frame="40" value="12.8298" />
                    <Key frame="60" value="12.8298" />
                    <Key frame="80" value="12.8298" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="37.1806" />
                    <Key frame="20" value="37.1806" />
                    <Key frame="30" value="31.0282" />
                    <Key frame="40" value="1.49656" />
                    <Key frame="60" value="-52.469" />
                    <Key frame="80" value="1.145" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-16.697" />
                    <Key frame="20" value="-15.9939" />
                    <Key frame="30" value="-16.6091" />
                    <Key frame="40" value="-6.4137" />
                    <Key frame="60" value="-0.349159" />
                    <Key frame="80" value="-5.79845" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="87.5426" />
                    <Key frame="20" value="84.2906" />
                    <Key frame="30" value="23.1179" />
                    <Key frame="40" value="23.1179" />
                    <Key frame="60" value="21.7117" />
                    <Key frame="80" value="21.7117" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="52.7326" />
                    <Key frame="20" value="81.7369" />
                    <Key frame="30" value="90.5261" />
                    <Key frame="40" value="90.5261" />
                    <Key frame="60" value="90.5261" />
                    <Key frame="80" value="90.5261" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="10" value="0.3452" />
                    <Key frame="20" value="0.3452" />
                    <Key frame="30" value="0.3452" />
                    <Key frame="40" value="0.3452" />
                    <Key frame="60" value="0.3452" />
                    <Key frame="80" value="0.3452" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="10" value="-22.5027" />
                    <Key frame="20" value="-7.91266" />
                    <Key frame="30" value="22.2342" />
                    <Key frame="40" value="21.0037" />
                    <Key frame="60" value="22.3221" />
                    <Key frame="80" value="21.0037" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="10" value="-4.39218" />
                    <Key frame="20" value="-4.39218" />
                    <Key frame="30" value="-7.29261" />
                    <Key frame="40" value="-8.08364" />
                    <Key frame="60" value="-8.08364" />
                    <Key frame="80" value="-8.08364" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.1777" />
                    <Key frame="20" value="-29.1777" />
                    <Key frame="30" value="-29.1777" />
                    <Key frame="40" value="-20.3006" />
                    <Key frame="60" value="-16.697" />
                    <Key frame="80" value="-19.3338" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="10" value="99.1443" />
                    <Key frame="20" value="55.6379" />
                    <Key frame="30" value="-5.53478" />
                    <Key frame="40" value="-5.53478" />
                    <Key frame="60" value="-5.53478" />
                    <Key frame="80" value="-5.53478" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="101.429" />
                    <Key frame="20" value="103.012" />
                    <Key frame="30" value="107.23" />
                    <Key frame="40" value="108.549" />
                    <Key frame="60" value="108.549" />
                    <Key frame="80" value="108.549" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="-18.8991" />
                    <Key frame="20" value="-23.5574" />
                    <Key frame="30" value="-15.8229" />
                    <Key frame="40" value="-16.4382" />
                    <Key frame="60" value="-16.4382" />
                    <Key frame="80" value="-16.4382" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="10" value="11.7751" />
                    <Key frame="20" value="9.05044" />
                    <Key frame="30" value="6.4137" />
                    <Key frame="40" value="5.79845" />
                    <Key frame="60" value="5.79845" />
                    <Key frame="80" value="5.79845" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="FüßeAnziehenRechts_Demo" id="21" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="526" y="968">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="80">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="nao">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="10" value="9.66569" />
                    <Key frame="20" value="9.66569" />
                    <Key frame="30" value="9.66569" />
                    <Key frame="40" value="10.2809" />
                    <Key frame="60" value="10.9841" />
                    <Key frame="80" value="10.9841" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="10" value="-0.793436" />
                    <Key frame="20" value="-0.793436" />
                    <Key frame="30" value="-0.793436" />
                    <Key frame="40" value="-0.793436" />
                    <Key frame="60" value="-0.793436" />
                    <Key frame="80" value="-0.793436" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="49.3927" />
                    <Key frame="20" value="49.3927" />
                    <Key frame="30" value="49.3927" />
                    <Key frame="40" value="5.00743" />
                    <Key frame="60" value="1.93121" />
                    <Key frame="80" value="4.04062" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-2.1949" />
                    <Key frame="20" value="-2.1949" />
                    <Key frame="30" value="-2.1949" />
                    <Key frame="40" value="4.83644" />
                    <Key frame="60" value="5.71537" />
                    <Key frame="80" value="5.71537" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="-86.2194" />
                    <Key frame="20" value="-86.2194" />
                    <Key frame="30" value="-19.8611" />
                    <Key frame="40" value="-19.8611" />
                    <Key frame="60" value="-19.2459" />
                    <Key frame="80" value="-19.2459" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="-53.4406" />
                    <Key frame="20" value="-84.9937" />
                    <Key frame="30" value="-92.4645" />
                    <Key frame="40" value="-92.4645" />
                    <Key frame="60" value="-92.4645" />
                    <Key frame="80" value="-92.4645" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="10" value="0.2856" />
                    <Key frame="20" value="0.2856" />
                    <Key frame="30" value="0.2856" />
                    <Key frame="40" value="0.2856" />
                    <Key frame="60" value="0.2856" />
                    <Key frame="80" value="0.2856" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="10" value="21.1843" />
                    <Key frame="20" value="21.1843" />
                    <Key frame="30" value="21.1843" />
                    <Key frame="40" value="20.5691" />
                    <Key frame="60" value="20.3054" />
                    <Key frame="80" value="20.3054" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="10" value="6.41851" />
                    <Key frame="20" value="6.41851" />
                    <Key frame="30" value="6.41851" />
                    <Key frame="40" value="6.41851" />
                    <Key frame="60" value="8.00056" />
                    <Key frame="80" value="7.38531" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.1777" />
                    <Key frame="20" value="-29.1777" />
                    <Key frame="30" value="-29.1777" />
                    <Key frame="40" value="-20.3006" />
                    <Key frame="60" value="-19.949" />
                    <Key frame="80" value="-19.949" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="10" value="-5.62747" />
                    <Key frame="20" value="-5.62747" />
                    <Key frame="30" value="-5.62747" />
                    <Key frame="40" value="-5.62747" />
                    <Key frame="60" value="-5.53958" />
                    <Key frame="80" value="-5.53958" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="101.864" />
                    <Key frame="20" value="102.743" />
                    <Key frame="30" value="107.577" />
                    <Key frame="40" value="108.895" />
                    <Key frame="60" value="108.895" />
                    <Key frame="80" value="108.895" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="16.8728" />
                    <Key frame="20" value="18.4549" />
                    <Key frame="30" value="13.9724" />
                    <Key frame="40" value="13.9724" />
                    <Key frame="60" value="14.5876" />
                    <Key frame="80" value="14.5876" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="10" value="12.8298" />
                    <Key frame="20" value="12.8298" />
                    <Key frame="30" value="12.8298" />
                    <Key frame="40" value="12.8298" />
                    <Key frame="60" value="12.8298" />
                    <Key frame="80" value="12.8298" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="37.1806" />
                    <Key frame="20" value="37.1806" />
                    <Key frame="30" value="31.0282" />
                    <Key frame="40" value="1.49656" />
                    <Key frame="60" value="-50.1838" />
                    <Key frame="80" value="1.05711" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-16.697" />
                    <Key frame="20" value="-15.9939" />
                    <Key frame="30" value="-16.6091" />
                    <Key frame="40" value="-6.4137" />
                    <Key frame="60" value="-5.00743" />
                    <Key frame="80" value="-5.62267" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="87.5426" />
                    <Key frame="20" value="84.2906" />
                    <Key frame="30" value="23.1179" />
                    <Key frame="40" value="23.1179" />
                    <Key frame="60" value="21.7117" />
                    <Key frame="80" value="21.0085" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="52.7326" />
                    <Key frame="20" value="81.7369" />
                    <Key frame="30" value="90.5261" />
                    <Key frame="40" value="90.5261" />
                    <Key frame="60" value="90.5261" />
                    <Key frame="80" value="90.5261" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="10" value="0.3452" />
                    <Key frame="20" value="0.3452" />
                    <Key frame="30" value="0.3452" />
                    <Key frame="40" value="0.3452" />
                    <Key frame="60" value="0.3452" />
                    <Key frame="80" value="0.3452" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="10" value="-22.5027" />
                    <Key frame="20" value="-7.91266" />
                    <Key frame="30" value="22.2342" />
                    <Key frame="40" value="21.0037" />
                    <Key frame="60" value="22.9373" />
                    <Key frame="80" value="20.8279" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="10" value="-4.39218" />
                    <Key frame="20" value="-4.39218" />
                    <Key frame="30" value="-7.29261" />
                    <Key frame="40" value="-8.08364" />
                    <Key frame="60" value="-7.46839" />
                    <Key frame="80" value="-8.08364" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.1777" />
                    <Key frame="20" value="-29.1777" />
                    <Key frame="30" value="-29.1777" />
                    <Key frame="40" value="-20.3006" />
                    <Key frame="60" value="-19.949" />
                    <Key frame="80" value="-19.949" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="10" value="99.1443" />
                    <Key frame="20" value="55.6379" />
                    <Key frame="30" value="-5.53478" />
                    <Key frame="40" value="-5.53478" />
                    <Key frame="60" value="-4.91954" />
                    <Key frame="80" value="-4.91954" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="101.429" />
                    <Key frame="20" value="103.012" />
                    <Key frame="30" value="107.23" />
                    <Key frame="40" value="108.549" />
                    <Key frame="60" value="108.549" />
                    <Key frame="80" value="108.549" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="-18.8991" />
                    <Key frame="20" value="-23.5574" />
                    <Key frame="30" value="-15.8229" />
                    <Key frame="40" value="-16.4382" />
                    <Key frame="60" value="-16.4382" />
                    <Key frame="80" value="-16.4382" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="10" value="11.7751" />
                    <Key frame="20" value="9.05044" />
                    <Key frame="30" value="6.4137" />
                    <Key frame="40" value="5.79845" />
                    <Key frame="60" value="5.79845" />
                    <Key frame="80" value="5.79845" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="FüßeAnziehenLinks_Demo" id="22" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="532" y="1074">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="81">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="nao">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="10" value="9.66569" />
                    <Key frame="20" value="9.66569" />
                    <Key frame="30" value="9.66569" />
                    <Key frame="40" value="10.2809" />
                    <Key frame="61" value="10.9841" />
                    <Key frame="81" value="10.9841" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="10" value="-0.793436" />
                    <Key frame="20" value="-0.793436" />
                    <Key frame="30" value="-0.793436" />
                    <Key frame="40" value="-0.793436" />
                    <Key frame="61" value="-0.793436" />
                    <Key frame="81" value="-0.793436" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="49.3927" />
                    <Key frame="20" value="49.3927" />
                    <Key frame="30" value="49.3927" />
                    <Key frame="40" value="5.00743" />
                    <Key frame="61" value="-50.0128" />
                    <Key frame="81" value="3.86484" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-2.1949" />
                    <Key frame="20" value="-2.1949" />
                    <Key frame="30" value="-2.1949" />
                    <Key frame="40" value="4.83644" />
                    <Key frame="61" value="4.83644" />
                    <Key frame="81" value="4.83644" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="-86.2194" />
                    <Key frame="20" value="-86.2194" />
                    <Key frame="30" value="-19.8611" />
                    <Key frame="40" value="-19.8611" />
                    <Key frame="61" value="-19.2459" />
                    <Key frame="81" value="-19.2459" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="-53.4406" />
                    <Key frame="20" value="-84.9937" />
                    <Key frame="30" value="-92.4645" />
                    <Key frame="40" value="-92.4645" />
                    <Key frame="61" value="-92.4645" />
                    <Key frame="81" value="-92.4645" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="10" value="0.2856" />
                    <Key frame="20" value="0.2856" />
                    <Key frame="30" value="0.2856" />
                    <Key frame="40" value="0.2856" />
                    <Key frame="61" value="0.2856" />
                    <Key frame="81" value="0.2856" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="10" value="21.1843" />
                    <Key frame="20" value="21.1843" />
                    <Key frame="30" value="21.1843" />
                    <Key frame="40" value="20.5691" />
                    <Key frame="61" value="23.1179" />
                    <Key frame="81" value="20.4812" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="10" value="6.41851" />
                    <Key frame="20" value="6.41851" />
                    <Key frame="30" value="6.41851" />
                    <Key frame="40" value="6.41851" />
                    <Key frame="61" value="6.59429" />
                    <Key frame="81" value="7.38531" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.1777" />
                    <Key frame="20" value="-29.1777" />
                    <Key frame="30" value="-29.1777" />
                    <Key frame="40" value="-20.3006" />
                    <Key frame="61" value="-19.7732" />
                    <Key frame="81" value="-19.7732" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="10" value="-5.62747" />
                    <Key frame="20" value="-5.62747" />
                    <Key frame="30" value="-5.62747" />
                    <Key frame="40" value="-5.62747" />
                    <Key frame="61" value="-4.66066" />
                    <Key frame="81" value="-5.45169" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="101.864" />
                    <Key frame="20" value="102.743" />
                    <Key frame="30" value="107.577" />
                    <Key frame="40" value="108.895" />
                    <Key frame="61" value="108.895" />
                    <Key frame="81" value="108.895" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="16.8728" />
                    <Key frame="20" value="18.4549" />
                    <Key frame="30" value="13.9724" />
                    <Key frame="40" value="13.9724" />
                    <Key frame="61" value="14.5876" />
                    <Key frame="81" value="14.5876" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="10" value="12.8298" />
                    <Key frame="20" value="12.8298" />
                    <Key frame="30" value="12.8298" />
                    <Key frame="40" value="12.8298" />
                    <Key frame="61" value="12.8298" />
                    <Key frame="81" value="12.8298" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="37.1806" />
                    <Key frame="20" value="37.1806" />
                    <Key frame="30" value="31.0282" />
                    <Key frame="40" value="1.49656" />
                    <Key frame="61" value="1.67234" />
                    <Key frame="81" value="1.67234" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-16.697" />
                    <Key frame="20" value="-15.9939" />
                    <Key frame="30" value="-16.6091" />
                    <Key frame="40" value="-6.4137" />
                    <Key frame="61" value="-6.2379" />
                    <Key frame="81" value="-6.2379" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="87.5426" />
                    <Key frame="20" value="84.2906" />
                    <Key frame="30" value="23.1179" />
                    <Key frame="40" value="23.1179" />
                    <Key frame="61" value="22.4148" />
                    <Key frame="81" value="21.7117" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="52.7326" />
                    <Key frame="20" value="81.7369" />
                    <Key frame="30" value="90.5261" />
                    <Key frame="40" value="90.5261" />
                    <Key frame="61" value="90.5261" />
                    <Key frame="81" value="90.5261" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="10" value="0.3452" />
                    <Key frame="20" value="0.3452" />
                    <Key frame="30" value="0.3452" />
                    <Key frame="40" value="0.3452" />
                    <Key frame="61" value="0.3452" />
                    <Key frame="81" value="0.3452" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="10" value="-22.5027" />
                    <Key frame="20" value="-7.91266" />
                    <Key frame="30" value="22.2342" />
                    <Key frame="40" value="21.0037" />
                    <Key frame="61" value="20.9158" />
                    <Key frame="81" value="20.9158" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="10" value="-4.39218" />
                    <Key frame="20" value="-4.39218" />
                    <Key frame="30" value="-7.29261" />
                    <Key frame="40" value="-8.08364" />
                    <Key frame="61" value="-8.08364" />
                    <Key frame="81" value="-8.08364" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.1777" />
                    <Key frame="20" value="-29.1777" />
                    <Key frame="30" value="-29.1777" />
                    <Key frame="40" value="-20.3006" />
                    <Key frame="61" value="-19.7732" />
                    <Key frame="81" value="-19.7732" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="10" value="99.1443" />
                    <Key frame="20" value="55.6379" />
                    <Key frame="30" value="-5.53478" />
                    <Key frame="40" value="-5.53478" />
                    <Key frame="61" value="-5.53478" />
                    <Key frame="81" value="-5.53478" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="101.429" />
                    <Key frame="20" value="103.012" />
                    <Key frame="30" value="107.23" />
                    <Key frame="40" value="108.549" />
                    <Key frame="61" value="108.549" />
                    <Key frame="81" value="108.549" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="-18.8991" />
                    <Key frame="20" value="-23.5574" />
                    <Key frame="30" value="-15.8229" />
                    <Key frame="40" value="-16.4382" />
                    <Key frame="61" value="-16.4382" />
                    <Key frame="81" value="-16.4382" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="10" value="11.7751" />
                    <Key frame="20" value="9.05044" />
                    <Key frame="30" value="6.4137" />
                    <Key frame="40" value="5.79845" />
                    <Key frame="61" value="5.79845" />
                    <Key frame="81" value="5.79845" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="FüßeBewegen_Demo" id="23" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="694" y="858">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="105">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="nao">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="10" value="9.66569" />
                    <Key frame="20" value="9.66569" />
                    <Key frame="30" value="9.66569" />
                    <Key frame="40" value="10.2809" />
                    <Key frame="60" value="10.9841" />
                    <Key frame="75" value="10.9841" />
                    <Key frame="90" value="10.9841" />
                    <Key frame="105" value="10.9841" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="10" value="-0.793436" />
                    <Key frame="20" value="-0.793436" />
                    <Key frame="30" value="-0.793436" />
                    <Key frame="40" value="-0.793436" />
                    <Key frame="60" value="-0.793436" />
                    <Key frame="75" value="-0.793436" />
                    <Key frame="90" value="-0.178186" />
                    <Key frame="105" value="-0.178186" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="49.3927" />
                    <Key frame="20" value="49.3927" />
                    <Key frame="30" value="49.3927" />
                    <Key frame="40" value="5.00743" />
                    <Key frame="60" value="-13.9772" />
                    <Key frame="75" value="-13.9772" />
                    <Key frame="90" value="-15.3835" />
                    <Key frame="105" value="4.48007" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-2.1949" />
                    <Key frame="20" value="-2.1949" />
                    <Key frame="30" value="-2.1949" />
                    <Key frame="40" value="4.83644" />
                    <Key frame="60" value="28.0399" />
                    <Key frame="75" value="0.969218" />
                    <Key frame="90" value="-22.8494" />
                    <Key frame="105" value="4.74855" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="-86.2194" />
                    <Key frame="20" value="-86.2194" />
                    <Key frame="30" value="-19.8611" />
                    <Key frame="40" value="-19.8611" />
                    <Key frame="60" value="-19.2459" />
                    <Key frame="75" value="-19.2459" />
                    <Key frame="90" value="-19.2459" />
                    <Key frame="105" value="-19.2459" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="-53.4406" />
                    <Key frame="20" value="-84.9937" />
                    <Key frame="30" value="-92.4645" />
                    <Key frame="40" value="-92.4645" />
                    <Key frame="60" value="-92.4645" />
                    <Key frame="75" value="-92.4645" />
                    <Key frame="90" value="-92.4645" />
                    <Key frame="105" value="-92.4645" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="10" value="0.2856" />
                    <Key frame="20" value="0.2856" />
                    <Key frame="30" value="0.2856" />
                    <Key frame="40" value="0.2856" />
                    <Key frame="60" value="0.2856" />
                    <Key frame="75" value="0.2856" />
                    <Key frame="90" value="0.2856" />
                    <Key frame="105" value="0.2856" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="10" value="21.1843" />
                    <Key frame="20" value="21.1843" />
                    <Key frame="30" value="21.1843" />
                    <Key frame="40" value="20.5691" />
                    <Key frame="60" value="21.7117" />
                    <Key frame="75" value="21.7117" />
                    <Key frame="90" value="21.7117" />
                    <Key frame="105" value="20.2175" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="10" value="6.41851" />
                    <Key frame="20" value="6.41851" />
                    <Key frame="30" value="6.41851" />
                    <Key frame="40" value="6.41851" />
                    <Key frame="60" value="7.29742" />
                    <Key frame="75" value="6.06693" />
                    <Key frame="90" value="6.06693" />
                    <Key frame="105" value="6.68218" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.1777" />
                    <Key frame="20" value="-29.1777" />
                    <Key frame="30" value="-29.1777" />
                    <Key frame="40" value="-20.3006" />
                    <Key frame="60" value="-21.4432" />
                    <Key frame="75" value="-17.6638" />
                    <Key frame="90" value="-16.4334" />
                    <Key frame="105" value="-19.4217" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="10" value="-5.62747" />
                    <Key frame="20" value="-5.62747" />
                    <Key frame="30" value="-5.62747" />
                    <Key frame="40" value="-5.62747" />
                    <Key frame="60" value="-4.92435" />
                    <Key frame="75" value="-4.92435" />
                    <Key frame="90" value="-4.92435" />
                    <Key frame="105" value="-4.92435" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="101.864" />
                    <Key frame="20" value="102.743" />
                    <Key frame="30" value="107.577" />
                    <Key frame="40" value="108.895" />
                    <Key frame="60" value="108.895" />
                    <Key frame="75" value="108.895" />
                    <Key frame="90" value="108.895" />
                    <Key frame="105" value="108.895" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="16.8728" />
                    <Key frame="20" value="18.4549" />
                    <Key frame="30" value="13.9724" />
                    <Key frame="40" value="13.9724" />
                    <Key frame="60" value="14.5876" />
                    <Key frame="75" value="14.5876" />
                    <Key frame="90" value="15.2029" />
                    <Key frame="105" value="15.2029" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="10" value="12.8298" />
                    <Key frame="20" value="12.8298" />
                    <Key frame="30" value="12.8298" />
                    <Key frame="40" value="12.8298" />
                    <Key frame="60" value="12.8298" />
                    <Key frame="75" value="12.8298" />
                    <Key frame="90" value="12.8298" />
                    <Key frame="105" value="12.8298" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="37.1806" />
                    <Key frame="20" value="37.1806" />
                    <Key frame="30" value="31.0282" />
                    <Key frame="40" value="1.49656" />
                    <Key frame="60" value="-18.6306" />
                    <Key frame="75" value="-16.4334" />
                    <Key frame="90" value="-16.4334" />
                    <Key frame="105" value="1.40867" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-16.697" />
                    <Key frame="20" value="-15.9939" />
                    <Key frame="30" value="-16.6091" />
                    <Key frame="40" value="-6.4137" />
                    <Key frame="60" value="-31.1992" />
                    <Key frame="75" value="4.83644" />
                    <Key frame="90" value="22.8542" />
                    <Key frame="105" value="-5.71056" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="87.5426" />
                    <Key frame="20" value="84.2906" />
                    <Key frame="30" value="23.1179" />
                    <Key frame="40" value="23.1179" />
                    <Key frame="60" value="21.0085" />
                    <Key frame="75" value="21.0085" />
                    <Key frame="90" value="21.0085" />
                    <Key frame="105" value="21.6238" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="52.7326" />
                    <Key frame="20" value="81.7369" />
                    <Key frame="30" value="90.5261" />
                    <Key frame="40" value="90.5261" />
                    <Key frame="60" value="90.5261" />
                    <Key frame="75" value="90.5261" />
                    <Key frame="90" value="90.5261" />
                    <Key frame="105" value="90.5261" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="10" value="0.3452" />
                    <Key frame="20" value="0.3452" />
                    <Key frame="30" value="0.3452" />
                    <Key frame="40" value="0.3452" />
                    <Key frame="60" value="0.3452" />
                    <Key frame="75" value="0.3452" />
                    <Key frame="90" value="0.3452" />
                    <Key frame="105" value="0.3452" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="10" value="-22.5027" />
                    <Key frame="20" value="-7.91266" />
                    <Key frame="30" value="22.2342" />
                    <Key frame="40" value="21.0037" />
                    <Key frame="60" value="22.41" />
                    <Key frame="75" value="22.41" />
                    <Key frame="90" value="22.41" />
                    <Key frame="105" value="21.0916" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="10" value="-4.39218" />
                    <Key frame="20" value="-4.39218" />
                    <Key frame="30" value="-7.29261" />
                    <Key frame="40" value="-8.08364" />
                    <Key frame="60" value="-7.64417" />
                    <Key frame="75" value="-7.02893" />
                    <Key frame="90" value="-7.02893" />
                    <Key frame="105" value="-8.25942" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.1777" />
                    <Key frame="20" value="-29.1777" />
                    <Key frame="30" value="-29.1777" />
                    <Key frame="40" value="-20.3006" />
                    <Key frame="60" value="-21.4432" />
                    <Key frame="75" value="-17.6638" />
                    <Key frame="90" value="-16.4334" />
                    <Key frame="105" value="-19.4217" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="10" value="99.1443" />
                    <Key frame="20" value="55.6379" />
                    <Key frame="30" value="-5.53478" />
                    <Key frame="40" value="-5.53478" />
                    <Key frame="60" value="-4.91954" />
                    <Key frame="75" value="-4.91954" />
                    <Key frame="90" value="-4.91954" />
                    <Key frame="105" value="-4.91954" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="101.429" />
                    <Key frame="20" value="103.012" />
                    <Key frame="30" value="107.23" />
                    <Key frame="40" value="108.549" />
                    <Key frame="60" value="108.549" />
                    <Key frame="75" value="108.549" />
                    <Key frame="90" value="108.549" />
                    <Key frame="105" value="108.549" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="-18.8991" />
                    <Key frame="20" value="-23.5574" />
                    <Key frame="30" value="-15.8229" />
                    <Key frame="40" value="-16.4382" />
                    <Key frame="60" value="-16.4382" />
                    <Key frame="75" value="-16.4382" />
                    <Key frame="90" value="-16.4382" />
                    <Key frame="105" value="-15.735" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="10" value="11.7751" />
                    <Key frame="20" value="9.05044" />
                    <Key frame="30" value="6.4137" />
                    <Key frame="40" value="5.79845" />
                    <Key frame="60" value="5.79845" />
                    <Key frame="75" value="5.79845" />
                    <Key frame="90" value="5.79845" />
                    <Key frame="105" value="5.79845" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="FüßeBewegenRechts_Demo" id="25" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="691" y="971">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="105">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="nao">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="10" value="9.66569" />
                    <Key frame="20" value="9.66569" />
                    <Key frame="30" value="9.66569" />
                    <Key frame="40" value="10.2809" />
                    <Key frame="60" value="10.9841" />
                    <Key frame="75" value="10.9841" />
                    <Key frame="90" value="10.9841" />
                    <Key frame="105" value="10.9841" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="10" value="-0.793436" />
                    <Key frame="20" value="-0.793436" />
                    <Key frame="30" value="-0.793436" />
                    <Key frame="40" value="-0.793436" />
                    <Key frame="60" value="-0.178186" />
                    <Key frame="75" value="-0.178186" />
                    <Key frame="90" value="-0.178186" />
                    <Key frame="105" value="-0.178186" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="49.3927" />
                    <Key frame="20" value="49.3927" />
                    <Key frame="30" value="49.3927" />
                    <Key frame="40" value="5.00743" />
                    <Key frame="60" value="-15.3835" />
                    <Key frame="75" value="-15.9987" />
                    <Key frame="90" value="-15.9987" />
                    <Key frame="105" value="-15.9987" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-2.1949" />
                    <Key frame="20" value="-2.1949" />
                    <Key frame="30" value="-2.1949" />
                    <Key frame="40" value="4.83644" />
                    <Key frame="60" value="-5.00743" />
                    <Key frame="75" value="-5.00743" />
                    <Key frame="90" value="-5.00743" />
                    <Key frame="105" value="-5.00743" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="-86.2194" />
                    <Key frame="20" value="-86.2194" />
                    <Key frame="30" value="-19.8611" />
                    <Key frame="40" value="-19.8611" />
                    <Key frame="60" value="-19.2459" />
                    <Key frame="75" value="-20.5643" />
                    <Key frame="90" value="-20.5643" />
                    <Key frame="105" value="-20.5643" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="-53.4406" />
                    <Key frame="20" value="-84.9937" />
                    <Key frame="30" value="-92.4645" />
                    <Key frame="40" value="-92.4645" />
                    <Key frame="60" value="-92.4645" />
                    <Key frame="75" value="-92.4645" />
                    <Key frame="90" value="-92.4645" />
                    <Key frame="105" value="-92.4645" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="10" value="0.2856" />
                    <Key frame="20" value="0.2856" />
                    <Key frame="30" value="0.2856" />
                    <Key frame="40" value="0.2856" />
                    <Key frame="60" value="0.3128" />
                    <Key frame="75" value="0.3388" />
                    <Key frame="90" value="0.3388" />
                    <Key frame="105" value="0.3388" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="10" value="21.1843" />
                    <Key frame="20" value="21.1843" />
                    <Key frame="30" value="21.1843" />
                    <Key frame="40" value="20.5691" />
                    <Key frame="60" value="21.7996" />
                    <Key frame="75" value="21.7996" />
                    <Key frame="90" value="22.4148" />
                    <Key frame="105" value="22.4148" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="10" value="6.41851" />
                    <Key frame="20" value="6.41851" />
                    <Key frame="30" value="6.41851" />
                    <Key frame="40" value="6.41851" />
                    <Key frame="60" value="7.29742" />
                    <Key frame="75" value="6.68218" />
                    <Key frame="90" value="6.68218" />
                    <Key frame="105" value="6.68218" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.1777" />
                    <Key frame="20" value="-29.1777" />
                    <Key frame="30" value="-29.1777" />
                    <Key frame="40" value="-20.3006" />
                    <Key frame="60" value="-17.8396" />
                    <Key frame="75" value="-17.2244" />
                    <Key frame="90" value="-17.2244" />
                    <Key frame="105" value="-17.2244" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="10" value="-5.62747" />
                    <Key frame="20" value="-5.62747" />
                    <Key frame="30" value="-5.62747" />
                    <Key frame="40" value="-5.62747" />
                    <Key frame="60" value="-4.92435" />
                    <Key frame="75" value="-4.92435" />
                    <Key frame="90" value="-4.92435" />
                    <Key frame="105" value="-4.92435" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="101.864" />
                    <Key frame="20" value="102.743" />
                    <Key frame="30" value="107.577" />
                    <Key frame="40" value="108.895" />
                    <Key frame="60" value="108.895" />
                    <Key frame="75" value="108.895" />
                    <Key frame="90" value="108.895" />
                    <Key frame="105" value="108.895" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="16.8728" />
                    <Key frame="20" value="18.4549" />
                    <Key frame="30" value="13.9724" />
                    <Key frame="40" value="13.9724" />
                    <Key frame="60" value="15.2029" />
                    <Key frame="75" value="15.2029" />
                    <Key frame="90" value="15.2029" />
                    <Key frame="105" value="15.2029" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="10" value="12.8298" />
                    <Key frame="20" value="12.8298" />
                    <Key frame="30" value="12.8298" />
                    <Key frame="40" value="12.8298" />
                    <Key frame="60" value="3.60116" />
                    <Key frame="75" value="-3.07861" />
                    <Key frame="90" value="-3.07861" />
                    <Key frame="105" value="-3.07861" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="37.1806" />
                    <Key frame="20" value="37.1806" />
                    <Key frame="30" value="31.0282" />
                    <Key frame="40" value="1.49656" />
                    <Key frame="60" value="-21.2674" />
                    <Key frame="75" value="-14.6755" />
                    <Key frame="90" value="-14.6755" />
                    <Key frame="105" value="-14.6755" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-16.697" />
                    <Key frame="20" value="-15.9939" />
                    <Key frame="30" value="-16.6091" />
                    <Key frame="40" value="-6.4137" />
                    <Key frame="60" value="-23.0252" />
                    <Key frame="75" value="-0.964409" />
                    <Key frame="90" value="22.3269" />
                    <Key frame="105" value="0.00240423" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="87.5426" />
                    <Key frame="20" value="84.2906" />
                    <Key frame="30" value="23.1179" />
                    <Key frame="40" value="23.1179" />
                    <Key frame="60" value="20.3054" />
                    <Key frame="75" value="18.987" />
                    <Key frame="90" value="18.987" />
                    <Key frame="105" value="18.987" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="52.7326" />
                    <Key frame="20" value="81.7369" />
                    <Key frame="30" value="90.5261" />
                    <Key frame="40" value="90.5261" />
                    <Key frame="60" value="91.1413" />
                    <Key frame="75" value="91.9323" />
                    <Key frame="90" value="91.9323" />
                    <Key frame="105" value="91.9323" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="10" value="0.3452" />
                    <Key frame="20" value="0.3452" />
                    <Key frame="30" value="0.3452" />
                    <Key frame="40" value="0.3452" />
                    <Key frame="60" value="0.3272" />
                    <Key frame="75" value="0.3044" />
                    <Key frame="90" value="0.3044" />
                    <Key frame="105" value="0.3044" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="10" value="-22.5027" />
                    <Key frame="20" value="-7.91266" />
                    <Key frame="30" value="22.2342" />
                    <Key frame="40" value="21.0037" />
                    <Key frame="60" value="22.41" />
                    <Key frame="75" value="22.41" />
                    <Key frame="90" value="22.41" />
                    <Key frame="105" value="22.41" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="10" value="-4.39218" />
                    <Key frame="20" value="-4.39218" />
                    <Key frame="30" value="-7.29261" />
                    <Key frame="40" value="-8.08364" />
                    <Key frame="60" value="-5.62267" />
                    <Key frame="75" value="-5.62267" />
                    <Key frame="90" value="-5.62267" />
                    <Key frame="105" value="-5.62267" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.1777" />
                    <Key frame="20" value="-29.1777" />
                    <Key frame="30" value="-29.1777" />
                    <Key frame="40" value="-20.3006" />
                    <Key frame="60" value="-17.8396" />
                    <Key frame="75" value="-17.2244" />
                    <Key frame="90" value="-17.2244" />
                    <Key frame="105" value="-17.2244" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="10" value="99.1443" />
                    <Key frame="20" value="55.6379" />
                    <Key frame="30" value="-5.53478" />
                    <Key frame="40" value="-5.53478" />
                    <Key frame="60" value="-4.91954" />
                    <Key frame="75" value="-4.91954" />
                    <Key frame="90" value="-4.91954" />
                    <Key frame="105" value="-4.91954" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="101.429" />
                    <Key frame="20" value="103.012" />
                    <Key frame="30" value="107.23" />
                    <Key frame="40" value="108.549" />
                    <Key frame="60" value="108.549" />
                    <Key frame="75" value="108.549" />
                    <Key frame="90" value="108.549" />
                    <Key frame="105" value="108.549" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="-18.8991" />
                    <Key frame="20" value="-23.5574" />
                    <Key frame="30" value="-15.8229" />
                    <Key frame="40" value="-16.4382" />
                    <Key frame="60" value="-15.735" />
                    <Key frame="75" value="-15.735" />
                    <Key frame="90" value="-15.735" />
                    <Key frame="105" value="-15.735" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="10" value="11.7751" />
                    <Key frame="20" value="9.05044" />
                    <Key frame="30" value="6.4137" />
                    <Key frame="40" value="5.79845" />
                    <Key frame="60" value="-5.01224" />
                    <Key frame="75" value="-11.5162" />
                    <Key frame="90" value="-11.5162" />
                    <Key frame="105" value="-11.5162" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="FüßeBewegenLinks_Demo" id="26" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="694" y="1080">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="105">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="nao">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="10" value="9.66569" />
                    <Key frame="20" value="9.66569" />
                    <Key frame="30" value="9.66569" />
                    <Key frame="40" value="10.2809" />
                    <Key frame="60" value="10.9841" />
                    <Key frame="75" value="10.9841" />
                    <Key frame="90" value="10.9841" />
                    <Key frame="105" value="10.9841" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="10" value="-0.793436" />
                    <Key frame="20" value="-0.793436" />
                    <Key frame="30" value="-0.793436" />
                    <Key frame="40" value="-0.793436" />
                    <Key frame="60" value="-0.178186" />
                    <Key frame="75" value="-0.178186" />
                    <Key frame="90" value="-0.178186" />
                    <Key frame="105" value="-0.178186" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="49.3927" />
                    <Key frame="20" value="49.3927" />
                    <Key frame="30" value="49.3927" />
                    <Key frame="40" value="5.00743" />
                    <Key frame="60" value="-21.6238" />
                    <Key frame="75" value="-14.6803" />
                    <Key frame="90" value="-14.6803" />
                    <Key frame="105" value="-14.6803" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-2.1949" />
                    <Key frame="20" value="-2.1949" />
                    <Key frame="30" value="-2.1949" />
                    <Key frame="40" value="4.83644" />
                    <Key frame="60" value="22.4148" />
                    <Key frame="75" value="1.05711" />
                    <Key frame="90" value="-22.3221" />
                    <Key frame="105" value="-0.0854867" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="-86.2194" />
                    <Key frame="20" value="-86.2194" />
                    <Key frame="30" value="-19.8611" />
                    <Key frame="40" value="-19.8611" />
                    <Key frame="60" value="-18.8064" />
                    <Key frame="75" value="-18.8064" />
                    <Key frame="90" value="-18.8064" />
                    <Key frame="105" value="-18.8064" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="-53.4406" />
                    <Key frame="20" value="-84.9937" />
                    <Key frame="30" value="-92.4645" />
                    <Key frame="40" value="-92.4645" />
                    <Key frame="60" value="-92.4645" />
                    <Key frame="75" value="-92.4645" />
                    <Key frame="90" value="-92.4645" />
                    <Key frame="105" value="-92.4645" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="10" value="0.2856" />
                    <Key frame="20" value="0.2856" />
                    <Key frame="30" value="0.2856" />
                    <Key frame="40" value="0.2856" />
                    <Key frame="60" value="0.3084" />
                    <Key frame="75" value="0.3084" />
                    <Key frame="90" value="0.3084" />
                    <Key frame="105" value="0.3084" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="10" value="21.1843" />
                    <Key frame="20" value="21.1843" />
                    <Key frame="30" value="21.1843" />
                    <Key frame="40" value="20.5691" />
                    <Key frame="60" value="22.4148" />
                    <Key frame="75" value="23.03" />
                    <Key frame="90" value="23.03" />
                    <Key frame="105" value="22.4148" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="10" value="6.41851" />
                    <Key frame="20" value="6.41851" />
                    <Key frame="30" value="6.41851" />
                    <Key frame="40" value="6.41851" />
                    <Key frame="60" value="5.45169" />
                    <Key frame="75" value="6.15482" />
                    <Key frame="90" value="5.53958" />
                    <Key frame="105" value="4.92435" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.1777" />
                    <Key frame="20" value="-29.1777" />
                    <Key frame="30" value="-29.1777" />
                    <Key frame="40" value="-20.3006" />
                    <Key frame="60" value="-17.2244" />
                    <Key frame="75" value="-17.2244" />
                    <Key frame="90" value="-17.2244" />
                    <Key frame="105" value="-17.2244" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="10" value="-5.62747" />
                    <Key frame="20" value="-5.62747" />
                    <Key frame="30" value="-5.62747" />
                    <Key frame="40" value="-5.62747" />
                    <Key frame="60" value="-5.53958" />
                    <Key frame="75" value="-5.53958" />
                    <Key frame="90" value="-4.92435" />
                    <Key frame="105" value="-4.92435" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="101.864" />
                    <Key frame="20" value="102.743" />
                    <Key frame="30" value="107.577" />
                    <Key frame="40" value="108.895" />
                    <Key frame="60" value="108.895" />
                    <Key frame="75" value="108.895" />
                    <Key frame="90" value="108.895" />
                    <Key frame="105" value="108.895" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="16.8728" />
                    <Key frame="20" value="18.4549" />
                    <Key frame="30" value="13.9724" />
                    <Key frame="40" value="13.9724" />
                    <Key frame="60" value="15.2029" />
                    <Key frame="75" value="15.2029" />
                    <Key frame="90" value="15.2029" />
                    <Key frame="105" value="15.2029" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="10" value="12.8298" />
                    <Key frame="20" value="12.8298" />
                    <Key frame="30" value="12.8298" />
                    <Key frame="40" value="12.8298" />
                    <Key frame="60" value="11.072" />
                    <Key frame="75" value="11.7751" />
                    <Key frame="90" value="11.7751" />
                    <Key frame="105" value="11.7751" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="37.1806" />
                    <Key frame="20" value="37.1806" />
                    <Key frame="30" value="31.0282" />
                    <Key frame="40" value="1.49656" />
                    <Key frame="60" value="-16.0818" />
                    <Key frame="75" value="-16.0818" />
                    <Key frame="90" value="-16.0818" />
                    <Key frame="105" value="-16.0818" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-16.697" />
                    <Key frame="20" value="-15.9939" />
                    <Key frame="30" value="-16.6091" />
                    <Key frame="40" value="-6.4137" />
                    <Key frame="60" value="4.92435" />
                    <Key frame="75" value="4.92435" />
                    <Key frame="90" value="4.92435" />
                    <Key frame="105" value="4.92435" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="87.5426" />
                    <Key frame="20" value="84.2906" />
                    <Key frame="30" value="23.1179" />
                    <Key frame="40" value="23.1179" />
                    <Key frame="60" value="20.4812" />
                    <Key frame="75" value="20.4812" />
                    <Key frame="90" value="20.4812" />
                    <Key frame="105" value="20.4812" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="52.7326" />
                    <Key frame="20" value="81.7369" />
                    <Key frame="30" value="90.5261" />
                    <Key frame="40" value="90.5261" />
                    <Key frame="60" value="91.9323" />
                    <Key frame="75" value="91.9323" />
                    <Key frame="90" value="91.9323" />
                    <Key frame="105" value="91.9323" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="10" value="0.3452" />
                    <Key frame="20" value="0.3452" />
                    <Key frame="30" value="0.3452" />
                    <Key frame="40" value="0.3452" />
                    <Key frame="60" value="0.3336" />
                    <Key frame="75" value="0.3336" />
                    <Key frame="90" value="0.3336" />
                    <Key frame="105" value="0.3336" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="10" value="-22.5027" />
                    <Key frame="20" value="-7.91266" />
                    <Key frame="30" value="22.2342" />
                    <Key frame="40" value="21.0037" />
                    <Key frame="60" value="22.41" />
                    <Key frame="75" value="22.41" />
                    <Key frame="90" value="22.41" />
                    <Key frame="105" value="22.41" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="10" value="-4.39218" />
                    <Key frame="20" value="-4.39218" />
                    <Key frame="30" value="-7.29261" />
                    <Key frame="40" value="-8.08364" />
                    <Key frame="60" value="-6.2379" />
                    <Key frame="75" value="-6.2379" />
                    <Key frame="90" value="-6.2379" />
                    <Key frame="105" value="-6.2379" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.1777" />
                    <Key frame="20" value="-29.1777" />
                    <Key frame="30" value="-29.1777" />
                    <Key frame="40" value="-20.3006" />
                    <Key frame="60" value="-17.2244" />
                    <Key frame="75" value="-17.2244" />
                    <Key frame="90" value="-17.2244" />
                    <Key frame="105" value="-17.2244" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="10" value="99.1443" />
                    <Key frame="20" value="55.6379" />
                    <Key frame="30" value="-5.53478" />
                    <Key frame="40" value="-5.53478" />
                    <Key frame="60" value="-5.53478" />
                    <Key frame="75" value="-5.53478" />
                    <Key frame="90" value="-5.53478" />
                    <Key frame="105" value="-5.53478" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="101.429" />
                    <Key frame="20" value="103.012" />
                    <Key frame="30" value="107.23" />
                    <Key frame="40" value="108.549" />
                    <Key frame="60" value="108.549" />
                    <Key frame="75" value="108.549" />
                    <Key frame="90" value="108.549" />
                    <Key frame="105" value="108.549" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="-18.8991" />
                    <Key frame="20" value="-23.5574" />
                    <Key frame="30" value="-15.8229" />
                    <Key frame="40" value="-16.4382" />
                    <Key frame="60" value="-15.735" />
                    <Key frame="75" value="-15.735" />
                    <Key frame="90" value="-15.735" />
                    <Key frame="105" value="-15.735" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="10" value="11.7751" />
                    <Key frame="20" value="9.05044" />
                    <Key frame="30" value="6.4137" />
                    <Key frame="40" value="5.79845" />
                    <Key frame="60" value="3.42537" />
                    <Key frame="75" value="3.42537" />
                    <Key frame="90" value="3.42537" />
                    <Key frame="105" value="3.42537" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="FüßeKreisen_Demo" id="27" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="858" y="870">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="120">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="nao">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="10" value="9.66569" />
                    <Key frame="20" value="9.66569" />
                    <Key frame="30" value="9.66569" />
                    <Key frame="40" value="10.2809" />
                    <Key frame="60" value="10.9841" />
                    <Key frame="80" value="10.9841" />
                    <Key frame="100" value="10.9841" />
                    <Key frame="120" value="10.9841" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="10" value="-0.793436" />
                    <Key frame="20" value="-0.793436" />
                    <Key frame="30" value="-0.793436" />
                    <Key frame="40" value="-0.793436" />
                    <Key frame="60" value="-0.178186" />
                    <Key frame="80" value="-0.178186" />
                    <Key frame="100" value="-0.178186" />
                    <Key frame="120" value="-0.178186" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="49.3927" />
                    <Key frame="20" value="49.3927" />
                    <Key frame="30" value="49.3927" />
                    <Key frame="40" value="5.00743" />
                    <Key frame="60" value="-14.6803" />
                    <Key frame="80" value="-48.0792" />
                    <Key frame="100" value="-15.9987" />
                    <Key frame="120" value="4.65585" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-2.1949" />
                    <Key frame="20" value="-2.1949" />
                    <Key frame="30" value="-2.1949" />
                    <Key frame="40" value="4.83644" />
                    <Key frame="60" value="27.8641" />
                    <Key frame="80" value="9.84628" />
                    <Key frame="100" value="-22.3221" />
                    <Key frame="120" value="4.83644" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="-86.2194" />
                    <Key frame="20" value="-86.2194" />
                    <Key frame="30" value="-19.8611" />
                    <Key frame="40" value="-19.8611" />
                    <Key frame="60" value="-18.8064" />
                    <Key frame="80" value="-18.8064" />
                    <Key frame="100" value="-18.8064" />
                    <Key frame="120" value="-18.8064" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="-53.4406" />
                    <Key frame="20" value="-84.9937" />
                    <Key frame="30" value="-92.4645" />
                    <Key frame="40" value="-92.4645" />
                    <Key frame="60" value="-92.4645" />
                    <Key frame="80" value="-92.4645" />
                    <Key frame="100" value="-92.4645" />
                    <Key frame="120" value="-92.4645" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="10" value="0.2856" />
                    <Key frame="20" value="0.2856" />
                    <Key frame="30" value="0.2856" />
                    <Key frame="40" value="0.2856" />
                    <Key frame="60" value="0.3084" />
                    <Key frame="80" value="0.3084" />
                    <Key frame="100" value="0.3084" />
                    <Key frame="120" value="0.3084" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="10" value="21.1843" />
                    <Key frame="20" value="21.1843" />
                    <Key frame="30" value="21.1843" />
                    <Key frame="40" value="20.5691" />
                    <Key frame="60" value="21.7996" />
                    <Key frame="80" value="22.4148" />
                    <Key frame="100" value="22.4148" />
                    <Key frame="120" value="20.3933" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="10" value="6.41851" />
                    <Key frame="20" value="6.41851" />
                    <Key frame="30" value="6.41851" />
                    <Key frame="40" value="6.41851" />
                    <Key frame="60" value="8.44001" />
                    <Key frame="80" value="5.45169" />
                    <Key frame="100" value="5.45169" />
                    <Key frame="120" value="6.77007" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.1777" />
                    <Key frame="20" value="-29.1777" />
                    <Key frame="30" value="-29.1777" />
                    <Key frame="40" value="-20.3006" />
                    <Key frame="60" value="-21.7948" />
                    <Key frame="80" value="-16.5212" />
                    <Key frame="100" value="-16.5212" />
                    <Key frame="120" value="-19.5975" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="10" value="-5.62747" />
                    <Key frame="20" value="-5.62747" />
                    <Key frame="30" value="-5.62747" />
                    <Key frame="40" value="-5.62747" />
                    <Key frame="60" value="-4.92435" />
                    <Key frame="80" value="-4.92435" />
                    <Key frame="100" value="-4.92435" />
                    <Key frame="120" value="-4.92435" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="101.864" />
                    <Key frame="20" value="102.743" />
                    <Key frame="30" value="107.577" />
                    <Key frame="40" value="108.895" />
                    <Key frame="60" value="108.895" />
                    <Key frame="80" value="108.895" />
                    <Key frame="100" value="108.895" />
                    <Key frame="120" value="108.895" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="16.8728" />
                    <Key frame="20" value="18.4549" />
                    <Key frame="30" value="13.9724" />
                    <Key frame="40" value="13.9724" />
                    <Key frame="60" value="15.2029" />
                    <Key frame="80" value="15.2029" />
                    <Key frame="100" value="15.2029" />
                    <Key frame="120" value="15.2029" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="10" value="12.8298" />
                    <Key frame="20" value="12.8298" />
                    <Key frame="30" value="12.8298" />
                    <Key frame="40" value="12.8298" />
                    <Key frame="60" value="12.3903" />
                    <Key frame="80" value="12.3903" />
                    <Key frame="100" value="12.3903" />
                    <Key frame="120" value="12.3903" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="37.1806" />
                    <Key frame="20" value="37.1806" />
                    <Key frame="30" value="31.0282" />
                    <Key frame="40" value="1.49656" />
                    <Key frame="60" value="-18.6306" />
                    <Key frame="80" value="-52.9084" />
                    <Key frame="100" value="-16.9607" />
                    <Key frame="120" value="1.67234" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-16.697" />
                    <Key frame="20" value="-15.9939" />
                    <Key frame="30" value="-16.6091" />
                    <Key frame="40" value="-6.4137" />
                    <Key frame="60" value="-31.1992" />
                    <Key frame="80" value="-0.43705" />
                    <Key frame="100" value="22.7664" />
                    <Key frame="120" value="-5.88634" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="87.5426" />
                    <Key frame="20" value="84.2906" />
                    <Key frame="30" value="23.1179" />
                    <Key frame="40" value="23.1179" />
                    <Key frame="60" value="21.1843" />
                    <Key frame="80" value="21.1843" />
                    <Key frame="100" value="21.1843" />
                    <Key frame="120" value="21.7996" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="52.7326" />
                    <Key frame="20" value="81.7369" />
                    <Key frame="30" value="90.5261" />
                    <Key frame="40" value="90.5261" />
                    <Key frame="60" value="91.9323" />
                    <Key frame="80" value="91.9323" />
                    <Key frame="100" value="91.9323" />
                    <Key frame="120" value="91.9323" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="10" value="0.3452" />
                    <Key frame="20" value="0.3452" />
                    <Key frame="30" value="0.3452" />
                    <Key frame="40" value="0.3452" />
                    <Key frame="60" value="0.3336" />
                    <Key frame="80" value="0.3336" />
                    <Key frame="100" value="0.3336" />
                    <Key frame="120" value="0.3336" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="10" value="-22.5027" />
                    <Key frame="20" value="-7.91266" />
                    <Key frame="30" value="22.2342" />
                    <Key frame="40" value="21.0037" />
                    <Key frame="60" value="22.41" />
                    <Key frame="80" value="22.41" />
                    <Key frame="100" value="22.41" />
                    <Key frame="120" value="20.9158" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="10" value="-4.39218" />
                    <Key frame="20" value="-4.39218" />
                    <Key frame="30" value="-7.29261" />
                    <Key frame="40" value="-8.08364" />
                    <Key frame="60" value="-8.17153" />
                    <Key frame="80" value="-8.17153" />
                    <Key frame="100" value="-7.55628" />
                    <Key frame="120" value="-7.55628" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.1777" />
                    <Key frame="20" value="-29.1777" />
                    <Key frame="30" value="-29.1777" />
                    <Key frame="40" value="-20.3006" />
                    <Key frame="60" value="-21.7948" />
                    <Key frame="80" value="-16.5212" />
                    <Key frame="100" value="-16.5212" />
                    <Key frame="120" value="-19.5975" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="10" value="99.1443" />
                    <Key frame="20" value="55.6379" />
                    <Key frame="30" value="-5.53478" />
                    <Key frame="40" value="-5.53478" />
                    <Key frame="60" value="-5.53478" />
                    <Key frame="80" value="-5.53478" />
                    <Key frame="100" value="-5.53478" />
                    <Key frame="120" value="-5.53478" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="101.429" />
                    <Key frame="20" value="103.012" />
                    <Key frame="30" value="107.23" />
                    <Key frame="40" value="108.549" />
                    <Key frame="60" value="108.549" />
                    <Key frame="80" value="108.549" />
                    <Key frame="100" value="108.549" />
                    <Key frame="120" value="108.549" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="-18.8991" />
                    <Key frame="20" value="-23.5574" />
                    <Key frame="30" value="-15.8229" />
                    <Key frame="40" value="-16.4382" />
                    <Key frame="60" value="-15.735" />
                    <Key frame="80" value="-15.735" />
                    <Key frame="100" value="-15.735" />
                    <Key frame="120" value="-15.735" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="10" value="11.7751" />
                    <Key frame="20" value="9.05044" />
                    <Key frame="30" value="6.4137" />
                    <Key frame="40" value="5.79845" />
                    <Key frame="60" value="3.42537" />
                    <Key frame="80" value="3.42537" />
                    <Key frame="100" value="3.42537" />
                    <Key frame="120" value="3.42537" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="FüßeKreisenRechts_Demo" id="29" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="860" y="977">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="120">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="nao">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="10" value="9.66569" />
                    <Key frame="20" value="9.66569" />
                    <Key frame="30" value="9.66569" />
                    <Key frame="40" value="10.2809" />
                    <Key frame="60" value="10.9841" />
                    <Key frame="80" value="10.9841" />
                    <Key frame="100" value="10.9841" />
                    <Key frame="120" value="10.9841" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="10" value="-0.793436" />
                    <Key frame="20" value="-0.793436" />
                    <Key frame="30" value="-0.793436" />
                    <Key frame="40" value="-0.793436" />
                    <Key frame="60" value="-0.178186" />
                    <Key frame="80" value="-0.178186" />
                    <Key frame="100" value="-0.178186" />
                    <Key frame="120" value="-0.178186" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="49.3927" />
                    <Key frame="20" value="49.3927" />
                    <Key frame="30" value="49.3927" />
                    <Key frame="40" value="5.00743" />
                    <Key frame="60" value="1.66754" />
                    <Key frame="80" value="1.66754" />
                    <Key frame="100" value="1.66754" />
                    <Key frame="120" value="1.66754" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-2.1949" />
                    <Key frame="20" value="-2.1949" />
                    <Key frame="30" value="-2.1949" />
                    <Key frame="40" value="4.83644" />
                    <Key frame="60" value="5.71537" />
                    <Key frame="80" value="5.71537" />
                    <Key frame="100" value="5.71537" />
                    <Key frame="120" value="5.71537" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="-86.2194" />
                    <Key frame="20" value="-86.2194" />
                    <Key frame="30" value="-19.8611" />
                    <Key frame="40" value="-19.8611" />
                    <Key frame="60" value="-18.8064" />
                    <Key frame="80" value="-18.8064" />
                    <Key frame="100" value="-18.8064" />
                    <Key frame="120" value="-18.8064" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="-53.4406" />
                    <Key frame="20" value="-84.9937" />
                    <Key frame="30" value="-92.4645" />
                    <Key frame="40" value="-92.4645" />
                    <Key frame="60" value="-92.4645" />
                    <Key frame="80" value="-92.4645" />
                    <Key frame="100" value="-92.4645" />
                    <Key frame="120" value="-92.4645" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="10" value="0.2856" />
                    <Key frame="20" value="0.2856" />
                    <Key frame="30" value="0.2856" />
                    <Key frame="40" value="0.2856" />
                    <Key frame="60" value="0.3084" />
                    <Key frame="80" value="0.298" />
                    <Key frame="100" value="0.298" />
                    <Key frame="120" value="0.298" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="10" value="21.1843" />
                    <Key frame="20" value="21.1843" />
                    <Key frame="30" value="21.1843" />
                    <Key frame="40" value="20.5691" />
                    <Key frame="60" value="20.4812" />
                    <Key frame="80" value="20.4812" />
                    <Key frame="100" value="20.4812" />
                    <Key frame="120" value="20.4812" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="10" value="6.41851" />
                    <Key frame="20" value="6.41851" />
                    <Key frame="30" value="6.41851" />
                    <Key frame="40" value="6.41851" />
                    <Key frame="60" value="7.29742" />
                    <Key frame="80" value="8.5279" />
                    <Key frame="100" value="8.5279" />
                    <Key frame="120" value="8.5279" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.1777" />
                    <Key frame="20" value="-29.1777" />
                    <Key frame="30" value="-29.1777" />
                    <Key frame="40" value="-20.3006" />
                    <Key frame="60" value="-18.9822" />
                    <Key frame="80" value="-19.5975" />
                    <Key frame="100" value="-19.5975" />
                    <Key frame="120" value="-20.2127" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="10" value="-5.62747" />
                    <Key frame="20" value="-5.62747" />
                    <Key frame="30" value="-5.62747" />
                    <Key frame="40" value="-5.62747" />
                    <Key frame="60" value="-5.53958" />
                    <Key frame="80" value="-5.53958" />
                    <Key frame="100" value="-5.53958" />
                    <Key frame="120" value="-5.53958" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="101.864" />
                    <Key frame="20" value="102.743" />
                    <Key frame="30" value="107.577" />
                    <Key frame="40" value="108.895" />
                    <Key frame="60" value="108.895" />
                    <Key frame="80" value="108.895" />
                    <Key frame="100" value="108.895" />
                    <Key frame="120" value="108.895" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="16.8728" />
                    <Key frame="20" value="18.4549" />
                    <Key frame="30" value="13.9724" />
                    <Key frame="40" value="13.9724" />
                    <Key frame="60" value="15.2029" />
                    <Key frame="80" value="15.2029" />
                    <Key frame="100" value="15.2029" />
                    <Key frame="120" value="15.2029" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="10" value="12.8298" />
                    <Key frame="20" value="12.8298" />
                    <Key frame="30" value="12.8298" />
                    <Key frame="40" value="12.8298" />
                    <Key frame="60" value="12.3903" />
                    <Key frame="80" value="12.3903" />
                    <Key frame="100" value="12.3903" />
                    <Key frame="120" value="12.3903" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="37.1806" />
                    <Key frame="20" value="37.1806" />
                    <Key frame="30" value="31.0282" />
                    <Key frame="40" value="1.49656" />
                    <Key frame="60" value="-14.9392" />
                    <Key frame="80" value="-50.4474" />
                    <Key frame="100" value="-19.0701" />
                    <Key frame="120" value="4.3091" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-16.697" />
                    <Key frame="20" value="-15.9939" />
                    <Key frame="30" value="-16.6091" />
                    <Key frame="40" value="-6.4137" />
                    <Key frame="60" value="22.5027" />
                    <Key frame="80" value="-3.95273" />
                    <Key frame="100" value="-18.8943" />
                    <Key frame="120" value="-5.09532" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="87.5426" />
                    <Key frame="20" value="84.2906" />
                    <Key frame="30" value="23.1179" />
                    <Key frame="40" value="23.1179" />
                    <Key frame="60" value="20.5691" />
                    <Key frame="80" value="19.778" />
                    <Key frame="100" value="19.1628" />
                    <Key frame="120" value="19.1628" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="52.7326" />
                    <Key frame="20" value="81.7369" />
                    <Key frame="30" value="90.5261" />
                    <Key frame="40" value="90.5261" />
                    <Key frame="60" value="91.9323" />
                    <Key frame="80" value="91.9323" />
                    <Key frame="100" value="91.9323" />
                    <Key frame="120" value="91.9323" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="10" value="0.3452" />
                    <Key frame="20" value="0.3452" />
                    <Key frame="30" value="0.3452" />
                    <Key frame="40" value="0.3452" />
                    <Key frame="60" value="0.3336" />
                    <Key frame="80" value="0.3336" />
                    <Key frame="100" value="0.3336" />
                    <Key frame="120" value="0.3336" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="10" value="-22.5027" />
                    <Key frame="20" value="-7.91266" />
                    <Key frame="30" value="22.2342" />
                    <Key frame="40" value="21.0037" />
                    <Key frame="60" value="23.201" />
                    <Key frame="80" value="23.201" />
                    <Key frame="100" value="23.201" />
                    <Key frame="120" value="20.4764" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="10" value="-4.39218" />
                    <Key frame="20" value="-4.39218" />
                    <Key frame="30" value="-7.29261" />
                    <Key frame="40" value="-8.08364" />
                    <Key frame="60" value="-4.56796" />
                    <Key frame="80" value="-6.4137" />
                    <Key frame="100" value="-7.02893" />
                    <Key frame="120" value="-7.02893" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.1777" />
                    <Key frame="20" value="-29.1777" />
                    <Key frame="30" value="-29.1777" />
                    <Key frame="40" value="-20.3006" />
                    <Key frame="60" value="-18.9822" />
                    <Key frame="80" value="-19.5975" />
                    <Key frame="100" value="-19.5975" />
                    <Key frame="120" value="-20.2127" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="10" value="99.1443" />
                    <Key frame="20" value="55.6379" />
                    <Key frame="30" value="-5.53478" />
                    <Key frame="40" value="-5.53478" />
                    <Key frame="60" value="-5.53478" />
                    <Key frame="80" value="-4.83163" />
                    <Key frame="100" value="-4.83163" />
                    <Key frame="120" value="-4.83163" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="101.429" />
                    <Key frame="20" value="103.012" />
                    <Key frame="30" value="107.23" />
                    <Key frame="40" value="108.549" />
                    <Key frame="60" value="108.549" />
                    <Key frame="80" value="108.549" />
                    <Key frame="100" value="108.549" />
                    <Key frame="120" value="108.549" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="-18.8991" />
                    <Key frame="20" value="-23.5574" />
                    <Key frame="30" value="-15.8229" />
                    <Key frame="40" value="-16.4382" />
                    <Key frame="60" value="-16.3503" />
                    <Key frame="80" value="-16.3503" />
                    <Key frame="100" value="-16.3503" />
                    <Key frame="120" value="-16.3503" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="10" value="11.7751" />
                    <Key frame="20" value="9.05044" />
                    <Key frame="30" value="6.4137" />
                    <Key frame="40" value="5.79845" />
                    <Key frame="60" value="4.39218" />
                    <Key frame="80" value="4.39218" />
                    <Key frame="100" value="4.39218" />
                    <Key frame="120" value="4.39218" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="FüßeKreisenLinks_Demo" id="30" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="868" y="1089">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="120">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="nao">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="10" value="9.66569" />
                    <Key frame="20" value="9.66569" />
                    <Key frame="30" value="9.66569" />
                    <Key frame="40" value="10.2809" />
                    <Key frame="60" value="10.9841" />
                    <Key frame="80" value="10.9841" />
                    <Key frame="100" value="10.9841" />
                    <Key frame="120" value="10.9841" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="10" value="-0.793436" />
                    <Key frame="20" value="-0.793436" />
                    <Key frame="30" value="-0.793436" />
                    <Key frame="40" value="-0.793436" />
                    <Key frame="60" value="-0.178186" />
                    <Key frame="80" value="-0.178186" />
                    <Key frame="100" value="-0.178186" />
                    <Key frame="120" value="-0.178186" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="49.3927" />
                    <Key frame="20" value="49.3927" />
                    <Key frame="30" value="49.3927" />
                    <Key frame="40" value="5.00743" />
                    <Key frame="60" value="-14.6803" />
                    <Key frame="80" value="-50.5401" />
                    <Key frame="100" value="-19.1628" />
                    <Key frame="120" value="3.95273" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-2.1949" />
                    <Key frame="20" value="-2.1949" />
                    <Key frame="30" value="-2.1949" />
                    <Key frame="40" value="4.83644" />
                    <Key frame="60" value="-21.8826" />
                    <Key frame="80" value="4.13332" />
                    <Key frame="100" value="18.7233" />
                    <Key frame="120" value="5.10013" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="-86.2194" />
                    <Key frame="20" value="-86.2194" />
                    <Key frame="30" value="-19.8611" />
                    <Key frame="40" value="-19.8611" />
                    <Key frame="60" value="-18.8064" />
                    <Key frame="80" value="-18.8064" />
                    <Key frame="100" value="-18.8064" />
                    <Key frame="120" value="-18.8064" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="-53.4406" />
                    <Key frame="20" value="-84.9937" />
                    <Key frame="30" value="-92.4645" />
                    <Key frame="40" value="-92.4645" />
                    <Key frame="60" value="-92.4645" />
                    <Key frame="80" value="-92.4645" />
                    <Key frame="100" value="-92.4645" />
                    <Key frame="120" value="-92.4645" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="10" value="0.2856" />
                    <Key frame="20" value="0.2856" />
                    <Key frame="30" value="0.2856" />
                    <Key frame="40" value="0.2856" />
                    <Key frame="60" value="0.3084" />
                    <Key frame="80" value="0.3084" />
                    <Key frame="100" value="0.3084" />
                    <Key frame="120" value="0.3084" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="10" value="21.1843" />
                    <Key frame="20" value="21.1843" />
                    <Key frame="30" value="21.1843" />
                    <Key frame="40" value="20.5691" />
                    <Key frame="60" value="23.2058" />
                    <Key frame="80" value="23.2058" />
                    <Key frame="100" value="23.2058" />
                    <Key frame="120" value="20.4812" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="10" value="6.41851" />
                    <Key frame="20" value="6.41851" />
                    <Key frame="30" value="6.41851" />
                    <Key frame="40" value="6.41851" />
                    <Key frame="60" value="4.66066" />
                    <Key frame="80" value="6.68218" />
                    <Key frame="100" value="7.29742" />
                    <Key frame="120" value="7.29742" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.1777" />
                    <Key frame="20" value="-29.1777" />
                    <Key frame="30" value="-29.1777" />
                    <Key frame="40" value="-20.3006" />
                    <Key frame="60" value="-18.9822" />
                    <Key frame="80" value="-19.5975" />
                    <Key frame="100" value="-19.5975" />
                    <Key frame="120" value="-20.2127" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="10" value="-5.62747" />
                    <Key frame="20" value="-5.62747" />
                    <Key frame="30" value="-5.62747" />
                    <Key frame="40" value="-5.62747" />
                    <Key frame="60" value="-4.92435" />
                    <Key frame="80" value="-4.92435" />
                    <Key frame="100" value="-4.92435" />
                    <Key frame="120" value="-5.53958" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="101.864" />
                    <Key frame="20" value="102.743" />
                    <Key frame="30" value="107.577" />
                    <Key frame="40" value="108.895" />
                    <Key frame="60" value="108.895" />
                    <Key frame="80" value="108.895" />
                    <Key frame="100" value="108.895" />
                    <Key frame="120" value="108.895" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="16.8728" />
                    <Key frame="20" value="18.4549" />
                    <Key frame="30" value="13.9724" />
                    <Key frame="40" value="13.9724" />
                    <Key frame="60" value="15.2029" />
                    <Key frame="80" value="15.2029" />
                    <Key frame="100" value="15.2029" />
                    <Key frame="120" value="15.2029" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="10" value="12.8298" />
                    <Key frame="20" value="12.8298" />
                    <Key frame="30" value="12.8298" />
                    <Key frame="40" value="12.8298" />
                    <Key frame="60" value="12.3903" />
                    <Key frame="80" value="12.3903" />
                    <Key frame="100" value="12.3903" />
                    <Key frame="120" value="12.3903" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="37.1806" />
                    <Key frame="20" value="37.1806" />
                    <Key frame="30" value="31.0282" />
                    <Key frame="40" value="1.49656" />
                    <Key frame="60" value="1.67234" />
                    <Key frame="80" value="1.67234" />
                    <Key frame="100" value="1.67234" />
                    <Key frame="120" value="1.67234" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-16.697" />
                    <Key frame="20" value="-15.9939" />
                    <Key frame="30" value="-16.6091" />
                    <Key frame="40" value="-6.4137" />
                    <Key frame="60" value="-5.88634" />
                    <Key frame="80" value="-5.88634" />
                    <Key frame="100" value="-5.88634" />
                    <Key frame="120" value="-5.88634" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="87.5426" />
                    <Key frame="20" value="84.2906" />
                    <Key frame="30" value="23.1179" />
                    <Key frame="40" value="23.1179" />
                    <Key frame="60" value="21.7996" />
                    <Key frame="80" value="21.1843" />
                    <Key frame="100" value="20.4812" />
                    <Key frame="120" value="19.8659" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="52.7326" />
                    <Key frame="20" value="81.7369" />
                    <Key frame="30" value="90.5261" />
                    <Key frame="40" value="90.5261" />
                    <Key frame="60" value="91.9323" />
                    <Key frame="80" value="91.9323" />
                    <Key frame="100" value="91.9323" />
                    <Key frame="120" value="91.9323" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="10" value="0.3452" />
                    <Key frame="20" value="0.3452" />
                    <Key frame="30" value="0.3452" />
                    <Key frame="40" value="0.3452" />
                    <Key frame="60" value="0.3336" />
                    <Key frame="80" value="0.3336" />
                    <Key frame="100" value="0.3336" />
                    <Key frame="120" value="0.3336" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="10" value="-22.5027" />
                    <Key frame="20" value="-7.91266" />
                    <Key frame="30" value="22.2342" />
                    <Key frame="40" value="21.0037" />
                    <Key frame="60" value="20.3006" />
                    <Key frame="80" value="20.3006" />
                    <Key frame="100" value="20.3006" />
                    <Key frame="120" value="20.9158" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="10" value="-4.39218" />
                    <Key frame="20" value="-4.39218" />
                    <Key frame="30" value="-7.29261" />
                    <Key frame="40" value="-8.08364" />
                    <Key frame="60" value="-8.17153" />
                    <Key frame="80" value="-8.17153" />
                    <Key frame="100" value="-8.17153" />
                    <Key frame="120" value="-8.17153" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.1777" />
                    <Key frame="20" value="-29.1777" />
                    <Key frame="30" value="-29.1777" />
                    <Key frame="40" value="-20.3006" />
                    <Key frame="60" value="-18.9822" />
                    <Key frame="80" value="-19.5975" />
                    <Key frame="100" value="-19.5975" />
                    <Key frame="120" value="-20.2127" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="10" value="99.1443" />
                    <Key frame="20" value="55.6379" />
                    <Key frame="30" value="-5.53478" />
                    <Key frame="40" value="-5.53478" />
                    <Key frame="60" value="-5.53478" />
                    <Key frame="80" value="-5.53478" />
                    <Key frame="100" value="-5.53478" />
                    <Key frame="120" value="-5.53478" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="101.429" />
                    <Key frame="20" value="103.012" />
                    <Key frame="30" value="107.23" />
                    <Key frame="40" value="108.549" />
                    <Key frame="60" value="108.549" />
                    <Key frame="80" value="108.549" />
                    <Key frame="100" value="108.549" />
                    <Key frame="120" value="108.549" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="-18.8991" />
                    <Key frame="20" value="-23.5574" />
                    <Key frame="30" value="-15.8229" />
                    <Key frame="40" value="-16.4382" />
                    <Key frame="60" value="-16.3503" />
                    <Key frame="80" value="-16.3503" />
                    <Key frame="100" value="-16.3503" />
                    <Key frame="120" value="-16.3503" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="10" value="11.7751" />
                    <Key frame="20" value="9.05044" />
                    <Key frame="30" value="6.4137" />
                    <Key frame="40" value="5.79845" />
                    <Key frame="60" value="4.39218" />
                    <Key frame="80" value="4.39218" />
                    <Key frame="100" value="4.39218" />
                    <Key frame="120" value="4.39218" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="HandFaustenLiegend_Demo" id="42" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="568" y="731">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="82">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="nao">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="10" value="9.75358" />
                    <Key frame="20" value="9.75358" />
                    <Key frame="30" value="9.75358" />
                    <Key frame="40" value="10.5446" />
                    <Key frame="45" value="10.5446" />
                    <Key frame="61" value="10.5446" />
                    <Key frame="67" value="10.5446" />
                    <Key frame="82" value="10.5446" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="10" value="-0.178186" />
                    <Key frame="20" value="-0.178186" />
                    <Key frame="30" value="-0.178186" />
                    <Key frame="40" value="-0.178186" />
                    <Key frame="45" value="-0.178186" />
                    <Key frame="61" value="-0.178186" />
                    <Key frame="67" value="-0.178186" />
                    <Key frame="82" value="-0.178186" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="49.217" />
                    <Key frame="20" value="49.217" />
                    <Key frame="30" value="49.217" />
                    <Key frame="40" value="5.18321" />
                    <Key frame="45" value="5.18321" />
                    <Key frame="61" value="5.18321" />
                    <Key frame="67" value="5.18321" />
                    <Key frame="82" value="5.18321" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-2.28279" />
                    <Key frame="20" value="-2.28279" />
                    <Key frame="30" value="-2.28279" />
                    <Key frame="40" value="4.83644" />
                    <Key frame="45" value="4.83644" />
                    <Key frame="61" value="4.83644" />
                    <Key frame="67" value="4.83644" />
                    <Key frame="82" value="4.83644" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="-85.0768" />
                    <Key frame="20" value="-85.8678" />
                    <Key frame="30" value="-20.0369" />
                    <Key frame="40" value="-19.4217" />
                    <Key frame="45" value="-18.8064" />
                    <Key frame="61" value="-18.8064" />
                    <Key frame="67" value="-18.8064" />
                    <Key frame="82" value="-18.8064" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="-53.7042" />
                    <Key frame="20" value="-84.9937" />
                    <Key frame="30" value="-92.4645" />
                    <Key frame="40" value="-92.4645" />
                    <Key frame="45" value="-93.0797" />
                    <Key frame="61" value="-93.0797" />
                    <Key frame="67" value="-93.0797" />
                    <Key frame="82" value="-93.0797" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="10" value="0.2788" />
                    <Key frame="20" value="0.2788" />
                    <Key frame="30" value="0.2788" />
                    <Key frame="40" value="0.2788" />
                    <Key frame="45" value="0.818" />
                    <Key frame="61" value="0.818" />
                    <Key frame="67" value="0.3056" />
                    <Key frame="82" value="0.3056" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="10" value="20.7449" />
                    <Key frame="20" value="20.7449" />
                    <Key frame="30" value="21.3601" />
                    <Key frame="40" value="20.7449" />
                    <Key frame="45" value="20.7449" />
                    <Key frame="61" value="20.7449" />
                    <Key frame="67" value="20.7449" />
                    <Key frame="82" value="20.7449" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="10" value="6.59429" />
                    <Key frame="20" value="6.59429" />
                    <Key frame="30" value="6.59429" />
                    <Key frame="40" value="6.59429" />
                    <Key frame="45" value="6.59429" />
                    <Key frame="61" value="6.59429" />
                    <Key frame="67" value="6.59429" />
                    <Key frame="82" value="6.59429" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.1777" />
                    <Key frame="20" value="-29.1777" />
                    <Key frame="30" value="-29.1777" />
                    <Key frame="40" value="-20.3885" />
                    <Key frame="45" value="-20.3885" />
                    <Key frame="61" value="-20.3885" />
                    <Key frame="67" value="-20.3885" />
                    <Key frame="82" value="-20.3885" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="10" value="-5.3638" />
                    <Key frame="20" value="-5.3638" />
                    <Key frame="30" value="-5.3638" />
                    <Key frame="40" value="-5.3638" />
                    <Key frame="45" value="-5.3638" />
                    <Key frame="61" value="-5.3638" />
                    <Key frame="67" value="-5.3638" />
                    <Key frame="82" value="-5.3638" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="101.952" />
                    <Key frame="20" value="102.655" />
                    <Key frame="30" value="108.104" />
                    <Key frame="40" value="108.72" />
                    <Key frame="45" value="108.72" />
                    <Key frame="61" value="108.72" />
                    <Key frame="67" value="108.72" />
                    <Key frame="82" value="108.72" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="16.4334" />
                    <Key frame="20" value="18.0154" />
                    <Key frame="30" value="13.9724" />
                    <Key frame="40" value="13.9724" />
                    <Key frame="45" value="13.9724" />
                    <Key frame="61" value="13.9724" />
                    <Key frame="67" value="13.9724" />
                    <Key frame="82" value="13.9724" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="10" value="12.5661" />
                    <Key frame="20" value="12.5661" />
                    <Key frame="30" value="12.5661" />
                    <Key frame="40" value="12.5661" />
                    <Key frame="45" value="5.44688" />
                    <Key frame="61" value="5.44688" />
                    <Key frame="67" value="11.1598" />
                    <Key frame="82" value="11.1598" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="10" value="37.4443" />
                    <Key frame="20" value="37.4443" />
                    <Key frame="30" value="30.9403" />
                    <Key frame="40" value="1.49656" />
                    <Key frame="45" value="1.49656" />
                    <Key frame="61" value="1.49656" />
                    <Key frame="67" value="1.49656" />
                    <Key frame="82" value="1.49656" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="10" value="-16.6091" />
                    <Key frame="20" value="-16.6091" />
                    <Key frame="30" value="-16.6091" />
                    <Key frame="40" value="-6.67737" />
                    <Key frame="45" value="-6.67737" />
                    <Key frame="61" value="-6.67737" />
                    <Key frame="67" value="-6.67737" />
                    <Key frame="82" value="-6.67737" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="10" value="86.8394" />
                    <Key frame="20" value="84.3785" />
                    <Key frame="30" value="23.2058" />
                    <Key frame="40" value="23.2058" />
                    <Key frame="45" value="19.2507" />
                    <Key frame="61" value="19.2507" />
                    <Key frame="67" value="21.0085" />
                    <Key frame="82" value="21.0085" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="10" value="52.9084" />
                    <Key frame="20" value="81.649" />
                    <Key frame="30" value="90.5261" />
                    <Key frame="40" value="90.5261" />
                    <Key frame="45" value="91.8445" />
                    <Key frame="61" value="91.8445" />
                    <Key frame="67" value="91.8445" />
                    <Key frame="82" value="91.8445" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="10" value="0.3448" />
                    <Key frame="20" value="0.3448" />
                    <Key frame="30" value="0.3448" />
                    <Key frame="40" value="0.3448" />
                    <Key frame="45" value="0.8156" />
                    <Key frame="61" value="0.8156" />
                    <Key frame="67" value="0.3588" />
                    <Key frame="82" value="0.3588" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="10" value="-22.8542" />
                    <Key frame="20" value="-7.91266" />
                    <Key frame="30" value="22.2342" />
                    <Key frame="40" value="20.74" />
                    <Key frame="45" value="20.74" />
                    <Key frame="61" value="20.74" />
                    <Key frame="67" value="20.74" />
                    <Key frame="82" value="20.74" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="10" value="-4.65585" />
                    <Key frame="20" value="-4.65585" />
                    <Key frame="30" value="-7.20472" />
                    <Key frame="40" value="-7.99575" />
                    <Key frame="45" value="-7.99575" />
                    <Key frame="61" value="-7.99575" />
                    <Key frame="67" value="-7.99575" />
                    <Key frame="82" value="-7.99575" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="10" value="-29.1777" />
                    <Key frame="20" value="-29.1777" />
                    <Key frame="30" value="-29.1777" />
                    <Key frame="40" value="-20.3885" />
                    <Key frame="45" value="-20.3885" />
                    <Key frame="61" value="-20.3885" />
                    <Key frame="67" value="-20.3885" />
                    <Key frame="82" value="-20.3885" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="10" value="99.2322" />
                    <Key frame="20" value="55.55" />
                    <Key frame="30" value="-5.53478" />
                    <Key frame="40" value="-5.53478" />
                    <Key frame="45" value="-5.53478" />
                    <Key frame="61" value="-5.53478" />
                    <Key frame="67" value="-5.53478" />
                    <Key frame="82" value="-5.53478" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="10" value="101.517" />
                    <Key frame="20" value="103.627" />
                    <Key frame="30" value="107.23" />
                    <Key frame="40" value="108.549" />
                    <Key frame="45" value="108.549" />
                    <Key frame="61" value="108.549" />
                    <Key frame="67" value="108.549" />
                    <Key frame="82" value="108.549" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="10" value="-18.8112" />
                    <Key frame="20" value="-23.4695" />
                    <Key frame="30" value="-16.6139" />
                    <Key frame="40" value="-16.6139" />
                    <Key frame="45" value="-15.0319" />
                    <Key frame="61" value="-15.0319" />
                    <Key frame="67" value="-15.0319" />
                    <Key frame="82" value="-15.0319" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="10" value="10.8962" />
                    <Key frame="20" value="8.87466" />
                    <Key frame="30" value="6.32579" />
                    <Key frame="40" value="6.32579" />
                    <Key frame="45" value="6.32579" />
                    <Key frame="61" value="6.32579" />
                    <Key frame="67" value="6.32579" />
                    <Key frame="82" value="6.32579" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="WolleWickelnLiegend_Init" id="20" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="719" y="720">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="80">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="nao">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="40" value="9.92936" />
                    <Key frame="44" value="9.92936" />
                    <Key frame="48" value="9.92936" />
                    <Key frame="52" value="9.92936" />
                    <Key frame="56" value="9.92936" />
                    <Key frame="60" value="9.92936" />
                    <Key frame="64" value="9.92936" />
                    <Key frame="68" value="9.92936" />
                    <Key frame="72" value="9.92936" />
                    <Key frame="76" value="9.92936" />
                    <Key frame="80" value="9.92936" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="40" value="-0.178186" />
                    <Key frame="44" value="-0.178186" />
                    <Key frame="48" value="-0.178186" />
                    <Key frame="52" value="-0.178186" />
                    <Key frame="56" value="-0.178186" />
                    <Key frame="60" value="-0.178186" />
                    <Key frame="64" value="-0.178186" />
                    <Key frame="68" value="-0.178186" />
                    <Key frame="72" value="-0.178186" />
                    <Key frame="76" value="-0.178186" />
                    <Key frame="80" value="-0.178186" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="40" value="48.8654" />
                    <Key frame="44" value="48.8654" />
                    <Key frame="48" value="48.8654" />
                    <Key frame="52" value="48.8654" />
                    <Key frame="56" value="48.8654" />
                    <Key frame="60" value="48.8654" />
                    <Key frame="64" value="48.8654" />
                    <Key frame="68" value="48.8654" />
                    <Key frame="72" value="48.8654" />
                    <Key frame="76" value="48.8654" />
                    <Key frame="80" value="48.8654" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="40" value="-1.84332" />
                    <Key frame="44" value="-1.84332" />
                    <Key frame="48" value="-1.84332" />
                    <Key frame="52" value="-1.84332" />
                    <Key frame="56" value="-1.84332" />
                    <Key frame="60" value="-1.84332" />
                    <Key frame="64" value="-1.84332" />
                    <Key frame="68" value="-1.84332" />
                    <Key frame="72" value="-1.84332" />
                    <Key frame="76" value="-1.84332" />
                    <Key frame="80" value="-1.84332" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="40" value="-87.8893" />
                    <Key frame="44" value="-86.5709" />
                    <Key frame="48" value="-72.1567" />
                    <Key frame="52" value="-62.9281" />
                    <Key frame="56" value="-59.764" />
                    <Key frame="60" value="-63.7191" />
                    <Key frame="64" value="-72.1567" />
                    <Key frame="68" value="-78.9244" />
                    <Key frame="72" value="-86.5709" />
                    <Key frame="76" value="-87.2741" />
                    <Key frame="80" value="-87.9772" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="40" value="-22.239" />
                    <Key frame="44" value="-17.0534" />
                    <Key frame="48" value="-14.5045" />
                    <Key frame="52" value="-15.1198" />
                    <Key frame="56" value="-26.1063" />
                    <Key frame="60" value="-35.247" />
                    <Key frame="64" value="-41.9268" />
                    <Key frame="68" value="-42.6299" />
                    <Key frame="72" value="-42.6299" />
                    <Key frame="76" value="-36.038" />
                    <Key frame="80" value="-28.0399" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="40" value="0.304" />
                    <Key frame="44" value="0.304" />
                    <Key frame="48" value="0.304" />
                    <Key frame="52" value="0.304" />
                    <Key frame="56" value="0.304" />
                    <Key frame="60" value="0.304" />
                    <Key frame="64" value="0.304" />
                    <Key frame="68" value="0.304" />
                    <Key frame="72" value="0.304" />
                    <Key frame="76" value="0.304" />
                    <Key frame="80" value="0.304" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="40" value="21.5359" />
                    <Key frame="44" value="21.5359" />
                    <Key frame="48" value="21.5359" />
                    <Key frame="52" value="21.5359" />
                    <Key frame="56" value="21.5359" />
                    <Key frame="60" value="21.5359" />
                    <Key frame="64" value="21.5359" />
                    <Key frame="68" value="21.5359" />
                    <Key frame="72" value="21.5359" />
                    <Key frame="76" value="21.5359" />
                    <Key frame="80" value="21.5359" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="40" value="6.59429" />
                    <Key frame="44" value="6.59429" />
                    <Key frame="48" value="6.59429" />
                    <Key frame="52" value="6.59429" />
                    <Key frame="56" value="6.59429" />
                    <Key frame="60" value="6.59429" />
                    <Key frame="64" value="6.59429" />
                    <Key frame="68" value="6.59429" />
                    <Key frame="72" value="6.59429" />
                    <Key frame="76" value="6.59429" />
                    <Key frame="80" value="6.59429" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="40" value="-29.705" />
                    <Key frame="44" value="-29.705" />
                    <Key frame="48" value="-29.705" />
                    <Key frame="52" value="-29.705" />
                    <Key frame="56" value="-29.705" />
                    <Key frame="60" value="-29.705" />
                    <Key frame="64" value="-29.705" />
                    <Key frame="68" value="-29.705" />
                    <Key frame="72" value="-29.705" />
                    <Key frame="76" value="-29.705" />
                    <Key frame="80" value="-29.705" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="40" value="-5.3638" />
                    <Key frame="44" value="-5.3638" />
                    <Key frame="48" value="-5.3638" />
                    <Key frame="52" value="-5.3638" />
                    <Key frame="56" value="-5.3638" />
                    <Key frame="60" value="-5.3638" />
                    <Key frame="64" value="-5.3638" />
                    <Key frame="68" value="-5.3638" />
                    <Key frame="72" value="-5.3638" />
                    <Key frame="76" value="-5.3638" />
                    <Key frame="80" value="-5.3638" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="40" value="49.217" />
                    <Key frame="44" value="48.338" />
                    <Key frame="48" value="47.7228" />
                    <Key frame="52" value="47.7228" />
                    <Key frame="56" value="42.0977" />
                    <Key frame="60" value="39.8125" />
                    <Key frame="64" value="39.8125" />
                    <Key frame="68" value="39.8125" />
                    <Key frame="72" value="39.1973" />
                    <Key frame="76" value="39.1973" />
                    <Key frame="80" value="44.7345" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="40" value="16.1697" />
                    <Key frame="44" value="11.2477" />
                    <Key frame="48" value="7.99575" />
                    <Key frame="52" value="10.1051" />
                    <Key frame="56" value="12.8298" />
                    <Key frame="60" value="14.4118" />
                    <Key frame="64" value="15.0271" />
                    <Key frame="68" value="14.0603" />
                    <Key frame="72" value="10.8962" />
                    <Key frame="76" value="10.8962" />
                    <Key frame="80" value="10.8962" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="40" value="-50.8917" />
                    <Key frame="44" value="-49.9249" />
                    <Key frame="48" value="-49.9249" />
                    <Key frame="52" value="-52.8253" />
                    <Key frame="56" value="-55.7258" />
                    <Key frame="60" value="-56.4289" />
                    <Key frame="64" value="-57.0441" />
                    <Key frame="68" value="-57.0441" />
                    <Key frame="72" value="-52.7374" />
                    <Key frame="76" value="-50.5401" />
                    <Key frame="80" value="-50.5401" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="40" value="36.2138" />
                    <Key frame="44" value="36.2138" />
                    <Key frame="48" value="36.2138" />
                    <Key frame="52" value="36.2138" />
                    <Key frame="56" value="36.2138" />
                    <Key frame="60" value="36.2138" />
                    <Key frame="64" value="36.2138" />
                    <Key frame="68" value="36.2138" />
                    <Key frame="72" value="36.2138" />
                    <Key frame="76" value="36.2138" />
                    <Key frame="80" value="36.2138" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="40" value="-15.6423" />
                    <Key frame="44" value="-15.6423" />
                    <Key frame="48" value="-15.6423" />
                    <Key frame="52" value="-15.6423" />
                    <Key frame="56" value="-15.6423" />
                    <Key frame="60" value="-15.6423" />
                    <Key frame="64" value="-15.6423" />
                    <Key frame="68" value="-15.6423" />
                    <Key frame="72" value="-15.6423" />
                    <Key frame="76" value="-15.6423" />
                    <Key frame="80" value="-15.6423" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="40" value="54.5832" />
                    <Key frame="44" value="76.4682" />
                    <Key frame="48" value="84.8179" />
                    <Key frame="52" value="88.4215" />
                    <Key frame="56" value="88.4215" />
                    <Key frame="60" value="88.4215" />
                    <Key frame="64" value="82.6206" />
                    <Key frame="68" value="66.3607" />
                    <Key frame="72" value="55.6379" />
                    <Key frame="76" value="52.0343" />
                    <Key frame="80" value="52.0343" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="40" value="21.0037" />
                    <Key frame="44" value="30.5839" />
                    <Key frame="48" value="32.166" />
                    <Key frame="52" value="31.3749" />
                    <Key frame="56" value="24.1678" />
                    <Key frame="60" value="22.1463" />
                    <Key frame="64" value="16.8728" />
                    <Key frame="68" value="16.0818" />
                    <Key frame="72" value="16.0818" />
                    <Key frame="76" value="18.1033" />
                    <Key frame="80" value="19.3338" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="40" value="0.3104" />
                    <Key frame="44" value="0.3104" />
                    <Key frame="48" value="0.3104" />
                    <Key frame="52" value="0.3104" />
                    <Key frame="56" value="0.3104" />
                    <Key frame="60" value="0.3104" />
                    <Key frame="64" value="0.3104" />
                    <Key frame="68" value="0.3104" />
                    <Key frame="72" value="0.3104" />
                    <Key frame="76" value="0.3104" />
                    <Key frame="80" value="0.3104" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="40" value="24.1678" />
                    <Key frame="44" value="24.1678" />
                    <Key frame="48" value="24.1678" />
                    <Key frame="52" value="24.1678" />
                    <Key frame="56" value="24.1678" />
                    <Key frame="60" value="24.1678" />
                    <Key frame="64" value="24.1678" />
                    <Key frame="68" value="24.1678" />
                    <Key frame="72" value="24.1678" />
                    <Key frame="76" value="24.1678" />
                    <Key frame="80" value="24.1678" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="40" value="-4.04062" />
                    <Key frame="44" value="-4.04062" />
                    <Key frame="48" value="-4.04062" />
                    <Key frame="52" value="-4.04062" />
                    <Key frame="56" value="-4.04062" />
                    <Key frame="60" value="-4.04062" />
                    <Key frame="64" value="-4.04062" />
                    <Key frame="68" value="-4.04062" />
                    <Key frame="72" value="-4.04062" />
                    <Key frame="76" value="-4.04062" />
                    <Key frame="80" value="-4.04062" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="40" value="-29.705" />
                    <Key frame="44" value="-29.705" />
                    <Key frame="48" value="-29.705" />
                    <Key frame="52" value="-29.705" />
                    <Key frame="56" value="-29.705" />
                    <Key frame="60" value="-29.705" />
                    <Key frame="64" value="-29.705" />
                    <Key frame="68" value="-29.705" />
                    <Key frame="72" value="-29.705" />
                    <Key frame="76" value="-29.705" />
                    <Key frame="80" value="-29.705" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="40" value="-5.53478" />
                    <Key frame="44" value="-5.53478" />
                    <Key frame="48" value="-5.53478" />
                    <Key frame="52" value="-5.53478" />
                    <Key frame="56" value="-5.53478" />
                    <Key frame="60" value="-5.53478" />
                    <Key frame="64" value="-5.53478" />
                    <Key frame="68" value="-5.53478" />
                    <Key frame="72" value="-5.53478" />
                    <Key frame="76" value="-5.53478" />
                    <Key frame="80" value="-5.53478" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="40" value="26.8094" />
                    <Key frame="44" value="35.1591" />
                    <Key frame="48" value="35.1591" />
                    <Key frame="52" value="38.2353" />
                    <Key frame="56" value="42.8936" />
                    <Key frame="60" value="46.9366" />
                    <Key frame="64" value="47.5518" />
                    <Key frame="68" value="48.1671" />
                    <Key frame="72" value="48.1671" />
                    <Key frame="76" value="41.1357" />
                    <Key frame="80" value="33.1376" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="40" value="-10.0221" />
                    <Key frame="44" value="-18.987" />
                    <Key frame="48" value="-17.3171" />
                    <Key frame="52" value="-12.3951" />
                    <Key frame="56" value="-10.5494" />
                    <Key frame="60" value="-12.3073" />
                    <Key frame="64" value="-12.3073" />
                    <Key frame="68" value="-7.4732" />
                    <Key frame="72" value="-6.3306" />
                    <Key frame="76" value="-6.94585" />
                    <Key frame="80" value="-9.40682" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="40" value="65.6527" />
                    <Key frame="44" value="76.3755" />
                    <Key frame="48" value="74.7056" />
                    <Key frame="52" value="65.6527" />
                    <Key frame="56" value="63.1917" />
                    <Key frame="60" value="63.1917" />
                    <Key frame="64" value="63.1917" />
                    <Key frame="68" value="68.5531" />
                    <Key frame="72" value="69.6957" />
                    <Key frame="76" value="69.6957" />
                    <Key frame="80" value="69.6957" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="HipRaise_ArmsUp Init" id="73" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="882" y="732">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="130">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="20" value="9.92936" />
                    <Key frame="40" value="19.158" />
                    <Key frame="100" value="30.496" />
                    <Key frame="130" value="21.8826" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="20" value="-0.793436" />
                    <Key frame="40" value="-0.178186" />
                    <Key frame="100" value="-0.178186" />
                    <Key frame="130" value="-1.58445" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="20" value="49.4806" />
                    <Key frame="40" value="31.7265" />
                    <Key frame="100" value="18.7185" />
                    <Key frame="130" value="31.1113" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="20" value="-1.84332" />
                    <Key frame="40" value="0.266077" />
                    <Key frame="100" value="6.94585" />
                    <Key frame="130" value="0.705531" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="20" value="-85.7799" />
                    <Key frame="40" value="-81.649" />
                    <Key frame="100" value="-11.863" />
                    <Key frame="130" value="-57.8304" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="20" value="-55.8137" />
                    <Key frame="40" value="-94.5739" />
                    <Key frame="100" value="-62.845" />
                    <Key frame="130" value="-66.7122" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="20" value="0.3764" />
                    <Key frame="40" value="0.3344" />
                    <Key frame="100" value="0.3344" />
                    <Key frame="130" value="0.3344" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="20" value="21.5359" />
                    <Key frame="40" value="-31.1992" />
                    <Key frame="100" value="10.7252" />
                    <Key frame="130" value="-30.9355" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="20" value="6.59429" />
                    <Key frame="40" value="13.8014" />
                    <Key frame="100" value="13.0983" />
                    <Key frame="130" value="13.8893" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="20" value="-29.0019" />
                    <Key frame="40" value="-1.66754" />
                    <Key frame="100" value="0.441859" />
                    <Key frame="130" value="-1.75543" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="20" value="-5.3638" />
                    <Key frame="40" value="98.4363" />
                    <Key frame="100" value="83.4069" />
                    <Key frame="130" value="98.3484" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="20" value="107.841" />
                    <Key frame="40" value="120.146" />
                    <Key frame="100" value="25.5741" />
                    <Key frame="130" value="43.0645" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="20" value="20.2127" />
                    <Key frame="40" value="26.7167" />
                    <Key frame="100" value="5.97423" />
                    <Key frame="130" value="18.1912" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="20" value="10.9841" />
                    <Key frame="40" value="-15.3835" />
                    <Key frame="100" value="-27.0731" />
                    <Key frame="130" value="-39.9931" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="20" value="37.4443" />
                    <Key frame="40" value="33.1376" />
                    <Key frame="100" value="18.7233" />
                    <Key frame="130" value="32.1708" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="20" value="-16.2576" />
                    <Key frame="40" value="-5.18321" />
                    <Key frame="100" value="-2.72224" />
                    <Key frame="130" value="-4.30429" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="20" value="87.5426" />
                    <Key frame="40" value="82.1812" />
                    <Key frame="100" value="13.4498" />
                    <Key frame="130" value="51.507" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="20" value="54.3147" />
                    <Key frame="40" value="88.944" />
                    <Key frame="100" value="52.9963" />
                    <Key frame="130" value="75.5845" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="20" value="0.3448" />
                    <Key frame="40" value="0.3552" />
                    <Key frame="100" value="0.3552" />
                    <Key frame="130" value="0.3552" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="20" value="-22.239" />
                    <Key frame="40" value="-37.708" />
                    <Key frame="100" value="13.445" />
                    <Key frame="130" value="-37.3564" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="20" value="-4.56796" />
                    <Key frame="40" value="-14.4997" />
                    <Key frame="100" value="-15.115" />
                    <Key frame="130" value="-15.115" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="20" value="-29.0019" />
                    <Key frame="40" value="-1.66754" />
                    <Key frame="100" value="0.441859" />
                    <Key frame="130" value="-1.75543" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="20" value="99.9353" />
                    <Key frame="40" value="101.342" />
                    <Key frame="100" value="81.2144" />
                    <Key frame="130" value="102.045" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="20" value="101.078" />
                    <Key frame="40" value="112.24" />
                    <Key frame="100" value="25.4031" />
                    <Key frame="130" value="41.4873" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="20" value="-21.6238" />
                    <Key frame="40" value="-33.8407" />
                    <Key frame="100" value="-9.93417" />
                    <Key frame="130" value="-9.31893" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="20" value="12.8298" />
                    <Key frame="40" value="10.7204" />
                    <Key frame="100" value="32.6933" />
                    <Key frame="130" value="10.193" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Link inputowner="15" indexofinput="2" outputowner="14" indexofoutput="4" />
            <Link inputowner="16" indexofinput="2" outputowner="15" indexofoutput="4" />
            <Link inputowner="11" indexofinput="2" outputowner="10" indexofoutput="4" />
            <Link inputowner="12" indexofinput="2" outputowner="11" indexofoutput="4" />
            <Link inputowner="13" indexofinput="2" outputowner="12" indexofoutput="4" />
            <Link inputowner="8" indexofinput="2" outputowner="13" indexofoutput="4" />
            <Link inputowner="9" indexofinput="2" outputowner="7" indexofoutput="4" />
            <Link inputowner="7" indexofinput="2" outputowner="6" indexofoutput="4" />
            <Link inputowner="3" indexofinput="2" outputowner="0" indexofoutput="2" />
            <Link inputowner="17" indexofinput="2" outputowner="45" indexofoutput="4" />
            <Link inputowner="18" indexofinput="2" outputowner="17" indexofoutput="4" />
            <Link inputowner="21" indexofinput="2" outputowner="19" indexofoutput="4" />
            <Link inputowner="22" indexofinput="2" outputowner="21" indexofoutput="4" />
            <Link inputowner="25" indexofinput="2" outputowner="23" indexofoutput="4" />
            <Link inputowner="26" indexofinput="2" outputowner="25" indexofoutput="4" />
            <Link inputowner="29" indexofinput="2" outputowner="27" indexofoutput="4" />
            <Link inputowner="30" indexofinput="2" outputowner="29" indexofoutput="4" />
          </Diagram>
        </BehaviorKeyframe>
      </BehaviorLayer>
    </Timeline>
  </Box>
</ChoregrapheProject>
