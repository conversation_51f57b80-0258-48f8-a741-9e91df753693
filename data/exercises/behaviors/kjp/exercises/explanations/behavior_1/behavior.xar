<?xml version="1.0" encoding="UTF-8" ?>
<ChoregrapheProject xmlns="http://www.ald.softbankrobotics.com/schema/choregraphe/project.xsd" xar_version="3">
  <Box name="root" id="-1" localization="8" tooltip="Root box of Choregraphe&apos;s behavior. Highest level possible." x="0" y="0">
    <bitmap>media/images/box/root.png</bitmap>
    <script language="4">
      <content>
        <![CDATA[]]>
      </content>
    </script>
    <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
    <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
    <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
    <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
    <Timeline enable="0">
      <BehaviorLayer name="behavior_layer1">
        <BehaviorKeyframe name="keyframe1" index="1">
          <Diagram scale="70.7107">
            <Box name="DehnenNacken" id="1" localization="8" tooltip="Stand&#x0A;Vorne&#x0A;Halten&#x0A;Links&#x0A;Halten&#x0A;Rechts&#x0A;Halten&#x0A;Vorne&#x0A;Halten&#x0A;Stand" x="1380" y="56">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="179">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="37" value="-8.79157" />
                    <Key frame="39" value="28.6503" />
                    <Key frame="73" value="28.6503" />
                    <Key frame="75" value="16.697" />
                    <Key frame="110" value="16.697" />
                    <Key frame="112" value="14.3239" />
                    <Key frame="150" value="14.3239" />
                    <Key frame="152" value="28.6503" />
                    <Key frame="177" value="28.6503" />
                    <Key frame="179" value="-8.79157" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="37" value="-1.05711" />
                    <Key frame="39" value="-1.67234" />
                    <Key frame="73" value="-1.67234" />
                    <Key frame="75" value="86.1315" />
                    <Key frame="110" value="86.1315" />
                    <Key frame="112" value="-91.1461" />
                    <Key frame="150" value="-91.1461" />
                    <Key frame="152" value="-1.67234" />
                    <Key frame="177" value="-1.67234" />
                    <Key frame="179" value="-1.05711" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="37" value="5.71056" />
                    <Key frame="39" value="5.35899" />
                    <Key frame="73" value="5.35899" />
                    <Key frame="75" value="4.74374" />
                    <Key frame="110" value="4.74374" />
                    <Key frame="112" value="4.74374" />
                    <Key frame="150" value="4.74374" />
                    <Key frame="152" value="5.35899" />
                    <Key frame="177" value="5.35899" />
                    <Key frame="179" value="5.71056" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="37" value="-6.06212" />
                    <Key frame="39" value="-6.2379" />
                    <Key frame="73" value="-6.2379" />
                    <Key frame="75" value="-6.2379" />
                    <Key frame="110" value="-6.2379" />
                    <Key frame="112" value="-6.2379" />
                    <Key frame="150" value="-6.2379" />
                    <Key frame="152" value="-6.2379" />
                    <Key frame="177" value="-6.2379" />
                    <Key frame="179" value="-6.06212" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="37" value="-22.6737" />
                    <Key frame="39" value="-23.3768" />
                    <Key frame="73" value="-23.3768" />
                    <Key frame="75" value="-23.3768" />
                    <Key frame="110" value="-23.3768" />
                    <Key frame="112" value="-23.3768" />
                    <Key frame="150" value="-23.3768" />
                    <Key frame="152" value="-23.3768" />
                    <Key frame="177" value="-23.3768" />
                    <Key frame="179" value="-22.6737" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="37" value="-66.9759" />
                    <Key frame="39" value="-67.8548" />
                    <Key frame="73" value="-67.8548" />
                    <Key frame="75" value="-67.8548" />
                    <Key frame="110" value="-67.8548" />
                    <Key frame="112" value="-67.8548" />
                    <Key frame="150" value="-67.8548" />
                    <Key frame="152" value="-67.8548" />
                    <Key frame="177" value="-67.8548" />
                    <Key frame="179" value="-66.9759" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="37" value="0.3192" />
                    <Key frame="39" value="0.3124" />
                    <Key frame="73" value="0.3124" />
                    <Key frame="75" value="0.3124" />
                    <Key frame="110" value="0.3124" />
                    <Key frame="112" value="0.3124" />
                    <Key frame="150" value="0.3124" />
                    <Key frame="152" value="0.3124" />
                    <Key frame="177" value="0.3124" />
                    <Key frame="179" value="0.3192" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="37" value="8.00056" />
                    <Key frame="39" value="7.12163" />
                    <Key frame="73" value="7.12163" />
                    <Key frame="75" value="7.12163" />
                    <Key frame="110" value="7.12163" />
                    <Key frame="112" value="7.12163" />
                    <Key frame="150" value="7.12163" />
                    <Key frame="152" value="7.12163" />
                    <Key frame="177" value="7.12163" />
                    <Key frame="179" value="8.00056" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="37" value="6.24271" />
                    <Key frame="39" value="6.77007" />
                    <Key frame="73" value="6.77007" />
                    <Key frame="75" value="6.77007" />
                    <Key frame="110" value="6.77007" />
                    <Key frame="112" value="6.77007" />
                    <Key frame="150" value="6.77007" />
                    <Key frame="152" value="6.77007" />
                    <Key frame="177" value="6.77007" />
                    <Key frame="179" value="6.24271" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="37" value="-10.193" />
                    <Key frame="39" value="-10.193" />
                    <Key frame="73" value="-10.193" />
                    <Key frame="75" value="-9.5778" />
                    <Key frame="110" value="-9.5778" />
                    <Key frame="112" value="-9.5778" />
                    <Key frame="150" value="-9.5778" />
                    <Key frame="152" value="-10.193" />
                    <Key frame="177" value="-10.193" />
                    <Key frame="179" value="-10.193" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="37" value="-5.18802" />
                    <Key frame="39" value="-5.27591" />
                    <Key frame="73" value="-5.27591" />
                    <Key frame="75" value="-5.18802" />
                    <Key frame="110" value="-5.18802" />
                    <Key frame="112" value="-5.18802" />
                    <Key frame="150" value="-5.18802" />
                    <Key frame="152" value="-5.27591" />
                    <Key frame="177" value="-5.27591" />
                    <Key frame="179" value="-5.18802" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="37" value="83.2311" />
                    <Key frame="39" value="83.1432" />
                    <Key frame="73" value="83.1432" />
                    <Key frame="75" value="83.1432" />
                    <Key frame="110" value="83.1432" />
                    <Key frame="112" value="83.1432" />
                    <Key frame="150" value="83.1432" />
                    <Key frame="152" value="83.1432" />
                    <Key frame="177" value="83.1432" />
                    <Key frame="179" value="83.2311" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="37" value="11.3356" />
                    <Key frame="39" value="11.863" />
                    <Key frame="73" value="11.863" />
                    <Key frame="75" value="11.863" />
                    <Key frame="110" value="11.863" />
                    <Key frame="112" value="11.863" />
                    <Key frame="150" value="11.863" />
                    <Key frame="152" value="11.863" />
                    <Key frame="177" value="11.863" />
                    <Key frame="179" value="11.3356" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="37" value="4.39218" />
                    <Key frame="39" value="4.56796" />
                    <Key frame="73" value="4.56796" />
                    <Key frame="75" value="4.56796" />
                    <Key frame="110" value="4.56796" />
                    <Key frame="112" value="4.56796" />
                    <Key frame="150" value="4.56796" />
                    <Key frame="152" value="4.56796" />
                    <Key frame="177" value="4.56796" />
                    <Key frame="179" value="4.39218" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="37" value="5.62747" />
                    <Key frame="39" value="4.83644" />
                    <Key frame="73" value="4.83644" />
                    <Key frame="75" value="4.83644" />
                    <Key frame="110" value="4.83644" />
                    <Key frame="112" value="4.83644" />
                    <Key frame="150" value="4.83644" />
                    <Key frame="152" value="4.83644" />
                    <Key frame="177" value="4.83644" />
                    <Key frame="179" value="5.62747" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="37" value="6.77007" />
                    <Key frame="39" value="5.89115" />
                    <Key frame="73" value="5.89115" />
                    <Key frame="75" value="6.5064" />
                    <Key frame="110" value="6.5064" />
                    <Key frame="112" value="6.5064" />
                    <Key frame="150" value="6.5064" />
                    <Key frame="152" value="5.89115" />
                    <Key frame="177" value="5.89115" />
                    <Key frame="179" value="6.77007" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="37" value="22.7664" />
                    <Key frame="39" value="21.7996" />
                    <Key frame="73" value="21.7996" />
                    <Key frame="75" value="19.3386" />
                    <Key frame="110" value="19.3386" />
                    <Key frame="112" value="19.3386" />
                    <Key frame="150" value="19.3386" />
                    <Key frame="152" value="21.7996" />
                    <Key frame="177" value="21.7996" />
                    <Key frame="179" value="22.7664" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="37" value="68.0258" />
                    <Key frame="39" value="68.2016" />
                    <Key frame="73" value="68.2016" />
                    <Key frame="75" value="68.2016" />
                    <Key frame="110" value="68.2016" />
                    <Key frame="112" value="68.2016" />
                    <Key frame="150" value="68.2016" />
                    <Key frame="152" value="68.2016" />
                    <Key frame="177" value="68.2016" />
                    <Key frame="179" value="68.0258" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="37" value="0.318" />
                    <Key frame="39" value="0.3932" />
                    <Key frame="73" value="0.3932" />
                    <Key frame="75" value="0.4956" />
                    <Key frame="110" value="0.4956" />
                    <Key frame="112" value="0.4956" />
                    <Key frame="150" value="0.4956" />
                    <Key frame="152" value="0.3932" />
                    <Key frame="177" value="0.3932" />
                    <Key frame="179" value="0.318" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="37" value="7.11683" />
                    <Key frame="39" value="7.20472" />
                    <Key frame="73" value="7.20472" />
                    <Key frame="75" value="7.11683" />
                    <Key frame="110" value="7.11683" />
                    <Key frame="112" value="7.11683" />
                    <Key frame="150" value="7.11683" />
                    <Key frame="152" value="7.20472" />
                    <Key frame="177" value="7.20472" />
                    <Key frame="179" value="7.11683" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="37" value="-6.85315" />
                    <Key frame="39" value="-6.58948" />
                    <Key frame="73" value="-6.58948" />
                    <Key frame="75" value="-6.58948" />
                    <Key frame="110" value="-6.58948" />
                    <Key frame="112" value="-6.58948" />
                    <Key frame="150" value="-6.58948" />
                    <Key frame="152" value="-6.58948" />
                    <Key frame="177" value="-6.58948" />
                    <Key frame="179" value="-6.85315" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="37" value="-10.193" />
                    <Key frame="39" value="-10.193" />
                    <Key frame="73" value="-10.193" />
                    <Key frame="75" value="-9.5778" />
                    <Key frame="110" value="-9.5778" />
                    <Key frame="112" value="-9.5778" />
                    <Key frame="150" value="-9.5778" />
                    <Key frame="152" value="-10.193" />
                    <Key frame="177" value="-10.193" />
                    <Key frame="179" value="-10.193" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="37" value="-5.2711" />
                    <Key frame="39" value="-5.88634" />
                    <Key frame="73" value="-5.88634" />
                    <Key frame="75" value="-5.2711" />
                    <Key frame="110" value="-5.2711" />
                    <Key frame="112" value="-5.2711" />
                    <Key frame="150" value="-5.2711" />
                    <Key frame="152" value="-5.88634" />
                    <Key frame="177" value="-5.88634" />
                    <Key frame="179" value="-5.2711" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="37" value="82.2691" />
                    <Key frame="39" value="82.4448" />
                    <Key frame="73" value="82.4448" />
                    <Key frame="75" value="83.148" />
                    <Key frame="110" value="83.148" />
                    <Key frame="112" value="83.148" />
                    <Key frame="150" value="83.148" />
                    <Key frame="152" value="82.4448" />
                    <Key frame="177" value="82.4448" />
                    <Key frame="179" value="82.2691" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="37" value="-11.5162" />
                    <Key frame="39" value="-5.80326" />
                    <Key frame="73" value="-5.80326" />
                    <Key frame="75" value="-1.67234" />
                    <Key frame="110" value="-1.67234" />
                    <Key frame="112" value="-1.05711" />
                    <Key frame="150" value="-1.05711" />
                    <Key frame="152" value="-5.80326" />
                    <Key frame="177" value="-5.80326" />
                    <Key frame="179" value="-11.5162" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="37" value="4.83163" />
                    <Key frame="39" value="4.65585" />
                    <Key frame="73" value="4.65585" />
                    <Key frame="75" value="4.65585" />
                    <Key frame="110" value="4.65585" />
                    <Key frame="112" value="4.65585" />
                    <Key frame="150" value="4.65585" />
                    <Key frame="152" value="4.65585" />
                    <Key frame="177" value="4.65585" />
                    <Key frame="179" value="4.83163" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="Stand" id="2" localization="8" tooltip="The robot goes from its current postition to the asked posture." x="90" y="59">
              <bitmap>media/images/box/box-diagram.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[class MyClass(GeneratedClass):
    def __init__(self):
        GeneratedClass.__init__(self, False)

    def onLoad(self):
        self.nTries = 0
        self.postureService = self.session().service("ALRobotPosture")
        pass

    def onUnload(self):
        self.postureService.stopMove()

    def onInput_onStart(self):
        if(self.nTries != self.getParameter("Maximum of tries")):
            self.nTries = self.getParameter("Maximum of tries")
            self.postureService.setMaxTryNumber(self.nTries)

        result = self.postureService.goToPosture(self.getParameter("Name"), self.getParameter("Speed (%)")/100.)
        if(result):
            self.success()
        else:
            self.failure()
        pass

    def onInput_onStop(self):
        self.onUnload() #~ it is recommanded to call onUnload of this box in a onStop method, as the code written in onUnload is used to stop the box as well
        pass]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="success" type="1" type_size="1" nature="1" inner="0" tooltip="Stimulated if the posture has been reached." id="4" />
              <Output name="failure" type="1" type_size="1" nature="1" inner="0" tooltip="Stimulated if the posture could not be reached." id="5" />
              <Parameter name="Name" inherits_from_parent="0" content_type="3" value="Stand" default_value="Stand" custom_choice="1" tooltip="Name of the posture to go to." id="6">
                <Choice value="Crouch" />
                <Choice value="LyingBack" />
                <Choice value="LyingBelly" />
                <Choice value="Sit" />
                <Choice value="SitRelax" />
                <Choice value="StandInit" />
                <Choice value="Stand" />
                <Choice value="StandZero" />
              </Parameter>
              <Parameter name="Speed (%)" inherits_from_parent="0" content_type="1" value="80" default_value="80" min="0" max="100" tooltip="Speed to go to the posture." id="7" />
              <Parameter name="Maximum of tries" inherits_from_parent="0" content_type="1" value="3" default_value="3" min="1" max="10" tooltip="The maximum number of fails of go to posture before stimulating the failure output." id="8" />
              <Resource name="All motors" type="Lock" timeout="0" />
              <Resource name="Stiffness" type="Lock" timeout="0" />
            </Box>
            <Box name="DehnenRumpf" id="3" localization="8" tooltip="Stand&#x0A;Kopf vorne&#x0A;Halten&#x0A;Beugen&#x0A;Halten&#x0A;Kopf vorne&#x0A;Halten&#x0A;Stand&#x0A;" x="1381" y="173">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="210">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="nao">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="2" value="-9.31893" />
                    <Key frame="30" value="33.9238" />
                    <Key frame="39" value="33.9238" />
                    <Key frame="119" value="34.0996" />
                    <Key frame="131" value="25.7499" />
                    <Key frame="188" value="33.9238" />
                    <Key frame="210" value="-9.31893" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="2" value="0.349159" />
                    <Key frame="30" value="-1.145" />
                    <Key frame="39" value="-1.145" />
                    <Key frame="119" value="-5.3638" />
                    <Key frame="131" value="-4.48488" />
                    <Key frame="188" value="-1.145" />
                    <Key frame="210" value="0.349159" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="2" value="5.71056" />
                    <Key frame="30" value="4.30429" />
                    <Key frame="39" value="4.30429" />
                    <Key frame="119" value="-4.13332" />
                    <Key frame="131" value="6.2379" />
                    <Key frame="188" value="4.30429" />
                    <Key frame="210" value="5.71056" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="2" value="-6.50159" />
                    <Key frame="30" value="-7.3805" />
                    <Key frame="39" value="-7.3805" />
                    <Key frame="119" value="-11.5114" />
                    <Key frame="131" value="-12.1267" />
                    <Key frame="188" value="-7.3805" />
                    <Key frame="210" value="-6.50159" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="2" value="-22.5858" />
                    <Key frame="30" value="-19.2459" />
                    <Key frame="39" value="-19.2459" />
                    <Key frame="119" value="-12.1267" />
                    <Key frame="131" value="-11.4235" />
                    <Key frame="188" value="-19.2459" />
                    <Key frame="210" value="-22.5858" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="2" value="-69.5248" />
                    <Key frame="30" value="-69.4369" />
                    <Key frame="39" value="-69.4369" />
                    <Key frame="119" value="-73.9193" />
                    <Key frame="131" value="-73.9193" />
                    <Key frame="188" value="-69.4369" />
                    <Key frame="210" value="-69.5248" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="2" value="0.2976" />
                    <Key frame="30" value="0.294" />
                    <Key frame="39" value="0.294" />
                    <Key frame="119" value="0.282" />
                    <Key frame="131" value="0.282" />
                    <Key frame="188" value="0.294" />
                    <Key frame="210" value="0.2976" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="2" value="7.91266" />
                    <Key frame="30" value="5.80326" />
                    <Key frame="39" value="5.80326" />
                    <Key frame="119" value="-9.5778" />
                    <Key frame="131" value="-11.1598" />
                    <Key frame="188" value="5.80326" />
                    <Key frame="210" value="7.91266" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="2" value="6.68218" />
                    <Key frame="30" value="6.5064" />
                    <Key frame="39" value="6.5064" />
                    <Key frame="119" value="3.43018" />
                    <Key frame="131" value="3.43018" />
                    <Key frame="188" value="6.5064" />
                    <Key frame="210" value="6.68218" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="2" value="-9.13833" />
                    <Key frame="30" value="-12.2146" />
                    <Key frame="39" value="-12.2146" />
                    <Key frame="119" value="-21.8826" />
                    <Key frame="131" value="-22.4979" />
                    <Key frame="188" value="-12.2146" />
                    <Key frame="210" value="-9.13833" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="2" value="-4.92435" />
                    <Key frame="30" value="-5.3638" />
                    <Key frame="39" value="-5.3638" />
                    <Key frame="119" value="-5.53958" />
                    <Key frame="131" value="-4.92435" />
                    <Key frame="188" value="-5.3638" />
                    <Key frame="210" value="-4.92435" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="2" value="83.1432" />
                    <Key frame="30" value="80.5943" />
                    <Key frame="39" value="80.5943" />
                    <Key frame="119" value="48.2502" />
                    <Key frame="131" value="49.4806" />
                    <Key frame="188" value="80.5943" />
                    <Key frame="210" value="83.1432" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="2" value="12.5661" />
                    <Key frame="30" value="7.99575" />
                    <Key frame="39" value="7.99575" />
                    <Key frame="119" value="-1.76024" />
                    <Key frame="131" value="-2.63916" />
                    <Key frame="188" value="7.99575" />
                    <Key frame="210" value="12.5661" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="2" value="8.17153" />
                    <Key frame="30" value="8.52309" />
                    <Key frame="39" value="8.52309" />
                    <Key frame="119" value="8.52309" />
                    <Key frame="131" value="8.52309" />
                    <Key frame="188" value="8.52309" />
                    <Key frame="210" value="8.17153" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="2" value="5.71537" />
                    <Key frame="30" value="6.15482" />
                    <Key frame="39" value="6.15482" />
                    <Key frame="119" value="3.86964" />
                    <Key frame="131" value="14.7682" />
                    <Key frame="188" value="6.15482" />
                    <Key frame="210" value="5.71537" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="2" value="6.5064" />
                    <Key frame="30" value="8.44001" />
                    <Key frame="39" value="8.44001" />
                    <Key frame="119" value="18.196" />
                    <Key frame="131" value="16.9655" />
                    <Key frame="188" value="8.44001" />
                    <Key frame="210" value="6.5064" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="2" value="22.239" />
                    <Key frame="30" value="19.6023" />
                    <Key frame="39" value="19.6023" />
                    <Key frame="119" value="13.7135" />
                    <Key frame="131" value="13.7135" />
                    <Key frame="188" value="19.6023" />
                    <Key frame="210" value="22.239" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="2" value="68.9926" />
                    <Key frame="30" value="69.52" />
                    <Key frame="39" value="69.52" />
                    <Key frame="119" value="72.5962" />
                    <Key frame="131" value="72.5962" />
                    <Key frame="188" value="69.52" />
                    <Key frame="210" value="68.9926" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="2" value="0.3" />
                    <Key frame="30" value="0.302" />
                    <Key frame="39" value="0.302" />
                    <Key frame="119" value="0.2884" />
                    <Key frame="131" value="0.2884" />
                    <Key frame="188" value="0.302" />
                    <Key frame="210" value="0.3" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="2" value="7.55628" />
                    <Key frame="30" value="2.1949" />
                    <Key frame="39" value="2.1949" />
                    <Key frame="119" value="-20.3054" />
                    <Key frame="131" value="-23.1179" />
                    <Key frame="188" value="2.1949" />
                    <Key frame="210" value="7.55628" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="2" value="-7.11683" />
                    <Key frame="30" value="-7.73207" />
                    <Key frame="39" value="-7.73207" />
                    <Key frame="119" value="-12.654" />
                    <Key frame="131" value="-13.2692" />
                    <Key frame="188" value="-7.73207" />
                    <Key frame="210" value="-7.11683" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="2" value="-9.13833" />
                    <Key frame="30" value="-12.2146" />
                    <Key frame="39" value="-12.2146" />
                    <Key frame="119" value="-21.8826" />
                    <Key frame="131" value="-22.4979" />
                    <Key frame="188" value="-12.2146" />
                    <Key frame="210" value="-9.13833" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="2" value="-5.00743" />
                    <Key frame="30" value="-5.2711" />
                    <Key frame="39" value="-5.2711" />
                    <Key frame="119" value="-5.2711" />
                    <Key frame="131" value="-5.2711" />
                    <Key frame="188" value="-5.2711" />
                    <Key frame="210" value="-5.00743" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="2" value="82.0054" />
                    <Key frame="30" value="79.5444" />
                    <Key frame="39" value="79.5444" />
                    <Key frame="119" value="55.9894" />
                    <Key frame="131" value="55.9894" />
                    <Key frame="188" value="79.5444" />
                    <Key frame="210" value="82.0054" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="2" value="-12.483" />
                    <Key frame="30" value="-9.14314" />
                    <Key frame="39" value="-9.14314" />
                    <Key frame="119" value="-3.60597" />
                    <Key frame="131" value="-2.72705" />
                    <Key frame="188" value="-9.14314" />
                    <Key frame="210" value="-12.483" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="2" value="3.60116" />
                    <Key frame="30" value="3.68905" />
                    <Key frame="39" value="3.68905" />
                    <Key frame="119" value="-4.48488" />
                    <Key frame="131" value="-4.48488" />
                    <Key frame="188" value="3.68905" />
                    <Key frame="210" value="3.60116" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="DehnenWade" id="4" localization="8" tooltip="Stand&#x0A;Dehnung&#x0A;Halten&#x0A;Stand" x="1386" y="289">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="124">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="32" value="-10.901" />
                    <Key frame="80" value="-17.2292" />
                    <Key frame="90" value="-17.2292" />
                    <Key frame="124" value="-10.901" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="32" value="0.612832" />
                    <Key frame="80" value="0.876518" />
                    <Key frame="90" value="0.876518" />
                    <Key frame="124" value="0.612832" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="32" value="5.18321" />
                    <Key frame="80" value="-12.1315" />
                    <Key frame="90" value="-12.1315" />
                    <Key frame="124" value="5.18321" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="32" value="-6.4137" />
                    <Key frame="80" value="-2.98591" />
                    <Key frame="90" value="-2.98591" />
                    <Key frame="124" value="-6.4137" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="32" value="-22.9373" />
                    <Key frame="80" value="-10.8962" />
                    <Key frame="90" value="-10.8962" />
                    <Key frame="124" value="-22.9373" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="32" value="-69.9642" />
                    <Key frame="80" value="-87.6305" />
                    <Key frame="90" value="-87.6305" />
                    <Key frame="124" value="-69.9642" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="32" value="0.3008" />
                    <Key frame="80" value="0.2972" />
                    <Key frame="90" value="0.2972" />
                    <Key frame="124" value="0.3008" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="32" value="7.12163" />
                    <Key frame="80" value="-45.965" />
                    <Key frame="90" value="-45.965" />
                    <Key frame="124" value="7.12163" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="32" value="6.24271" />
                    <Key frame="80" value="5.27591" />
                    <Key frame="90" value="5.27591" />
                    <Key frame="124" value="6.24271" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="32" value="-10.2809" />
                    <Key frame="80" value="5.53958" />
                    <Key frame="90" value="5.53958" />
                    <Key frame="124" value="-10.2809" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="32" value="-5.53958" />
                    <Key frame="80" value="57.303" />
                    <Key frame="90" value="57.303" />
                    <Key frame="124" value="-5.53958" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="32" value="84.8131" />
                    <Key frame="80" value="95.448" />
                    <Key frame="90" value="95.448" />
                    <Key frame="124" value="84.8131" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="32" value="12.2146" />
                    <Key frame="80" value="10.1051" />
                    <Key frame="90" value="10.1051" />
                    <Key frame="124" value="12.2146" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="32" value="4.74374" />
                    <Key frame="80" value="-15.8229" />
                    <Key frame="90" value="-15.8229" />
                    <Key frame="124" value="4.74374" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="32" value="5.01224" />
                    <Key frame="80" value="-28.7382" />
                    <Key frame="90" value="-28.7382" />
                    <Key frame="124" value="5.01224" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="32" value="6.3306" />
                    <Key frame="80" value="7.20952" />
                    <Key frame="90" value="7.20952" />
                    <Key frame="124" value="6.3306" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="32" value="22.5906" />
                    <Key frame="80" value="5.01224" />
                    <Key frame="90" value="5.01224" />
                    <Key frame="124" value="22.5906" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="32" value="70.0473" />
                    <Key frame="80" value="87.5378" />
                    <Key frame="90" value="87.5378" />
                    <Key frame="124" value="70.0473" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="32" value="0.294" />
                    <Key frame="80" value="0.2856" />
                    <Key frame="90" value="0.2856" />
                    <Key frame="124" value="0.294" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="32" value="7.20472" />
                    <Key frame="80" value="27.8593" />
                    <Key frame="90" value="27.8593" />
                    <Key frame="124" value="7.20472" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="32" value="-6.4137" />
                    <Key frame="80" value="-4.12851" />
                    <Key frame="90" value="-4.12851" />
                    <Key frame="124" value="-6.4137" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="32" value="-10.2809" />
                    <Key frame="80" value="5.53958" />
                    <Key frame="90" value="5.53958" />
                    <Key frame="124" value="-10.2809" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="32" value="-5.71056" />
                    <Key frame="80" value="-2.98591" />
                    <Key frame="90" value="-2.98591" />
                    <Key frame="124" value="-5.71056" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="32" value="81.7417" />
                    <Key frame="80" value="79.896" />
                    <Key frame="90" value="79.896" />
                    <Key frame="124" value="81.7417" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="32" value="-10.8131" />
                    <Key frame="80" value="-1.05711" />
                    <Key frame="90" value="-1.05711" />
                    <Key frame="124" value="-10.8131" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="32" value="7.20472" />
                    <Key frame="80" value="22.41" />
                    <Key frame="90" value="22.41" />
                    <Key frame="124" value="7.20472" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="DehnenSeite" id="5" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="1387" y="401">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="-1">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
              </Timeline>
            </Box>
            <Box name="DehnenOberschenkel" id="6" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="1385" y="517">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="75">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="15" value="6.15001" />
                    <Key frame="34" value="-47.6397" />
                    <Key frame="43" value="-47.6397" />
                    <Key frame="75" value="6.15001" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="15" value="3.0738" />
                    <Key frame="34" value="-1.76024" />
                    <Key frame="43" value="-1.76024" />
                    <Key frame="75" value="3.0738" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="15" value="42.3614" />
                    <Key frame="34" value="42.8888" />
                    <Key frame="43" value="42.8888" />
                    <Key frame="75" value="42.3614" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="15" value="-0.261268" />
                    <Key frame="34" value="0.00240423" />
                    <Key frame="43" value="0.00240423" />
                    <Key frame="75" value="-0.261268" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="15" value="-7.73207" />
                    <Key frame="34" value="-7.20472" />
                    <Key frame="43" value="-7.20472" />
                    <Key frame="75" value="-7.73207" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="15" value="-7.64898" />
                    <Key frame="34" value="-8.08845" />
                    <Key frame="43" value="-8.08845" />
                    <Key frame="75" value="-7.64898" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="15" value="0.0556" />
                    <Key frame="34" value="0.0488" />
                    <Key frame="43" value="0.0488" />
                    <Key frame="75" value="0.0556" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="15" value="16.7897" />
                    <Key frame="34" value="17.405" />
                    <Key frame="43" value="17.405" />
                    <Key frame="75" value="16.7897" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="15" value="5.18802" />
                    <Key frame="34" value="4.83644" />
                    <Key frame="43" value="4.83644" />
                    <Key frame="75" value="5.18802" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="15" value="-1.22808" />
                    <Key frame="34" value="-1.22808" />
                    <Key frame="43" value="-1.22808" />
                    <Key frame="75" value="-1.22808" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="15" value="-5.97904" />
                    <Key frame="34" value="-5.71537" />
                    <Key frame="43" value="-5.71537" />
                    <Key frame="75" value="-5.97904" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="15" value="-78.7534" />
                    <Key frame="34" value="-74.9741" />
                    <Key frame="43" value="-74.9741" />
                    <Key frame="75" value="-78.7534" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="15" value="5.71056" />
                    <Key frame="34" value="7.11683" />
                    <Key frame="43" value="7.11683" />
                    <Key frame="75" value="5.71056" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="15" value="-18.4597" />
                    <Key frame="34" value="-19.9538" />
                    <Key frame="43" value="-19.9538" />
                    <Key frame="75" value="-18.4597" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="15" value="53.0011" />
                    <Key frame="34" value="53.2648" />
                    <Key frame="43" value="53.2648" />
                    <Key frame="75" value="53.0011" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="15" value="0.0902951" />
                    <Key frame="34" value="-0.173378" />
                    <Key frame="43" value="-0.173378" />
                    <Key frame="75" value="0.0902951" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="15" value="23.8211" />
                    <Key frame="34" value="27.3367" />
                    <Key frame="43" value="27.3367" />
                    <Key frame="75" value="23.8211" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="15" value="75.6724" />
                    <Key frame="34" value="75.4087" />
                    <Key frame="43" value="75.4087" />
                    <Key frame="75" value="75.6724" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="15" value="0.192" />
                    <Key frame="34" value="0.6156" />
                    <Key frame="43" value="0.6156" />
                    <Key frame="75" value="0.192" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="15" value="7.81997" />
                    <Key frame="34" value="16.2576" />
                    <Key frame="43" value="16.2576" />
                    <Key frame="75" value="7.81997" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="15" value="-9.75358" />
                    <Key frame="34" value="-9.84147" />
                    <Key frame="43" value="-9.84147" />
                    <Key frame="75" value="-9.75358" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="15" value="-1.22808" />
                    <Key frame="34" value="-1.22808" />
                    <Key frame="43" value="-1.22808" />
                    <Key frame="75" value="-1.22808" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="15" value="-4.91954" />
                    <Key frame="34" value="100.463" />
                    <Key frame="43" value="100.463" />
                    <Key frame="75" value="-4.91954" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="15" value="-63.807" />
                    <Key frame="34" value="-61.5218" />
                    <Key frame="43" value="-61.5218" />
                    <Key frame="75" value="-63.807" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="15" value="-5.89115" />
                    <Key frame="34" value="-4.48488" />
                    <Key frame="43" value="-4.48488" />
                    <Key frame="75" value="-5.89115" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="15" value="-4.57277" />
                    <Key frame="34" value="0.788627" />
                    <Key frame="43" value="0.788627" />
                    <Key frame="75" value="-4.57277" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="Lying Back" id="8" localization="8" tooltip="The robot goes from its current postition to the asked posture." x="90" y="194">
              <bitmap>media/images/box/box-diagram.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[class MyClass(GeneratedClass):
    def __init__(self):
        GeneratedClass.__init__(self, False)

    def onLoad(self):
        self.nTries = 0
        self.postureService = self.session().service("ALRobotPosture")
        pass

    def onUnload(self):
        self.postureService.stopMove()

    def onInput_onStart(self):
        if(self.nTries != self.getParameter("Maximum of tries")):
            self.nTries = self.getParameter("Maximum of tries")
            self.postureService.setMaxTryNumber(self.nTries)

        result = self.postureService.goToPosture(self.getParameter("Name"), self.getParameter("Speed (%)")/100.)
        if(result):
            self.success()
        else:
            self.failure()
        pass

    def onInput_onStop(self):
        self.onUnload() #~ it is recommanded to call onUnload of this box in a onStop method, as the code written in onUnload is used to stop the box as well
        pass]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="success" type="1" type_size="1" nature="1" inner="0" tooltip="Stimulated if the posture has been reached." id="4" />
              <Output name="failure" type="1" type_size="1" nature="1" inner="0" tooltip="Stimulated if the posture could not be reached." id="5" />
              <Parameter name="Name" inherits_from_parent="0" content_type="3" value="LyingBack" default_value="Stand" custom_choice="1" tooltip="Name of the posture to go to." id="6">
                <Choice value="Crouch" />
                <Choice value="LyingBack" />
                <Choice value="LyingBelly" />
                <Choice value="Sit" />
                <Choice value="SitRelax" />
                <Choice value="StandInit" />
                <Choice value="Stand" />
                <Choice value="StandZero" />
              </Parameter>
              <Parameter name="Speed (%)" inherits_from_parent="0" content_type="1" value="80" default_value="80" min="0" max="100" tooltip="Speed to go to the posture." id="7" />
              <Parameter name="Maximum of tries" inherits_from_parent="0" content_type="1" value="3" default_value="3" min="1" max="10" tooltip="The maximum number of fails of go to posture before stimulating the failure output." id="8" />
              <Resource name="All motors" type="Lock" timeout="0" />
              <Resource name="Stiffness" type="Lock" timeout="0" />
            </Box>
            <Box name="Squat" id="10" localization="8" tooltip="Arme vor&#x0A;Runter&#x0A;Halten&#x0A;Hoch&#x0A;Stand" x="1253" y="290">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="105">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="15" value="-9.14314" />
                    <Key frame="40" value="-6.15482" />
                    <Key frame="65" value="-6.15482" />
                    <Key frame="90" value="-9.14314" />
                    <Key frame="105" value="-8.70368" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="15" value="-0.529749" />
                    <Key frame="40" value="-1.145" />
                    <Key frame="65" value="-1.145" />
                    <Key frame="90" value="-0.529749" />
                    <Key frame="105" value="-1.05711" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="15" value="4.83163" />
                    <Key frame="40" value="-32.0829" />
                    <Key frame="65" value="-32.0829" />
                    <Key frame="90" value="4.83163" />
                    <Key frame="105" value="4.83163" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="15" value="-6.58948" />
                    <Key frame="40" value="-7.02893" />
                    <Key frame="65" value="-7.02893" />
                    <Key frame="90" value="-6.58948" />
                    <Key frame="105" value="-6.32579" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="15" value="-2.1949" />
                    <Key frame="40" value="-2.1949" />
                    <Key frame="65" value="-2.1949" />
                    <Key frame="90" value="-2.1949" />
                    <Key frame="105" value="-23.2889" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="15" value="-54.1437" />
                    <Key frame="40" value="-54.1437" />
                    <Key frame="65" value="-54.1437" />
                    <Key frame="90" value="-54.1437" />
                    <Key frame="105" value="-67.3275" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="15" value="0.294" />
                    <Key frame="40" value="0.294" />
                    <Key frame="65" value="0.294" />
                    <Key frame="90" value="0.294" />
                    <Key frame="105" value="0.2848" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="15" value="7.4732" />
                    <Key frame="40" value="-52.9084" />
                    <Key frame="65" value="-52.9084" />
                    <Key frame="90" value="7.4732" />
                    <Key frame="105" value="7.29742" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="15" value="7.12163" />
                    <Key frame="40" value="8.17634" />
                    <Key frame="65" value="8.17634" />
                    <Key frame="90" value="7.12163" />
                    <Key frame="105" value="6.85796" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="15" value="-10.1051" />
                    <Key frame="40" value="-16.5212" />
                    <Key frame="65" value="-16.5212" />
                    <Key frame="90" value="-10.1051" />
                    <Key frame="105" value="-9.75358" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="15" value="-5.18802" />
                    <Key frame="40" value="88.6804" />
                    <Key frame="65" value="88.6804" />
                    <Key frame="90" value="-5.18802" />
                    <Key frame="105" value="-5.45169" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="15" value="2.89802" />
                    <Key frame="40" value="4.83163" />
                    <Key frame="65" value="4.83163" />
                    <Key frame="90" value="2.89802" />
                    <Key frame="105" value="82.1764" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="15" value="-9.84628" />
                    <Key frame="40" value="-9.14314" />
                    <Key frame="65" value="-9.14314" />
                    <Key frame="90" value="-9.84628" />
                    <Key frame="105" value="11.5114" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="15" value="26.7167" />
                    <Key frame="40" value="26.7167" />
                    <Key frame="65" value="26.7167" />
                    <Key frame="90" value="26.7167" />
                    <Key frame="105" value="7.3805" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="15" value="5.01224" />
                    <Key frame="40" value="-33.0449" />
                    <Key frame="65" value="-33.0449" />
                    <Key frame="90" value="5.01224" />
                    <Key frame="105" value="5.10013" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="15" value="7.03374" />
                    <Key frame="40" value="10.6373" />
                    <Key frame="65" value="10.6373" />
                    <Key frame="90" value="7.03374" />
                    <Key frame="105" value="6.3306" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="15" value="5.62747" />
                    <Key frame="40" value="5.62747" />
                    <Key frame="65" value="5.62747" />
                    <Key frame="90" value="5.62747" />
                    <Key frame="105" value="23.2937" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="15" value="51.5022" />
                    <Key frame="40" value="51.5022" />
                    <Key frame="65" value="51.5022" />
                    <Key frame="90" value="51.5022" />
                    <Key frame="105" value="67.7621" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="15" value="0.2852" />
                    <Key frame="40" value="0.2852" />
                    <Key frame="65" value="0.2852" />
                    <Key frame="90" value="0.2852" />
                    <Key frame="105" value="0.29" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="15" value="7.20472" />
                    <Key frame="40" value="-46.585" />
                    <Key frame="65" value="-46.585" />
                    <Key frame="90" value="7.20472" />
                    <Key frame="105" value="7.20472" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="15" value="-7.20472" />
                    <Key frame="40" value="-7.46839" />
                    <Key frame="65" value="-7.46839" />
                    <Key frame="90" value="-7.20472" />
                    <Key frame="105" value="-6.32579" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="15" value="-10.1051" />
                    <Key frame="40" value="-16.5212" />
                    <Key frame="65" value="-16.5212" />
                    <Key frame="90" value="-10.1051" />
                    <Key frame="105" value="-9.75358" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="15" value="-5.62267" />
                    <Key frame="40" value="83.6753" />
                    <Key frame="65" value="83.6753" />
                    <Key frame="90" value="-5.62267" />
                    <Key frame="105" value="-5.53478" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="15" value="3.51807" />
                    <Key frame="40" value="4.83644" />
                    <Key frame="65" value="4.83644" />
                    <Key frame="90" value="3.51807" />
                    <Key frame="105" value="82.2691" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="15" value="8.17153" />
                    <Key frame="40" value="6.94104" />
                    <Key frame="65" value="6.94104" />
                    <Key frame="90" value="8.17153" />
                    <Key frame="105" value="-11.5162" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="15" value="-33.9286" />
                    <Key frame="40" value="-33.9286" />
                    <Key frame="65" value="-33.9286" />
                    <Key frame="90" value="-33.9286" />
                    <Key frame="105" value="3.33748" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="Klappmesser" id="11" localization="8" tooltip="LyingBack&#x0A;Ausgestreckt&#x0A;Klappt&#x0A;Ausgestreckt&#x0A;LyingBack" x="1254" y="527">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="125">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="nao">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="5" value="10.0173" />
                    <Key frame="35" value="13.7087" />
                    <Key frame="65" value="13.7087" />
                    <Key frame="95" value="13.7087" />
                    <Key frame="125" value="10.0173" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="5" value="-1.32078" />
                    <Key frame="35" value="-0.705531" />
                    <Key frame="65" value="-0.705531" />
                    <Key frame="95" value="-0.705531" />
                    <Key frame="125" value="-1.32078" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="5" value="49.5685" />
                    <Key frame="35" value="-1.145" />
                    <Key frame="65" value="-1.145" />
                    <Key frame="95" value="-1.145" />
                    <Key frame="125" value="49.5685" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="5" value="-2.1949" />
                    <Key frame="35" value="-2.107" />
                    <Key frame="65" value="-8.34731" />
                    <Key frame="95" value="-2.107" />
                    <Key frame="125" value="-2.1949" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="5" value="-87.5378" />
                    <Key frame="35" value="-2.81013" />
                    <Key frame="65" value="-1.0523" />
                    <Key frame="95" value="-2.81013" />
                    <Key frame="125" value="-87.5378" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="5" value="-49.6612" />
                    <Key frame="35" value="-85.2574" />
                    <Key frame="65" value="-85.2574" />
                    <Key frame="95" value="-85.2574" />
                    <Key frame="125" value="-49.6612" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="5" value="0.3904" />
                    <Key frame="35" value="0.8496" />
                    <Key frame="65" value="0.8496" />
                    <Key frame="95" value="0.8496" />
                    <Key frame="125" value="0.3904" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="5" value="21.5359" />
                    <Key frame="35" value="24.7879" />
                    <Key frame="65" value="-57.6546" />
                    <Key frame="95" value="24.7879" />
                    <Key frame="125" value="21.5359" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="5" value="6.41851" />
                    <Key frame="35" value="6.94585" />
                    <Key frame="65" value="0.178186" />
                    <Key frame="95" value="6.94585" />
                    <Key frame="125" value="6.41851" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="5" value="-29.2655" />
                    <Key frame="35" value="-29.2655" />
                    <Key frame="65" value="-5.00743" />
                    <Key frame="95" value="-29.2655" />
                    <Key frame="125" value="-29.2655" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="5" value="-5.3638" />
                    <Key frame="35" value="-2.99072" />
                    <Key frame="65" value="-4.74855" />
                    <Key frame="95" value="-2.99072" />
                    <Key frame="125" value="-5.3638" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="5" value="107.665" />
                    <Key frame="35" value="-89.2125" />
                    <Key frame="65" value="3.51326" />
                    <Key frame="95" value="-89.2125" />
                    <Key frame="125" value="107.665" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="5" value="13.8845" />
                    <Key frame="35" value="-0.00240423" />
                    <Key frame="65" value="-10.3736" />
                    <Key frame="95" value="-0.00240423" />
                    <Key frame="125" value="13.8845" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="5" value="12.7419" />
                    <Key frame="35" value="68.2016" />
                    <Key frame="65" value="86.2194" />
                    <Key frame="95" value="68.2016" />
                    <Key frame="125" value="12.7419" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="5" value="37.0048" />
                    <Key frame="35" value="16.3503" />
                    <Key frame="65" value="4.83644" />
                    <Key frame="95" value="16.3503" />
                    <Key frame="125" value="37.0048" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="5" value="-16.2576" />
                    <Key frame="35" value="-16.2576" />
                    <Key frame="65" value="-4.48007" />
                    <Key frame="95" value="-16.2576" />
                    <Key frame="125" value="-16.2576" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="5" value="86.9273" />
                    <Key frame="35" value="3.51807" />
                    <Key frame="65" value="1.49656" />
                    <Key frame="95" value="3.51807" />
                    <Key frame="125" value="86.9273" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="5" value="49.6564" />
                    <Key frame="35" value="70.3989" />
                    <Key frame="65" value="70.3989" />
                    <Key frame="95" value="70.3989" />
                    <Key frame="125" value="49.6564" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="5" value="0.352" />
                    <Key frame="35" value="0.8288" />
                    <Key frame="65" value="0.8288" />
                    <Key frame="95" value="0.8288" />
                    <Key frame="125" value="0.352" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="5" value="-23.03" />
                    <Key frame="35" value="26.3651" />
                    <Key frame="65" value="-58.9778" />
                    <Key frame="95" value="26.3651" />
                    <Key frame="125" value="-23.03" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="5" value="-4.65585" />
                    <Key frame="35" value="-0.964409" />
                    <Key frame="65" value="4.22121" />
                    <Key frame="95" value="-0.964409" />
                    <Key frame="125" value="-4.65585" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="5" value="-29.2655" />
                    <Key frame="35" value="-29.2655" />
                    <Key frame="65" value="-5.00743" />
                    <Key frame="95" value="-29.2655" />
                    <Key frame="125" value="-29.2655" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="5" value="99.5837" />
                    <Key frame="35" value="-4.83163" />
                    <Key frame="65" value="-4.83163" />
                    <Key frame="95" value="-4.83163" />
                    <Key frame="125" value="99.5837" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="5" value="101.693" />
                    <Key frame="35" value="-89.2956" />
                    <Key frame="65" value="6.06693" />
                    <Key frame="95" value="-89.2956" />
                    <Key frame="125" value="101.693" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="5" value="-14.7682" />
                    <Key frame="35" value="0.876518" />
                    <Key frame="65" value="7.81997" />
                    <Key frame="95" value="0.876518" />
                    <Key frame="125" value="-14.7682" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="5" value="11.7751" />
                    <Key frame="35" value="-50.8917" />
                    <Key frame="65" value="-57.8352" />
                    <Key frame="95" value="-50.8917" />
                    <Key frame="125" value="11.7751" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="HipRaise" id="12" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="1228" y="161">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="90">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="5" value="10.0173" />
                    <Key frame="15" value="22.9373" />
                    <Key frame="25" value="24.5194" />
                    <Key frame="40" value="33.6601" />
                    <Key frame="55" value="33.6601" />
                    <Key frame="70" value="24.5194" />
                    <Key frame="80" value="22.9373" />
                    <Key frame="90" value="10.0173" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="5" value="-1.32078" />
                    <Key frame="15" value="-0.0902951" />
                    <Key frame="25" value="-0.705531" />
                    <Key frame="40" value="-3.25439" />
                    <Key frame="55" value="-3.25439" />
                    <Key frame="70" value="-0.705531" />
                    <Key frame="80" value="-0.0902951" />
                    <Key frame="90" value="-1.32078" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="5" value="49.5685" />
                    <Key frame="15" value="31.6386" />
                    <Key frame="25" value="31.7265" />
                    <Key frame="40" value="18.1912" />
                    <Key frame="55" value="18.1912" />
                    <Key frame="70" value="31.7265" />
                    <Key frame="80" value="31.6386" />
                    <Key frame="90" value="49.5685" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="5" value="-2.1949" />
                    <Key frame="15" value="0.441859" />
                    <Key frame="25" value="0.441859" />
                    <Key frame="40" value="7.12163" />
                    <Key frame="55" value="7.12163" />
                    <Key frame="70" value="0.441859" />
                    <Key frame="80" value="0.441859" />
                    <Key frame="90" value="-2.1949" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="5" value="-87.5378" />
                    <Key frame="15" value="-82.2643" />
                    <Key frame="25" value="-20.6522" />
                    <Key frame="40" value="-6.76526" />
                    <Key frame="55" value="-6.76526" />
                    <Key frame="70" value="-20.6522" />
                    <Key frame="80" value="-82.2643" />
                    <Key frame="90" value="-87.5378" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="5" value="-49.6612" />
                    <Key frame="15" value="-95.6286" />
                    <Key frame="25" value="-59.4172" />
                    <Key frame="40" value="-59.7688" />
                    <Key frame="55" value="-59.7688" />
                    <Key frame="70" value="-59.4172" />
                    <Key frame="80" value="-95.6286" />
                    <Key frame="90" value="-49.6612" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="5" value="0.3904" />
                    <Key frame="15" value="0.3168" />
                    <Key frame="25" value="0.3016" />
                    <Key frame="40" value="0.3052" />
                    <Key frame="55" value="0.3052" />
                    <Key frame="70" value="0.3016" />
                    <Key frame="80" value="0.3168" />
                    <Key frame="90" value="0.3904" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="5" value="21.5359" />
                    <Key frame="15" value="-31.3749" />
                    <Key frame="25" value="-31.1992" />
                    <Key frame="40" value="11.5162" />
                    <Key frame="55" value="11.5162" />
                    <Key frame="70" value="-31.1992" />
                    <Key frame="80" value="-31.3749" />
                    <Key frame="90" value="21.5359" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="5" value="6.41851" />
                    <Key frame="15" value="14.4166" />
                    <Key frame="25" value="14.5924" />
                    <Key frame="40" value="12.3951" />
                    <Key frame="55" value="12.3951" />
                    <Key frame="70" value="14.5924" />
                    <Key frame="80" value="14.4166" />
                    <Key frame="90" value="6.41851" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="5" value="-29.2655" />
                    <Key frame="15" value="-1.66754" />
                    <Key frame="25" value="-1.84332" />
                    <Key frame="40" value="0.969218" />
                    <Key frame="55" value="0.969218" />
                    <Key frame="70" value="-1.84332" />
                    <Key frame="80" value="-1.66754" />
                    <Key frame="90" value="-29.2655" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="5" value="-5.3638" />
                    <Key frame="15" value="99.2274" />
                    <Key frame="25" value="98.8758" />
                    <Key frame="40" value="83.2311" />
                    <Key frame="55" value="83.2311" />
                    <Key frame="70" value="98.8758" />
                    <Key frame="80" value="99.2274" />
                    <Key frame="90" value="-5.3638" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="5" value="107.665" />
                    <Key frame="15" value="120.849" />
                    <Key frame="25" value="113.729" />
                    <Key frame="40" value="121.025" />
                    <Key frame="55" value="121.025" />
                    <Key frame="70" value="113.729" />
                    <Key frame="80" value="120.849" />
                    <Key frame="90" value="107.665" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="5" value="13.8845" />
                    <Key frame="15" value="28.6503" />
                    <Key frame="25" value="18.4549" />
                    <Key frame="40" value="18.4549" />
                    <Key frame="55" value="18.4549" />
                    <Key frame="70" value="18.4549" />
                    <Key frame="80" value="28.6503" />
                    <Key frame="90" value="13.8845" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="5" value="12.7419" />
                    <Key frame="15" value="-16.9655" />
                    <Key frame="25" value="42.6251" />
                    <Key frame="40" value="36.6484" />
                    <Key frame="55" value="36.6484" />
                    <Key frame="70" value="42.6251" />
                    <Key frame="80" value="-16.9655" />
                    <Key frame="90" value="12.7419" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="5" value="37.0048" />
                    <Key frame="15" value="32.6981" />
                    <Key frame="25" value="32.8739" />
                    <Key frame="40" value="18.2839" />
                    <Key frame="55" value="18.2839" />
                    <Key frame="70" value="32.8739" />
                    <Key frame="80" value="32.6981" />
                    <Key frame="90" value="37.0048" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="5" value="-16.2576" />
                    <Key frame="15" value="-4.74374" />
                    <Key frame="25" value="-4.91954" />
                    <Key frame="40" value="-2.28279" />
                    <Key frame="55" value="-2.28279" />
                    <Key frame="70" value="-4.91954" />
                    <Key frame="80" value="-4.74374" />
                    <Key frame="90" value="-16.2576" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="5" value="86.9273" />
                    <Key frame="15" value="81.9175" />
                    <Key frame="25" value="14.7682" />
                    <Key frame="40" value="6.94585" />
                    <Key frame="55" value="6.94585" />
                    <Key frame="70" value="14.7682" />
                    <Key frame="80" value="81.9175" />
                    <Key frame="90" value="86.9273" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="5" value="49.6564" />
                    <Key frame="15" value="89.9987" />
                    <Key frame="25" value="66.5316" />
                    <Key frame="40" value="35.1543" />
                    <Key frame="55" value="35.1543" />
                    <Key frame="70" value="66.5316" />
                    <Key frame="80" value="89.9987" />
                    <Key frame="90" value="49.6564" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="5" value="0.352" />
                    <Key frame="15" value="0.3652" />
                    <Key frame="25" value="0.3628" />
                    <Key frame="40" value="0.3628" />
                    <Key frame="55" value="0.3628" />
                    <Key frame="70" value="0.3628" />
                    <Key frame="80" value="0.3652" />
                    <Key frame="90" value="0.352" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="5" value="-23.03" />
                    <Key frame="15" value="-37.9716" />
                    <Key frame="25" value="-37.9716" />
                    <Key frame="40" value="14.0603" />
                    <Key frame="55" value="14.0603" />
                    <Key frame="70" value="-37.9716" />
                    <Key frame="80" value="-37.9716" />
                    <Key frame="90" value="-23.03" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="5" value="-4.65585" />
                    <Key frame="15" value="-15.0271" />
                    <Key frame="25" value="-15.115" />
                    <Key frame="40" value="-14.4997" />
                    <Key frame="55" value="-14.4997" />
                    <Key frame="70" value="-15.115" />
                    <Key frame="80" value="-15.0271" />
                    <Key frame="90" value="-4.65585" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="5" value="-29.2655" />
                    <Key frame="15" value="-1.66754" />
                    <Key frame="25" value="-1.84332" />
                    <Key frame="40" value="0.969218" />
                    <Key frame="55" value="0.969218" />
                    <Key frame="70" value="-1.84332" />
                    <Key frame="80" value="-1.66754" />
                    <Key frame="90" value="-29.2655" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="5" value="99.5837" />
                    <Key frame="15" value="102.045" />
                    <Key frame="25" value="102.396" />
                    <Key frame="40" value="81.3023" />
                    <Key frame="55" value="81.3023" />
                    <Key frame="70" value="102.396" />
                    <Key frame="80" value="102.045" />
                    <Key frame="90" value="99.5837" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="5" value="101.693" />
                    <Key frame="15" value="111.537" />
                    <Key frame="25" value="105.736" />
                    <Key frame="40" value="121.732" />
                    <Key frame="55" value="121.732" />
                    <Key frame="70" value="105.736" />
                    <Key frame="80" value="111.537" />
                    <Key frame="90" value="101.693" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="5" value="-14.7682" />
                    <Key frame="15" value="-35.6864" />
                    <Key frame="25" value="-23.7332" />
                    <Key frame="40" value="-21.2722" />
                    <Key frame="55" value="-21.2722" />
                    <Key frame="70" value="-23.7332" />
                    <Key frame="80" value="-35.6864" />
                    <Key frame="90" value="-14.7682" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="5" value="11.7751" />
                    <Key frame="15" value="9.22623" />
                    <Key frame="25" value="-61.2629" />
                    <Key frame="40" value="-19.0749" />
                    <Key frame="55" value="-19.0749" />
                    <Key frame="70" value="-61.2629" />
                    <Key frame="80" value="9.22623" />
                    <Key frame="90" value="11.7751" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="Boxen" id="14" localization="8" tooltip="Stand&#x0A;Position&#x0A;Halten&#x0A;Rechter Arm&#x0A;Linker Arm&#x0A;Rechter Arm&#x0A;Linker Arm&#x0A;Position&#x0A;Stand" x="1215" y="44">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="80">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="nao">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="7" value="-8.26423" />
                    <Key frame="20" value="-8.26423" />
                    <Key frame="30" value="-8.26423" />
                    <Key frame="35" value="-8.26423" />
                    <Key frame="40" value="-8.26423" />
                    <Key frame="45" value="-8.26423" />
                    <Key frame="50" value="-8.26423" />
                    <Key frame="65" value="-8.26423" />
                    <Key frame="80" value="-8.26423" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="7" value="-1.23289" />
                    <Key frame="20" value="-1.23289" />
                    <Key frame="30" value="-1.23289" />
                    <Key frame="35" value="-1.23289" />
                    <Key frame="40" value="-1.23289" />
                    <Key frame="45" value="-1.23289" />
                    <Key frame="50" value="-1.23289" />
                    <Key frame="65" value="-1.23289" />
                    <Key frame="80" value="-1.23289" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="7" value="5.00743" />
                    <Key frame="20" value="4.30429" />
                    <Key frame="30" value="4.30429" />
                    <Key frame="35" value="4.30429" />
                    <Key frame="40" value="4.30429" />
                    <Key frame="45" value="4.30429" />
                    <Key frame="50" value="4.30429" />
                    <Key frame="65" value="4.30429" />
                    <Key frame="80" value="5.00743" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="7" value="-6.2379" />
                    <Key frame="20" value="-6.2379" />
                    <Key frame="30" value="-6.2379" />
                    <Key frame="35" value="-6.2379" />
                    <Key frame="40" value="-6.2379" />
                    <Key frame="45" value="-6.2379" />
                    <Key frame="50" value="-6.2379" />
                    <Key frame="65" value="-6.2379" />
                    <Key frame="80" value="-6.2379" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="7" value="-22.6737" />
                    <Key frame="20" value="-84.3737" />
                    <Key frame="30" value="-84.3737" />
                    <Key frame="35" value="-66.7074" />
                    <Key frame="40" value="-9.66569" />
                    <Key frame="45" value="-66.7074" />
                    <Key frame="50" value="-9.66569" />
                    <Key frame="65" value="-84.3737" />
                    <Key frame="80" value="-22.6737" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="7" value="-70.5795" />
                    <Key frame="20" value="-67.5033" />
                    <Key frame="30" value="-67.5033" />
                    <Key frame="35" value="-28.0399" />
                    <Key frame="40" value="-73.3041" />
                    <Key frame="45" value="-28.0399" />
                    <Key frame="50" value="-73.3041" />
                    <Key frame="65" value="-67.5033" />
                    <Key frame="80" value="-70.5795" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="7" value="0.2936" />
                    <Key frame="20" value="0.2936" />
                    <Key frame="30" value="0.2936" />
                    <Key frame="35" value="0.2936" />
                    <Key frame="40" value="0.2936" />
                    <Key frame="45" value="0.2936" />
                    <Key frame="50" value="0.2936" />
                    <Key frame="65" value="0.2936" />
                    <Key frame="80" value="0.2936" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="7" value="7.91266" />
                    <Key frame="20" value="7.91266" />
                    <Key frame="30" value="7.91266" />
                    <Key frame="35" value="7.91266" />
                    <Key frame="40" value="7.91266" />
                    <Key frame="45" value="7.91266" />
                    <Key frame="50" value="7.91266" />
                    <Key frame="65" value="7.91266" />
                    <Key frame="80" value="7.91266" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="7" value="7.20952" />
                    <Key frame="20" value="7.20952" />
                    <Key frame="30" value="7.20952" />
                    <Key frame="35" value="7.20952" />
                    <Key frame="40" value="7.20952" />
                    <Key frame="45" value="7.20952" />
                    <Key frame="50" value="7.20952" />
                    <Key frame="65" value="7.20952" />
                    <Key frame="80" value="7.20952" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="7" value="-9.13833" />
                    <Key frame="20" value="-9.13833" />
                    <Key frame="30" value="-9.13833" />
                    <Key frame="35" value="-9.13833" />
                    <Key frame="40" value="-9.13833" />
                    <Key frame="45" value="-9.13833" />
                    <Key frame="50" value="-9.13833" />
                    <Key frame="65" value="-9.13833" />
                    <Key frame="80" value="-9.13833" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="7" value="-5.27591" />
                    <Key frame="20" value="-5.89115" />
                    <Key frame="30" value="-5.89115" />
                    <Key frame="35" value="-5.89115" />
                    <Key frame="40" value="-5.89115" />
                    <Key frame="45" value="-5.89115" />
                    <Key frame="50" value="-5.89115" />
                    <Key frame="65" value="-5.89115" />
                    <Key frame="80" value="-5.27591" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="7" value="82.8795" />
                    <Key frame="20" value="47.6349" />
                    <Key frame="30" value="47.6349" />
                    <Key frame="35" value="20.9158" />
                    <Key frame="40" value="9.84147" />
                    <Key frame="45" value="20.9158" />
                    <Key frame="50" value="9.84147" />
                    <Key frame="65" value="47.6349" />
                    <Key frame="80" value="82.8795" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="7" value="11.2477" />
                    <Key frame="20" value="-7.20952" />
                    <Key frame="30" value="-7.20952" />
                    <Key frame="35" value="7.81997" />
                    <Key frame="40" value="-8.87946" />
                    <Key frame="45" value="7.81997" />
                    <Key frame="50" value="-8.87946" />
                    <Key frame="65" value="-7.20952" />
                    <Key frame="80" value="11.2477" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="7" value="4.39218" />
                    <Key frame="20" value="13.7087" />
                    <Key frame="30" value="13.7087" />
                    <Key frame="35" value="-30.8524" />
                    <Key frame="40" value="-19.3386" />
                    <Key frame="45" value="-30.8524" />
                    <Key frame="50" value="-19.3386" />
                    <Key frame="65" value="13.7087" />
                    <Key frame="80" value="4.39218" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="7" value="5.27591" />
                    <Key frame="20" value="4.83644" />
                    <Key frame="30" value="4.83644" />
                    <Key frame="35" value="4.83644" />
                    <Key frame="40" value="5.45169" />
                    <Key frame="45" value="4.83644" />
                    <Key frame="50" value="5.45169" />
                    <Key frame="65" value="4.83644" />
                    <Key frame="80" value="5.27591" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="7" value="6.68218" />
                    <Key frame="20" value="7.29742" />
                    <Key frame="30" value="7.29742" />
                    <Key frame="35" value="7.29742" />
                    <Key frame="40" value="7.29742" />
                    <Key frame="45" value="7.29742" />
                    <Key frame="50" value="7.29742" />
                    <Key frame="65" value="7.29742" />
                    <Key frame="80" value="6.68218" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="7" value="23.8211" />
                    <Key frame="20" value="85.1695" />
                    <Key frame="30" value="85.1695" />
                    <Key frame="35" value="3.25439" />
                    <Key frame="40" value="74.0072" />
                    <Key frame="45" value="3.25439" />
                    <Key frame="50" value="74.0072" />
                    <Key frame="65" value="85.1695" />
                    <Key frame="80" value="23.8211" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="7" value="67.9379" />
                    <Key frame="20" value="66.8832" />
                    <Key frame="30" value="66.8832" />
                    <Key frame="35" value="36.1211" />
                    <Key frame="40" value="22.1463" />
                    <Key frame="45" value="36.1211" />
                    <Key frame="50" value="22.1463" />
                    <Key frame="65" value="66.8832" />
                    <Key frame="80" value="67.9379" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="7" value="0.3016" />
                    <Key frame="20" value="0.3016" />
                    <Key frame="30" value="0.3016" />
                    <Key frame="35" value="0.3016" />
                    <Key frame="40" value="0.3016" />
                    <Key frame="45" value="0.3016" />
                    <Key frame="50" value="0.3016" />
                    <Key frame="65" value="0.3016" />
                    <Key frame="80" value="0.3016" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="7" value="7.29261" />
                    <Key frame="20" value="7.29261" />
                    <Key frame="30" value="7.29261" />
                    <Key frame="35" value="7.29261" />
                    <Key frame="40" value="7.29261" />
                    <Key frame="45" value="7.29261" />
                    <Key frame="50" value="7.29261" />
                    <Key frame="65" value="7.29261" />
                    <Key frame="80" value="7.29261" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="7" value="-7.11683" />
                    <Key frame="20" value="-7.11683" />
                    <Key frame="30" value="-7.11683" />
                    <Key frame="35" value="-7.11683" />
                    <Key frame="40" value="-7.11683" />
                    <Key frame="45" value="-7.11683" />
                    <Key frame="50" value="-7.11683" />
                    <Key frame="65" value="-7.11683" />
                    <Key frame="80" value="-7.11683" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="7" value="-9.13833" />
                    <Key frame="20" value="-9.13833" />
                    <Key frame="30" value="-9.13833" />
                    <Key frame="35" value="-9.13833" />
                    <Key frame="40" value="-9.13833" />
                    <Key frame="45" value="-9.13833" />
                    <Key frame="50" value="-9.13833" />
                    <Key frame="65" value="-9.13833" />
                    <Key frame="80" value="-9.13833" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="7" value="-5.09532" />
                    <Key frame="20" value="-5.09532" />
                    <Key frame="30" value="-5.09532" />
                    <Key frame="35" value="-5.09532" />
                    <Key frame="40" value="-5.09532" />
                    <Key frame="45" value="-5.09532" />
                    <Key frame="50" value="-5.09532" />
                    <Key frame="65" value="-5.09532" />
                    <Key frame="80" value="-5.09532" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="7" value="81.8296" />
                    <Key frame="20" value="46.4971" />
                    <Key frame="30" value="46.4971" />
                    <Key frame="35" value="-0.524941" />
                    <Key frame="40" value="20.1296" />
                    <Key frame="45" value="-0.524941" />
                    <Key frame="50" value="20.1296" />
                    <Key frame="65" value="46.4971" />
                    <Key frame="80" value="81.8296" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="7" value="-11.5162" />
                    <Key frame="20" value="2.0191" />
                    <Key frame="30" value="2.0191" />
                    <Key frame="35" value="6.67737" />
                    <Key frame="40" value="-11.8678" />
                    <Key frame="45" value="6.67737" />
                    <Key frame="50" value="-11.8678" />
                    <Key frame="65" value="2.0191" />
                    <Key frame="80" value="-11.5162" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="7" value="7.73207" />
                    <Key frame="20" value="-13.7135" />
                    <Key frame="30" value="-13.7135" />
                    <Key frame="35" value="20.6522" />
                    <Key frame="40" value="11.5993" />
                    <Key frame="45" value="20.6522" />
                    <Key frame="50" value="11.5993" />
                    <Key frame="65" value="-13.7135" />
                    <Key frame="80" value="7.73207" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="RussianTwist Demo" id="61" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="1104" y="288">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="65">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="25" value="-3.25439" />
                    <Key frame="35" value="-3.25439" />
                    <Key frame="45" value="-3.25439" />
                    <Key frame="55" value="-3.25439" />
                    <Key frame="65" value="-3.25439" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="25" value="-2.63916" />
                    <Key frame="35" value="19.5975" />
                    <Key frame="45" value="-2.63916" />
                    <Key frame="55" value="-19.5975" />
                    <Key frame="65" value="-2.63916" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="25" value="49.3049" />
                    <Key frame="35" value="49.3049" />
                    <Key frame="45" value="49.3049" />
                    <Key frame="55" value="49.6612" />
                    <Key frame="65" value="49.3049" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="25" value="-0.612832" />
                    <Key frame="35" value="-0.612832" />
                    <Key frame="45" value="-0.612832" />
                    <Key frame="55" value="-1.145" />
                    <Key frame="65" value="-0.612832" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="25" value="-87.4499" />
                    <Key frame="35" value="-86.4831" />
                    <Key frame="45" value="-87.4499" />
                    <Key frame="55" value="-69.0853" />
                    <Key frame="65" value="-87.4499" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="25" value="-35.3349" />
                    <Key frame="35" value="-43.2451" />
                    <Key frame="45" value="-35.3349" />
                    <Key frame="55" value="-26.1014" />
                    <Key frame="65" value="-35.3349" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="25" value="0.286" />
                    <Key frame="35" value="0.286" />
                    <Key frame="45" value="0.286" />
                    <Key frame="55" value="0.2908" />
                    <Key frame="65" value="0.286" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="25" value="-82.3521" />
                    <Key frame="35" value="-85.1647" />
                    <Key frame="45" value="-82.3521" />
                    <Key frame="55" value="-85.9605" />
                    <Key frame="65" value="-82.3521" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="25" value="16.4382" />
                    <Key frame="35" value="15.8229" />
                    <Key frame="45" value="16.4382" />
                    <Key frame="55" value="13.7966" />
                    <Key frame="65" value="16.4382" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="25" value="-30.496" />
                    <Key frame="35" value="-28.7382" />
                    <Key frame="45" value="-30.496" />
                    <Key frame="55" value="-28.7382" />
                    <Key frame="65" value="-30.496" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="25" value="77.9576" />
                    <Key frame="35" value="78.1333" />
                    <Key frame="45" value="77.9576" />
                    <Key frame="55" value="81.478" />
                    <Key frame="65" value="77.9576" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="25" value="34.7148" />
                    <Key frame="35" value="59.9398" />
                    <Key frame="45" value="34.7148" />
                    <Key frame="55" value="39.3779" />
                    <Key frame="65" value="34.7148" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="25" value="11.3356" />
                    <Key frame="35" value="32.4296" />
                    <Key frame="45" value="11.3356" />
                    <Key frame="55" value="-13.8845" />
                    <Key frame="65" value="11.3356" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="25" value="-35.5107" />
                    <Key frame="35" value="-15.6471" />
                    <Key frame="45" value="-35.5107" />
                    <Key frame="55" value="-8.4352" />
                    <Key frame="65" value="-35.5107" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="25" value="49.5733" />
                    <Key frame="35" value="49.6612" />
                    <Key frame="45" value="49.5733" />
                    <Key frame="55" value="49.3049" />
                    <Key frame="65" value="49.5733" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="25" value="0.441859" />
                    <Key frame="35" value="1.145" />
                    <Key frame="45" value="0.441859" />
                    <Key frame="55" value="0.612832" />
                    <Key frame="65" value="0.441859" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="25" value="87.7183" />
                    <Key frame="35" value="69.0853" />
                    <Key frame="45" value="87.7183" />
                    <Key frame="55" value="86.4831" />
                    <Key frame="65" value="87.7183" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="25" value="48.2502" />
                    <Key frame="35" value="26.1014" />
                    <Key frame="45" value="48.2502" />
                    <Key frame="55" value="43.2451" />
                    <Key frame="65" value="48.2502" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="25" value="0.2908" />
                    <Key frame="35" value="0.2908" />
                    <Key frame="45" value="0.2908" />
                    <Key frame="55" value="0.286" />
                    <Key frame="65" value="0.2908" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="25" value="-83.3238" />
                    <Key frame="35" value="-85.9605" />
                    <Key frame="45" value="-83.3238" />
                    <Key frame="55" value="-85.1647" />
                    <Key frame="65" value="-83.3238" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="25" value="-14.5876" />
                    <Key frame="35" value="-13.7966" />
                    <Key frame="45" value="-14.5876" />
                    <Key frame="55" value="-15.8229" />
                    <Key frame="65" value="-14.5876" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="25" value="-30.496" />
                    <Key frame="35" value="-28.7382" />
                    <Key frame="45" value="-30.496" />
                    <Key frame="55" value="-28.7382" />
                    <Key frame="65" value="-30.496" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="25" value="80.2476" />
                    <Key frame="35" value="81.478" />
                    <Key frame="45" value="80.2476" />
                    <Key frame="55" value="78.1333" />
                    <Key frame="65" value="80.2476" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="25" value="40.7842" />
                    <Key frame="35" value="39.3779" />
                    <Key frame="45" value="40.7842" />
                    <Key frame="55" value="59.9398" />
                    <Key frame="65" value="40.7842" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="25" value="-5.71537" />
                    <Key frame="35" value="13.8845" />
                    <Key frame="45" value="-5.71537" />
                    <Key frame="55" value="-32.4296" />
                    <Key frame="65" value="-5.71537" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="25" value="4.91954" />
                    <Key frame="35" value="8.4352" />
                    <Key frame="45" value="4.91954" />
                    <Key frame="55" value="15.6471" />
                    <Key frame="65" value="4.91954" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="LegRaise Demo" id="25" localization="8" tooltip="Init&#x0A;Init&#x0A;Init&#x0A;Hoch&#x0A;Runter" x="1103" y="406">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="105">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="15" value="9.92936" />
                    <Key frame="30" value="11.1598" />
                    <Key frame="45" value="9.92936" />
                    <Key frame="75" value="13.7087" />
                    <Key frame="105" value="13.7087" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="15" value="-0.881327" />
                    <Key frame="30" value="-0.793436" />
                    <Key frame="45" value="-0.881327" />
                    <Key frame="75" value="2.28279" />
                    <Key frame="105" value="2.28279" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="15" value="49.4806" />
                    <Key frame="30" value="49.5685" />
                    <Key frame="45" value="49.4806" />
                    <Key frame="75" value="45.1739" />
                    <Key frame="105" value="45.1739" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="15" value="-2.0191" />
                    <Key frame="30" value="-1.75543" />
                    <Key frame="45" value="-2.0191" />
                    <Key frame="75" value="-1.49175" />
                    <Key frame="105" value="-1.49175" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="15" value="-80.2427" />
                    <Key frame="30" value="-41.3067" />
                    <Key frame="45" value="-18.8943" />
                    <Key frame="75" value="-18.2791" />
                    <Key frame="105" value="-18.2791" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="15" value="-55.9894" />
                    <Key frame="30" value="-90.443" />
                    <Key frame="45" value="-96.5075" />
                    <Key frame="75" value="-96.5075" />
                    <Key frame="105" value="-96.5075" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="15" value="0.2892" />
                    <Key frame="30" value="0.2968" />
                    <Key frame="45" value="0.2892" />
                    <Key frame="75" value="0.2892" />
                    <Key frame="105" value="0.2892" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="15" value="21.2722" />
                    <Key frame="30" value="21.2722" />
                    <Key frame="45" value="21.2722" />
                    <Key frame="75" value="-84.1979" />
                    <Key frame="105" value="1.58445" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="15" value="6.24271" />
                    <Key frame="30" value="6.06693" />
                    <Key frame="45" value="6.24271" />
                    <Key frame="75" value="-0.261268" />
                    <Key frame="105" value="-0.261268" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="15" value="-29.1777" />
                    <Key frame="30" value="-29.0898" />
                    <Key frame="45" value="-29.1777" />
                    <Key frame="75" value="-5.44688" />
                    <Key frame="105" value="4.22121" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="15" value="-5.18802" />
                    <Key frame="30" value="-4.92435" />
                    <Key frame="45" value="-5.18802" />
                    <Key frame="75" value="-5.71537" />
                    <Key frame="105" value="-4.48488" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="15" value="79.4517" />
                    <Key frame="30" value="80.5943" />
                    <Key frame="45" value="107.138" />
                    <Key frame="75" value="108.104" />
                    <Key frame="105" value="108.104" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="15" value="2.0191" />
                    <Key frame="30" value="7.81997" />
                    <Key frame="45" value="5.62267" />
                    <Key frame="75" value="5.71056" />
                    <Key frame="105" value="5.71056" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="15" value="7.3805" />
                    <Key frame="30" value="15.0271" />
                    <Key frame="45" value="16.4334" />
                    <Key frame="75" value="16.4334" />
                    <Key frame="105" value="16.4334" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="15" value="37.5322" />
                    <Key frame="30" value="31.6434" />
                    <Key frame="45" value="30.5008" />
                    <Key frame="75" value="43.7725" />
                    <Key frame="105" value="41.6631" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="15" value="-16.1697" />
                    <Key frame="30" value="-20.3885" />
                    <Key frame="45" value="-16.1697" />
                    <Key frame="75" value="-1.14019" />
                    <Key frame="105" value="-1.31597" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="15" value="85.0816" />
                    <Key frame="30" value="45.4424" />
                    <Key frame="45" value="20.657" />
                    <Key frame="75" value="20.657" />
                    <Key frame="105" value="20.657" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="15" value="47.8107" />
                    <Key frame="30" value="68.641" />
                    <Key frame="45" value="78.4849" />
                    <Key frame="75" value="78.4849" />
                    <Key frame="105" value="78.4849" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="15" value="0.35" />
                    <Key frame="30" value="0.3528" />
                    <Key frame="45" value="0.35" />
                    <Key frame="75" value="0.35" />
                    <Key frame="105" value="0.35" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="15" value="-23.2937" />
                    <Key frame="30" value="-4.48488" />
                    <Key frame="45" value="13.8845" />
                    <Key frame="75" value="-88.4215" />
                    <Key frame="105" value="1.57965" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="15" value="-4.83163" />
                    <Key frame="30" value="-6.2379" />
                    <Key frame="45" value="-3.77694" />
                    <Key frame="75" value="-6.67737" />
                    <Key frame="105" value="-6.06212" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="15" value="-29.1777" />
                    <Key frame="30" value="-29.0898" />
                    <Key frame="45" value="-29.1777" />
                    <Key frame="75" value="-5.44688" />
                    <Key frame="105" value="4.22121" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="15" value="99.4079" />
                    <Key frame="30" value="49.2218" />
                    <Key frame="45" value="13.1862" />
                    <Key frame="75" value="-5.53478" />
                    <Key frame="105" value="-2.0191" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="15" value="75.5014" />
                    <Key frame="30" value="72.5131" />
                    <Key frame="45" value="108.021" />
                    <Key frame="75" value="108.812" />
                    <Key frame="105" value="108.812" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="15" value="-14.7682" />
                    <Key frame="30" value="-23.3816" />
                    <Key frame="45" value="-17.2292" />
                    <Key frame="75" value="-16.6139" />
                    <Key frame="105" value="-16.6139" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="15" value="11.3356" />
                    <Key frame="30" value="-5.18802" />
                    <Key frame="45" value="-10.2857" />
                    <Key frame="75" value="-10.2857" />
                    <Key frame="105" value="-10.2857" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="Sit" id="26" localization="8" tooltip="The robot goes from its current postition to the asked posture." x="92" y="324">
              <bitmap>media/images/box/box-diagram.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[class MyClass(GeneratedClass):
    def __init__(self):
        GeneratedClass.__init__(self, False)

    def onLoad(self):
        self.nTries = 0
        self.postureService = self.session().service("ALRobotPosture")
        pass

    def onUnload(self):
        self.postureService.stopMove()

    def onInput_onStart(self):
        if(self.nTries != self.getParameter("Maximum of tries")):
            self.nTries = self.getParameter("Maximum of tries")
            self.postureService.setMaxTryNumber(self.nTries)

        result = self.postureService.goToPosture(self.getParameter("Name"), self.getParameter("Speed (%)")/100.)
        if(result):
            self.success()
        else:
            self.failure()
        pass

    def onInput_onStop(self):
        self.onUnload() #~ it is recommanded to call onUnload of this box in a onStop method, as the code written in onUnload is used to stop the box as well
        pass]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="success" type="1" type_size="1" nature="1" inner="0" tooltip="Stimulated if the posture has been reached." id="4" />
              <Output name="failure" type="1" type_size="1" nature="1" inner="0" tooltip="Stimulated if the posture could not be reached." id="5" />
              <Parameter name="Name" inherits_from_parent="0" content_type="3" value="Sit" default_value="Stand" custom_choice="1" tooltip="Name of the posture to go to." id="6">
                <Choice value="Crouch" />
                <Choice value="LyingBack" />
                <Choice value="LyingBelly" />
                <Choice value="Sit" />
                <Choice value="SitRelax" />
                <Choice value="StandInit" />
                <Choice value="Stand" />
                <Choice value="StandZero" />
              </Parameter>
              <Parameter name="Speed (%)" inherits_from_parent="0" content_type="1" value="80" default_value="80" min="0" max="100" tooltip="Speed to go to the posture." id="7" />
              <Parameter name="Maximum of tries" inherits_from_parent="0" content_type="1" value="3" default_value="3" min="1" max="10" tooltip="The maximum number of fails of go to posture before stimulating the failure output." id="8" />
              <Resource name="All motors" type="Lock" timeout="0" />
              <Resource name="Stiffness" type="Lock" timeout="0" />
            </Box>
            <Box name="RussianTwist Demo" id="27" localization="8" tooltip="Position&#x0A;Links&#x0A;Position&#x0A;Rechts&#x0A;Position" x="1113" y="522">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="70">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="30" value="-3.25439" />
                    <Key frame="40" value="-3.25439" />
                    <Key frame="50" value="-3.25439" />
                    <Key frame="60" value="-3.25439" />
                    <Key frame="70" value="-3.25439" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="30" value="-2.63916" />
                    <Key frame="40" value="19.5975" />
                    <Key frame="50" value="-2.63916" />
                    <Key frame="60" value="-19.5975" />
                    <Key frame="70" value="-2.63916" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="30" value="49.3049" />
                    <Key frame="40" value="49.3049" />
                    <Key frame="50" value="49.3049" />
                    <Key frame="60" value="49.6612" />
                    <Key frame="70" value="49.3049" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="30" value="-0.612832" />
                    <Key frame="40" value="-0.612832" />
                    <Key frame="50" value="-0.612832" />
                    <Key frame="60" value="-1.145" />
                    <Key frame="70" value="-0.612832" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="30" value="-87.4499" />
                    <Key frame="40" value="-86.4831" />
                    <Key frame="50" value="-87.4499" />
                    <Key frame="60" value="-69.0853" />
                    <Key frame="70" value="-87.4499" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="30" value="-35.3349" />
                    <Key frame="40" value="-43.2451" />
                    <Key frame="50" value="-35.3349" />
                    <Key frame="60" value="-26.1014" />
                    <Key frame="70" value="-35.3349" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="30" value="0.286" />
                    <Key frame="40" value="0.286" />
                    <Key frame="50" value="0.286" />
                    <Key frame="60" value="0.2908" />
                    <Key frame="70" value="0.286" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="30" value="-82.3521" />
                    <Key frame="40" value="-85.1647" />
                    <Key frame="50" value="-82.3521" />
                    <Key frame="60" value="-85.9605" />
                    <Key frame="70" value="-82.3521" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="30" value="16.4382" />
                    <Key frame="40" value="15.8229" />
                    <Key frame="50" value="16.4382" />
                    <Key frame="60" value="13.7966" />
                    <Key frame="70" value="16.4382" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="30" value="-30.496" />
                    <Key frame="40" value="-28.7382" />
                    <Key frame="50" value="-30.496" />
                    <Key frame="60" value="-28.7382" />
                    <Key frame="70" value="-30.496" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="30" value="77.9576" />
                    <Key frame="40" value="78.1333" />
                    <Key frame="50" value="77.9576" />
                    <Key frame="60" value="81.478" />
                    <Key frame="70" value="77.9576" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="30" value="34.7148" />
                    <Key frame="40" value="59.9398" />
                    <Key frame="50" value="34.7148" />
                    <Key frame="60" value="39.3779" />
                    <Key frame="70" value="34.7148" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="30" value="11.3356" />
                    <Key frame="40" value="32.4296" />
                    <Key frame="50" value="11.3356" />
                    <Key frame="60" value="-13.8845" />
                    <Key frame="70" value="11.3356" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="30" value="-35.5107" />
                    <Key frame="40" value="-15.6471" />
                    <Key frame="50" value="-35.5107" />
                    <Key frame="60" value="-8.4352" />
                    <Key frame="70" value="-35.5107" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="30" value="49.5733" />
                    <Key frame="40" value="49.6612" />
                    <Key frame="50" value="49.5733" />
                    <Key frame="60" value="49.3049" />
                    <Key frame="70" value="49.5733" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="30" value="0.441859" />
                    <Key frame="40" value="1.145" />
                    <Key frame="50" value="0.441859" />
                    <Key frame="60" value="0.612832" />
                    <Key frame="70" value="0.441859" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="30" value="87.7183" />
                    <Key frame="40" value="69.0853" />
                    <Key frame="50" value="87.7183" />
                    <Key frame="60" value="86.4831" />
                    <Key frame="70" value="87.7183" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="30" value="48.2502" />
                    <Key frame="40" value="26.1014" />
                    <Key frame="50" value="48.2502" />
                    <Key frame="60" value="43.2451" />
                    <Key frame="70" value="48.2502" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="30" value="0.2908" />
                    <Key frame="40" value="0.2908" />
                    <Key frame="50" value="0.2908" />
                    <Key frame="60" value="0.286" />
                    <Key frame="70" value="0.2908" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="30" value="-83.3238" />
                    <Key frame="40" value="-85.9605" />
                    <Key frame="50" value="-83.3238" />
                    <Key frame="60" value="-85.1647" />
                    <Key frame="70" value="-83.3238" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="30" value="-14.5876" />
                    <Key frame="40" value="-13.7966" />
                    <Key frame="50" value="-14.5876" />
                    <Key frame="60" value="-15.8229" />
                    <Key frame="70" value="-14.5876" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="30" value="-30.496" />
                    <Key frame="40" value="-28.7382" />
                    <Key frame="50" value="-30.496" />
                    <Key frame="60" value="-28.7382" />
                    <Key frame="70" value="-30.496" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="30" value="80.2476" />
                    <Key frame="40" value="81.478" />
                    <Key frame="50" value="80.2476" />
                    <Key frame="60" value="78.1333" />
                    <Key frame="70" value="80.2476" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="30" value="40.7842" />
                    <Key frame="40" value="39.3779" />
                    <Key frame="50" value="40.7842" />
                    <Key frame="60" value="59.9398" />
                    <Key frame="70" value="40.7842" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="30" value="-5.71537" />
                    <Key frame="40" value="13.8845" />
                    <Key frame="50" value="-5.71537" />
                    <Key frame="60" value="-32.4296" />
                    <Key frame="70" value="-5.71537" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="30" value="4.91954" />
                    <Key frame="40" value="8.4352" />
                    <Key frame="50" value="4.91954" />
                    <Key frame="60" value="15.6471" />
                    <Key frame="70" value="4.91954" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="Hacker Demo" id="24" localization="8" tooltip="Init&#x0A;Whole Hacker Repeat&#x0A;Stand" x="1383" y="636">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="180">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="30" value="-12.3951" />
                    <Key frame="40" value="-12.3951" />
                    <Key frame="50" value="-12.3951" />
                    <Key frame="60" value="-12.3951" />
                    <Key frame="70" value="-12.3951" />
                    <Key frame="80" value="-14.944" />
                    <Key frame="90" value="-14.944" />
                    <Key frame="100" value="-14.944" />
                    <Key frame="110" value="-16.0866" />
                    <Key frame="120" value="-14.944" />
                    <Key frame="130" value="-14.944" />
                    <Key frame="140" value="-14.944" />
                    <Key frame="150" value="-12.3951" />
                    <Key frame="180" value="-11.0768" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="30" value="-0.61764" />
                    <Key frame="40" value="-0.61764" />
                    <Key frame="50" value="0.61764" />
                    <Key frame="60" value="-0.61764" />
                    <Key frame="70" value="0.61764" />
                    <Key frame="80" value="-0.441859" />
                    <Key frame="90" value="-0.441859" />
                    <Key frame="100" value="-0.441859" />
                    <Key frame="110" value="-0.178186" />
                    <Key frame="120" value="-0.441859" />
                    <Key frame="130" value="-0.441859" />
                    <Key frame="140" value="-0.441859" />
                    <Key frame="150" value="0.61764" />
                    <Key frame="180" value="0.349159" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="30" value="5.09532" />
                    <Key frame="40" value="5.09532" />
                    <Key frame="50" value="5.10013" />
                    <Key frame="60" value="5.09532" />
                    <Key frame="70" value="5.10013" />
                    <Key frame="80" value="5.35899" />
                    <Key frame="90" value="5.35899" />
                    <Key frame="100" value="5.35899" />
                    <Key frame="110" value="5.09532" />
                    <Key frame="120" value="5.35899" />
                    <Key frame="130" value="5.35899" />
                    <Key frame="140" value="5.35899" />
                    <Key frame="150" value="5.10013" />
                    <Key frame="180" value="5.35899" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="30" value="-5.79845" />
                    <Key frame="40" value="-5.79845" />
                    <Key frame="50" value="-6.5064" />
                    <Key frame="60" value="-5.79845" />
                    <Key frame="70" value="-6.5064" />
                    <Key frame="80" value="-6.06212" />
                    <Key frame="90" value="-6.06212" />
                    <Key frame="100" value="-6.06212" />
                    <Key frame="110" value="-6.06212" />
                    <Key frame="120" value="-6.06212" />
                    <Key frame="130" value="-6.06212" />
                    <Key frame="140" value="-6.06212" />
                    <Key frame="150" value="-6.5064" />
                    <Key frame="180" value="-6.4137" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="30" value="-12.4782" />
                    <Key frame="40" value="-12.4782" />
                    <Key frame="50" value="-12.5709" />
                    <Key frame="60" value="-12.4782" />
                    <Key frame="70" value="-12.5709" />
                    <Key frame="80" value="-11.7751" />
                    <Key frame="90" value="-1.57965" />
                    <Key frame="100" value="-1.57965" />
                    <Key frame="110" value="-2.72224" />
                    <Key frame="120" value="-1.57965" />
                    <Key frame="130" value="-1.57965" />
                    <Key frame="140" value="-11.7751" />
                    <Key frame="150" value="-12.5709" />
                    <Key frame="180" value="-23.2889" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="30" value="-80.0718" />
                    <Key frame="40" value="-80.0718" />
                    <Key frame="50" value="-83.7584" />
                    <Key frame="60" value="-80.0718" />
                    <Key frame="70" value="-83.7584" />
                    <Key frame="80" value="-82.7964" />
                    <Key frame="90" value="-74.6225" />
                    <Key frame="100" value="-74.6225" />
                    <Key frame="110" value="-75.062" />
                    <Key frame="120" value="-74.6225" />
                    <Key frame="130" value="-74.6225" />
                    <Key frame="140" value="-82.7964" />
                    <Key frame="150" value="-83.7584" />
                    <Key frame="180" value="-69.6127" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="30" value="0.2928" />
                    <Key frame="40" value="0.2928" />
                    <Key frame="50" value="0.3144" />
                    <Key frame="60" value="0.2928" />
                    <Key frame="70" value="0.3144" />
                    <Key frame="80" value="0.304" />
                    <Key frame="90" value="0.304" />
                    <Key frame="100" value="0.304" />
                    <Key frame="110" value="0.304" />
                    <Key frame="120" value="0.304" />
                    <Key frame="130" value="0.304" />
                    <Key frame="140" value="0.304" />
                    <Key frame="150" value="0.3144" />
                    <Key frame="180" value="0.302" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="30" value="4.04543" />
                    <Key frame="40" value="4.04543" />
                    <Key frame="50" value="7.3805" />
                    <Key frame="60" value="4.04543" />
                    <Key frame="70" value="7.3805" />
                    <Key frame="80" value="7.73688" />
                    <Key frame="90" value="7.73688" />
                    <Key frame="100" value="7.73688" />
                    <Key frame="110" value="8.17634" />
                    <Key frame="120" value="7.73688" />
                    <Key frame="130" value="7.73688" />
                    <Key frame="140" value="7.73688" />
                    <Key frame="150" value="7.3805" />
                    <Key frame="180" value="7.91266" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="30" value="5.97904" />
                    <Key frame="40" value="5.97904" />
                    <Key frame="50" value="6.85315" />
                    <Key frame="60" value="5.97904" />
                    <Key frame="70" value="6.85315" />
                    <Key frame="80" value="6.68218" />
                    <Key frame="90" value="6.68218" />
                    <Key frame="100" value="6.68218" />
                    <Key frame="110" value="6.68218" />
                    <Key frame="120" value="6.68218" />
                    <Key frame="130" value="6.68218" />
                    <Key frame="140" value="6.68218" />
                    <Key frame="150" value="6.85315" />
                    <Key frame="180" value="6.3306" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="30" value="-9.5778" />
                    <Key frame="40" value="-9.5778" />
                    <Key frame="50" value="-9.5778" />
                    <Key frame="60" value="-9.5778" />
                    <Key frame="70" value="-9.5778" />
                    <Key frame="80" value="-9.48991" />
                    <Key frame="90" value="-9.48991" />
                    <Key frame="100" value="-9.48991" />
                    <Key frame="110" value="-9.40201" />
                    <Key frame="120" value="-9.48991" />
                    <Key frame="130" value="-9.48991" />
                    <Key frame="140" value="-9.48991" />
                    <Key frame="150" value="-9.5778" />
                    <Key frame="180" value="-9.66569" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="30" value="-1.145" />
                    <Key frame="40" value="-1.145" />
                    <Key frame="50" value="-5.71056" />
                    <Key frame="60" value="-1.145" />
                    <Key frame="70" value="-5.71056" />
                    <Key frame="80" value="-4.57277" />
                    <Key frame="90" value="-4.57277" />
                    <Key frame="100" value="-4.57277" />
                    <Key frame="110" value="-4.74855" />
                    <Key frame="120" value="-4.57277" />
                    <Key frame="130" value="-4.57277" />
                    <Key frame="140" value="-4.57277" />
                    <Key frame="150" value="-5.71056" />
                    <Key frame="180" value="-5.10013" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="30" value="15.2029" />
                    <Key frame="40" value="15.2029" />
                    <Key frame="50" value="-18.1033" />
                    <Key frame="60" value="15.2029" />
                    <Key frame="70" value="-18.1033" />
                    <Key frame="80" value="-7.82477" />
                    <Key frame="90" value="-46.6729" />
                    <Key frame="100" value="-46.6729" />
                    <Key frame="110" value="-65.8333" />
                    <Key frame="120" value="-46.6729" />
                    <Key frame="130" value="-46.6729" />
                    <Key frame="140" value="-7.82477" />
                    <Key frame="150" value="-18.1033" />
                    <Key frame="180" value="81.5611" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="30" value="-4.3091" />
                    <Key frame="40" value="-4.3091" />
                    <Key frame="50" value="-1.0523" />
                    <Key frame="60" value="-4.3091" />
                    <Key frame="70" value="-1.0523" />
                    <Key frame="80" value="-1.58445" />
                    <Key frame="90" value="0.43705" />
                    <Key frame="100" value="0.43705" />
                    <Key frame="110" value="4.39218" />
                    <Key frame="120" value="0.43705" />
                    <Key frame="130" value="0.43705" />
                    <Key frame="140" value="-1.58445" />
                    <Key frame="150" value="-1.0523" />
                    <Key frame="180" value="11.2477" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="30" value="-1.40867" />
                    <Key frame="40" value="-1.40867" />
                    <Key frame="50" value="-19.0701" />
                    <Key frame="60" value="-1.40867" />
                    <Key frame="70" value="-19.0701" />
                    <Key frame="80" value="-17.405" />
                    <Key frame="90" value="-13.4498" />
                    <Key frame="100" value="-13.4498" />
                    <Key frame="110" value="-14.5924" />
                    <Key frame="120" value="-13.4498" />
                    <Key frame="130" value="-13.4498" />
                    <Key frame="140" value="-17.405" />
                    <Key frame="150" value="-19.0701" />
                    <Key frame="180" value="4.04062" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="30" value="5.10013" />
                    <Key frame="40" value="5.10013" />
                    <Key frame="50" value="5.09532" />
                    <Key frame="60" value="5.10013" />
                    <Key frame="70" value="5.09532" />
                    <Key frame="80" value="5.27591" />
                    <Key frame="90" value="5.27591" />
                    <Key frame="100" value="5.27591" />
                    <Key frame="110" value="4.92435" />
                    <Key frame="120" value="5.27591" />
                    <Key frame="130" value="5.27591" />
                    <Key frame="140" value="5.27591" />
                    <Key frame="150" value="5.09532" />
                    <Key frame="180" value="4.92435" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="30" value="6.5064" />
                    <Key frame="40" value="6.5064" />
                    <Key frame="50" value="5.79845" />
                    <Key frame="60" value="6.5064" />
                    <Key frame="70" value="5.79845" />
                    <Key frame="80" value="5.71537" />
                    <Key frame="90" value="5.71537" />
                    <Key frame="100" value="5.71537" />
                    <Key frame="110" value="5.89115" />
                    <Key frame="120" value="5.71537" />
                    <Key frame="130" value="5.71537" />
                    <Key frame="140" value="5.71537" />
                    <Key frame="150" value="5.79845" />
                    <Key frame="180" value="6.41851" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="30" value="12.5709" />
                    <Key frame="40" value="12.5709" />
                    <Key frame="50" value="12.4782" />
                    <Key frame="60" value="12.5709" />
                    <Key frame="70" value="12.4782" />
                    <Key frame="80" value="6.59429" />
                    <Key frame="90" value="6.59429" />
                    <Key frame="100" value="7.91266" />
                    <Key frame="110" value="6.77007" />
                    <Key frame="120" value="7.91266" />
                    <Key frame="130" value="6.59429" />
                    <Key frame="140" value="6.59429" />
                    <Key frame="150" value="12.4782" />
                    <Key frame="180" value="22.9421" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="30" value="83.7584" />
                    <Key frame="40" value="83.7584" />
                    <Key frame="50" value="80.0718" />
                    <Key frame="60" value="83.7584" />
                    <Key frame="70" value="80.0718" />
                    <Key frame="80" value="86.2194" />
                    <Key frame="90" value="86.2194" />
                    <Key frame="100" value="86.2194" />
                    <Key frame="110" value="86.9225" />
                    <Key frame="120" value="86.2194" />
                    <Key frame="130" value="86.2194" />
                    <Key frame="140" value="86.2194" />
                    <Key frame="150" value="80.0718" />
                    <Key frame="180" value="70.0473" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="30" value="0.3144" />
                    <Key frame="40" value="0.3144" />
                    <Key frame="50" value="0.2928" />
                    <Key frame="60" value="0.3144" />
                    <Key frame="70" value="0.2928" />
                    <Key frame="80" value="0.3092" />
                    <Key frame="90" value="0.3092" />
                    <Key frame="100" value="0.3092" />
                    <Key frame="110" value="0.3092" />
                    <Key frame="120" value="0.3092" />
                    <Key frame="130" value="0.3092" />
                    <Key frame="140" value="0.3092" />
                    <Key frame="150" value="0.2928" />
                    <Key frame="180" value="0.3072" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="30" value="7.3805" />
                    <Key frame="40" value="7.3805" />
                    <Key frame="50" value="4.04543" />
                    <Key frame="60" value="7.3805" />
                    <Key frame="70" value="4.04543" />
                    <Key frame="80" value="4.12851" />
                    <Key frame="90" value="4.12851" />
                    <Key frame="100" value="4.12851" />
                    <Key frame="110" value="3.86484" />
                    <Key frame="120" value="4.12851" />
                    <Key frame="130" value="4.12851" />
                    <Key frame="140" value="4.12851" />
                    <Key frame="150" value="4.04543" />
                    <Key frame="180" value="7.55628" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="30" value="-6.85315" />
                    <Key frame="40" value="-6.85315" />
                    <Key frame="50" value="-5.97904" />
                    <Key frame="60" value="-6.85315" />
                    <Key frame="70" value="-5.97904" />
                    <Key frame="80" value="-6.2379" />
                    <Key frame="90" value="-6.2379" />
                    <Key frame="100" value="-6.2379" />
                    <Key frame="110" value="-6.2379" />
                    <Key frame="120" value="-6.2379" />
                    <Key frame="130" value="-6.2379" />
                    <Key frame="140" value="-6.2379" />
                    <Key frame="150" value="-5.97904" />
                    <Key frame="180" value="-6.50159" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="30" value="-9.5778" />
                    <Key frame="40" value="-9.5778" />
                    <Key frame="50" value="-9.5778" />
                    <Key frame="60" value="-9.5778" />
                    <Key frame="70" value="-9.5778" />
                    <Key frame="80" value="-9.48991" />
                    <Key frame="90" value="-9.48991" />
                    <Key frame="100" value="-9.48991" />
                    <Key frame="110" value="-9.40201" />
                    <Key frame="120" value="-9.48991" />
                    <Key frame="130" value="-9.48991" />
                    <Key frame="140" value="-9.48991" />
                    <Key frame="150" value="-9.5778" />
                    <Key frame="180" value="-9.66569" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="30" value="-5.71056" />
                    <Key frame="40" value="-5.71056" />
                    <Key frame="50" value="-1.145" />
                    <Key frame="60" value="-5.71056" />
                    <Key frame="70" value="-1.145" />
                    <Key frame="80" value="-0.876518" />
                    <Key frame="90" value="-0.876518" />
                    <Key frame="100" value="-0.876518" />
                    <Key frame="110" value="-0.700723" />
                    <Key frame="120" value="-0.876518" />
                    <Key frame="130" value="-0.876518" />
                    <Key frame="140" value="-0.876518" />
                    <Key frame="150" value="-1.145" />
                    <Key frame="180" value="-4.91954" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="30" value="-18.1033" />
                    <Key frame="40" value="-18.1033" />
                    <Key frame="50" value="15.2029" />
                    <Key frame="60" value="-18.1033" />
                    <Key frame="70" value="15.2029" />
                    <Key frame="80" value="-26.8046" />
                    <Key frame="90" value="-26.8046" />
                    <Key frame="100" value="-65.7406" />
                    <Key frame="110" value="-61.0823" />
                    <Key frame="120" value="-65.7406" />
                    <Key frame="130" value="-26.8046" />
                    <Key frame="140" value="-26.8046" />
                    <Key frame="150" value="15.2029" />
                    <Key frame="180" value="82.4448" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="30" value="1.0523" />
                    <Key frame="40" value="1.0523" />
                    <Key frame="50" value="4.3091" />
                    <Key frame="60" value="1.0523" />
                    <Key frame="70" value="4.3091" />
                    <Key frame="80" value="5.97423" />
                    <Key frame="90" value="5.97423" />
                    <Key frame="100" value="2.72224" />
                    <Key frame="110" value="1.31597" />
                    <Key frame="120" value="2.72224" />
                    <Key frame="130" value="5.97423" />
                    <Key frame="140" value="5.97423" />
                    <Key frame="150" value="4.3091" />
                    <Key frame="180" value="-11.0768" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="30" value="19.0701" />
                    <Key frame="40" value="19.0701" />
                    <Key frame="50" value="1.40867" />
                    <Key frame="60" value="19.0701" />
                    <Key frame="70" value="1.40867" />
                    <Key frame="80" value="12.9177" />
                    <Key frame="90" value="12.9177" />
                    <Key frame="100" value="17.9275" />
                    <Key frame="110" value="17.2244" />
                    <Key frame="120" value="17.9275" />
                    <Key frame="130" value="12.9177" />
                    <Key frame="140" value="12.9177" />
                    <Key frame="150" value="1.40867" />
                    <Key frame="180" value="4.74374" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Link inputowner="2" indexofinput="2" outputowner="0" indexofoutput="2" />
          </Diagram>
        </BehaviorKeyframe>
      </BehaviorLayer>
    </Timeline>
  </Box>
</ChoregrapheProject>
