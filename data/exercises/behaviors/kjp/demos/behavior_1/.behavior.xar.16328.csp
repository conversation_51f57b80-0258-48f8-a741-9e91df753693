<?xml version="1.0" encoding="UTF-8" ?>
<ChoregrapheProject xmlns="http://www.ald.softbankrobotics.com/schema/choregraphe/project.xsd" xar_version="3">
  <Box name="root" id="-1" localization="8" tooltip="Root box of Choregraphe&apos;s behavior. Highest level possible." x="0" y="0">
    <bitmap>media/images/box/root.png</bitmap>
    <script language="4">
      <content>
        <![CDATA[]]>
      </content>
    </script>
    <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
    <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
    <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
    <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
    <Timeline enable="0">
      <BehaviorLayer name="behavior_layer1">
        <BehaviorKeyframe name="keyframe1" index="1">
          <Diagram scale="100">
            <Box name="DehnenNacken" id="1" localization="8" tooltip="Stand&#x0A;Vorne&#x0A;Halten&#x0A;Links&#x0A;Halten&#x0A;Rechts&#x0A;Halten&#x0A;Vorne&#x0A;Halten&#x0A;Stand" x="1380" y="56">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="179">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="37" value="-8.79157" />
                    <Key frame="39" value="28.6503" />
                    <Key frame="73" value="28.6503" />
                    <Key frame="75" value="16.697" />
                    <Key frame="110" value="16.697" />
                    <Key frame="112" value="14.3239" />
                    <Key frame="150" value="14.3239" />
                    <Key frame="152" value="28.6503" />
                    <Key frame="177" value="28.6503" />
                    <Key frame="179" value="-8.79157" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="37" value="-1.05711" />
                    <Key frame="39" value="-1.67234" />
                    <Key frame="73" value="-1.67234" />
                    <Key frame="75" value="86.1315" />
                    <Key frame="110" value="86.1315" />
                    <Key frame="112" value="-91.1461" />
                    <Key frame="150" value="-91.1461" />
                    <Key frame="152" value="-1.67234" />
                    <Key frame="177" value="-1.67234" />
                    <Key frame="179" value="-1.05711" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="37" value="5.71056" />
                    <Key frame="39" value="5.35899" />
                    <Key frame="73" value="5.35899" />
                    <Key frame="75" value="4.74374" />
                    <Key frame="110" value="4.74374" />
                    <Key frame="112" value="4.74374" />
                    <Key frame="150" value="4.74374" />
                    <Key frame="152" value="5.35899" />
                    <Key frame="177" value="5.35899" />
                    <Key frame="179" value="5.71056" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="37" value="-6.06212" />
                    <Key frame="39" value="-6.2379" />
                    <Key frame="73" value="-6.2379" />
                    <Key frame="75" value="-6.2379" />
                    <Key frame="110" value="-6.2379" />
                    <Key frame="112" value="-6.2379" />
                    <Key frame="150" value="-6.2379" />
                    <Key frame="152" value="-6.2379" />
                    <Key frame="177" value="-6.2379" />
                    <Key frame="179" value="-6.06212" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="37" value="-22.6737" />
                    <Key frame="39" value="-23.3768" />
                    <Key frame="73" value="-23.3768" />
                    <Key frame="75" value="-23.3768" />
                    <Key frame="110" value="-23.3768" />
                    <Key frame="112" value="-23.3768" />
                    <Key frame="150" value="-23.3768" />
                    <Key frame="152" value="-23.3768" />
                    <Key frame="177" value="-23.3768" />
                    <Key frame="179" value="-22.6737" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="37" value="-66.9759" />
                    <Key frame="39" value="-67.8548" />
                    <Key frame="73" value="-67.8548" />
                    <Key frame="75" value="-67.8548" />
                    <Key frame="110" value="-67.8548" />
                    <Key frame="112" value="-67.8548" />
                    <Key frame="150" value="-67.8548" />
                    <Key frame="152" value="-67.8548" />
                    <Key frame="177" value="-67.8548" />
                    <Key frame="179" value="-66.9759" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="37" value="0.3192" />
                    <Key frame="39" value="0.3124" />
                    <Key frame="73" value="0.3124" />
                    <Key frame="75" value="0.3124" />
                    <Key frame="110" value="0.3124" />
                    <Key frame="112" value="0.3124" />
                    <Key frame="150" value="0.3124" />
                    <Key frame="152" value="0.3124" />
                    <Key frame="177" value="0.3124" />
                    <Key frame="179" value="0.3192" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="37" value="8.00056" />
                    <Key frame="39" value="7.12163" />
                    <Key frame="73" value="7.12163" />
                    <Key frame="75" value="7.12163" />
                    <Key frame="110" value="7.12163" />
                    <Key frame="112" value="7.12163" />
                    <Key frame="150" value="7.12163" />
                    <Key frame="152" value="7.12163" />
                    <Key frame="177" value="7.12163" />
                    <Key frame="179" value="8.00056" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="37" value="6.24271" />
                    <Key frame="39" value="6.77007" />
                    <Key frame="73" value="6.77007" />
                    <Key frame="75" value="6.77007" />
                    <Key frame="110" value="6.77007" />
                    <Key frame="112" value="6.77007" />
                    <Key frame="150" value="6.77007" />
                    <Key frame="152" value="6.77007" />
                    <Key frame="177" value="6.77007" />
                    <Key frame="179" value="6.24271" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="37" value="-10.193" />
                    <Key frame="39" value="-10.193" />
                    <Key frame="73" value="-10.193" />
                    <Key frame="75" value="-9.5778" />
                    <Key frame="110" value="-9.5778" />
                    <Key frame="112" value="-9.5778" />
                    <Key frame="150" value="-9.5778" />
                    <Key frame="152" value="-10.193" />
                    <Key frame="177" value="-10.193" />
                    <Key frame="179" value="-10.193" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="37" value="-5.18802" />
                    <Key frame="39" value="-5.27591" />
                    <Key frame="73" value="-5.27591" />
                    <Key frame="75" value="-5.18802" />
                    <Key frame="110" value="-5.18802" />
                    <Key frame="112" value="-5.18802" />
                    <Key frame="150" value="-5.18802" />
                    <Key frame="152" value="-5.27591" />
                    <Key frame="177" value="-5.27591" />
                    <Key frame="179" value="-5.18802" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="37" value="83.2311" />
                    <Key frame="39" value="83.1432" />
                    <Key frame="73" value="83.1432" />
                    <Key frame="75" value="83.1432" />
                    <Key frame="110" value="83.1432" />
                    <Key frame="112" value="83.1432" />
                    <Key frame="150" value="83.1432" />
                    <Key frame="152" value="83.1432" />
                    <Key frame="177" value="83.1432" />
                    <Key frame="179" value="83.2311" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="37" value="11.3356" />
                    <Key frame="39" value="11.863" />
                    <Key frame="73" value="11.863" />
                    <Key frame="75" value="11.863" />
                    <Key frame="110" value="11.863" />
                    <Key frame="112" value="11.863" />
                    <Key frame="150" value="11.863" />
                    <Key frame="152" value="11.863" />
                    <Key frame="177" value="11.863" />
                    <Key frame="179" value="11.3356" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="37" value="4.39218" />
                    <Key frame="39" value="4.56796" />
                    <Key frame="73" value="4.56796" />
                    <Key frame="75" value="4.56796" />
                    <Key frame="110" value="4.56796" />
                    <Key frame="112" value="4.56796" />
                    <Key frame="150" value="4.56796" />
                    <Key frame="152" value="4.56796" />
                    <Key frame="177" value="4.56796" />
                    <Key frame="179" value="4.39218" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="37" value="5.62747" />
                    <Key frame="39" value="4.83644" />
                    <Key frame="73" value="4.83644" />
                    <Key frame="75" value="4.83644" />
                    <Key frame="110" value="4.83644" />
                    <Key frame="112" value="4.83644" />
                    <Key frame="150" value="4.83644" />
                    <Key frame="152" value="4.83644" />
                    <Key frame="177" value="4.83644" />
                    <Key frame="179" value="5.62747" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="37" value="6.77007" />
                    <Key frame="39" value="5.89115" />
                    <Key frame="73" value="5.89115" />
                    <Key frame="75" value="6.5064" />
                    <Key frame="110" value="6.5064" />
                    <Key frame="112" value="6.5064" />
                    <Key frame="150" value="6.5064" />
                    <Key frame="152" value="5.89115" />
                    <Key frame="177" value="5.89115" />
                    <Key frame="179" value="6.77007" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="37" value="22.7664" />
                    <Key frame="39" value="21.7996" />
                    <Key frame="73" value="21.7996" />
                    <Key frame="75" value="19.3386" />
                    <Key frame="110" value="19.3386" />
                    <Key frame="112" value="19.3386" />
                    <Key frame="150" value="19.3386" />
                    <Key frame="152" value="21.7996" />
                    <Key frame="177" value="21.7996" />
                    <Key frame="179" value="22.7664" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="37" value="68.0258" />
                    <Key frame="39" value="68.2016" />
                    <Key frame="73" value="68.2016" />
                    <Key frame="75" value="68.2016" />
                    <Key frame="110" value="68.2016" />
                    <Key frame="112" value="68.2016" />
                    <Key frame="150" value="68.2016" />
                    <Key frame="152" value="68.2016" />
                    <Key frame="177" value="68.2016" />
                    <Key frame="179" value="68.0258" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="37" value="0.318" />
                    <Key frame="39" value="0.3932" />
                    <Key frame="73" value="0.3932" />
                    <Key frame="75" value="0.4956" />
                    <Key frame="110" value="0.4956" />
                    <Key frame="112" value="0.4956" />
                    <Key frame="150" value="0.4956" />
                    <Key frame="152" value="0.3932" />
                    <Key frame="177" value="0.3932" />
                    <Key frame="179" value="0.318" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="37" value="7.11683" />
                    <Key frame="39" value="7.20472" />
                    <Key frame="73" value="7.20472" />
                    <Key frame="75" value="7.11683" />
                    <Key frame="110" value="7.11683" />
                    <Key frame="112" value="7.11683" />
                    <Key frame="150" value="7.11683" />
                    <Key frame="152" value="7.20472" />
                    <Key frame="177" value="7.20472" />
                    <Key frame="179" value="7.11683" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="37" value="-6.85315" />
                    <Key frame="39" value="-6.58948" />
                    <Key frame="73" value="-6.58948" />
                    <Key frame="75" value="-6.58948" />
                    <Key frame="110" value="-6.58948" />
                    <Key frame="112" value="-6.58948" />
                    <Key frame="150" value="-6.58948" />
                    <Key frame="152" value="-6.58948" />
                    <Key frame="177" value="-6.58948" />
                    <Key frame="179" value="-6.85315" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="37" value="-10.193" />
                    <Key frame="39" value="-10.193" />
                    <Key frame="73" value="-10.193" />
                    <Key frame="75" value="-9.5778" />
                    <Key frame="110" value="-9.5778" />
                    <Key frame="112" value="-9.5778" />
                    <Key frame="150" value="-9.5778" />
                    <Key frame="152" value="-10.193" />
                    <Key frame="177" value="-10.193" />
                    <Key frame="179" value="-10.193" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="37" value="-5.2711" />
                    <Key frame="39" value="-5.88634" />
                    <Key frame="73" value="-5.88634" />
                    <Key frame="75" value="-5.2711" />
                    <Key frame="110" value="-5.2711" />
                    <Key frame="112" value="-5.2711" />
                    <Key frame="150" value="-5.2711" />
                    <Key frame="152" value="-5.88634" />
                    <Key frame="177" value="-5.88634" />
                    <Key frame="179" value="-5.2711" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="37" value="82.2691" />
                    <Key frame="39" value="82.4448" />
                    <Key frame="73" value="82.4448" />
                    <Key frame="75" value="83.148" />
                    <Key frame="110" value="83.148" />
                    <Key frame="112" value="83.148" />
                    <Key frame="150" value="83.148" />
                    <Key frame="152" value="82.4448" />
                    <Key frame="177" value="82.4448" />
                    <Key frame="179" value="82.2691" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="37" value="-11.5162" />
                    <Key frame="39" value="-5.80326" />
                    <Key frame="73" value="-5.80326" />
                    <Key frame="75" value="-1.67234" />
                    <Key frame="110" value="-1.67234" />
                    <Key frame="112" value="-1.05711" />
                    <Key frame="150" value="-1.05711" />
                    <Key frame="152" value="-5.80326" />
                    <Key frame="177" value="-5.80326" />
                    <Key frame="179" value="-11.5162" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="37" value="4.83163" />
                    <Key frame="39" value="4.65585" />
                    <Key frame="73" value="4.65585" />
                    <Key frame="75" value="4.65585" />
                    <Key frame="110" value="4.65585" />
                    <Key frame="112" value="4.65585" />
                    <Key frame="150" value="4.65585" />
                    <Key frame="152" value="4.65585" />
                    <Key frame="177" value="4.65585" />
                    <Key frame="179" value="4.83163" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="Stand" id="2" localization="8" tooltip="The robot goes from its current postition to the asked posture." x="217" y="48">
              <bitmap>media/images/box/box-diagram.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[class MyClass(GeneratedClass):
    def __init__(self):
        GeneratedClass.__init__(self, False)

    def onLoad(self):
        self.nTries = 0
        self.postureService = self.session().service("ALRobotPosture")
        pass

    def onUnload(self):
        self.postureService.stopMove()

    def onInput_onStart(self):
        if(self.nTries != self.getParameter("Maximum of tries")):
            self.nTries = self.getParameter("Maximum of tries")
            self.postureService.setMaxTryNumber(self.nTries)

        result = self.postureService.goToPosture(self.getParameter("Name"), self.getParameter("Speed (%)")/100.)
        if(result):
            self.success()
        else:
            self.failure()
        pass

    def onInput_onStop(self):
        self.onUnload() #~ it is recommanded to call onUnload of this box in a onStop method, as the code written in onUnload is used to stop the box as well
        pass]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="success" type="1" type_size="1" nature="1" inner="0" tooltip="Stimulated if the posture has been reached." id="4" />
              <Output name="failure" type="1" type_size="1" nature="1" inner="0" tooltip="Stimulated if the posture could not be reached." id="5" />
              <Parameter name="Name" inherits_from_parent="0" content_type="3" value="Stand" default_value="Stand" custom_choice="1" tooltip="Name of the posture to go to." id="6">
                <Choice value="Crouch" />
                <Choice value="LyingBack" />
                <Choice value="LyingBelly" />
                <Choice value="Sit" />
                <Choice value="SitRelax" />
                <Choice value="StandInit" />
                <Choice value="Stand" />
                <Choice value="StandZero" />
              </Parameter>
              <Parameter name="Speed (%)" inherits_from_parent="0" content_type="1" value="80" default_value="80" min="0" max="100" tooltip="Speed to go to the posture." id="7" />
              <Parameter name="Maximum of tries" inherits_from_parent="0" content_type="1" value="3" default_value="3" min="1" max="10" tooltip="The maximum number of fails of go to posture before stimulating the failure output." id="8" />
              <Resource name="All motors" type="Lock" timeout="0" />
              <Resource name="Stiffness" type="Lock" timeout="0" />
            </Box>
            <Box name="DehnenRumpf" id="3" localization="8" tooltip="Stand&#x0A;Kopf vorne&#x0A;Halten&#x0A;Beugen&#x0A;Halten&#x0A;Kopf vorne&#x0A;Halten&#x0A;Stand&#x0A;" x="1060" y="155">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="210">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="nao">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="2" value="-9.31893" />
                    <Key frame="30" value="33.9238" />
                    <Key frame="39" value="33.9238" />
                    <Key frame="131" value="25.7499" />
                    <Key frame="188" value="33.9238" />
                    <Key frame="210" value="-9.31893" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="2" value="0.349159" />
                    <Key frame="30" value="-1.145" />
                    <Key frame="39" value="-1.145" />
                    <Key frame="131" value="-4.48488" />
                    <Key frame="188" value="-1.145" />
                    <Key frame="210" value="0.349159" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="2" value="5.71056" />
                    <Key frame="30" value="4.30429" />
                    <Key frame="39" value="4.30429" />
                    <Key frame="131" value="6.2379" />
                    <Key frame="188" value="4.30429" />
                    <Key frame="210" value="5.71056" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="2" value="-6.50159" />
                    <Key frame="30" value="-7.3805" />
                    <Key frame="39" value="-7.3805" />
                    <Key frame="131" value="-12.1267" />
                    <Key frame="188" value="-7.3805" />
                    <Key frame="210" value="-6.50159" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="2" value="-22.5858" />
                    <Key frame="30" value="-19.2459" />
                    <Key frame="39" value="-19.2459" />
                    <Key frame="131" value="-11.4235" />
                    <Key frame="188" value="-19.2459" />
                    <Key frame="210" value="-22.5858" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="2" value="-69.5248" />
                    <Key frame="30" value="-69.4369" />
                    <Key frame="39" value="-69.4369" />
                    <Key frame="131" value="-73.9193" />
                    <Key frame="188" value="-69.4369" />
                    <Key frame="210" value="-69.5248" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="2" value="0.2976" />
                    <Key frame="30" value="0.294" />
                    <Key frame="39" value="0.294" />
                    <Key frame="131" value="0.282" />
                    <Key frame="188" value="0.294" />
                    <Key frame="210" value="0.2976" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="2" value="7.91266" />
                    <Key frame="30" value="5.80326" />
                    <Key frame="39" value="5.80326" />
                    <Key frame="131" value="-11.1598" />
                    <Key frame="188" value="5.80326" />
                    <Key frame="210" value="7.91266" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="2" value="6.68218" />
                    <Key frame="30" value="6.5064" />
                    <Key frame="39" value="6.5064" />
                    <Key frame="131" value="3.43018" />
                    <Key frame="188" value="6.5064" />
                    <Key frame="210" value="6.68218" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="2" value="-9.13833" />
                    <Key frame="30" value="-12.2146" />
                    <Key frame="39" value="-12.2146" />
                    <Key frame="131" value="-22.4979" />
                    <Key frame="188" value="-12.2146" />
                    <Key frame="210" value="-9.13833" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="2" value="-4.92435" />
                    <Key frame="30" value="-5.3638" />
                    <Key frame="39" value="-5.3638" />
                    <Key frame="131" value="-4.92435" />
                    <Key frame="188" value="-5.3638" />
                    <Key frame="210" value="-4.92435" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="2" value="83.1432" />
                    <Key frame="30" value="80.5943" />
                    <Key frame="39" value="80.5943" />
                    <Key frame="131" value="49.4806" />
                    <Key frame="188" value="80.5943" />
                    <Key frame="210" value="83.1432" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="2" value="12.5661" />
                    <Key frame="30" value="7.99575" />
                    <Key frame="39" value="7.99575" />
                    <Key frame="131" value="-2.63916" />
                    <Key frame="188" value="7.99575" />
                    <Key frame="210" value="12.5661" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="2" value="8.17153" />
                    <Key frame="30" value="8.52309" />
                    <Key frame="39" value="8.52309" />
                    <Key frame="131" value="8.52309" />
                    <Key frame="188" value="8.52309" />
                    <Key frame="210" value="8.17153" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="2" value="5.71537" />
                    <Key frame="30" value="6.15482" />
                    <Key frame="39" value="6.15482" />
                    <Key frame="131" value="14.7682" />
                    <Key frame="188" value="6.15482" />
                    <Key frame="210" value="5.71537" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="2" value="6.5064" />
                    <Key frame="30" value="8.44001" />
                    <Key frame="39" value="8.44001" />
                    <Key frame="131" value="16.9655" />
                    <Key frame="188" value="8.44001" />
                    <Key frame="210" value="6.5064" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="2" value="22.239" />
                    <Key frame="30" value="19.6023" />
                    <Key frame="39" value="19.6023" />
                    <Key frame="131" value="13.7135" />
                    <Key frame="188" value="19.6023" />
                    <Key frame="210" value="22.239" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="2" value="68.9926" />
                    <Key frame="30" value="69.52" />
                    <Key frame="39" value="69.52" />
                    <Key frame="131" value="72.5962" />
                    <Key frame="188" value="69.52" />
                    <Key frame="210" value="68.9926" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="2" value="0.3" />
                    <Key frame="30" value="0.302" />
                    <Key frame="39" value="0.302" />
                    <Key frame="131" value="0.2884" />
                    <Key frame="188" value="0.302" />
                    <Key frame="210" value="0.3" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="2" value="7.55628" />
                    <Key frame="30" value="2.1949" />
                    <Key frame="39" value="2.1949" />
                    <Key frame="131" value="-23.1179" />
                    <Key frame="188" value="2.1949" />
                    <Key frame="210" value="7.55628" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="2" value="-7.11683" />
                    <Key frame="30" value="-7.73207" />
                    <Key frame="39" value="-7.73207" />
                    <Key frame="131" value="-13.2692" />
                    <Key frame="188" value="-7.73207" />
                    <Key frame="210" value="-7.11683" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="2" value="-9.13833" />
                    <Key frame="30" value="-12.2146" />
                    <Key frame="39" value="-12.2146" />
                    <Key frame="131" value="-22.4979" />
                    <Key frame="188" value="-12.2146" />
                    <Key frame="210" value="-9.13833" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="2" value="-5.00743" />
                    <Key frame="30" value="-5.2711" />
                    <Key frame="39" value="-5.2711" />
                    <Key frame="131" value="-5.2711" />
                    <Key frame="188" value="-5.2711" />
                    <Key frame="210" value="-5.00743" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="2" value="82.0054" />
                    <Key frame="30" value="79.5444" />
                    <Key frame="39" value="79.5444" />
                    <Key frame="131" value="55.9894" />
                    <Key frame="188" value="79.5444" />
                    <Key frame="210" value="82.0054" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="2" value="-12.483" />
                    <Key frame="30" value="-9.14314" />
                    <Key frame="39" value="-9.14314" />
                    <Key frame="131" value="-2.72705" />
                    <Key frame="188" value="-9.14314" />
                    <Key frame="210" value="-12.483" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="2" value="3.60116" />
                    <Key frame="30" value="3.68905" />
                    <Key frame="39" value="3.68905" />
                    <Key frame="131" value="-4.48488" />
                    <Key frame="188" value="3.68905" />
                    <Key frame="210" value="3.60116" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="DehnenWade" id="4" localization="8" tooltip="Stand&#x0A;Dehnung&#x0A;Halten&#x0A;Stand" x="278" y="439">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="90">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="32" value="-10.901" />
                    <Key frame="80" value="-17.2292" />
                    <Key frame="90" value="-17.2292" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="32" value="0.612832" />
                    <Key frame="80" value="0.876518" />
                    <Key frame="90" value="0.876518" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="32" value="5.18321" />
                    <Key frame="80" value="-12.1315" />
                    <Key frame="90" value="-12.1315" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="32" value="-6.4137" />
                    <Key frame="80" value="-2.98591" />
                    <Key frame="90" value="-2.98591" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="32" value="-22.9373" />
                    <Key frame="80" value="-10.8962" />
                    <Key frame="90" value="-10.8962" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="32" value="-69.9642" />
                    <Key frame="80" value="-87.6305" />
                    <Key frame="90" value="-87.6305" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="32" value="0.3008" />
                    <Key frame="80" value="0.2972" />
                    <Key frame="90" value="0.2972" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="32" value="7.12163" />
                    <Key frame="80" value="-45.965" />
                    <Key frame="90" value="-45.965" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="32" value="6.24271" />
                    <Key frame="80" value="5.27591" />
                    <Key frame="90" value="5.27591" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="32" value="-10.2809" />
                    <Key frame="80" value="5.53958" />
                    <Key frame="90" value="5.53958" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="32" value="-5.53958" />
                    <Key frame="80" value="57.303" />
                    <Key frame="90" value="57.303" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="32" value="84.8131" />
                    <Key frame="80" value="95.448" />
                    <Key frame="90" value="95.448" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="32" value="12.2146" />
                    <Key frame="80" value="10.1051" />
                    <Key frame="90" value="10.1051" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="32" value="4.74374" />
                    <Key frame="80" value="-15.8229" />
                    <Key frame="90" value="-15.8229" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="32" value="5.01224" />
                    <Key frame="80" value="-28.7382" />
                    <Key frame="90" value="-28.7382" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="32" value="6.3306" />
                    <Key frame="80" value="7.20952" />
                    <Key frame="90" value="7.20952" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="32" value="22.5906" />
                    <Key frame="80" value="5.01224" />
                    <Key frame="90" value="5.01224" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="32" value="70.0473" />
                    <Key frame="80" value="87.5378" />
                    <Key frame="90" value="87.5378" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="32" value="0.294" />
                    <Key frame="80" value="0.2856" />
                    <Key frame="90" value="0.2856" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="32" value="7.20472" />
                    <Key frame="80" value="27.8593" />
                    <Key frame="90" value="27.8593" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="32" value="-6.4137" />
                    <Key frame="80" value="-4.12851" />
                    <Key frame="90" value="-4.12851" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="32" value="-10.2809" />
                    <Key frame="80" value="5.53958" />
                    <Key frame="90" value="5.53958" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="32" value="-5.71056" />
                    <Key frame="80" value="-2.98591" />
                    <Key frame="90" value="-2.98591" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="32" value="81.7417" />
                    <Key frame="80" value="79.896" />
                    <Key frame="90" value="79.896" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="32" value="-10.8131" />
                    <Key frame="80" value="-1.05711" />
                    <Key frame="90" value="-1.05711" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="32" value="7.20472" />
                    <Key frame="80" value="22.41" />
                    <Key frame="90" value="22.41" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="DehnenOberschenkel" id="6" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="511" y="522">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="48">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="5" value="-13.5377" />
                    <Key frame="20" value="6.15001" />
                    <Key frame="39" value="-32.1708" />
                    <Key frame="48" value="-32.1708" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="5" value="-39.29" />
                    <Key frame="20" value="3.0738" />
                    <Key frame="39" value="-0.705531" />
                    <Key frame="48" value="-0.705531" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="5" value="42.5372" />
                    <Key frame="20" value="42.3614" />
                    <Key frame="39" value="42.5372" />
                    <Key frame="48" value="42.5372" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="5" value="-0.612832" />
                    <Key frame="20" value="-0.261268" />
                    <Key frame="39" value="0.00240423" />
                    <Key frame="48" value="0.00240423" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="5" value="-60.7308" />
                    <Key frame="20" value="-7.73207" />
                    <Key frame="39" value="-8.52309" />
                    <Key frame="48" value="-8.52309" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="5" value="-10.3736" />
                    <Key frame="20" value="-7.64898" />
                    <Key frame="39" value="-9.05525" />
                    <Key frame="48" value="-9.05525" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="5" value="0.0252" />
                    <Key frame="20" value="0.0556" />
                    <Key frame="39" value="0.0707999" />
                    <Key frame="48" value="0.0707999" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="5" value="17.0534" />
                    <Key frame="20" value="16.7897" />
                    <Key frame="39" value="17.0534" />
                    <Key frame="48" value="17.0534" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="5" value="5.18802" />
                    <Key frame="20" value="5.18802" />
                    <Key frame="39" value="5.18802" />
                    <Key frame="48" value="5.18802" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="5" value="-1.31597" />
                    <Key frame="20" value="-1.22808" />
                    <Key frame="39" value="-1.31597" />
                    <Key frame="48" value="-1.31597" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="5" value="-5.80326" />
                    <Key frame="20" value="-5.97904" />
                    <Key frame="39" value="-5.80326" />
                    <Key frame="48" value="-5.80326" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="5" value="-70.4037" />
                    <Key frame="20" value="-78.7534" />
                    <Key frame="39" value="-76.0287" />
                    <Key frame="48" value="-76.0287" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="5" value="7.29261" />
                    <Key frame="20" value="5.71056" />
                    <Key frame="39" value="5.2711" />
                    <Key frame="48" value="5.2711" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="5" value="-89.652" />
                    <Key frame="20" value="-18.4597" />
                    <Key frame="39" value="-23.6453" />
                    <Key frame="48" value="-23.6453" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="5" value="52.8253" />
                    <Key frame="20" value="53.0011" />
                    <Key frame="39" value="52.8253" />
                    <Key frame="48" value="52.8253" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="5" value="-0.0854867" />
                    <Key frame="20" value="0.0902951" />
                    <Key frame="39" value="-0.0854867" />
                    <Key frame="48" value="-0.0854867" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="5" value="84.8179" />
                    <Key frame="20" value="23.8211" />
                    <Key frame="39" value="26.1941" />
                    <Key frame="48" value="26.1941" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="5" value="40.0762" />
                    <Key frame="20" value="75.6724" />
                    <Key frame="39" value="75.3208" />
                    <Key frame="48" value="75.3208" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="5" value="0.1848" />
                    <Key frame="20" value="0.192" />
                    <Key frame="39" value="0.0156" />
                    <Key frame="48" value="0.2288" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="5" value="7.46839" />
                    <Key frame="20" value="7.81997" />
                    <Key frame="39" value="16.2576" />
                    <Key frame="48" value="16.2576" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="5" value="-9.66569" />
                    <Key frame="20" value="-9.75358" />
                    <Key frame="39" value="-9.66569" />
                    <Key frame="48" value="-9.66569" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="5" value="-1.31597" />
                    <Key frame="20" value="-1.22808" />
                    <Key frame="39" value="-1.31597" />
                    <Key frame="48" value="-1.31597" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="5" value="-4.74374" />
                    <Key frame="20" value="-4.91954" />
                    <Key frame="39" value="100.814" />
                    <Key frame="48" value="100.814" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="5" value="-41.2188" />
                    <Key frame="20" value="-63.807" />
                    <Key frame="39" value="-60.4671" />
                    <Key frame="48" value="-60.4671" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="5" value="-25.2273" />
                    <Key frame="20" value="-5.89115" />
                    <Key frame="39" value="-5.27591" />
                    <Key frame="48" value="-5.27591" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="5" value="90.1745" />
                    <Key frame="20" value="-4.57277" />
                    <Key frame="39" value="-0.266077" />
                    <Key frame="48" value="-0.266077" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="Lying Back" id="8" localization="8" tooltip="The robot goes from its current postition to the asked posture." x="146" y="197">
              <bitmap>media/images/box/box-diagram.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[class MyClass(GeneratedClass):
    def __init__(self):
        GeneratedClass.__init__(self, False)

    def onLoad(self):
        self.nTries = 0
        self.postureService = self.session().service("ALRobotPosture")
        pass

    def onUnload(self):
        self.postureService.stopMove()

    def onInput_onStart(self):
        if(self.nTries != self.getParameter("Maximum of tries")):
            self.nTries = self.getParameter("Maximum of tries")
            self.postureService.setMaxTryNumber(self.nTries)

        result = self.postureService.goToPosture(self.getParameter("Name"), self.getParameter("Speed (%)")/100.)
        if(result):
            self.success()
        else:
            self.failure()
        pass

    def onInput_onStop(self):
        self.onUnload() #~ it is recommanded to call onUnload of this box in a onStop method, as the code written in onUnload is used to stop the box as well
        pass]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="success" type="1" type_size="1" nature="1" inner="0" tooltip="Stimulated if the posture has been reached." id="4" />
              <Output name="failure" type="1" type_size="1" nature="1" inner="0" tooltip="Stimulated if the posture could not be reached." id="5" />
              <Parameter name="Name" inherits_from_parent="0" content_type="3" value="LyingBack" default_value="Stand" custom_choice="1" tooltip="Name of the posture to go to." id="6">
                <Choice value="Crouch" />
                <Choice value="LyingBack" />
                <Choice value="LyingBelly" />
                <Choice value="Sit" />
                <Choice value="SitRelax" />
                <Choice value="StandInit" />
                <Choice value="Stand" />
                <Choice value="StandZero" />
              </Parameter>
              <Parameter name="Speed (%)" inherits_from_parent="0" content_type="1" value="80" default_value="80" min="0" max="100" tooltip="Speed to go to the posture." id="7" />
              <Parameter name="Maximum of tries" inherits_from_parent="0" content_type="1" value="3" default_value="3" min="1" max="10" tooltip="The maximum number of fails of go to posture before stimulating the failure output." id="8" />
              <Resource name="All motors" type="Lock" timeout="0" />
              <Resource name="Stiffness" type="Lock" timeout="0" />
            </Box>
            <Box name="Squat" id="10" localization="8" tooltip="Arme vor&#x0A;Runter&#x0A;Halten&#x0A;Hoch&#x0A;Stand" x="1253" y="290">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="90">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="15" value="-9.14314" />
                    <Key frame="40" value="-6.15482" />
                    <Key frame="65" value="-6.15482" />
                    <Key frame="90" value="-9.14314" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="15" value="-0.529749" />
                    <Key frame="40" value="-1.145" />
                    <Key frame="65" value="-1.145" />
                    <Key frame="90" value="-0.529749" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="15" value="4.83163" />
                    <Key frame="40" value="-32.0829" />
                    <Key frame="65" value="-32.0829" />
                    <Key frame="90" value="4.83163" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="15" value="-6.58948" />
                    <Key frame="40" value="-7.02893" />
                    <Key frame="65" value="-7.02893" />
                    <Key frame="90" value="-6.58948" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="15" value="-2.1949" />
                    <Key frame="40" value="-2.1949" />
                    <Key frame="65" value="-2.1949" />
                    <Key frame="90" value="-2.1949" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="15" value="-54.1437" />
                    <Key frame="40" value="-54.1437" />
                    <Key frame="65" value="-54.1437" />
                    <Key frame="90" value="-54.1437" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="15" value="0.294" />
                    <Key frame="40" value="0.294" />
                    <Key frame="65" value="0.294" />
                    <Key frame="90" value="0.294" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="15" value="7.4732" />
                    <Key frame="40" value="-52.9084" />
                    <Key frame="65" value="-52.9084" />
                    <Key frame="90" value="7.4732" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="15" value="7.12163" />
                    <Key frame="40" value="8.17634" />
                    <Key frame="65" value="8.17634" />
                    <Key frame="90" value="7.12163" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="15" value="-10.1051" />
                    <Key frame="40" value="-16.5212" />
                    <Key frame="65" value="-16.5212" />
                    <Key frame="90" value="-10.1051" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="15" value="-5.18802" />
                    <Key frame="40" value="88.6804" />
                    <Key frame="65" value="88.6804" />
                    <Key frame="90" value="-5.18802" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="15" value="2.89802" />
                    <Key frame="40" value="4.83163" />
                    <Key frame="65" value="4.83163" />
                    <Key frame="90" value="2.89802" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="15" value="-9.84628" />
                    <Key frame="40" value="-9.14314" />
                    <Key frame="65" value="-9.14314" />
                    <Key frame="90" value="-9.84628" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="15" value="26.7167" />
                    <Key frame="40" value="26.7167" />
                    <Key frame="65" value="26.7167" />
                    <Key frame="90" value="26.7167" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="15" value="5.01224" />
                    <Key frame="40" value="-33.0449" />
                    <Key frame="65" value="-33.0449" />
                    <Key frame="90" value="5.01224" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="15" value="7.03374" />
                    <Key frame="40" value="10.6373" />
                    <Key frame="65" value="10.6373" />
                    <Key frame="90" value="7.03374" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="15" value="5.62747" />
                    <Key frame="40" value="5.62747" />
                    <Key frame="65" value="5.62747" />
                    <Key frame="90" value="5.62747" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="15" value="51.5022" />
                    <Key frame="40" value="51.5022" />
                    <Key frame="65" value="51.5022" />
                    <Key frame="90" value="51.5022" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="15" value="0.2852" />
                    <Key frame="40" value="0.2852" />
                    <Key frame="65" value="0.2852" />
                    <Key frame="90" value="0.2852" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="15" value="7.20472" />
                    <Key frame="40" value="-46.585" />
                    <Key frame="65" value="-46.585" />
                    <Key frame="90" value="7.20472" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="15" value="-7.20472" />
                    <Key frame="40" value="-7.46839" />
                    <Key frame="65" value="-7.46839" />
                    <Key frame="90" value="-7.20472" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="15" value="-10.1051" />
                    <Key frame="40" value="-16.5212" />
                    <Key frame="65" value="-16.5212" />
                    <Key frame="90" value="-10.1051" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="15" value="-5.62267" />
                    <Key frame="40" value="83.6753" />
                    <Key frame="65" value="83.6753" />
                    <Key frame="90" value="-5.62267" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="15" value="3.51807" />
                    <Key frame="40" value="4.83644" />
                    <Key frame="65" value="4.83644" />
                    <Key frame="90" value="3.51807" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="15" value="8.17153" />
                    <Key frame="40" value="6.94104" />
                    <Key frame="65" value="6.94104" />
                    <Key frame="90" value="8.17153" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="15" value="-33.9286" />
                    <Key frame="40" value="-33.9286" />
                    <Key frame="65" value="-33.9286" />
                    <Key frame="90" value="-33.9286" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="Klappmesser" id="11" localization="8" tooltip="LyingBack&#x0A;Ausgestreckt&#x0A;Klappt&#x0A;Ausgestreckt&#x0A;LyingBack" x="401" y="357">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="110">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="nao">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="5" value="10.0173" />
                    <Key frame="50" value="13.7087" />
                    <Key frame="80" value="13.7087" />
                    <Key frame="110" value="13.7087" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="5" value="-1.32078" />
                    <Key frame="50" value="-0.705531" />
                    <Key frame="80" value="-0.705531" />
                    <Key frame="110" value="-0.705531" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="5" value="49.5685" />
                    <Key frame="50" value="-1.145" />
                    <Key frame="80" value="-1.145" />
                    <Key frame="110" value="-1.145" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="5" value="-2.1949" />
                    <Key frame="50" value="-2.107" />
                    <Key frame="80" value="-8.34731" />
                    <Key frame="110" value="-2.107" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="5" value="-87.5378" />
                    <Key frame="50" value="-2.81013" />
                    <Key frame="80" value="-1.0523" />
                    <Key frame="110" value="-2.81013" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="5" value="-49.6612" />
                    <Key frame="50" value="-85.2574" />
                    <Key frame="80" value="-85.2574" />
                    <Key frame="110" value="-85.2574" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="5" value="0.3904" />
                    <Key frame="50" value="0.8496" />
                    <Key frame="80" value="0.8496" />
                    <Key frame="110" value="0.8496" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="5" value="21.5359" />
                    <Key frame="50" value="24.7879" />
                    <Key frame="80" value="-57.6546" />
                    <Key frame="110" value="24.7879" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="5" value="6.41851" />
                    <Key frame="50" value="6.94585" />
                    <Key frame="80" value="0.178186" />
                    <Key frame="110" value="6.94585" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="5" value="-29.2655" />
                    <Key frame="50" value="-29.2655" />
                    <Key frame="80" value="-5.00743" />
                    <Key frame="110" value="-29.2655" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="5" value="-5.3638" />
                    <Key frame="50" value="-2.99072" />
                    <Key frame="80" value="-4.74855" />
                    <Key frame="110" value="-2.99072" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="5" value="107.665" />
                    <Key frame="50" value="-89.2125" />
                    <Key frame="80" value="3.51326" />
                    <Key frame="110" value="-89.2125" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="5" value="13.8845" />
                    <Key frame="50" value="-0.00240423" />
                    <Key frame="80" value="-10.3736" />
                    <Key frame="110" value="-0.00240423" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="5" value="12.7419" />
                    <Key frame="50" value="68.2016" />
                    <Key frame="80" value="86.2194" />
                    <Key frame="110" value="68.2016" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="5" value="37.0048" />
                    <Key frame="50" value="16.3503" />
                    <Key frame="80" value="4.83644" />
                    <Key frame="110" value="16.3503" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="5" value="-16.2576" />
                    <Key frame="50" value="-16.2576" />
                    <Key frame="80" value="-4.48007" />
                    <Key frame="110" value="-16.2576" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="5" value="86.9273" />
                    <Key frame="50" value="3.51807" />
                    <Key frame="80" value="1.49656" />
                    <Key frame="110" value="3.51807" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="5" value="49.6564" />
                    <Key frame="50" value="70.3989" />
                    <Key frame="80" value="70.3989" />
                    <Key frame="110" value="70.3989" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="5" value="0.352" />
                    <Key frame="50" value="0.8288" />
                    <Key frame="80" value="0.8288" />
                    <Key frame="110" value="0.8288" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="5" value="-23.03" />
                    <Key frame="50" value="26.3651" />
                    <Key frame="80" value="-58.9778" />
                    <Key frame="110" value="26.3651" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="5" value="-4.65585" />
                    <Key frame="50" value="-0.964409" />
                    <Key frame="80" value="4.22121" />
                    <Key frame="110" value="-0.964409" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="5" value="-29.2655" />
                    <Key frame="50" value="-29.2655" />
                    <Key frame="80" value="-5.00743" />
                    <Key frame="110" value="-29.2655" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="5" value="99.5837" />
                    <Key frame="50" value="-4.83163" />
                    <Key frame="80" value="-4.83163" />
                    <Key frame="110" value="-4.83163" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="5" value="101.693" />
                    <Key frame="50" value="-89.2956" />
                    <Key frame="80" value="6.06693" />
                    <Key frame="110" value="-89.2956" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="5" value="-14.7682" />
                    <Key frame="50" value="0.876518" />
                    <Key frame="80" value="7.81997" />
                    <Key frame="110" value="0.876518" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="5" value="11.7751" />
                    <Key frame="50" value="-50.8917" />
                    <Key frame="80" value="-57.8352" />
                    <Key frame="110" value="-50.8917" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="HipRaise" id="12" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="1228" y="161">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="80">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="5" value="10.0173" />
                    <Key frame="20" value="22.9373" />
                    <Key frame="35" value="24.5194" />
                    <Key frame="50" value="33.6601" />
                    <Key frame="65" value="33.6601" />
                    <Key frame="80" value="24.5194" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="5" value="-1.32078" />
                    <Key frame="20" value="-0.0902951" />
                    <Key frame="35" value="-0.705531" />
                    <Key frame="50" value="-3.25439" />
                    <Key frame="65" value="-3.25439" />
                    <Key frame="80" value="-0.705531" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="5" value="49.5685" />
                    <Key frame="20" value="31.6386" />
                    <Key frame="35" value="31.7265" />
                    <Key frame="50" value="18.1912" />
                    <Key frame="65" value="18.1912" />
                    <Key frame="80" value="31.7265" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="5" value="-2.1949" />
                    <Key frame="20" value="0.441859" />
                    <Key frame="35" value="0.441859" />
                    <Key frame="50" value="7.12163" />
                    <Key frame="65" value="7.12163" />
                    <Key frame="80" value="0.441859" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="5" value="-87.5378" />
                    <Key frame="20" value="-82.2643" />
                    <Key frame="35" value="-20.6522" />
                    <Key frame="50" value="-6.76526" />
                    <Key frame="65" value="-6.76526" />
                    <Key frame="80" value="-20.6522" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="5" value="-49.6612" />
                    <Key frame="20" value="-95.6286" />
                    <Key frame="35" value="-59.4172" />
                    <Key frame="50" value="-59.7688" />
                    <Key frame="65" value="-59.7688" />
                    <Key frame="80" value="-59.4172" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="5" value="0.3904" />
                    <Key frame="20" value="0.3168" />
                    <Key frame="35" value="0.3016" />
                    <Key frame="50" value="0.3052" />
                    <Key frame="65" value="0.3052" />
                    <Key frame="80" value="0.3016" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="5" value="21.5359" />
                    <Key frame="20" value="-31.3749" />
                    <Key frame="35" value="-31.1992" />
                    <Key frame="50" value="11.5162" />
                    <Key frame="65" value="11.5162" />
                    <Key frame="80" value="-31.1992" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="5" value="6.41851" />
                    <Key frame="20" value="14.4166" />
                    <Key frame="35" value="14.5924" />
                    <Key frame="50" value="12.3951" />
                    <Key frame="65" value="12.3951" />
                    <Key frame="80" value="14.5924" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="5" value="-29.2655" />
                    <Key frame="20" value="-1.66754" />
                    <Key frame="35" value="-1.84332" />
                    <Key frame="50" value="0.969218" />
                    <Key frame="65" value="0.969218" />
                    <Key frame="80" value="-1.84332" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="5" value="-5.3638" />
                    <Key frame="20" value="99.2274" />
                    <Key frame="35" value="98.8758" />
                    <Key frame="50" value="83.2311" />
                    <Key frame="65" value="83.2311" />
                    <Key frame="80" value="98.8758" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="5" value="107.665" />
                    <Key frame="20" value="120.849" />
                    <Key frame="35" value="113.729" />
                    <Key frame="50" value="121.025" />
                    <Key frame="65" value="121.025" />
                    <Key frame="80" value="113.729" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="5" value="13.8845" />
                    <Key frame="20" value="28.6503" />
                    <Key frame="35" value="18.4549" />
                    <Key frame="50" value="18.4549" />
                    <Key frame="65" value="18.4549" />
                    <Key frame="80" value="18.4549" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="5" value="12.7419" />
                    <Key frame="20" value="-16.9655" />
                    <Key frame="35" value="42.6251" />
                    <Key frame="50" value="36.6484" />
                    <Key frame="65" value="36.6484" />
                    <Key frame="80" value="42.6251" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="5" value="37.0048" />
                    <Key frame="20" value="32.6981" />
                    <Key frame="35" value="32.8739" />
                    <Key frame="50" value="18.2839" />
                    <Key frame="65" value="18.2839" />
                    <Key frame="80" value="32.8739" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="5" value="-16.2576" />
                    <Key frame="20" value="-4.74374" />
                    <Key frame="35" value="-4.91954" />
                    <Key frame="50" value="-2.28279" />
                    <Key frame="65" value="-2.28279" />
                    <Key frame="80" value="-4.91954" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="5" value="86.9273" />
                    <Key frame="20" value="81.9175" />
                    <Key frame="35" value="14.7682" />
                    <Key frame="50" value="6.94585" />
                    <Key frame="65" value="6.94585" />
                    <Key frame="80" value="14.7682" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="5" value="49.6564" />
                    <Key frame="20" value="89.9987" />
                    <Key frame="35" value="66.5316" />
                    <Key frame="50" value="35.1543" />
                    <Key frame="65" value="35.1543" />
                    <Key frame="80" value="66.5316" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="5" value="0.352" />
                    <Key frame="20" value="0.3652" />
                    <Key frame="35" value="0.3628" />
                    <Key frame="50" value="0.3628" />
                    <Key frame="65" value="0.3628" />
                    <Key frame="80" value="0.3628" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="5" value="-23.03" />
                    <Key frame="20" value="-37.9716" />
                    <Key frame="35" value="-37.9716" />
                    <Key frame="50" value="14.0603" />
                    <Key frame="65" value="14.0603" />
                    <Key frame="80" value="-37.9716" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="5" value="-4.65585" />
                    <Key frame="20" value="-15.0271" />
                    <Key frame="35" value="-15.115" />
                    <Key frame="50" value="-14.4997" />
                    <Key frame="65" value="-14.4997" />
                    <Key frame="80" value="-15.115" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="5" value="-29.2655" />
                    <Key frame="20" value="-1.66754" />
                    <Key frame="35" value="-1.84332" />
                    <Key frame="50" value="0.969218" />
                    <Key frame="65" value="0.969218" />
                    <Key frame="80" value="-1.84332" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="5" value="99.5837" />
                    <Key frame="20" value="102.045" />
                    <Key frame="35" value="102.396" />
                    <Key frame="50" value="81.3023" />
                    <Key frame="65" value="81.3023" />
                    <Key frame="80" value="102.396" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="5" value="101.693" />
                    <Key frame="20" value="111.537" />
                    <Key frame="35" value="105.736" />
                    <Key frame="50" value="121.732" />
                    <Key frame="65" value="121.732" />
                    <Key frame="80" value="105.736" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="5" value="-14.7682" />
                    <Key frame="20" value="-35.6864" />
                    <Key frame="35" value="-23.7332" />
                    <Key frame="50" value="-21.2722" />
                    <Key frame="65" value="-21.2722" />
                    <Key frame="80" value="-23.7332" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="5" value="11.7751" />
                    <Key frame="20" value="9.22623" />
                    <Key frame="35" value="-61.2629" />
                    <Key frame="50" value="-19.0749" />
                    <Key frame="65" value="-19.0749" />
                    <Key frame="80" value="-61.2629" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="Boxen" id="14" localization="8" tooltip="Stand&#x0A;Position&#x0A;Halten&#x0A;Rechter Arm&#x0A;Linker Arm&#x0A;Rechter Arm&#x0A;Linker Arm&#x0A;Position&#x0A;Stand" x="1215" y="44">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="80">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="nao">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="7" value="-8.26423" />
                    <Key frame="20" value="-8.26423" />
                    <Key frame="30" value="-8.26423" />
                    <Key frame="35" value="-8.26423" />
                    <Key frame="40" value="-8.26423" />
                    <Key frame="45" value="-8.26423" />
                    <Key frame="50" value="-8.26423" />
                    <Key frame="65" value="-8.26423" />
                    <Key frame="80" value="-8.26423" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="7" value="-1.23289" />
                    <Key frame="20" value="-1.23289" />
                    <Key frame="30" value="-1.23289" />
                    <Key frame="35" value="-1.23289" />
                    <Key frame="40" value="-1.23289" />
                    <Key frame="45" value="-1.23289" />
                    <Key frame="50" value="-1.23289" />
                    <Key frame="65" value="-1.23289" />
                    <Key frame="80" value="-1.23289" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="7" value="5.00743" />
                    <Key frame="20" value="4.30429" />
                    <Key frame="30" value="4.30429" />
                    <Key frame="35" value="4.30429" />
                    <Key frame="40" value="4.30429" />
                    <Key frame="45" value="4.30429" />
                    <Key frame="50" value="4.30429" />
                    <Key frame="65" value="4.30429" />
                    <Key frame="80" value="5.00743" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="7" value="-6.2379" />
                    <Key frame="20" value="-6.2379" />
                    <Key frame="30" value="-6.2379" />
                    <Key frame="35" value="-6.2379" />
                    <Key frame="40" value="-6.2379" />
                    <Key frame="45" value="-6.2379" />
                    <Key frame="50" value="-6.2379" />
                    <Key frame="65" value="-6.2379" />
                    <Key frame="80" value="-6.2379" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="7" value="-22.6737" />
                    <Key frame="20" value="-84.3737" />
                    <Key frame="30" value="-84.3737" />
                    <Key frame="35" value="-66.7074" />
                    <Key frame="40" value="-9.66569" />
                    <Key frame="45" value="-66.7074" />
                    <Key frame="50" value="-9.66569" />
                    <Key frame="65" value="-84.3737" />
                    <Key frame="80" value="-22.6737" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="7" value="-70.5795" />
                    <Key frame="20" value="-67.5033" />
                    <Key frame="30" value="-67.5033" />
                    <Key frame="35" value="-28.0399" />
                    <Key frame="40" value="-73.3041" />
                    <Key frame="45" value="-28.0399" />
                    <Key frame="50" value="-73.3041" />
                    <Key frame="65" value="-67.5033" />
                    <Key frame="80" value="-70.5795" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="7" value="0.2936" />
                    <Key frame="20" value="0.2936" />
                    <Key frame="30" value="0.2936" />
                    <Key frame="35" value="0.2936" />
                    <Key frame="40" value="0.2936" />
                    <Key frame="45" value="0.2936" />
                    <Key frame="50" value="0.2936" />
                    <Key frame="65" value="0.2936" />
                    <Key frame="80" value="0.2936" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="7" value="7.91266" />
                    <Key frame="20" value="7.91266" />
                    <Key frame="30" value="7.91266" />
                    <Key frame="35" value="7.91266" />
                    <Key frame="40" value="7.91266" />
                    <Key frame="45" value="7.91266" />
                    <Key frame="50" value="7.91266" />
                    <Key frame="65" value="7.91266" />
                    <Key frame="80" value="7.91266" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="7" value="7.20952" />
                    <Key frame="20" value="7.20952" />
                    <Key frame="30" value="7.20952" />
                    <Key frame="35" value="7.20952" />
                    <Key frame="40" value="7.20952" />
                    <Key frame="45" value="7.20952" />
                    <Key frame="50" value="7.20952" />
                    <Key frame="65" value="7.20952" />
                    <Key frame="80" value="7.20952" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="7" value="-9.13833" />
                    <Key frame="20" value="-9.13833" />
                    <Key frame="30" value="-9.13833" />
                    <Key frame="35" value="-9.13833" />
                    <Key frame="40" value="-9.13833" />
                    <Key frame="45" value="-9.13833" />
                    <Key frame="50" value="-9.13833" />
                    <Key frame="65" value="-9.13833" />
                    <Key frame="80" value="-9.13833" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="7" value="-5.27591" />
                    <Key frame="20" value="-5.89115" />
                    <Key frame="30" value="-5.89115" />
                    <Key frame="35" value="-5.89115" />
                    <Key frame="40" value="-5.89115" />
                    <Key frame="45" value="-5.89115" />
                    <Key frame="50" value="-5.89115" />
                    <Key frame="65" value="-5.89115" />
                    <Key frame="80" value="-5.27591" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="7" value="82.8795" />
                    <Key frame="20" value="47.6349" />
                    <Key frame="30" value="47.6349" />
                    <Key frame="35" value="20.9158" />
                    <Key frame="40" value="9.84147" />
                    <Key frame="45" value="20.9158" />
                    <Key frame="50" value="9.84147" />
                    <Key frame="65" value="47.6349" />
                    <Key frame="80" value="82.8795" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="7" value="11.2477" />
                    <Key frame="20" value="-7.20952" />
                    <Key frame="30" value="-7.20952" />
                    <Key frame="35" value="7.81997" />
                    <Key frame="40" value="-8.87946" />
                    <Key frame="45" value="7.81997" />
                    <Key frame="50" value="-8.87946" />
                    <Key frame="65" value="-7.20952" />
                    <Key frame="80" value="11.2477" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="7" value="4.39218" />
                    <Key frame="20" value="13.7087" />
                    <Key frame="30" value="13.7087" />
                    <Key frame="35" value="-30.8524" />
                    <Key frame="40" value="-19.3386" />
                    <Key frame="45" value="-30.8524" />
                    <Key frame="50" value="-19.3386" />
                    <Key frame="65" value="13.7087" />
                    <Key frame="80" value="4.39218" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="7" value="5.27591" />
                    <Key frame="20" value="4.83644" />
                    <Key frame="30" value="4.83644" />
                    <Key frame="35" value="4.83644" />
                    <Key frame="40" value="5.45169" />
                    <Key frame="45" value="4.83644" />
                    <Key frame="50" value="5.45169" />
                    <Key frame="65" value="4.83644" />
                    <Key frame="80" value="5.27591" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="7" value="6.68218" />
                    <Key frame="20" value="7.29742" />
                    <Key frame="30" value="7.29742" />
                    <Key frame="35" value="7.29742" />
                    <Key frame="40" value="7.29742" />
                    <Key frame="45" value="7.29742" />
                    <Key frame="50" value="7.29742" />
                    <Key frame="65" value="7.29742" />
                    <Key frame="80" value="6.68218" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="7" value="23.8211" />
                    <Key frame="20" value="85.1695" />
                    <Key frame="30" value="85.1695" />
                    <Key frame="35" value="3.25439" />
                    <Key frame="40" value="74.0072" />
                    <Key frame="45" value="3.25439" />
                    <Key frame="50" value="74.0072" />
                    <Key frame="65" value="85.1695" />
                    <Key frame="80" value="23.8211" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="7" value="67.9379" />
                    <Key frame="20" value="66.8832" />
                    <Key frame="30" value="66.8832" />
                    <Key frame="35" value="36.1211" />
                    <Key frame="40" value="22.1463" />
                    <Key frame="45" value="36.1211" />
                    <Key frame="50" value="22.1463" />
                    <Key frame="65" value="66.8832" />
                    <Key frame="80" value="67.9379" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="7" value="0.3016" />
                    <Key frame="20" value="0.3016" />
                    <Key frame="30" value="0.3016" />
                    <Key frame="35" value="0.3016" />
                    <Key frame="40" value="0.3016" />
                    <Key frame="45" value="0.3016" />
                    <Key frame="50" value="0.3016" />
                    <Key frame="65" value="0.3016" />
                    <Key frame="80" value="0.3016" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="7" value="7.29261" />
                    <Key frame="20" value="7.29261" />
                    <Key frame="30" value="7.29261" />
                    <Key frame="35" value="7.29261" />
                    <Key frame="40" value="7.29261" />
                    <Key frame="45" value="7.29261" />
                    <Key frame="50" value="7.29261" />
                    <Key frame="65" value="7.29261" />
                    <Key frame="80" value="7.29261" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="7" value="-7.11683" />
                    <Key frame="20" value="-7.11683" />
                    <Key frame="30" value="-7.11683" />
                    <Key frame="35" value="-7.11683" />
                    <Key frame="40" value="-7.11683" />
                    <Key frame="45" value="-7.11683" />
                    <Key frame="50" value="-7.11683" />
                    <Key frame="65" value="-7.11683" />
                    <Key frame="80" value="-7.11683" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="7" value="-9.13833" />
                    <Key frame="20" value="-9.13833" />
                    <Key frame="30" value="-9.13833" />
                    <Key frame="35" value="-9.13833" />
                    <Key frame="40" value="-9.13833" />
                    <Key frame="45" value="-9.13833" />
                    <Key frame="50" value="-9.13833" />
                    <Key frame="65" value="-9.13833" />
                    <Key frame="80" value="-9.13833" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="7" value="-5.09532" />
                    <Key frame="20" value="-5.09532" />
                    <Key frame="30" value="-5.09532" />
                    <Key frame="35" value="-5.09532" />
                    <Key frame="40" value="-5.09532" />
                    <Key frame="45" value="-5.09532" />
                    <Key frame="50" value="-5.09532" />
                    <Key frame="65" value="-5.09532" />
                    <Key frame="80" value="-5.09532" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="7" value="81.8296" />
                    <Key frame="20" value="46.4971" />
                    <Key frame="30" value="46.4971" />
                    <Key frame="35" value="-0.524941" />
                    <Key frame="40" value="20.1296" />
                    <Key frame="45" value="-0.524941" />
                    <Key frame="50" value="20.1296" />
                    <Key frame="65" value="46.4971" />
                    <Key frame="80" value="81.8296" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="7" value="-11.5162" />
                    <Key frame="20" value="2.0191" />
                    <Key frame="30" value="2.0191" />
                    <Key frame="35" value="6.67737" />
                    <Key frame="40" value="-11.8678" />
                    <Key frame="45" value="6.67737" />
                    <Key frame="50" value="-11.8678" />
                    <Key frame="65" value="2.0191" />
                    <Key frame="80" value="-11.5162" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="7" value="7.73207" />
                    <Key frame="20" value="-13.7135" />
                    <Key frame="30" value="-13.7135" />
                    <Key frame="35" value="20.6522" />
                    <Key frame="40" value="11.5993" />
                    <Key frame="45" value="20.6522" />
                    <Key frame="50" value="11.5993" />
                    <Key frame="65" value="-13.7135" />
                    <Key frame="80" value="7.73207" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="RussianTwist Demo" id="61" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="1104" y="288">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="65">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="25" value="-3.25439" />
                    <Key frame="35" value="-3.25439" />
                    <Key frame="45" value="-3.25439" />
                    <Key frame="55" value="-3.25439" />
                    <Key frame="65" value="-3.25439" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="25" value="-2.63916" />
                    <Key frame="35" value="19.5975" />
                    <Key frame="45" value="-2.63916" />
                    <Key frame="55" value="-19.5975" />
                    <Key frame="65" value="-2.63916" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="25" value="49.3049" />
                    <Key frame="35" value="49.3049" />
                    <Key frame="45" value="49.3049" />
                    <Key frame="55" value="49.6612" />
                    <Key frame="65" value="49.3049" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="25" value="-0.612832" />
                    <Key frame="35" value="-0.612832" />
                    <Key frame="45" value="-0.612832" />
                    <Key frame="55" value="-1.145" />
                    <Key frame="65" value="-0.612832" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="25" value="-87.4499" />
                    <Key frame="35" value="-86.4831" />
                    <Key frame="45" value="-87.4499" />
                    <Key frame="55" value="-69.0853" />
                    <Key frame="65" value="-87.4499" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="25" value="-35.3349" />
                    <Key frame="35" value="-43.2451" />
                    <Key frame="45" value="-35.3349" />
                    <Key frame="55" value="-26.1014" />
                    <Key frame="65" value="-35.3349" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="25" value="0.286" />
                    <Key frame="35" value="0.286" />
                    <Key frame="45" value="0.286" />
                    <Key frame="55" value="0.2908" />
                    <Key frame="65" value="0.286" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="25" value="-82.3521" />
                    <Key frame="35" value="-85.1647" />
                    <Key frame="45" value="-82.3521" />
                    <Key frame="55" value="-85.9605" />
                    <Key frame="65" value="-82.3521" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="25" value="16.4382" />
                    <Key frame="35" value="15.8229" />
                    <Key frame="45" value="16.4382" />
                    <Key frame="55" value="13.7966" />
                    <Key frame="65" value="16.4382" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="25" value="-30.496" />
                    <Key frame="35" value="-28.7382" />
                    <Key frame="45" value="-30.496" />
                    <Key frame="55" value="-28.7382" />
                    <Key frame="65" value="-30.496" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="25" value="77.9576" />
                    <Key frame="35" value="78.1333" />
                    <Key frame="45" value="77.9576" />
                    <Key frame="55" value="81.478" />
                    <Key frame="65" value="77.9576" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="25" value="34.7148" />
                    <Key frame="35" value="59.9398" />
                    <Key frame="45" value="34.7148" />
                    <Key frame="55" value="39.3779" />
                    <Key frame="65" value="34.7148" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="25" value="11.3356" />
                    <Key frame="35" value="32.4296" />
                    <Key frame="45" value="11.3356" />
                    <Key frame="55" value="-13.8845" />
                    <Key frame="65" value="11.3356" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="25" value="-35.5107" />
                    <Key frame="35" value="-15.6471" />
                    <Key frame="45" value="-35.5107" />
                    <Key frame="55" value="-8.4352" />
                    <Key frame="65" value="-35.5107" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="25" value="49.5733" />
                    <Key frame="35" value="49.6612" />
                    <Key frame="45" value="49.5733" />
                    <Key frame="55" value="49.3049" />
                    <Key frame="65" value="49.5733" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="25" value="0.441859" />
                    <Key frame="35" value="1.145" />
                    <Key frame="45" value="0.441859" />
                    <Key frame="55" value="0.612832" />
                    <Key frame="65" value="0.441859" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="25" value="87.7183" />
                    <Key frame="35" value="69.0853" />
                    <Key frame="45" value="87.7183" />
                    <Key frame="55" value="86.4831" />
                    <Key frame="65" value="87.7183" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="25" value="48.2502" />
                    <Key frame="35" value="26.1014" />
                    <Key frame="45" value="48.2502" />
                    <Key frame="55" value="43.2451" />
                    <Key frame="65" value="48.2502" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="25" value="0.2908" />
                    <Key frame="35" value="0.2908" />
                    <Key frame="45" value="0.2908" />
                    <Key frame="55" value="0.286" />
                    <Key frame="65" value="0.2908" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="25" value="-83.3238" />
                    <Key frame="35" value="-85.9605" />
                    <Key frame="45" value="-83.3238" />
                    <Key frame="55" value="-85.1647" />
                    <Key frame="65" value="-83.3238" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="25" value="-14.5876" />
                    <Key frame="35" value="-13.7966" />
                    <Key frame="45" value="-14.5876" />
                    <Key frame="55" value="-15.8229" />
                    <Key frame="65" value="-14.5876" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="25" value="-30.496" />
                    <Key frame="35" value="-28.7382" />
                    <Key frame="45" value="-30.496" />
                    <Key frame="55" value="-28.7382" />
                    <Key frame="65" value="-30.496" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="25" value="80.2476" />
                    <Key frame="35" value="81.478" />
                    <Key frame="45" value="80.2476" />
                    <Key frame="55" value="78.1333" />
                    <Key frame="65" value="80.2476" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="25" value="40.7842" />
                    <Key frame="35" value="39.3779" />
                    <Key frame="45" value="40.7842" />
                    <Key frame="55" value="59.9398" />
                    <Key frame="65" value="40.7842" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="25" value="-5.71537" />
                    <Key frame="35" value="13.8845" />
                    <Key frame="45" value="-5.71537" />
                    <Key frame="55" value="-32.4296" />
                    <Key frame="65" value="-5.71537" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="25" value="4.91954" />
                    <Key frame="35" value="8.4352" />
                    <Key frame="45" value="4.91954" />
                    <Key frame="55" value="15.6471" />
                    <Key frame="65" value="4.91954" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="LegRaise Demo" id="25" localization="8" tooltip="Init&#x0A;Init&#x0A;Init&#x0A;Hoch&#x0A;Runter" x="1103" y="406">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="105">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="15" value="9.92936" />
                    <Key frame="30" value="11.1598" />
                    <Key frame="45" value="9.92936" />
                    <Key frame="75" value="13.7087" />
                    <Key frame="105" value="13.7087" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="15" value="-0.881327" />
                    <Key frame="30" value="-0.793436" />
                    <Key frame="45" value="-0.881327" />
                    <Key frame="75" value="2.28279" />
                    <Key frame="105" value="2.28279" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="15" value="49.4806" />
                    <Key frame="30" value="49.5685" />
                    <Key frame="45" value="49.4806" />
                    <Key frame="75" value="45.1739" />
                    <Key frame="105" value="45.1739" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="15" value="-2.0191" />
                    <Key frame="30" value="-1.75543" />
                    <Key frame="45" value="-2.0191" />
                    <Key frame="75" value="-1.49175" />
                    <Key frame="105" value="-1.49175" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="15" value="-80.2427" />
                    <Key frame="30" value="-41.3067" />
                    <Key frame="45" value="-18.8943" />
                    <Key frame="75" value="-18.2791" />
                    <Key frame="105" value="-18.2791" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="15" value="-55.9894" />
                    <Key frame="30" value="-90.443" />
                    <Key frame="45" value="-96.5075" />
                    <Key frame="75" value="-96.5075" />
                    <Key frame="105" value="-96.5075" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="15" value="0.2892" />
                    <Key frame="30" value="0.2968" />
                    <Key frame="45" value="0.2892" />
                    <Key frame="75" value="0.2892" />
                    <Key frame="105" value="0.2892" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="15" value="21.2722" />
                    <Key frame="30" value="21.2722" />
                    <Key frame="45" value="21.2722" />
                    <Key frame="75" value="-84.1979" />
                    <Key frame="105" value="1.58445" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="15" value="6.24271" />
                    <Key frame="30" value="6.06693" />
                    <Key frame="45" value="6.24271" />
                    <Key frame="75" value="-0.261268" />
                    <Key frame="105" value="-0.261268" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="15" value="-29.1777" />
                    <Key frame="30" value="-29.0898" />
                    <Key frame="45" value="-29.1777" />
                    <Key frame="75" value="-5.44688" />
                    <Key frame="105" value="4.22121" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="15" value="-5.18802" />
                    <Key frame="30" value="-4.92435" />
                    <Key frame="45" value="-5.18802" />
                    <Key frame="75" value="-5.71537" />
                    <Key frame="105" value="-4.48488" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="15" value="79.4517" />
                    <Key frame="30" value="80.5943" />
                    <Key frame="45" value="107.138" />
                    <Key frame="75" value="108.104" />
                    <Key frame="105" value="108.104" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="15" value="2.0191" />
                    <Key frame="30" value="7.81997" />
                    <Key frame="45" value="5.62267" />
                    <Key frame="75" value="5.71056" />
                    <Key frame="105" value="5.71056" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="15" value="7.3805" />
                    <Key frame="30" value="15.0271" />
                    <Key frame="45" value="16.4334" />
                    <Key frame="75" value="16.4334" />
                    <Key frame="105" value="16.4334" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="15" value="37.5322" />
                    <Key frame="30" value="31.6434" />
                    <Key frame="45" value="30.5008" />
                    <Key frame="75" value="43.7725" />
                    <Key frame="105" value="41.6631" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="15" value="-16.1697" />
                    <Key frame="30" value="-20.3885" />
                    <Key frame="45" value="-16.1697" />
                    <Key frame="75" value="-1.14019" />
                    <Key frame="105" value="-1.31597" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="15" value="85.0816" />
                    <Key frame="30" value="45.4424" />
                    <Key frame="45" value="20.657" />
                    <Key frame="75" value="20.657" />
                    <Key frame="105" value="20.657" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="15" value="47.8107" />
                    <Key frame="30" value="68.641" />
                    <Key frame="45" value="78.4849" />
                    <Key frame="75" value="78.4849" />
                    <Key frame="105" value="78.4849" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="15" value="0.35" />
                    <Key frame="30" value="0.3528" />
                    <Key frame="45" value="0.35" />
                    <Key frame="75" value="0.35" />
                    <Key frame="105" value="0.35" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="15" value="-23.2937" />
                    <Key frame="30" value="-4.48488" />
                    <Key frame="45" value="13.8845" />
                    <Key frame="75" value="-88.4215" />
                    <Key frame="105" value="1.57965" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="15" value="-4.83163" />
                    <Key frame="30" value="-6.2379" />
                    <Key frame="45" value="-3.77694" />
                    <Key frame="75" value="-6.67737" />
                    <Key frame="105" value="-6.06212" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="15" value="-29.1777" />
                    <Key frame="30" value="-29.0898" />
                    <Key frame="45" value="-29.1777" />
                    <Key frame="75" value="-5.44688" />
                    <Key frame="105" value="4.22121" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="15" value="99.4079" />
                    <Key frame="30" value="49.2218" />
                    <Key frame="45" value="13.1862" />
                    <Key frame="75" value="-5.53478" />
                    <Key frame="105" value="-2.0191" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="15" value="75.5014" />
                    <Key frame="30" value="72.5131" />
                    <Key frame="45" value="108.021" />
                    <Key frame="75" value="108.812" />
                    <Key frame="105" value="108.812" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="15" value="-14.7682" />
                    <Key frame="30" value="-23.3816" />
                    <Key frame="45" value="-17.2292" />
                    <Key frame="75" value="-16.6139" />
                    <Key frame="105" value="-16.6139" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="15" value="11.3356" />
                    <Key frame="30" value="-5.18802" />
                    <Key frame="45" value="-10.2857" />
                    <Key frame="75" value="-10.2857" />
                    <Key frame="105" value="-10.2857" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="Sit" id="26" localization="8" tooltip="The robot goes from its current postition to the asked posture." x="92" y="324">
              <bitmap>media/images/box/box-diagram.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[class MyClass(GeneratedClass):
    def __init__(self):
        GeneratedClass.__init__(self, False)

    def onLoad(self):
        self.nTries = 0
        self.postureService = self.session().service("ALRobotPosture")
        pass

    def onUnload(self):
        self.postureService.stopMove()

    def onInput_onStart(self):
        if(self.nTries != self.getParameter("Maximum of tries")):
            self.nTries = self.getParameter("Maximum of tries")
            self.postureService.setMaxTryNumber(self.nTries)

        result = self.postureService.goToPosture(self.getParameter("Name"), self.getParameter("Speed (%)")/100.)
        if(result):
            self.success()
        else:
            self.failure()
        pass

    def onInput_onStop(self):
        self.onUnload() #~ it is recommanded to call onUnload of this box in a onStop method, as the code written in onUnload is used to stop the box as well
        pass]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="success" type="1" type_size="1" nature="1" inner="0" tooltip="Stimulated if the posture has been reached." id="4" />
              <Output name="failure" type="1" type_size="1" nature="1" inner="0" tooltip="Stimulated if the posture could not be reached." id="5" />
              <Parameter name="Name" inherits_from_parent="0" content_type="3" value="Sit" default_value="Stand" custom_choice="1" tooltip="Name of the posture to go to." id="6">
                <Choice value="Crouch" />
                <Choice value="LyingBack" />
                <Choice value="LyingBelly" />
                <Choice value="Sit" />
                <Choice value="SitRelax" />
                <Choice value="StandInit" />
                <Choice value="Stand" />
                <Choice value="StandZero" />
              </Parameter>
              <Parameter name="Speed (%)" inherits_from_parent="0" content_type="1" value="80" default_value="80" min="0" max="100" tooltip="Speed to go to the posture." id="7" />
              <Parameter name="Maximum of tries" inherits_from_parent="0" content_type="1" value="3" default_value="3" min="1" max="10" tooltip="The maximum number of fails of go to posture before stimulating the failure output." id="8" />
              <Resource name="All motors" type="Lock" timeout="0" />
              <Resource name="Stiffness" type="Lock" timeout="0" />
            </Box>
            <Box name="RussianTwist Demo" id="27" localization="8" tooltip="Position&#x0A;Links&#x0A;Position&#x0A;Rechts&#x0A;Position" x="1113" y="522">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="70">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="30" value="-3.25439" />
                    <Key frame="40" value="-3.25439" />
                    <Key frame="50" value="-3.25439" />
                    <Key frame="60" value="-3.25439" />
                    <Key frame="70" value="-3.25439" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="30" value="-2.63916" />
                    <Key frame="40" value="19.5975" />
                    <Key frame="50" value="-2.63916" />
                    <Key frame="60" value="-19.5975" />
                    <Key frame="70" value="-2.63916" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="30" value="49.3049" />
                    <Key frame="40" value="49.3049" />
                    <Key frame="50" value="49.3049" />
                    <Key frame="60" value="49.6612" />
                    <Key frame="70" value="49.3049" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="30" value="-0.612832" />
                    <Key frame="40" value="-0.612832" />
                    <Key frame="50" value="-0.612832" />
                    <Key frame="60" value="-1.145" />
                    <Key frame="70" value="-0.612832" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="30" value="-87.4499" />
                    <Key frame="40" value="-86.4831" />
                    <Key frame="50" value="-87.4499" />
                    <Key frame="60" value="-69.0853" />
                    <Key frame="70" value="-87.4499" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="30" value="-35.3349" />
                    <Key frame="40" value="-43.2451" />
                    <Key frame="50" value="-35.3349" />
                    <Key frame="60" value="-26.1014" />
                    <Key frame="70" value="-35.3349" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="30" value="0.286" />
                    <Key frame="40" value="0.286" />
                    <Key frame="50" value="0.286" />
                    <Key frame="60" value="0.2908" />
                    <Key frame="70" value="0.286" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="30" value="-82.3521" />
                    <Key frame="40" value="-85.1647" />
                    <Key frame="50" value="-82.3521" />
                    <Key frame="60" value="-85.9605" />
                    <Key frame="70" value="-82.3521" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="30" value="16.4382" />
                    <Key frame="40" value="15.8229" />
                    <Key frame="50" value="16.4382" />
                    <Key frame="60" value="13.7966" />
                    <Key frame="70" value="16.4382" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="30" value="-30.496" />
                    <Key frame="40" value="-28.7382" />
                    <Key frame="50" value="-30.496" />
                    <Key frame="60" value="-28.7382" />
                    <Key frame="70" value="-30.496" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="30" value="77.9576" />
                    <Key frame="40" value="78.1333" />
                    <Key frame="50" value="77.9576" />
                    <Key frame="60" value="81.478" />
                    <Key frame="70" value="77.9576" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="30" value="34.7148" />
                    <Key frame="40" value="59.9398" />
                    <Key frame="50" value="34.7148" />
                    <Key frame="60" value="39.3779" />
                    <Key frame="70" value="34.7148" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="30" value="11.3356" />
                    <Key frame="40" value="32.4296" />
                    <Key frame="50" value="11.3356" />
                    <Key frame="60" value="-13.8845" />
                    <Key frame="70" value="11.3356" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="30" value="-35.5107" />
                    <Key frame="40" value="-15.6471" />
                    <Key frame="50" value="-35.5107" />
                    <Key frame="60" value="-8.4352" />
                    <Key frame="70" value="-35.5107" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="30" value="49.5733" />
                    <Key frame="40" value="49.6612" />
                    <Key frame="50" value="49.5733" />
                    <Key frame="60" value="49.3049" />
                    <Key frame="70" value="49.5733" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="30" value="0.441859" />
                    <Key frame="40" value="1.145" />
                    <Key frame="50" value="0.441859" />
                    <Key frame="60" value="0.612832" />
                    <Key frame="70" value="0.441859" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="30" value="87.7183" />
                    <Key frame="40" value="69.0853" />
                    <Key frame="50" value="87.7183" />
                    <Key frame="60" value="86.4831" />
                    <Key frame="70" value="87.7183" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="30" value="48.2502" />
                    <Key frame="40" value="26.1014" />
                    <Key frame="50" value="48.2502" />
                    <Key frame="60" value="43.2451" />
                    <Key frame="70" value="48.2502" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="30" value="0.2908" />
                    <Key frame="40" value="0.2908" />
                    <Key frame="50" value="0.2908" />
                    <Key frame="60" value="0.286" />
                    <Key frame="70" value="0.2908" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="30" value="-83.3238" />
                    <Key frame="40" value="-85.9605" />
                    <Key frame="50" value="-83.3238" />
                    <Key frame="60" value="-85.1647" />
                    <Key frame="70" value="-83.3238" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="30" value="-14.5876" />
                    <Key frame="40" value="-13.7966" />
                    <Key frame="50" value="-14.5876" />
                    <Key frame="60" value="-15.8229" />
                    <Key frame="70" value="-14.5876" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="30" value="-30.496" />
                    <Key frame="40" value="-28.7382" />
                    <Key frame="50" value="-30.496" />
                    <Key frame="60" value="-28.7382" />
                    <Key frame="70" value="-30.496" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="30" value="80.2476" />
                    <Key frame="40" value="81.478" />
                    <Key frame="50" value="80.2476" />
                    <Key frame="60" value="78.1333" />
                    <Key frame="70" value="80.2476" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="30" value="40.7842" />
                    <Key frame="40" value="39.3779" />
                    <Key frame="50" value="40.7842" />
                    <Key frame="60" value="59.9398" />
                    <Key frame="70" value="40.7842" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="30" value="-5.71537" />
                    <Key frame="40" value="13.8845" />
                    <Key frame="50" value="-5.71537" />
                    <Key frame="60" value="-32.4296" />
                    <Key frame="70" value="-5.71537" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="30" value="4.91954" />
                    <Key frame="40" value="8.4352" />
                    <Key frame="50" value="4.91954" />
                    <Key frame="60" value="15.6471" />
                    <Key frame="70" value="4.91954" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="Hacker Demo" id="24" localization="8" tooltip="Init&#x0A;Whole Hacker Repeat&#x0A;Stand" x="1101" y="672">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="160">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="15" value="-11.0768" />
                    <Key frame="40" value="-12.3951" />
                    <Key frame="50" value="-12.3951" />
                    <Key frame="60" value="-12.3951" />
                    <Key frame="70" value="-12.3951" />
                    <Key frame="80" value="-12.3951" />
                    <Key frame="90" value="-14.944" />
                    <Key frame="100" value="-14.944" />
                    <Key frame="110" value="-14.944" />
                    <Key frame="120" value="-16.0866" />
                    <Key frame="130" value="-14.944" />
                    <Key frame="140" value="-14.944" />
                    <Key frame="150" value="-14.944" />
                    <Key frame="160" value="-12.3951" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="15" value="0.349159" />
                    <Key frame="40" value="-0.61764" />
                    <Key frame="50" value="-0.61764" />
                    <Key frame="60" value="0.61764" />
                    <Key frame="70" value="-0.61764" />
                    <Key frame="80" value="0.61764" />
                    <Key frame="90" value="-0.441859" />
                    <Key frame="100" value="-0.441859" />
                    <Key frame="110" value="-0.441859" />
                    <Key frame="120" value="-0.178186" />
                    <Key frame="130" value="-0.441859" />
                    <Key frame="140" value="-0.441859" />
                    <Key frame="150" value="-0.441859" />
                    <Key frame="160" value="0.61764" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="15" value="5.35899" />
                    <Key frame="40" value="5.09532" />
                    <Key frame="50" value="5.09532" />
                    <Key frame="60" value="5.10013" />
                    <Key frame="70" value="5.09532" />
                    <Key frame="80" value="5.10013" />
                    <Key frame="90" value="5.35899" />
                    <Key frame="100" value="5.35899" />
                    <Key frame="110" value="5.35899" />
                    <Key frame="120" value="5.09532" />
                    <Key frame="130" value="5.35899" />
                    <Key frame="140" value="5.35899" />
                    <Key frame="150" value="5.35899" />
                    <Key frame="160" value="5.10013" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="15" value="-6.4137" />
                    <Key frame="40" value="-5.79845" />
                    <Key frame="50" value="-5.79845" />
                    <Key frame="60" value="-6.5064" />
                    <Key frame="70" value="-5.79845" />
                    <Key frame="80" value="-6.5064" />
                    <Key frame="90" value="-6.06212" />
                    <Key frame="100" value="-6.06212" />
                    <Key frame="110" value="-6.06212" />
                    <Key frame="120" value="-6.06212" />
                    <Key frame="130" value="-6.06212" />
                    <Key frame="140" value="-6.06212" />
                    <Key frame="150" value="-6.06212" />
                    <Key frame="160" value="-6.5064" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="15" value="-23.2889" />
                    <Key frame="40" value="-12.4782" />
                    <Key frame="50" value="-12.4782" />
                    <Key frame="60" value="-12.5709" />
                    <Key frame="70" value="-12.4782" />
                    <Key frame="80" value="-12.5709" />
                    <Key frame="90" value="-11.7751" />
                    <Key frame="100" value="-1.57965" />
                    <Key frame="110" value="-1.57965" />
                    <Key frame="120" value="-2.72224" />
                    <Key frame="130" value="-1.57965" />
                    <Key frame="140" value="-1.57965" />
                    <Key frame="150" value="-11.7751" />
                    <Key frame="160" value="-12.5709" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="15" value="-69.6127" />
                    <Key frame="40" value="-80.0718" />
                    <Key frame="50" value="-80.0718" />
                    <Key frame="60" value="-83.7584" />
                    <Key frame="70" value="-80.0718" />
                    <Key frame="80" value="-83.7584" />
                    <Key frame="90" value="-82.7964" />
                    <Key frame="100" value="-74.6225" />
                    <Key frame="110" value="-74.6225" />
                    <Key frame="120" value="-75.062" />
                    <Key frame="130" value="-74.6225" />
                    <Key frame="140" value="-74.6225" />
                    <Key frame="150" value="-82.7964" />
                    <Key frame="160" value="-83.7584" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="15" value="0.302" />
                    <Key frame="40" value="0.2928" />
                    <Key frame="50" value="0.2928" />
                    <Key frame="60" value="0.3144" />
                    <Key frame="70" value="0.2928" />
                    <Key frame="80" value="0.3144" />
                    <Key frame="90" value="0.304" />
                    <Key frame="100" value="0.304" />
                    <Key frame="110" value="0.304" />
                    <Key frame="120" value="0.304" />
                    <Key frame="130" value="0.304" />
                    <Key frame="140" value="0.304" />
                    <Key frame="150" value="0.304" />
                    <Key frame="160" value="0.3144" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="15" value="7.91266" />
                    <Key frame="40" value="4.04543" />
                    <Key frame="50" value="4.04543" />
                    <Key frame="60" value="7.3805" />
                    <Key frame="70" value="4.04543" />
                    <Key frame="80" value="7.3805" />
                    <Key frame="90" value="7.73688" />
                    <Key frame="100" value="7.73688" />
                    <Key frame="110" value="7.73688" />
                    <Key frame="120" value="8.17634" />
                    <Key frame="130" value="7.73688" />
                    <Key frame="140" value="7.73688" />
                    <Key frame="150" value="7.73688" />
                    <Key frame="160" value="7.3805" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="15" value="6.3306" />
                    <Key frame="40" value="5.97904" />
                    <Key frame="50" value="5.97904" />
                    <Key frame="60" value="6.85315" />
                    <Key frame="70" value="5.97904" />
                    <Key frame="80" value="6.85315" />
                    <Key frame="90" value="6.68218" />
                    <Key frame="100" value="6.68218" />
                    <Key frame="110" value="6.68218" />
                    <Key frame="120" value="6.68218" />
                    <Key frame="130" value="6.68218" />
                    <Key frame="140" value="6.68218" />
                    <Key frame="150" value="6.68218" />
                    <Key frame="160" value="6.85315" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="15" value="-9.66569" />
                    <Key frame="40" value="-9.5778" />
                    <Key frame="50" value="-9.5778" />
                    <Key frame="60" value="-9.5778" />
                    <Key frame="70" value="-9.5778" />
                    <Key frame="80" value="-9.5778" />
                    <Key frame="90" value="-9.48991" />
                    <Key frame="100" value="-9.48991" />
                    <Key frame="110" value="-9.48991" />
                    <Key frame="120" value="-9.40201" />
                    <Key frame="130" value="-9.48991" />
                    <Key frame="140" value="-9.48991" />
                    <Key frame="150" value="-9.48991" />
                    <Key frame="160" value="-9.5778" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="15" value="-5.10013" />
                    <Key frame="40" value="-1.145" />
                    <Key frame="50" value="-1.145" />
                    <Key frame="60" value="-5.71056" />
                    <Key frame="70" value="-1.145" />
                    <Key frame="80" value="-5.71056" />
                    <Key frame="90" value="-4.57277" />
                    <Key frame="100" value="-4.57277" />
                    <Key frame="110" value="-4.57277" />
                    <Key frame="120" value="-4.74855" />
                    <Key frame="130" value="-4.57277" />
                    <Key frame="140" value="-4.57277" />
                    <Key frame="150" value="-4.57277" />
                    <Key frame="160" value="-5.71056" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="15" value="81.5611" />
                    <Key frame="40" value="15.2029" />
                    <Key frame="50" value="15.2029" />
                    <Key frame="60" value="-18.1033" />
                    <Key frame="70" value="15.2029" />
                    <Key frame="80" value="-18.1033" />
                    <Key frame="90" value="-7.82477" />
                    <Key frame="100" value="-46.6729" />
                    <Key frame="110" value="-46.6729" />
                    <Key frame="120" value="-65.8333" />
                    <Key frame="130" value="-46.6729" />
                    <Key frame="140" value="-46.6729" />
                    <Key frame="150" value="-7.82477" />
                    <Key frame="160" value="-18.1033" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="15" value="11.2477" />
                    <Key frame="40" value="-4.3091" />
                    <Key frame="50" value="-4.3091" />
                    <Key frame="60" value="-1.0523" />
                    <Key frame="70" value="-4.3091" />
                    <Key frame="80" value="-1.0523" />
                    <Key frame="90" value="-1.58445" />
                    <Key frame="100" value="0.43705" />
                    <Key frame="110" value="0.43705" />
                    <Key frame="120" value="4.39218" />
                    <Key frame="130" value="0.43705" />
                    <Key frame="140" value="0.43705" />
                    <Key frame="150" value="-1.58445" />
                    <Key frame="160" value="-1.0523" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="15" value="4.04062" />
                    <Key frame="40" value="-1.40867" />
                    <Key frame="50" value="-1.40867" />
                    <Key frame="60" value="-19.0701" />
                    <Key frame="70" value="-1.40867" />
                    <Key frame="80" value="-19.0701" />
                    <Key frame="90" value="-17.405" />
                    <Key frame="100" value="-13.4498" />
                    <Key frame="110" value="-13.4498" />
                    <Key frame="120" value="-14.5924" />
                    <Key frame="130" value="-13.4498" />
                    <Key frame="140" value="-13.4498" />
                    <Key frame="150" value="-17.405" />
                    <Key frame="160" value="-19.0701" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="15" value="4.92435" />
                    <Key frame="40" value="5.10013" />
                    <Key frame="50" value="5.10013" />
                    <Key frame="60" value="5.09532" />
                    <Key frame="70" value="5.10013" />
                    <Key frame="80" value="5.09532" />
                    <Key frame="90" value="5.27591" />
                    <Key frame="100" value="5.27591" />
                    <Key frame="110" value="5.27591" />
                    <Key frame="120" value="4.92435" />
                    <Key frame="130" value="5.27591" />
                    <Key frame="140" value="5.27591" />
                    <Key frame="150" value="5.27591" />
                    <Key frame="160" value="5.09532" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="15" value="6.41851" />
                    <Key frame="40" value="6.5064" />
                    <Key frame="50" value="6.5064" />
                    <Key frame="60" value="5.79845" />
                    <Key frame="70" value="6.5064" />
                    <Key frame="80" value="5.79845" />
                    <Key frame="90" value="5.71537" />
                    <Key frame="100" value="5.71537" />
                    <Key frame="110" value="5.71537" />
                    <Key frame="120" value="5.89115" />
                    <Key frame="130" value="5.71537" />
                    <Key frame="140" value="5.71537" />
                    <Key frame="150" value="5.71537" />
                    <Key frame="160" value="5.79845" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="15" value="22.9421" />
                    <Key frame="40" value="12.5709" />
                    <Key frame="50" value="12.5709" />
                    <Key frame="60" value="12.4782" />
                    <Key frame="70" value="12.5709" />
                    <Key frame="80" value="12.4782" />
                    <Key frame="90" value="6.59429" />
                    <Key frame="100" value="6.59429" />
                    <Key frame="110" value="7.91266" />
                    <Key frame="120" value="6.77007" />
                    <Key frame="130" value="7.91266" />
                    <Key frame="140" value="6.59429" />
                    <Key frame="150" value="6.59429" />
                    <Key frame="160" value="12.4782" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="15" value="70.0473" />
                    <Key frame="40" value="83.7584" />
                    <Key frame="50" value="83.7584" />
                    <Key frame="60" value="80.0718" />
                    <Key frame="70" value="83.7584" />
                    <Key frame="80" value="80.0718" />
                    <Key frame="90" value="86.2194" />
                    <Key frame="100" value="86.2194" />
                    <Key frame="110" value="86.2194" />
                    <Key frame="120" value="86.9225" />
                    <Key frame="130" value="86.2194" />
                    <Key frame="140" value="86.2194" />
                    <Key frame="150" value="86.2194" />
                    <Key frame="160" value="80.0718" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="15" value="0.3072" />
                    <Key frame="40" value="0.3144" />
                    <Key frame="50" value="0.3144" />
                    <Key frame="60" value="0.2928" />
                    <Key frame="70" value="0.3144" />
                    <Key frame="80" value="0.2928" />
                    <Key frame="90" value="0.3092" />
                    <Key frame="100" value="0.3092" />
                    <Key frame="110" value="0.3092" />
                    <Key frame="120" value="0.3092" />
                    <Key frame="130" value="0.3092" />
                    <Key frame="140" value="0.3092" />
                    <Key frame="150" value="0.3092" />
                    <Key frame="160" value="0.2928" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="15" value="7.55628" />
                    <Key frame="40" value="7.3805" />
                    <Key frame="50" value="7.3805" />
                    <Key frame="60" value="4.04543" />
                    <Key frame="70" value="7.3805" />
                    <Key frame="80" value="4.04543" />
                    <Key frame="90" value="4.12851" />
                    <Key frame="100" value="4.12851" />
                    <Key frame="110" value="4.12851" />
                    <Key frame="120" value="3.86484" />
                    <Key frame="130" value="4.12851" />
                    <Key frame="140" value="4.12851" />
                    <Key frame="150" value="4.12851" />
                    <Key frame="160" value="4.04543" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="15" value="-6.50159" />
                    <Key frame="40" value="-6.85315" />
                    <Key frame="50" value="-6.85315" />
                    <Key frame="60" value="-5.97904" />
                    <Key frame="70" value="-6.85315" />
                    <Key frame="80" value="-5.97904" />
                    <Key frame="90" value="-6.2379" />
                    <Key frame="100" value="-6.2379" />
                    <Key frame="110" value="-6.2379" />
                    <Key frame="120" value="-6.2379" />
                    <Key frame="130" value="-6.2379" />
                    <Key frame="140" value="-6.2379" />
                    <Key frame="150" value="-6.2379" />
                    <Key frame="160" value="-5.97904" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="15" value="-9.66569" />
                    <Key frame="40" value="-9.5778" />
                    <Key frame="50" value="-9.5778" />
                    <Key frame="60" value="-9.5778" />
                    <Key frame="70" value="-9.5778" />
                    <Key frame="80" value="-9.5778" />
                    <Key frame="90" value="-9.48991" />
                    <Key frame="100" value="-9.48991" />
                    <Key frame="110" value="-9.48991" />
                    <Key frame="120" value="-9.40201" />
                    <Key frame="130" value="-9.48991" />
                    <Key frame="140" value="-9.48991" />
                    <Key frame="150" value="-9.48991" />
                    <Key frame="160" value="-9.5778" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="15" value="-4.91954" />
                    <Key frame="40" value="-5.71056" />
                    <Key frame="50" value="-5.71056" />
                    <Key frame="60" value="-1.145" />
                    <Key frame="70" value="-5.71056" />
                    <Key frame="80" value="-1.145" />
                    <Key frame="90" value="-0.876518" />
                    <Key frame="100" value="-0.876518" />
                    <Key frame="110" value="-0.876518" />
                    <Key frame="120" value="-0.700723" />
                    <Key frame="130" value="-0.876518" />
                    <Key frame="140" value="-0.876518" />
                    <Key frame="150" value="-0.876518" />
                    <Key frame="160" value="-1.145" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="15" value="82.4448" />
                    <Key frame="40" value="-18.1033" />
                    <Key frame="50" value="-18.1033" />
                    <Key frame="60" value="15.2029" />
                    <Key frame="70" value="-18.1033" />
                    <Key frame="80" value="15.2029" />
                    <Key frame="90" value="-26.8046" />
                    <Key frame="100" value="-26.8046" />
                    <Key frame="110" value="-65.7406" />
                    <Key frame="120" value="-61.0823" />
                    <Key frame="130" value="-65.7406" />
                    <Key frame="140" value="-26.8046" />
                    <Key frame="150" value="-26.8046" />
                    <Key frame="160" value="15.2029" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="15" value="-11.0768" />
                    <Key frame="40" value="1.0523" />
                    <Key frame="50" value="1.0523" />
                    <Key frame="60" value="4.3091" />
                    <Key frame="70" value="1.0523" />
                    <Key frame="80" value="4.3091" />
                    <Key frame="90" value="5.97423" />
                    <Key frame="100" value="5.97423" />
                    <Key frame="110" value="2.72224" />
                    <Key frame="120" value="1.31597" />
                    <Key frame="130" value="2.72224" />
                    <Key frame="140" value="5.97423" />
                    <Key frame="150" value="5.97423" />
                    <Key frame="160" value="4.3091" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="15" value="4.74374" />
                    <Key frame="40" value="19.0701" />
                    <Key frame="50" value="19.0701" />
                    <Key frame="60" value="1.40867" />
                    <Key frame="70" value="19.0701" />
                    <Key frame="80" value="1.40867" />
                    <Key frame="90" value="12.9177" />
                    <Key frame="100" value="12.9177" />
                    <Key frame="110" value="17.9275" />
                    <Key frame="120" value="17.2244" />
                    <Key frame="130" value="17.9275" />
                    <Key frame="140" value="12.9177" />
                    <Key frame="150" value="12.9177" />
                    <Key frame="160" value="1.40867" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="Plank Demo" id="7" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="310" y="162">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="240">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="1" value="-8.79157" />
                    <Key frame="30" value="5.2711" />
                    <Key frame="70" value="-8.26423" />
                    <Key frame="120" value="15.0271" />
                    <Key frame="160" value="21.7068" />
                    <Key frame="190" value="21.7068" />
                    <Key frame="220" value="21.7068" />
                    <Key frame="240" value="21.7068" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="1" value="-0.529749" />
                    <Key frame="30" value="-0.529749" />
                    <Key frame="70" value="-3.95753" />
                    <Key frame="120" value="-6.06693" />
                    <Key frame="160" value="10.0173" />
                    <Key frame="190" value="10.0173" />
                    <Key frame="220" value="10.4567" />
                    <Key frame="240" value="10.4567" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="1" value="5.00743" />
                    <Key frame="30" value="-68.7337" />
                    <Key frame="70" value="28.2108" />
                    <Key frame="120" value="-10.9889" />
                    <Key frame="160" value="27.4198" />
                    <Key frame="190" value="-48.8702" />
                    <Key frame="220" value="-0.705531" />
                    <Key frame="240" value="-0.705531" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="1" value="-6.32579" />
                    <Key frame="30" value="4.74855" />
                    <Key frame="70" value="-4.74374" />
                    <Key frame="120" value="-9.22623" />
                    <Key frame="160" value="-16.9607" />
                    <Key frame="190" value="-9.40201" />
                    <Key frame="220" value="0.0902951" />
                    <Key frame="240" value="0.705531" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="1" value="-23.2889" />
                    <Key frame="30" value="-59.5003" />
                    <Key frame="70" value="-49.5685" />
                    <Key frame="120" value="-85.1647" />
                    <Key frame="160" value="-82.44" />
                    <Key frame="190" value="-85.692" />
                    <Key frame="220" value="-89.0319" />
                    <Key frame="240" value="-89.0319" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="1" value="-68.2064" />
                    <Key frame="30" value="-48.5186" />
                    <Key frame="70" value="-52.4738" />
                    <Key frame="120" value="-84.9058" />
                    <Key frame="160" value="-96.0681" />
                    <Key frame="190" value="-92.1129" />
                    <Key frame="220" value="-85.9605" />
                    <Key frame="240" value="-85.3453" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="1" value="0.3012" />
                    <Key frame="30" value="0.0420001" />
                    <Key frame="70" value="0.0532" />
                    <Key frame="120" value="0.0532" />
                    <Key frame="160" value="0.0427999" />
                    <Key frame="190" value="0.0427999" />
                    <Key frame="220" value="0.0427999" />
                    <Key frame="240" value="0.0427999" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="1" value="7.38531" />
                    <Key frame="30" value="-39.7247" />
                    <Key frame="70" value="-72.3325" />
                    <Key frame="120" value="-72.2446" />
                    <Key frame="160" value="-88.3288" />
                    <Key frame="190" value="-86.2194" />
                    <Key frame="220" value="-12.4782" />
                    <Key frame="240" value="-13.0935" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="1" value="6.77007" />
                    <Key frame="30" value="-4.30429" />
                    <Key frame="70" value="9.58261" />
                    <Key frame="120" value="-18.6306" />
                    <Key frame="160" value="-18.6306" />
                    <Key frame="190" value="-18.6306" />
                    <Key frame="220" value="0.529749" />
                    <Key frame="240" value="0.529749" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="1" value="-9.66569" />
                    <Key frame="30" value="-13.3571" />
                    <Key frame="70" value="-60.1155" />
                    <Key frame="120" value="-61.6097" />
                    <Key frame="160" value="-51.5022" />
                    <Key frame="190" value="-64.7738" />
                    <Key frame="220" value="4.57277" />
                    <Key frame="240" value="3.86964" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="1" value="-5.27591" />
                    <Key frame="30" value="122.079" />
                    <Key frame="70" value="65.8285" />
                    <Key frame="120" value="74.7056" />
                    <Key frame="160" value="19.8611" />
                    <Key frame="190" value="120.058" />
                    <Key frame="220" value="4.56796" />
                    <Key frame="240" value="3.60116" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="1" value="83.319" />
                    <Key frame="30" value="80.7701" />
                    <Key frame="70" value="57.4788" />
                    <Key frame="120" value="46.0529" />
                    <Key frame="160" value="1.40386" />
                    <Key frame="190" value="0.788627" />
                    <Key frame="220" value="0.964409" />
                    <Key frame="240" value="0.964409" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="1" value="11.6872" />
                    <Key frame="30" value="11.072" />
                    <Key frame="70" value="37.8789" />
                    <Key frame="120" value="2.63435" />
                    <Key frame="160" value="-0.61764" />
                    <Key frame="190" value="-1.93602" />
                    <Key frame="220" value="-5.71537" />
                    <Key frame="240" value="-5.71537" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="1" value="6.58948" />
                    <Key frame="30" value="6.58948" />
                    <Key frame="70" value="-64.5149" />
                    <Key frame="120" value="-58.6262" />
                    <Key frame="160" value="-83.2359" />
                    <Key frame="190" value="-80.0718" />
                    <Key frame="220" value="-76.0287" />
                    <Key frame="240" value="-74.6225" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="1" value="5.53958" />
                    <Key frame="30" value="-67.059" />
                    <Key frame="70" value="-68.2016" />
                    <Key frame="120" value="-63.4554" />
                    <Key frame="160" value="-67.7621" />
                    <Key frame="190" value="-60.7308" />
                    <Key frame="220" value="7.82477" />
                    <Key frame="240" value="7.82477" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="1" value="6.41851" />
                    <Key frame="30" value="-4.2164" />
                    <Key frame="70" value="-2.54646" />
                    <Key frame="120" value="-0.524941" />
                    <Key frame="160" value="3.60597" />
                    <Key frame="190" value="5.97904" />
                    <Key frame="220" value="8.35212" />
                    <Key frame="240" value="8.96736" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="1" value="23.2937" />
                    <Key frame="30" value="59.593" />
                    <Key frame="70" value="16.3503" />
                    <Key frame="120" value="41.3115" />
                    <Key frame="160" value="83.8511" />
                    <Key frame="190" value="86.9273" />
                    <Key frame="220" value="88.5094" />
                    <Key frame="240" value="88.5094" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="1" value="68.0258" />
                    <Key frame="30" value="47.8986" />
                    <Key frame="70" value="39.5489" />
                    <Key frame="120" value="105.556" />
                    <Key frame="160" value="99.5789" />
                    <Key frame="190" value="97.4695" />
                    <Key frame="220" value="92.1081" />
                    <Key frame="240" value="89.9108" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="1" value="0.2984" />
                    <Key frame="30" value="0.04" />
                    <Key frame="70" value="0.0508" />
                    <Key frame="120" value="0.0404" />
                    <Key frame="160" value="0.0404" />
                    <Key frame="190" value="0.0724" />
                    <Key frame="220" value="0.1236" />
                    <Key frame="240" value="0.1524" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="1" value="7.29261" />
                    <Key frame="30" value="-39.9052" />
                    <Key frame="70" value="-25.0515" />
                    <Key frame="120" value="-76.2924" />
                    <Key frame="160" value="-87.8941" />
                    <Key frame="190" value="-87.2789" />
                    <Key frame="220" value="-11.7799" />
                    <Key frame="240" value="-12.483" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="1" value="-7.02893" />
                    <Key frame="30" value="3.78175" />
                    <Key frame="70" value="7.64898" />
                    <Key frame="120" value="14.8561" />
                    <Key frame="160" value="18.8991" />
                    <Key frame="190" value="18.8991" />
                    <Key frame="220" value="-3.86484" />
                    <Key frame="240" value="-3.86484" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="1" value="-9.66569" />
                    <Key frame="30" value="-13.3571" />
                    <Key frame="70" value="-60.1155" />
                    <Key frame="120" value="-61.6097" />
                    <Key frame="160" value="-51.5022" />
                    <Key frame="190" value="-64.7738" />
                    <Key frame="220" value="4.57277" />
                    <Key frame="240" value="3.86964" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="1" value="-5.18321" />
                    <Key frame="30" value="121.469" />
                    <Key frame="70" value="121.381" />
                    <Key frame="120" value="120.766" />
                    <Key frame="160" value="105.209" />
                    <Key frame="190" value="120.678" />
                    <Key frame="220" value="2.02391" />
                    <Key frame="240" value="1.40867" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="1" value="83.4117" />
                    <Key frame="30" value="80.5112" />
                    <Key frame="70" value="55.9015" />
                    <Key frame="120" value="8.26423" />
                    <Key frame="160" value="-17.9275" />
                    <Key frame="190" value="-7.46839" />
                    <Key frame="220" value="0.441859" />
                    <Key frame="240" value="0.441859" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="1" value="-12.1315" />
                    <Key frame="30" value="-11.5162" />
                    <Key frame="70" value="-8.26423" />
                    <Key frame="120" value="-1.93602" />
                    <Key frame="160" value="-4.13332" />
                    <Key frame="190" value="0.261268" />
                    <Key frame="220" value="9.22623" />
                    <Key frame="240" value="12.4782" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="1" value="6.58948" />
                    <Key frame="30" value="-4.48488" />
                    <Key frame="70" value="79.0123" />
                    <Key frame="120" value="-10.9889" />
                    <Key frame="160" value="-5.71537" />
                    <Key frame="190" value="19.5975" />
                    <Key frame="220" value="61.7855" />
                    <Key frame="240" value="76.9908" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="DehnenSeite Demo (1)" id="5" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="657" y="301">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="75">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="nao">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="5" value="-7.56109" />
                    <Key frame="50" value="-7.56109" />
                    <Key frame="75" value="-7.56109" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="5" value="0.793436" />
                    <Key frame="50" value="-0.793436" />
                    <Key frame="75" value="-0.793436" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="5" value="5.35899" />
                    <Key frame="50" value="5.18802" />
                    <Key frame="75" value="6.67737" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="5" value="-7.3805" />
                    <Key frame="50" value="-7.12163" />
                    <Key frame="75" value="-22.7616" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="5" value="-21.6238" />
                    <Key frame="50" value="-77.4302" />
                    <Key frame="75" value="-83.6705" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="5" value="-70.1352" />
                    <Key frame="50" value="31.7265" />
                    <Key frame="75" value="34.2754" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="5" value="0.3164" />
                    <Key frame="50" value="0.8208" />
                    <Key frame="75" value="0.8208" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="5" value="7.29742" />
                    <Key frame="50" value="7.99575" />
                    <Key frame="75" value="7.29742" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="5" value="7.12163" />
                    <Key frame="50" value="6.58948" />
                    <Key frame="75" value="43.6846" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="5" value="-8.78677" />
                    <Key frame="50" value="-8.78677" />
                    <Key frame="75" value="-8.78677" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="5" value="-5.10013" />
                    <Key frame="50" value="-4.39218" />
                    <Key frame="75" value="-5.10013" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="5" value="79.896" />
                    <Key frame="50" value="59.3245" />
                    <Key frame="75" value="57.303" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="5" value="11.3404" />
                    <Key frame="50" value="45.7013" />
                    <Key frame="75" value="61.6097" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="5" value="8.79157" />
                    <Key frame="50" value="-100.463" />
                    <Key frame="75" value="-101.166" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="5" value="5.80326" />
                    <Key frame="50" value="5.97423" />
                    <Key frame="75" value="6.06693" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="5" value="7.12163" />
                    <Key frame="50" value="7.3805" />
                    <Key frame="75" value="5.89115" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="5" value="21.0916" />
                    <Key frame="50" value="29.0946" />
                    <Key frame="75" value="29.0946" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="5" value="67.2396" />
                    <Key frame="50" value="27.9472" />
                    <Key frame="75" value="27.9472" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="5" value="0.3148" />
                    <Key frame="50" value="0.8876" />
                    <Key frame="75" value="0.8876" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="5" value="7.99575" />
                    <Key frame="50" value="7.29742" />
                    <Key frame="75" value="9.22623" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="5" value="-6.58948" />
                    <Key frame="50" value="-7.73688" />
                    <Key frame="75" value="9.84628" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="5" value="-8.78677" />
                    <Key frame="50" value="-8.78677" />
                    <Key frame="75" value="-8.78677" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="5" value="-5.00743" />
                    <Key frame="50" value="-5.10013" />
                    <Key frame="75" value="-4.39218" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="5" value="80.9459" />
                    <Key frame="50" value="-79.6275" />
                    <Key frame="75" value="-79.6275" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="5" value="-12.8298" />
                    <Key frame="50" value="5.00743" />
                    <Key frame="75" value="5.00743" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="5" value="2.55127" />
                    <Key frame="50" value="40.6915" />
                    <Key frame="75" value="40.6915" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="Plank Demo Old" id="9" localization="8" tooltip="This box is empty (contains a single motion layer with no motor position&#x0A;defined in it) and should be used to create any animation you would like." x="567" y="117">
              <bitmap>media/images/box/movement/move.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="onStopped" type="1" type_size="1" nature="1" inner="0" tooltip="Signal sent when box behavior is finished." id="4" />
              <Timeline enable="1" fps="25" start_frame="1" end_frame="-1" size="240">
                <BehaviorLayer name="behavior_layer1">
                  <BehaviorKeyframe name="keyframe1" index="1">
                    <Diagram />
                  </BehaviorKeyframe>
                </BehaviorLayer>
                <ActuatorList model="">
                  <ActuatorCurve name="value" actuator="HeadPitch" mute="0" unit="0">
                    <Key frame="1" value="-8.61579" />
                    <Key frame="30" value="6.32579" />
                    <Key frame="70" value="-10.0221" />
                    <Key frame="120" value="19.8611" />
                    <Key frame="160" value="19.949" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="HeadYaw" mute="0" unit="0">
                    <Key frame="1" value="-0.881327" />
                    <Key frame="30" value="-0.793436" />
                    <Key frame="70" value="-5.01224" />
                    <Key frame="120" value="-7.64898" />
                    <Key frame="160" value="11.5993" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnklePitch" mute="0" unit="0">
                    <Key frame="1" value="5.18321" />
                    <Key frame="30" value="-68.4701" />
                    <Key frame="70" value="28.7382" />
                    <Key frame="120" value="-11.2525" />
                    <Key frame="160" value="27.9472" />
                    <Key frame="190" value="-49.2218" />
                    <Key frame="220" value="-1.05711" />
                    <Key frame="240" value="-1.05711" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LAnkleRoll" mute="0" unit="0">
                    <Key frame="1" value="-6.67737" />
                    <Key frame="30" value="4.48488" />
                    <Key frame="70" value="-4.56796" />
                    <Key frame="120" value="-9.31412" />
                    <Key frame="160" value="-16.8728" />
                    <Key frame="190" value="-9.75358" />
                    <Key frame="220" value="0.61764" />
                    <Key frame="240" value="0.61764" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowRoll" mute="0" unit="0">
                    <Key frame="1" value="-23.2889" />
                    <Key frame="30" value="-60.555" />
                    <Key frame="70" value="-50.1838" />
                    <Key frame="120" value="-86.4831" />
                    <Key frame="160" value="-82.7916" />
                    <Key frame="240" value="-88.0651" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LElbowYaw" mute="0" unit="0">
                    <Key frame="1" value="-67.679" />
                    <Key frame="30" value="-45.9698" />
                    <Key frame="70" value="-53.4406" />
                    <Key frame="120" value="-85.8726" />
                    <Key frame="160" value="-97.3865" />
                    <Key frame="240" value="-82.0054" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHand" mute="0" unit="1">
                    <Key frame="1" value="0.2864" />
                    <Key frame="30" value="0.0272" />
                    <Key frame="70" value="0.058" />
                    <Key frame="120" value="0.0464" />
                    <Key frame="160" value="0.036" />
                    <Key frame="240" value="0.0336" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipPitch" mute="0" unit="0">
                    <Key frame="1" value="7.20952" />
                    <Key frame="30" value="-39.9883" />
                    <Key frame="70" value="-71.3657" />
                    <Key frame="120" value="-73.2993" />
                    <Key frame="160" value="-87.7135" />
                    <Key frame="190" value="-87.0104" />
                    <Key frame="220" value="-13.0056" />
                    <Key frame="240" value="-13.0056" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipRoll" mute="0" unit="0">
                    <Key frame="1" value="6.85796" />
                    <Key frame="30" value="-4.2164" />
                    <Key frame="70" value="9.93417" />
                    <Key frame="120" value="-19.2459" />
                    <Key frame="160" value="-18.2791" />
                    <Key frame="190" value="-19.2459" />
                    <Key frame="220" value="0.705531" />
                    <Key frame="240" value="0.705531" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LHipYawPitch" mute="0" unit="0">
                    <Key frame="1" value="-10.0173" />
                    <Key frame="30" value="-13.7966" />
                    <Key frame="70" value="-59.5882" />
                    <Key frame="120" value="-62.2249" />
                    <Key frame="160" value="-50.8869" />
                    <Key frame="190" value="-64.6859" />
                    <Key frame="220" value="3.34228" />
                    <Key frame="240" value="3.34228" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LKneePitch" mute="0" unit="0">
                    <Key frame="1" value="-5.80326" />
                    <Key frame="30" value="120.849" />
                    <Key frame="70" value="67.7621" />
                    <Key frame="120" value="73.9145" />
                    <Key frame="160" value="21.0037" />
                    <Key frame="190" value="120.058" />
                    <Key frame="220" value="3.51326" />
                    <Key frame="240" value="3.51326" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderPitch" mute="0" unit="0">
                    <Key frame="1" value="83.319" />
                    <Key frame="30" value="81.5611" />
                    <Key frame="70" value="55.4573" />
                    <Key frame="120" value="45.7013" />
                    <Key frame="160" value="-0.529749" />
                    <Key frame="240" value="4.91954" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LShoulderRoll" mute="0" unit="0">
                    <Key frame="1" value="11.6872" />
                    <Key frame="30" value="8.96255" />
                    <Key frame="70" value="40.0762" />
                    <Key frame="120" value="2.89802" />
                    <Key frame="160" value="-0.0902951" />
                    <Key frame="240" value="-8.35212" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="LWristYaw" mute="0" unit="0">
                    <Key frame="1" value="6.4137" />
                    <Key frame="30" value="7.20472" />
                    <Key frame="70" value="-65.8333" />
                    <Key frame="120" value="-56.8683" />
                    <Key frame="160" value="-85.521" />
                    <Key frame="240" value="-70.8431" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnklePitch" mute="0" unit="0">
                    <Key frame="1" value="5.62747" />
                    <Key frame="30" value="-67.2348" />
                    <Key frame="70" value="-67.6742" />
                    <Key frame="120" value="-63.8949" />
                    <Key frame="160" value="-67.4106" />
                    <Key frame="190" value="-60.4671" />
                    <Key frame="220" value="7.38531" />
                    <Key frame="240" value="7.38531" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RAnkleRoll" mute="0" unit="0">
                    <Key frame="1" value="7.03374" />
                    <Key frame="30" value="-4.56796" />
                    <Key frame="70" value="-2.89802" />
                    <Key frame="120" value="-0.173378" />
                    <Key frame="160" value="3.43018" />
                    <Key frame="190" value="6.41851" />
                    <Key frame="220" value="8.87946" />
                    <Key frame="240" value="8.87946" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowRoll" mute="0" unit="0">
                    <Key frame="1" value="23.4695" />
                    <Key frame="30" value="60.2961" />
                    <Key frame="70" value="16.0866" />
                    <Key frame="120" value="40.4326" />
                    <Key frame="160" value="86.0484" />
                    <Key frame="240" value="90.0035" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RElbowYaw" mute="0" unit="0">
                    <Key frame="1" value="68.3774" />
                    <Key frame="30" value="46.0529" />
                    <Key frame="70" value="38.7579" />
                    <Key frame="120" value="105.643" />
                    <Key frame="160" value="98.3484" />
                    <Key frame="240" value="87.6256" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHand" mute="0" unit="1">
                    <Key frame="1" value="0.2864" />
                    <Key frame="30" value="0.0248001" />
                    <Key frame="70" value="0.0552" />
                    <Key frame="120" value="0.0288" />
                    <Key frame="160" value="0.0504" />
                    <Key frame="240" value="0.1592" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipPitch" mute="0" unit="0">
                    <Key frame="1" value="7.02893" />
                    <Key frame="30" value="-40.2568" />
                    <Key frame="70" value="-24.6121" />
                    <Key frame="120" value="-76.5561" />
                    <Key frame="160" value="-88.4215" />
                    <Key frame="190" value="-86.7515" />
                    <Key frame="220" value="-13.5377" />
                    <Key frame="240" value="-13.5377" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipRoll" mute="0" unit="0">
                    <Key frame="1" value="-6.67737" />
                    <Key frame="30" value="4.13332" />
                    <Key frame="70" value="7.20952" />
                    <Key frame="120" value="15.4713" />
                    <Key frame="160" value="19.2507" />
                    <Key frame="190" value="18.7233" />
                    <Key frame="220" value="-3.95273" />
                    <Key frame="240" value="-3.95273" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RHipYawPitch" mute="0" unit="0">
                    <Key frame="1" value="-10.0173" />
                    <Key frame="30" value="-13.7966" />
                    <Key frame="70" value="-59.5882" />
                    <Key frame="120" value="-62.2249" />
                    <Key frame="160" value="-50.8869" />
                    <Key frame="190" value="-64.6859" />
                    <Key frame="220" value="3.34228" />
                    <Key frame="240" value="3.34228" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RKneePitch" mute="0" unit="0">
                    <Key frame="1" value="-6.06212" />
                    <Key frame="30" value="121.557" />
                    <Key frame="70" value="122.436" />
                    <Key frame="120" value="121.205" />
                    <Key frame="160" value="105.033" />
                    <Key frame="190" value="121.557" />
                    <Key frame="220" value="0.793436" />
                    <Key frame="240" value="0.793436" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderPitch" mute="0" unit="0">
                    <Key frame="1" value="83.148" />
                    <Key frame="30" value="81.6538" />
                    <Key frame="70" value="56.4289" />
                    <Key frame="120" value="8.35212" />
                    <Key frame="160" value="-18.1033" />
                    <Key frame="240" value="10.5494" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RShoulderRoll" mute="0" unit="0">
                    <Key frame="1" value="-11.6041" />
                    <Key frame="30" value="-9.84628" />
                    <Key frame="70" value="-8.44001" />
                    <Key frame="120" value="-1.49656" />
                    <Key frame="160" value="-2.1997" />
                    <Key frame="240" value="15.8181" />
                  </ActuatorCurve>
                  <ActuatorCurve name="value" actuator="RWristYaw" mute="0" unit="0">
                    <Key frame="1" value="5.09532" />
                    <Key frame="30" value="-6.24271" />
                    <Key frame="70" value="81.3853" />
                    <Key frame="120" value="-11.2525" />
                    <Key frame="160" value="-5.01224" />
                    <Key frame="240" value="82.2643" />
                  </ActuatorCurve>
                </ActuatorList>
              </Timeline>
            </Box>
            <Box name="LyingBelly" id="13" localization="8" tooltip="The robot goes from its current postition to the asked posture." x="105" y="541">
              <bitmap>media/images/box/box-diagram.png</bitmap>
              <script language="4">
                <content>
                  <![CDATA[class MyClass(GeneratedClass):
    def __init__(self):
        GeneratedClass.__init__(self, False)

    def onLoad(self):
        self.nTries = 0
        self.postureService = self.session().service("ALRobotPosture")
        pass

    def onUnload(self):
        self.postureService.stopMove()

    def onInput_onStart(self):
        if(self.nTries != self.getParameter("Maximum of tries")):
            self.nTries = self.getParameter("Maximum of tries")
            self.postureService.setMaxTryNumber(self.nTries)

        result = self.postureService.goToPosture(self.getParameter("Name"), self.getParameter("Speed (%)")/100.)
        if(result):
            self.success()
        else:
            self.failure()
        pass

    def onInput_onStop(self):
        self.onUnload() #~ it is recommanded to call onUnload of this box in a onStop method, as the code written in onUnload is used to stop the box as well
        pass]]>
                </content>
              </script>
              <Input name="onLoad" type="1" type_size="1" nature="0" inner="1" tooltip="Signal sent when diagram is loaded." id="1" />
              <Input name="onStart" type="1" type_size="1" nature="2" inner="0" tooltip="Box behavior starts when a signal is received on this input." id="2" />
              <Input name="onStop" type="1" type_size="1" nature="3" inner="0" tooltip="Box behavior stops when a signal is received on this input." id="3" />
              <Output name="success" type="1" type_size="1" nature="1" inner="0" tooltip="Stimulated if the posture has been reached." id="4" />
              <Output name="failure" type="1" type_size="1" nature="1" inner="0" tooltip="Stimulated if the posture could not be reached." id="5" />
              <Parameter name="Name" inherits_from_parent="0" content_type="3" value="LyingBelly" default_value="Stand" custom_choice="1" tooltip="Name of the posture to go to." id="6">
                <Choice value="Crouch" />
                <Choice value="LyingBack" />
                <Choice value="LyingBelly" />
                <Choice value="Sit" />
                <Choice value="SitRelax" />
                <Choice value="StandInit" />
                <Choice value="Stand" />
                <Choice value="StandZero" />
              </Parameter>
              <Parameter name="Speed (%)" inherits_from_parent="0" content_type="1" value="80" default_value="80" min="0" max="100" tooltip="Speed to go to the posture." id="7" />
              <Parameter name="Maximum of tries" inherits_from_parent="0" content_type="1" value="3" default_value="3" min="1" max="10" tooltip="The maximum number of fails of go to posture before stimulating the failure output." id="8" />
              <Resource name="All motors" type="Lock" timeout="0" />
              <Resource name="Stiffness" type="Lock" timeout="0" />
            </Box>
            <Link inputowner="2" indexofinput="2" outputowner="0" indexofoutput="2" />
          </Diagram>
        </BehaviorKeyframe>
      </BehaviorLayer>
    </Timeline>
  </Box>
</ChoregrapheProject>
