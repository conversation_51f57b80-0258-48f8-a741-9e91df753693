# Use Ubuntu 22.04 as the base image
FROM ubuntu:22.04

RUN apt update && apt install -y --no-install-recommends \
    wget \
    tar \
    libglib2.0-0 \
    sqlite3 \
    libflac8 \
    libvorbisenc2 \
    libdbus-1-3 \
    libgl1-mesa-dev \
    portaudio19-dev \
    netcat-openbsd \
    curl \
    gcc \
    sudo \
    && rm -rf /var/lib/apt/lists/* \
    && apt clean

# Install uv
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uv /uvx /bin/


# Set the working directory
WORKDIR /opt/naoqi

# Download the tar.gz file
RUN wget --no-check-certificate https://community-static.aldebaran.com/resources/2.8.8/choregraphe-2.8.8-ubuntu2204-standalone.tar.gz \
    && tar -xzf choregraphe-2.8.8-ubuntu2204-standalone.tar.gz \
    && rm choregraphe-2.8.8-ubuntu2204-standalone.tar.gz


# Create a new user and add it to the sudo group
# Simulation does not start if we dont do this,
# can be fixed by figuring out writable path argument 
RUN useradd -m max && \
    usermod -aG sudo max && \
    echo "max ALL=(ALL) NOPASSWD: ALL" >> /etc/sudoers

USER max
WORKDIR /opt/naoqi/choregraphe-2.8.8-ubuntu2204/bin

# Create script to run naoqi-bin, check logs, and modify file
COPY --chown=max:max check_and_modify.sh /home/<USER>/check_and_modify.sh

RUN chmod +x /home/<USER>/check_and_modify.sh
RUN /home/<USER>/check_and_modify.sh

# Expose port 8432 for the Gateway
EXPOSE 8432
# Run the naoqi-bin binary
CMD ["./naoqi-bin", "--qi-listen-url 127.0.0.1,0.0.0.0"]

