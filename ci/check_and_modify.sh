#!/bin/bash

# Exit immediately if a command exits with a non-zero status.
set -e

# --- Configuration ---
# Define file paths and the target log message for clarity and easy modification.
LOG_FILE="/home/<USER>/naoqi_log.txt"
NAOQI_BIN_PATH="/opt/naoqi/choregraphe-2.8.8-ubuntu2204/bin/naoqi-bin"
MANIFEST_FILE="/home/<USER>/.local/share/PackageManager/apps/core/manifest.xml"
TARGET_LOG_LINE="packagemanager: getPackages is deprecated please use packages instead."
OLD_URL="tcp://0.0.0.0:9559"
NEW_URL="tcp://0.0.0.0:8432"
WAIT_INTERVAL=5 # Seconds to wait between log checks
MAX_WAIT_TIME=300 # Maximum seconds to wait for the log line (5 minutes)
NAO_ROBOT_DESCRIPTION_FILE_NAME="NAOH25V60.xml"
# Define the path to choregraphe directory based on the NAOQI_BIN_PATH
CHOREGRAPHE_DIR=$(dirname "$(dirname "$NAOQI_BIN_PATH")")
# Path to the ALRobotModel.xml file
ROBOT_MODEL_FILE="$CHOREGRAPHE_DIR/etc/naoqi/ALRobotModel.xml"
# Original robot model file name to be replaced
OLD_ROBOT_MODEL="JULIETTEY20B2C.xml"


# --- Script Logic ---

# Ensure potential leftover log file is removed before starting
rm -f "$LOG_FILE"

echo "Starting naoqi-bin in background, logging to $LOG_FILE..."
# Start naoqi-bin, redirecting both stdout and stderr to the log file, run in background.
"$NAOQI_BIN_PATH" &> "$LOG_FILE" &
NAOQI_PID=$!
echo "naoqi-bin started with PID: $NAOQI_PID"

# Wait for the background process to potentially start and create the log file
sleep 2

echo "Waiting for target log line: '$TARGET_LOG_LINE'"
start_time=$(date +%s)

# Loop until the specific log line is found or timeout occurs
while true; do
    current_time=$(date +%s)
    elapsed_time=$((current_time - start_time))

    # Check for timeout
    if [ "$elapsed_time" -ge "$MAX_WAIT_TIME" ]; then
        echo "Error: Timeout waiting for target log line after ${MAX_WAIT_TIME} seconds." >&2
        # Attempt to kill the process before exiting
        echo "Killing naoqi-bin (PID: $NAOQI_PID)..." >&2
        kill "$NAOQI_PID" || echo "Failed to kill process $NAOQI_PID (maybe already stopped?)" >&2
        # Clean up log file on timeout failure
        rm -f "$LOG_FILE"
        exit 1 # Exit with error status
    fi

    # Check if the log file exists and is readable before trying to grep
    if [ -r "$LOG_FILE" ]; then
        # Use grep -q for quiet check (no output), check exit status directly
        if grep -qF "$TARGET_LOG_LINE" "$LOG_FILE"; then
            echo "Target log line found after $elapsed_time seconds."
            break # Exit the loop
        else
            # Log line not found yet
            # Optional: Print last line for debugging (can be noisy)
            # LAST_LINE=$(tail -n 1 "$LOG_FILE")
            # echo "Last log line: $LAST_LINE"
            echo "Log line not found yet. Waiting $WAIT_INTERVAL seconds... (Elapsed: ${elapsed_time}s)"
        fi
    else
        # Log file doesn't exist or isn't readable yet
        echo "Log file '$LOG_FILE' not found or not readable yet. Waiting $WAIT_INTERVAL seconds... (Elapsed: ${elapsed_time}s)"
    fi

    # Check if the background process is still running
    if ! kill -0 "$NAOQI_PID" 2>/dev/null; then
       echo "Error: naoqi-bin process (PID: $NAOQI_PID) stopped unexpectedly." >&2
       # Check the log file for potential errors
       if [ -r "$LOG_FILE" ]; then
           echo "Last 10 lines of log:" >&2
           tail -n 10 "$LOG_FILE" >&2
       fi
       rm -f "$LOG_FILE" # Clean up log file
       exit 1 # Exit with error status
    fi


    sleep "$WAIT_INTERVAL"
done

# --- File Modification ---
echo "Modifying manifest file: $MANIFEST_FILE"
# Check if manifest file exists before attempting modification
if [ ! -f "$MANIFEST_FILE" ]; then
    echo "Error: Manifest file '$MANIFEST_FILE' not found!" >&2
    # Attempt to kill the process before exiting
    echo "Killing naoqi-bin (PID: $NAOQI_PID)..." >&2
    kill "$NAOQI_PID" || echo "Failed to kill process $NAOQI_PID (maybe already stopped?)" >&2
    rm -f "$LOG_FILE" # Clean up log file
    exit 1 # Exit with error status
fi

# Use sed to replace the URL. Use a different delimiter (#) in case URLs contain slashes.
# Add error checking for sed command.
if sed -i "s#${OLD_URL}#${NEW_URL}#g" "$MANIFEST_FILE"; then
    echo "Manifest file successfully modified."
else
    echo "Error: Failed to modify manifest file '$MANIFEST_FILE' with sed." >&2
    # Attempt to kill the process before exiting
    echo "Killing naoqi-bin (PID: $NAOQI_PID)..." >&2
    kill "$NAOQI_PID" || echo "Failed to kill process $NAOQI_PID (maybe already stopped?)" >&2
    rm -f "$LOG_FILE" # Clean up log file
    exit 1 # Exit with error status
fi

# --- Robot Model File Modification ---
echo "Modifying robot model file: $ROBOT_MODEL_FILE"
# Check if robot model file exists before attempting modification
if [ ! -f "$ROBOT_MODEL_FILE" ]; then
    echo "Error: Robot model file '$ROBOT_MODEL_FILE' not found!" >&2
    # Attempt to kill the process before exiting
    echo "Killing naoqi-bin (PID: $NAOQI_PID)..." >&2
    kill "$NAOQI_PID" || echo "Failed to kill process $NAOQI_PID (maybe already stopped?)" >&2
    rm -f "$LOG_FILE" # Clean up log file
    exit 1 # Exit with error status
fi

# Use sed to replace the robot model file name. 
if sed -i "s#${OLD_ROBOT_MODEL}#${NAO_ROBOT_DESCRIPTION_FILE_NAME}#g" "$ROBOT_MODEL_FILE"; then
    echo "Robot model file successfully modified."
else
    echo "Error: Failed to modify robot model file '$ROBOT_MODEL_FILE' with sed." >&2
    # Attempt to kill the process before exiting
    echo "Killing naoqi-bin (PID: $NAOQI_PID)..." >&2
    kill "$NAOQI_PID" || echo "Failed to kill process $NAOQI_PID (maybe already stopped?)" >&2
    rm -f "$LOG_FILE" # Clean up log file
    exit 1 # Exit with error status
fi

# --- Cleanup ---
echo "Stopping naoqi-bin process (PID: $NAOQI_PID)..."
# Stop the naoqi-bin process gracefully first, then forcefully if needed
if ! kill "$NAOQI_PID"; then
    echo "Warning: Failed to kill process $NAOQI_PID gracefully (maybe already stopped?)."
    # Optional: Add kill -9 $NAOQI_PID after a short wait if needed
fi
# Wait briefly for the process to terminate
sleep 2

echo "Cleaning up temporary files..."
# Delete log file
rm -f "$LOG_FILE"

# Define paths for cleanup
BREAKPAD_DIR="/home/<USER>/.local/share/aldebaran/breakpad"
PKG_LOGS_DIR="/home/<USER>/.local/share/PackageManager/logs"

# Delete breakpad files if the directory exists
if [ -d "$BREAKPAD_DIR" ]; then
    echo "Removing breakpad files in $BREAKPAD_DIR..."
    rm -rf "${BREAKPAD_DIR:?}/"* # Added :? for safety against empty/unset variable
else
    echo "Breakpad directory not found, skipping cleanup."
fi

# Delete package manager logs if the directory exists
if [ -d "$PKG_LOGS_DIR" ]; then
    echo "Removing package manager logs in $PKG_LOGS_DIR..."
    rm -rf "${PKG_LOGS_DIR:?}/"* # Added :? for safety
else
    echo "Package manager logs directory not found, skipping cleanup."
fi


echo "Script finished successfully."
exit 0
