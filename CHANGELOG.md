# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- New `mobirobot.execution` module for centralized exercise session management
- `SessionManager` class consolidating functionality from Application, Orchestrator, and ExecutionManager
- Modular behavior building and execution system (`behavior_builder.py`, `behavior_executor.py`)
- Enhanced async task management with graceful cancellation (`task_manager.py`)
- Standardized service dependency injection (`services.py`)
- Robot control functions extracted from ExecutionManager (`robot_control.py`)
- Centralized data organization under `data/` directory
- New API schemas for type-safe web interactions (`schemas.py`)
- Workspace-based package architecture with independent versioning
- Dedicated packages: `mobirobot`, `mobirobot-api`, `mobirobot-cli`

### Changed
- **BREAKING**: Migrated from `src/exerciser` to `mobirobot.execution` architecture
- **BREAKING**: Reorganized codebase into workspace packages:
  - `src/mobirobot/` → `packages/mobirobot/`
  - `src/web/` → `packages/mobirobot-api/`
  - `src/mobirobot-cli/` → `packages/mobirobot-cli/`
  - `src/llm_interaction/` → integrated into `packages/mobirobot/llm_interaction/`
- **BREAKING**: Moved static data from root-level directories to `data/` structure:
  - `exercises/` → `data/exercises/`
  - LLM data → `data/prompts/`
  - Speech data → `data/sentences/`
- Updated web backend to use new execution services directly
- Simplified router implementations with cleaner service injection
- Enhanced error handling and logging throughout mobirobot services
- Improved pose detection integration with exercise tracking
- Updated configuration to support new workspace structure

### Removed
- **BREAKING**: Deprecated `src/exerciser/` module:
  - `application.py` (replaced by `mobirobot.execution.session_manager`)
  - `orchestrator.py` (split into modular `behavior_builder` and `behavior_executor`)
  - `task_management.py` (replaced by enhanced `task_manager`)
  - `exercise_executor.py` (functionality moved to execution module)
  - `behaviours/` directory (migrated to `mobirobot.execution.behaviors`)
- **BREAKING**: Entire `src/` directory structure (migrated to `packages/` workspace)
- Legacy `ExecutionManager` facade (replaced by direct execution service usage)
- Old test files and snapshots that relied on deprecated architecture
- Redundant service classes (`status_monitor_service`, execution manager services)

### Fixed
- Path resolution for new data directory structure
- Service initialization order and dependency management
- WebSocket handling with improved error recovery
- Simulation test compatibility with new architecture
- Package imports and dependencies for workspace structure

### Technical Details

This release represents a major architectural refactor that:

1. **Improves Maintainability**: Breaks down complex 500+ line classes into focused, single-responsibility modules
2. **Enhances Testability**: Functions and smaller classes are easier to unit test than large orchestrator classes
3. **Better Separation of Concerns**: Clear boundaries between session management, behavior execution, robot control, and state management
4. **Simplified Dependencies**: Explicit service container pattern replaces complex factory methods
5. **Cleaner Data Organization**: Centralized static data structure improves project navigation
6. **Workspace Architecture**: Independent package versioning and cleaner dependency management
7. **Modular Development**: Separate packages for API, core services, and CLI tools
8. **Preserved Functionality**: All existing behavior contracts and API endpoints remain unchanged

The refactor maintains backward compatibility at the API level while significantly improving the internal architecture for future development and maintenance. The new workspace structure enables independent development and deployment of different system components.