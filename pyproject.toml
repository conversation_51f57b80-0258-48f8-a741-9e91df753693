#:schema https://raw.githubusercontent.com/SchemaStore/schemastore/master/src/schemas/json/uv.json
[project]
name = "bewegungstherapie-mit-roboter"
version = "0.1.0"
description = ""
authors = [{ name = "Your Name", email = "<EMAIL>" }]
requires-python = ">=3.12"
readme = "README.md"
dependencies = [
    "fastapi[standard]>=0.112.1",
    "numpy>=1.23.0,<2",
    "openai>=1.25.1,<2",
    "speechrecognition>=3.14.1,<4",
    "pandas-stubs>=2.2.1.240316,<3",
    "faster-whisper>=1.1.1,<2",
    "pandas>=2.2.2,<3",
    "scikit-learn>1.2.2,<2",
    "pyaudio>=0.2.14,<0.3",
    "pyudev>=0.24.3,<0.25",
    "pydantic-settings>=2.6.0",
    "sentry-sdk>=2.17.0,<3",
    "python-dotenv>=1.0.1,<2",
    "sounddevice>=0.5.1,<0.6",
    "soundfile>=0.13.1,<0.14",
    "mediapipe~=0.10.11",
    "opencv-python-headless>=*********",
    "opencv-contrib-python>=*********",
    "nao",
    "buttons",
    "qi-stubs",
    "mobirobot",
    "mobirobot-cli",
    "psutil>=7.0.0",
    "structlog>=25.4.0",
    "orjson>=3.10.18",
]

[dependency-groups]
dev = [
    "pytest>=8.3.5",
    "pytest-cov>=6.0.0",
    "hypothesis>=6.118.8",
    "syrupy>=4.7.2",
    "pytest-sugar>=1.0.0",
    "ruff>=0.7.4",
    "basedpyright>=1.29.2",
    "poethepoet>=0.33.1",
    "pytest-mock>=3.14.0",
    "tombi>=0.3.57",
    "ty>=0.0.1a8",
    "mobirobot-api",
    "nao-simulation",
]
[tool.uv]
package = false
default-groups = ["dev"]

[tool.uv.workspace]
members = [
 "packages/*",
]

[tool.uv.sources]
nao = { workspace = true }
buttons = { workspace = true }
qi-stubs = { workspace = true }
mobirobot = { workspace = true }
mobirobot-cli = { workspace = true }
mobirobot-api = { workspace = true }
nao-simulation = { workspace = true }

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.pytest.ini_options]
testpaths = ["packages/", "./tests"]
addopts = "-m 'not simulation and not snapshot and not regiment_simulation'"
markers = [
    "simulation: mark as simulation test",
    "snapshot: mark as snapshot test",
    "regiment_simulation: mark as regiment simulation test",
    "unit: mark as unit test",
    "integration: mark as integration test",
]

[tool.basedpyright]
reportUnusedCallResult = false
reportPrivateUsage = false
typeCheckingMode = "recommended"
failOnWarnings = false
exclude = ["**/moves", "**/stretches", "**/demos", "**/.venv", "./util/*", "./stubs/*"]
include = ["packages/**", "tests/**"]
stubPath = "./stubs"

[tool.ruff]
target-version = "py312"
extend-include = ["./packages/", "./tests"]
exclude = ["**/moves", "**/stretches", "**/demos", "./util/*", "**/models/*"]
unsafe-fixes = true
respect-gitignore = true

[tool.ruff.lint]
extend-select = ["ALL"]
preview = true
ignore = ["D100", "E501", "ANN401", "D105", "S311", "S403", "ISC003", "ASYNC109", "S301", "CPY", "FBT", "COM812", "TD", "S404", "S104", "S603"]
extend-fixable = ["D301", "D205"]

[tool.ruff.lint.pydocstyle]
convention = "google"

[tool.ruff.lint.pydoclint]
ignore-one-line-docstrings = true

[tool.ruff.lint.flake8-annotations]
allow-star-arg-any = true
ignore-fully-untyped = true
mypy-init-return = true
suppress-none-returning = true

[tool.ruff.lint.flake8-pytest-style]
fixture-parentheses = true

[tool.ruff.lint.per-file-ignores]
"**/tests/*" = ["DOC", "D", "S101", "SLF001", "PLR2004", "B903"]
"**/tests/__init__.py" = ["D104"]
"./packages/qi-stubs/**/*.py" = ["N802", "N803", "D418"]
"./stubs/**/*.pyi" = ["N802", "N803"]
"packages/mobirobot-cli/**/*.py" = ["TRY301"]

[tool.ruff.format]
quote-style = "double"
docstring-code-format = true

[tool.coverage.run]
source = ["packages", "tests"]
omit = [
    "*/tests/*",
    "*/moves/*",
    "*/stretches/*",
    "*/demos/*",
    "./util/*",
    "*/stubs/*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
show_missing = true
skip_covered = false
precision = 2
